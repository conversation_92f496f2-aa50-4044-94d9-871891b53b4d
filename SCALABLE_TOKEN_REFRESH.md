# Scalable Token Refresh Implementation

## Overview

This document describes the scalable implementation of GoHighLevel token refresh using Redis-based distributed locking, which prevents the "invalid_grant" error in multi-instance deployments.

## Problem Solved

- **Race Conditions**: Multiple server instances trying to refresh the same token simultaneously
- **Invalid <PERSON> Errors**: Using already-consumed refresh tokens
- **Scalability**: Works across multiple server instances

## Implementation Details

### 1. Redis-based Distributed Lock

The implementation uses Redis to create a distributed lock that ensures only one instance can refresh a token at a time:

```typescript
const lockKey = `ghl:token_refresh_lock:${locationId}`;
const lockValue = `${Date.now()}_${Math.random()}`; // Unique per instance
const lockTTL = 30000; // 30 seconds

// Try to acquire lock (SET NX with expiration)
const lockAcquired = await this.redis.set(
  lockKey,
  lockValue,
  'PX',
  lockTTL,
  'NX',
);
```

### 2. Lock Acquisition Strategy

- **Unique Lock Value**: Each instance generates a unique value to identify its lock
- **Atomic Operation**: Uses Redis SET with NX (only if not exists) to ensure atomicity
- **TTL Protection**: 30-second expiration prevents deadlocks if an instance crashes
- **Retry Logic**: Waits up to 5 seconds (50 retries × 100ms) for lock acquisition

### 3. Smart Waiting

While waiting for a lock, the system:

- Checks every second if tokens were recently refreshed
- Uses document timestamps to detect fresh tokens
- Returns immediately if fresh tokens are found

### 4. Fallback Mechanism

If Redis is unavailable, the system falls back to in-memory locking:

```typescript
catch (error) {
  this.logger.error(`Redis error: ${error.message}`);
  return this.refereshTokenWithInMemoryLock(locationId);
}
```

### 5. Lock Release

The lock is only released by the instance that created it:

```typescript
const currentValue = await this.redis.get(lockKey);
if (currentValue === lockValue) {
  await this.redis.del(lockKey);
}
```

## Benefits

1. **Prevents Race Conditions**: Only one refresh per location across all instances
2. **High Availability**: Fallback to in-memory if Redis fails
3. **Performance**: Reduces unnecessary API calls to GoHighLevel
4. **Resilience**: Handles instance crashes with TTL
5. **Observability**: Comprehensive logging for debugging

## Configuration

Add these environment variables:

```bash
REDIS_HOST=your-redis-host
REDIS_PORT=6379
REDIS_PASSWORD=your-redis-password  # Optional
```

## Monitoring

Monitor these log messages:

- `Acquired refresh lock for location: {locationId}`
- `Released refresh lock for location: {locationId}`
- `Waiting for existing refresh to complete for location: {locationId}`
- `Found recently refreshed token for location: {locationId}`
- `Redis error during token refresh lock: {error}`

## Testing

To test the implementation:

1. **Single Instance**: Should work as before
2. **Multiple Instances**: Start multiple instances and trigger concurrent API calls
3. **Redis Failure**: Stop Redis and verify fallback works
4. **Lock Timeout**: Test with slow token refresh to verify timeout handling

## Future Improvements

1. **Metrics**: Add Prometheus metrics for lock wait times
2. **Circuit Breaker**: Implement circuit breaker for Redis failures
3. **Lock Duration**: Make lock TTL configurable
4. **Distributed Cache**: Cache tokens in Redis for faster access
