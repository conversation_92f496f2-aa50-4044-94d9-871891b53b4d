version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production # Use production stage
    ports:
      - '3000:3000'
    environment:
      NODE_ENV: production
      # You'll need to set these based on your environment
      GCS_ENV_BUCKET: enrollio_env
      GCS_ENV_FILE: env.staging
      # For local testing with external Redis
      REDIS_HOST: redis-16026.c82.us-east-1-2.ec2.redns.redis-cloud.com
      REDIS_PORT: 16026
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      REDIS_USERNAME: default
    networks:
      - enrollio-network

networks:
  enrollio-network:
    driver: bridge 