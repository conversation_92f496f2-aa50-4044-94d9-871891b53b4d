version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: builder # Use builder stage for development
    command: npm run start:dev
    ports:
      - '3000:3000'
    volumes:
      - ./src:/app/src
      - ./node_modules:/app/node_modules
    environment:
      NODE_ENV: development
      # Add your development environment variables here
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_USERNAME: ''
      REDIS_PASSWORD: ''
    depends_on:
      - redis
    networks:
      - enrollio-network

  redis:
    image: redis:7-alpine
    ports:
      - '6379:6379'
    volumes:
      - redis-data:/data
    networks:
      - enrollio-network

volumes:
  redis-data:

networks:
  enrollio-network:
    driver: bridge 