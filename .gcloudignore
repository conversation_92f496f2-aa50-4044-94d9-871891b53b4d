# This file specifies files that are *not* uploaded to Google Cloud
# using gcloud. It follows the same syntax as .gitignore, with the addition of
# "#!include" directives (which insert the entries of the given .gitignore-style
# file at that point).
#
# For more information, run:
#   $ gcloud topic gcloudignore
#
.gcloudignore
# If you would like to upload your .git directory, .gitignore file or files
# from your .gitignore file, remove the corresponding line
# below:
.git
.gitignore

# Node.js dependencies:
node_modules/

# IMPORTANT: Ensure the 'dist' folder (your build output) IS deployed
# If 'dist/' is in your .gitignore, this line will make sure it's NOT ignored by gcloud deploy.
!dist/

# Additional ignores from app.yaml skip_files
.github/
src/
tests/
test/
README.md
Dockerfile
nest-cli.json
tsconfig.*.json
.env
# .*ignore is covered by .gitignore and .gcloudignore itself
