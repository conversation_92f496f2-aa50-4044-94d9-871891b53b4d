import { describe, it, expect } from 'vitest';
import { isScheduledEntity } from '../src/utils/helpers/billing-proration';
import * as dayjs from 'dayjs';

describe('isScheduledEntity', () => {
  it('returns true if billingDate is in the future', () => {
    const entity = {
      session: {
        billingDate: dayjs().add(2, 'days').toISOString(),
      },
    };
    expect(isScheduledEntity(entity)).toBe(true);
  });

  it('returns false if billingDate is today', () => {
    const entity = {
      session: {
        billingDate: dayjs().toISOString(),
      },
    };
    expect(isScheduledEntity(entity)).toBe(false);
  });

  it('returns false if billingDate is in the past', () => {
    const entity = {
      session: {
        billingDate: dayjs().subtract(2, 'days').toISOString(),
      },
    };
    expect(isScheduledEntity(entity)).toBe(false);
  });

  it('returns false if session is missing', () => {
    const entity = {};
    expect(isScheduledEntity(entity)).toBe(false);
  });

  it('returns false if billingDate is null', () => {
    const entity = {
      session: {
        billingDate: null,
      },
    };
    expect(isScheduledEntity(entity)).toBe(false);
  });
});
