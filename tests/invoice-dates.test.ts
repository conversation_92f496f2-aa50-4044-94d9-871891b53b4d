import { describe, it, expect } from 'vitest';
import { generateInvoiceDates } from '../src/utils/invoiceDates';

const DEBUG_INVOICE_DATES = true;

const logDebug = (
  testName: string,
  startDate: Date,
  endDate: Date,
  cycle: string,
  billingDay: number | undefined,
  dates: Date[],
  expected: string[],
) => {
  if (DEBUG_INVOICE_DATES) {
    console.log('\n' + '='.repeat(60));
    console.log(`🔍 DEBUG: ${testName}`);
    console.log('='.repeat(60));
    console.log('📅 Input Parameters:');
    console.log(
      `   Start Date: ${startDate.toISOString().split('T')[0]} (${startDate.toDateString()})`,
    );
    console.log(
      `   End Date: ${endDate.toISOString().split('T')[0]} (${endDate.toDateString()})`,
    );
    console.log(`   Billing Cycle: ${cycle}`);
    console.log(`   Billing Day: ${billingDay || 'N/A'}`);
    console.log('📅 Expected dates:', expected);
    console.log(
      '🎯 Generated dates:',
      dates.map((d) => d.toISOString().split('T')[0]),
    );
    console.log('📊 Expected count:', expected.length);
    console.log('📊 Generated count:', dates.length);
    console.log(
      '✅ Match:',
      JSON.stringify(dates.map((d) => d.toISOString().split('T')[0])) ===
        JSON.stringify(expected),
    );
    console.log('='.repeat(60));
  }
};

describe('Invoice Dates Generation', () => {
  describe('Monthly Billing Scenarios', () => {
    it('Monthly 1: Billing Day 6th - Join Sept 15th, End Dec 18th', () => {
      const dates = generateInvoiceDates(
        new Date('2025-09-15'),
        new Date('2025-12-18'),
        'monthly',
        6,
      );

      const expected = ['2025-09-06', '2025-10-06', '2025-11-06', '2025-12-06'];

      logDebug(
        'Monthly 1: Billing Day 6th - Join Sept 15th, End Dec 18th',
        new Date('2025-09-15'),
        new Date('2025-12-18'),
        'monthly',
        6,
        dates,
        expected,
      );

      expect(dates).toHaveLength(4);
      expect(dates.map((d) => d.toISOString().split('T')[0])).toEqual(expected);
    });

    it('Monthly 2: Billing Day 6th - Join Sept 26th, End Dec 1st', () => {
      const dates = generateInvoiceDates(
        new Date('2025-09-26'),
        new Date('2025-12-01'),
        'monthly',
        6,
      );

      const expected = ['2025-09-06', '2025-10-06', '2025-11-06'];

      logDebug(
        'Monthly 2: Billing Day 6th - Join Sept 26th, End Dec 1st',
        new Date('2025-09-26'),
        new Date('2025-12-01'),
        'monthly',
        6,
        dates,
        expected,
      );

      expect(dates).toHaveLength(3);
      expect(dates.map((d) => d.toISOString().split('T')[0])).toEqual(expected);
    });

    it('Monthly 3: Billing Day 6th - Join Sept 1st, End Dec 18th', () => {
      const dates = generateInvoiceDates(
        new Date('2025-09-01'),
        new Date('2025-12-18'),
        'monthly',
        6,
      );

      const expected = ['2025-09-06', '2025-10-06', '2025-11-06', '2025-12-06'];

      logDebug(
        'Monthly 3: Billing Day 6th - Join Sept 1st, End Dec 18th',
        new Date('2025-09-01'),
        new Date('2025-12-18'),
        'monthly',
        6,
        dates,
        expected,
      );

      expect(dates).toHaveLength(4);
      expect(dates.map((d) => d.toISOString().split('T')[0])).toEqual(expected);
    });

    it('Monthly 4: Billing Day 6th - Join Sept 28th, End Dec 18th', () => {
      const dates = generateInvoiceDates(
        new Date('2025-09-28'),
        new Date('2025-12-18'),
        'monthly',
        6,
      );

      const expected = ['2025-09-06', '2025-10-06', '2025-11-06', '2025-12-06'];

      logDebug(
        'Monthly 4: Billing Day 6th - Join Sept 28th, End Dec 18th',
        new Date('2025-09-28'),
        new Date('2025-12-18'),
        'monthly',
        6,
        dates,
        expected,
      );

      expect(dates).toHaveLength(4);
      expect(dates.map((d) => d.toISOString().split('T')[0])).toEqual(expected);
    });

    it('Monthly 5: Year end transition', () => {
      const dates = generateInvoiceDates(
        new Date('2025-12-15'),
        new Date('2026-01-05'),
        'monthly',
        6,
      );

      const expected = ['2025-12-06'];

      logDebug(
        'Monthly 5: Year end transition',
        new Date('2025-12-15'),
        new Date('2026-01-05'),
        'monthly',
        6,
        dates,
        expected,
      );

      expect(dates).toHaveLength(1);
      expect(dates.map((d) => d.toISOString().split('T')[0])).toEqual(expected);
    });

    it('Monthly 6: Long term subscription', () => {
      const dates = generateInvoiceDates(
        new Date('2025-09-15'),
        new Date('2026-07-07'),
        'monthly',
        6,
      );

      const expected = [
        '2025-09-06',
        '2025-10-06',
        '2025-11-06',
        '2025-12-06',
        '2026-01-06',
        '2026-02-06',
        '2026-03-06',
        '2026-04-06',
        '2026-05-06',
        '2026-06-06',
        '2026-07-06',
      ];

      logDebug(
        'Monthly 6: Long term subscription',
        new Date('2025-09-15'),
        new Date('2026-07-07'),
        'monthly',
        6,
        dates,
        expected,
      );

      expect(dates).toHaveLength(11);
      expect(dates.map((d) => d.toISOString().split('T')[0])).toEqual(expected);
    });

    it('Monthly 7: Multiple months with partial start', () => {
      const dates = generateInvoiceDates(
        new Date('2025-06-28'),
        new Date('2025-10-03'),
        'monthly',
        1,
      );

      const expected = [
        '2025-06-01',
        '2025-07-01',
        '2025-08-01',
        '2025-09-01',
        '2025-10-01',
      ];

      logDebug(
        'Monthly 7: Multiple months with partial start',
        new Date('2025-06-28'),
        new Date('2025-10-03'),
        'monthly',
        1,
        dates,
        expected,
      );

      expect(dates).toHaveLength(5);
      expect(dates.map((d) => d.toISOString().split('T')[0])).toEqual(expected);
    });
  });

  describe('Weekly Billing Scenarios', () => {
    it('Weekly 1: Billing Day 2nd (Tuesday)', () => {
      const dates = generateInvoiceDates(
        new Date('2025-06-02'),
        new Date('2025-07-15'),
        'weekly',
      );

      const expected = [
        '2025-06-02',
        '2025-06-09',
        '2025-06-16',
        '2025-06-23',
        '2025-06-30',
        '2025-07-07',
        '2025-07-14',
      ];

      logDebug(
        'Weekly 1: Billing Day 2nd (Tuesday)',
        new Date('2025-06-02'),
        new Date('2025-07-15'),
        'weekly',
        undefined,
        dates,
        expected,
      );

      expect(dates).toHaveLength(7);
      expect(dates.map((d) => d.toISOString().split('T')[0])).toEqual(expected);
    });

    it('Weekly 2: Billing Day 6th (Friday)', () => {
      const dates = generateInvoiceDates(
        new Date('2025-06-01'),
        new Date('2025-07-31'),
        'weekly',
      );

      logDebug(
        'Weekly 2: Billing Day 6th (Friday)',
        new Date('2025-06-01'),
        new Date('2025-07-31'),
        'weekly',
        undefined,
        dates,
        ['Expected: 9 weekly dates'],
      );

      expect(dates).toHaveLength(9);
    });

    it('Weekly 3: Partial week at start and end', () => {
      const dates = generateInvoiceDates(
        new Date('2025-06-03'),
        new Date('2025-06-17'),
        'weekly',
      );

      logDebug(
        'Weekly 3: Partial week at start and end',
        new Date('2025-06-03'),
        new Date('2025-06-17'),
        'weekly',
        undefined,
        dates,
        ['Expected: 2 weekly dates'],
      );

      expect(dates).toHaveLength(2);
    });
  });

  describe('Bi-Weekly Billing Scenarios', () => {
    it('Bi-Weekly 1: Billing Day 2nd (Monday)', () => {
      const dates = generateInvoiceDates(
        new Date('2025-06-02'),
        new Date('2025-08-15'),
        'bi-weekly',
      );

      const expected = [
        '2025-06-02',
        '2025-06-16',
        '2025-06-30',
        '2025-07-14',
        '2025-07-28',
        '2025-08-11',
      ];

      logDebug(
        'Bi-Weekly 1: Billing Day 2nd (Monday)',
        new Date('2025-06-02'),
        new Date('2025-08-15'),
        'bi-weekly',
        undefined,
        dates,
        expected,
      );

      expect(dates).toHaveLength(6);
      expect(dates.map((d) => d.toISOString().split('T')[0])).toEqual(expected);
    });

    it('Bi-Weekly 2: Billing Day 6th (Friday)', () => {
      const dates = generateInvoiceDates(
        new Date('2025-06-01'),
        new Date('2025-09-01'),
        'bi-weekly',
      );

      logDebug(
        'Bi-Weekly 2: Billing Day 6th (Friday)',
        new Date('2025-06-01'),
        new Date('2025-09-01'),
        'bi-weekly',
        undefined,
        dates,
        ['Expected: 7 bi-weekly dates'],
      );

      expect(dates).toHaveLength(7);
    });

    it('Bi-Weekly 3: Partial periods at start and end', () => {
      const dates = generateInvoiceDates(
        new Date('2025-06-03'),
        new Date('2025-06-25'),
        'bi-weekly',
      );

      logDebug(
        'Bi-Weekly 3: Partial periods at start and end',
        new Date('2025-06-03'),
        new Date('2025-06-25'),
        'bi-weekly',
        undefined,
        dates,
        ['Expected: 2 bi-weekly dates'],
      );

      expect(dates).toHaveLength(2);
    });
  });

  describe('Edge Cases', () => {
    it('should handle billing day before class start day in same month', () => {
      const dates = generateInvoiceDates(
        new Date('2025-06-15'),
        new Date('2025-08-15'),
        'monthly',
        5,
      );

      const expected = ['2025-06-05', '2025-07-05', '2025-08-05'];

      logDebug(
        'Edge Case: billing day before class start day in same month',
        new Date('2025-06-15'),
        new Date('2025-08-15'),
        'monthly',
        5,
        dates,
        expected,
      );

      expect(dates).toHaveLength(3);
      expect(dates[1].getDate()).toBe(5); // Should be July 5th
    });

    it('should handle very short billing periods', () => {
      const dates = generateInvoiceDates(
        new Date('2025-06-28'),
        new Date('2025-07-03'),
        'monthly',
        1,
      );

      const expected = ['2025-06-01', '2025-07-01'];

      logDebug(
        'Edge Case: very short billing periods',
        new Date('2025-06-28'),
        new Date('2025-07-03'),
        'monthly',
        1,
        dates,
        expected,
      );

      expect(dates).toHaveLength(2);
      expect(dates.map((d) => d.toISOString().split('T')[0])).toEqual(expected);
    });

    it('should handle invalid inputs', () => {
      logDebug(
        'Edge Case: invalid inputs',
        new Date(),
        new Date(),
        '',
        undefined,
        [],
        ['Should throw errors'],
      );

      expect(() =>
        generateInvoiceDates(undefined as any, new Date(), 'monthly'),
      ).toThrow();
      expect(() =>
        generateInvoiceDates(new Date(), undefined as any, 'monthly'),
      ).toThrow();
      expect(() => generateInvoiceDates(new Date(), new Date(), '')).toThrow();
    });

    it('should handle invalid date ranges', () => {
      logDebug(
        'Edge Case: invalid date ranges',
        new Date('2025-12-31'),
        new Date('2025-01-01'),
        'monthly',
        undefined,
        [],
        ['Should throw errors'],
      );

      expect(() =>
        generateInvoiceDates(
          new Date('2025-12-31'),
          new Date('2025-01-01'),
          'monthly',
        ),
      ).toThrow();
    });

    it('should handle large date ranges', () => {
      logDebug(
        'Edge Case: large date ranges',
        new Date('2025-01-01'),
        new Date('2035-01-01'),
        'monthly',
        undefined,
        [],
        ['Should throw errors'],
      );

      expect(() =>
        generateInvoiceDates(
          new Date('2025-01-01'),
          new Date('2035-01-01'),
          'monthly',
        ),
      ).toThrow();
    });
  });
});
