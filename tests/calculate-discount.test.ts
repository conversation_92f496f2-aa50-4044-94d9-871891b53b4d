import { describe, it, expect, beforeEach, vi } from 'vitest';
import { DiscountService } from '../src/discount/discount.service';
import { Model, Types } from 'mongoose';

// Create real ObjectIds for testing
const createTestObjectId = () => new Types.ObjectId();
const STUDIO_ID = '683f54bfb59ec253c3d5ddbb';

// Mock discount data for testing with real ObjectIds
const mockDiscounts = {
  multiClassPercentDiscount: {
    _id: createTestObjectId(),
    studioId: STUDIO_ID,
    category: 'multi-class',
    type: 'percent',
    isActive: true,
    discounts: {
      1: 0, // First class: 0% discount
      2: 10, // Second class: 10% discount
      3: 20, // Third class: 20% discount
      4: 30, // Fourth class: 30% discount
    },
    discountRules: 'all-months',
    excludedClasses: [],
  },
  multiClassDollarDiscount: {
    _id: createTestObjectId(),
    studioId: STUDIO_ID,
    category: 'multi-class',
    type: 'dollars',
    isActive: true,
    discounts: {
      1: 0, // First class: $0 discount
      2: 5, // Second class: $5 discount
      3: 10, // Third class: $10 discount
      4: 15, // Fourth class: $15 discount
    },
    discountRules: 'all-months',
    excludedClasses: [],
  },
  multiStudentPercentDiscount: {
    _id: createTestObjectId(),
    studioId: STUDIO_ID,
    category: 'multi-student',
    type: 'percent',
    isActive: true,
    byStudent: {
      1: 0, // First student: 0% discount
      2: 15, // Second student: 15% discount
      3: 25, // Third student: 25% discount
      4: 35, // Fourth student: 35% discount
    },
    discountRules: 'all-months',
    excludedClasses: [],
  },
  multiStudentDollarDiscount: {
    _id: createTestObjectId(),
    studioId: STUDIO_ID,
    category: 'multi-student',
    type: 'dollars',
    isActive: true,
    amountByStudent: {
      1: 0, // First student: $0 discount
      2: 10, // Second student: $10 discount
      3: 20, // Third student: $20 discount
      4: 30, // Fourth student: $30 discount
    },
    discountRules: 'all-months',
    excludedClasses: [],
  },
  multiStudentByStudentDiscount: {
    _id: '507f1f77bcf86cd799439015',
    studioId: '507f1f77bcf86cd799439001',
    category: 'multi-student',
    type: 'by-student',
    isActive: true,
    byStudent: {
      1: 0, // First student: 0% discount
      2: 20, // Second student: 20% discount
      3: 30, // Third student: 30% discount
    },
    discountRules: 'first-month',
    excludedClasses: [],
  },
  multiStudentAmountByStudentDiscount: {
    _id: '507f1f77bcf86cd799439016',
    studioId: '507f1f77bcf86cd799439001',
    category: 'multi-student',
    type: 'amount-by-student',
    isActive: true,
    amountByStudent: {
      1: 0, // First student: $0 discount
      2: 25, // Second student: $25 discount
      3: 45, // Third student: $45 discount
    },
    discountRules: 'first-month',
    excludedClasses: [],
  },
  flatDiscount: {
    _id: '507f1f77bcf86cd799439017',
    studioId: '507f1f77bcf86cd799439001',
    category: 'multi-class',
    type: 'flat',
    isActive: true,
    flat: 15, // 15% flat discount
    discountRules: 'all-months',
    excludedClasses: [],
  },
  inactiveDiscount: {
    _id: '507f1f77bcf86cd799439018',
    studioId: '507f1f77bcf86cd799439001',
    category: 'multi-class',
    type: 'percent',
    isActive: false, // Inactive discount
    discounts: { 1: 50 },
    discountRules: 'all-months',
    excludedClasses: [],
  },
  excludedClassDiscount: {
    _id: '507f1f77bcf86cd799439019',
    studioId: '507f1f77bcf86cd799439001',
    category: 'multi-class',
    type: 'percent',
    isActive: true,
    discounts: { 1: 30 },
    discountRules: 'all-months',
    excludedClasses: ['507f1f77bcf86cd799439021'], // Excluded enrollment ID
  },
};

// Mock Model methods
const createMockModel = (mockData: any[]) =>
  ({
    find: vi.fn().mockReturnValue({
      lean: vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(mockData),
      }),
    }),
    findOne: vi.fn().mockReturnValue({
      lean: vi.fn().mockReturnValue({
        exec: vi.fn().mockResolvedValue(mockData[0] || null),
      }),
    }),
  }) as unknown as Model<any>;

describe('Discount Calculations', () => {
  let discountService: DiscountService;
  let mockDiscountModel: any;

  beforeEach(() => {
    mockDiscountModel = createMockModel([]);
    discountService = new DiscountService(mockDiscountModel);
  });

  describe('DiscountService.calculateDiscount', () => {
    describe('Multi-Class Discounts', () => {
      it('should calculate percent-based multi-class discount correctly', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([mockDiscounts.multiClassPercentDiscount]),
          }),
        });

        const params = {
          studioId: STUDIO_ID,
          student: {
            firstName: 'John',
            lastName: 'Doe',
            classPosition: 2, // Second class
            studentPosition: 1,
            tuitionFee: 100,
            enrollmentId: '684db9e78cd935d6ef581ee0',
          },
          category: 'multi-class' as const,
        };

        console.log(mockDiscounts.multiClassPercentDiscount);
        const result = await discountService.calculateDiscount(params);

        // Expected: 100 * 10% = 10 (second class gets 10% discount)
        expect(result.totalDiscount).toBe(10);
      });

      it('should calculate dollar-based multi-class discount correctly', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([mockDiscounts.multiClassDollarDiscount]),
          }),
        });

        const params = {
          studioId: STUDIO_ID,
          student: {
            firstName: 'Jane',
            lastName: 'Smith',
            classPosition: 3, // Third class
            studentPosition: 1,
            tuitionFee: 150,
            enrollmentId: '684db9e78cd935d6ef581ee0',
          },
          category: 'multi-class' as const,
        };

        console.log(mockDiscounts.multiClassDollarDiscount);

        const result = await discountService.calculateDiscount(params);

        // Expected: $10 flat discount for third class
        expect(result.totalDiscount).toBe(10);
      });

      it('should calculate flat discount correctly', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([mockDiscounts.flatDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Bob',
            lastName: 'Johnson',
            classPosition: 1,
            studentPosition: 1,
            tuitionFee: 200,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        console.log(mockDiscounts.flatDiscount);

        const result = await discountService.calculateDiscount(params);

        // Expected: 200 * 15% = 30 (flat 15% discount)
        expect(result.totalDiscount).toBe(30);
      });

      it('should handle class position beyond defined discounts', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([mockDiscounts.multiClassPercentDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Alice',
            lastName: 'Brown',
            classPosition: 10, // Position beyond defined discounts
            studentPosition: 1,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        console.log(mockDiscounts.multiClassPercentDiscount);

        const result = await discountService.calculateDiscount(params);

        // Expected: 0 (no discount defined for position 10)
        expect(result.totalDiscount).toBe(0);
      });
    });

    describe('Multi-Student Discounts', () => {
      it('should calculate percent-based multi-student discount correctly', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([mockDiscounts.multiStudentPercentDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Child',
            lastName: 'Two',
            classPosition: 1,
            studentPosition: 2, // Second student
            tuitionFee: 120,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-student' as const,
        };

        console.log(mockDiscounts.multiStudentPercentDiscount);

        const result = await discountService.calculateDiscount(params);

        // Expected: 120 * 15% = 18 (second student gets 15% discount)
        expect(result.totalDiscount).toBe(18);
      });

      it('should calculate dollar-based multi-student discount correctly', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([mockDiscounts.multiStudentDollarDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Child',
            lastName: 'Three',
            classPosition: 1,
            studentPosition: 3, // Third student
            tuitionFee: 80,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-student' as const,
        };

        console.log(mockDiscounts.multiStudentDollarDiscount);

        const result = await discountService.calculateDiscount(params);

        // Expected: $20 flat discount for third student
        expect(result.totalDiscount).toBe(20);
      });

      it('should calculate by-student discount correctly', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([mockDiscounts.multiStudentByStudentDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Child',
            lastName: 'Two',
            classPosition: 1,
            studentPosition: 2, // Second student
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-student' as const,
        };

        console.log(mockDiscounts.multiStudentByStudentDiscount);

        const result = await discountService.calculateDiscount(params);

        // Expected: 100 * 20% = 20 (second student gets 20% discount)
        expect(result.totalDiscount).toBe(20);
      });

      it('should calculate amount-by-student discount correctly', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([
                mockDiscounts.multiStudentAmountByStudentDiscount,
              ]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Child',
            lastName: 'Three',
            classPosition: 1,
            studentPosition: 3, // Third student
            tuitionFee: 150,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-student' as const,
        };

        console.log(mockDiscounts.multiStudentAmountByStudentDiscount);

        const result = await discountService.calculateDiscount(params);

        // Expected: $45 flat discount for third student
        expect(result.totalDiscount).toBe(45);
      });

      it('should handle student position beyond defined discounts', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([mockDiscounts.multiStudentPercentDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Child',
            lastName: 'Five',
            classPosition: 1,
            studentPosition: 5, // Position beyond defined discounts
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-student' as const,
        };

        console.log(mockDiscounts.multiStudentPercentDiscount);

        const result = await discountService.calculateDiscount(params);

        // Expected: 0 (no discount defined for position 5)
        expect(result.totalDiscount).toBe(0);
      });
    });

    describe('Combined Discounts', () => {
      it('should calculate combined multi-class and multi-student discounts', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([
                mockDiscounts.multiClassPercentDiscount,
                mockDiscounts.multiStudentPercentDiscount,
              ]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Combined',
            lastName: 'Discount',
            classPosition: 2, // Second class (10% discount)
            studentPosition: 2, // Second student (15% discount)
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'all' as const,
        };
        console.log(mockDiscounts.multiClassPercentDiscount);
        console.log(mockDiscounts.multiStudentPercentDiscount);

        const result = await discountService.calculateDiscount(params);

        // Expected: 10 (class) + 15 (student) = 25 total discount
        expect(result.totalDiscount).toBe(25);
      });

      it('should calculate mixed discount types correctly', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([
                mockDiscounts.multiClassDollarDiscount,
                mockDiscounts.multiStudentPercentDiscount,
              ]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Mixed',
            lastName: 'Discount',
            classPosition: 3, // Third class ($10 discount)
            studentPosition: 3, // Third student (25% discount)
            tuitionFee: 120,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'all' as const,
        };

        console.log(mockDiscounts.multiClassDollarDiscount);
        console.log(mockDiscounts.multiStudentPercentDiscount);

        const result = await discountService.calculateDiscount(params);

        // Expected: 10 (class dollar) + 30 (student percent: 120 * 25%) = 40 total discount
        expect(result.totalDiscount).toBe(40);
      });
    });

    describe('Edge Cases and Filters', () => {
      it('should skip inactive discounts', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([mockDiscounts.inactiveDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Inactive',
            lastName: 'Test',
            classPosition: 1,
            studentPosition: 1,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        console.log(mockDiscounts.inactiveDiscount);

        const result = await discountService.calculateDiscount(params);

        // Expected: 0 (discount is inactive)
        expect(result.totalDiscount).toBe(0);
      });

      it('should skip discounts for excluded classes', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([mockDiscounts.excludedClassDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Excluded',
            lastName: 'Class',
            classPosition: 1,
            studentPosition: 1,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439021', // Excluded enrollment ID
          },
          category: 'multi-class' as const,
        };

        console.log(mockDiscounts.excludedClassDiscount);

        const result = await discountService.calculateDiscount(params);

        // Expected: 0 (class is excluded)
        expect(result.totalDiscount).toBe(0);
      });

      it('should handle zero tuition fee - no discounts applied per business rule', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([mockDiscounts.multiClassPercentDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Zero',
            lastName: 'Fee',
            classPosition: 2,
            studentPosition: 1,
            tuitionFee: 0,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        console.log(mockDiscounts.multiClassPercentDiscount);

        const result = await discountService.calculateDiscount(params);

        // Expected: 0 (zero dollar classes get no discounts per business rule)
        expect(result.totalDiscount).toBe(0);
      });

      it('should not apply dollar-based discounts to zero tuition fee classes', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([mockDiscounts.multiClassDollarDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Zero',
            lastName: 'Dollar',
            classPosition: 2, // Would normally get $5 discount
            studentPosition: 1,
            tuitionFee: 0,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        console.log(mockDiscounts.multiClassDollarDiscount);

        const result = await discountService.calculateDiscount(params);

        // Expected: 0 (no discount applied to zero dollar classes)
        expect(result.totalDiscount).toBe(0);
      });

      it('should not apply flat discounts to zero tuition fee classes', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([mockDiscounts.flatDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Zero',
            lastName: 'Flat',
            classPosition: 1,
            studentPosition: 1,
            tuitionFee: 0,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        console.log(mockDiscounts.flatDiscount);

        const result = await discountService.calculateDiscount(params);

        // Expected: 0 (no flat discount applied to zero dollar classes)
        expect(result.totalDiscount).toBe(0);
      });

      it('should not apply multi-student discounts to zero tuition fee classes', async () => {
        const mixedDiscounts = [
          mockDiscounts.multiStudentPercentDiscount,
          mockDiscounts.multiStudentDollarDiscount,
          mockDiscounts.multiStudentByStudentDiscount,
          mockDiscounts.multiStudentAmountByStudentDiscount,
        ];

        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve(mixedDiscounts),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Zero',
            lastName: 'MultiStudent',
            classPosition: 1,
            studentPosition: 2, // Would normally get various discounts
            tuitionFee: 0,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-student' as const,
        };

        console.log(mockDiscounts.multiStudentPercentDiscount);
        console.log(mockDiscounts.multiStudentDollarDiscount);
        console.log(mockDiscounts.multiStudentByStudentDiscount);
        console.log(mockDiscounts.multiStudentAmountByStudentDiscount);

        const result = await discountService.calculateDiscount(params);

        // Expected: 0 (no multi-student discounts applied to zero dollar classes)
        expect(result.totalDiscount).toBe(0);
      });

      it('should not apply any combination of discounts to zero tuition fee classes', async () => {
        const allDiscounts = [
          mockDiscounts.multiClassPercentDiscount,
          mockDiscounts.multiClassDollarDiscount,
          mockDiscounts.flatDiscount,
          mockDiscounts.multiStudentPercentDiscount,
          mockDiscounts.multiStudentDollarDiscount,
        ];

        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve(allDiscounts),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Zero',
            lastName: 'AllDiscounts',
            classPosition: 2, // Would normally get multiple discounts
            studentPosition: 2, // Would normally get multiple discounts
            tuitionFee: 0,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'all' as const,
        };

        console.log(mockDiscounts.multiClassPercentDiscount);
        console.log(mockDiscounts.multiClassDollarDiscount);
        console.log(mockDiscounts.flatDiscount);
        console.log(mockDiscounts.multiStudentPercentDiscount);
        console.log(mockDiscounts.multiStudentDollarDiscount);

        const result = await discountService.calculateDiscount(params);

        // Expected: 0 (absolutely no discounts applied to zero dollar classes)
        expect(result.totalDiscount).toBe(0);
      });

      it('should apply discounts to very small positive tuition fees', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([mockDiscounts.multiClassPercentDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Small',
            lastName: 'Positive',
            classPosition: 2, // 10% discount
            studentPosition: 1,
            tuitionFee: 0.01, // Very small positive amount
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        console.log(mockDiscounts.multiClassPercentDiscount);

        const result = await discountService.calculateDiscount(params);

        // Expected: 0.01 * 10% = 0.001, rounded to 0
        expect(result.totalDiscount).toBe(0);
      });

      it('should not apply discounts to negative tuition fees', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([
                mockDiscounts.multiClassPercentDiscount,
                mockDiscounts.multiClassDollarDiscount,
              ]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Negative',
            lastName: 'Fee',
            classPosition: 2, // Would normally get discounts
            studentPosition: 1,
            tuitionFee: -50, // Negative amount
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        console.log(mockDiscounts.multiClassPercentDiscount);
        console.log(mockDiscounts.multiClassDollarDiscount);

        const result = await discountService.calculateDiscount(params);

        // Expected: 0 (no discounts applied to negative tuition fees)
        expect(result.totalDiscount).toBe(0);
      });

      it('should handle negative tuition fee - no discounts applied per business rule', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([mockDiscounts.multiClassPercentDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Negative',
            lastName: 'Fee',
            classPosition: 2,
            studentPosition: 1,
            tuitionFee: -100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        const result = await discountService.calculateDiscount(params);

        // Expected: 0 (no discounts applied to negative tuition fees per business rule)
        expect(result.totalDiscount).toBe(0);
      });

      it('should return 0 when no discounts exist', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'No',
            lastName: 'Discounts',
            classPosition: 1,
            studentPosition: 1,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'all' as const,
        };

        const result = await discountService.calculateDiscount(params);

        // Expected: 0 (no discounts configured)
        expect(result.totalDiscount).toBe(0);
      });

      it('should round discount to 2 decimal places', async () => {
        const discountWithDecimal = {
          ...mockDiscounts.multiClassPercentDiscount,
          discounts: { 1: 33.33 }, // 33.33% discount
        };

        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([discountWithDecimal]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Decimal',
            lastName: 'Test',
            classPosition: 1,
            studentPosition: 1,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        const result = await discountService.calculateDiscount(params);

        console.log(discountWithDecimal);
        // Expected: 33.33 (rounded to 2 decimal places)
        expect(result.totalDiscount).toBe(33.33);
      });
    });

    describe('Category Filtering', () => {
      it('should only apply multi-class discounts when category is multi-class', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([mockDiscounts.multiClassPercentDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Category',
            lastName: 'Filter',
            classPosition: 2,
            studentPosition: 2,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        const result = await discountService.calculateDiscount(params);

        console.log(mockDiscounts.multiClassPercentDiscount);
        // Expected: 10 (only multi-class discount applied)
        expect(result.totalDiscount).toBe(10);
      });

      it('should only apply multi-student discounts when category is multi-student', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([mockDiscounts.multiStudentPercentDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Category',
            lastName: 'Filter',
            classPosition: 2,
            studentPosition: 2,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-student' as const,
        };

        const result = await discountService.calculateDiscount(params);

        console.log(mockDiscounts.multiStudentPercentDiscount);
        // Expected: 15 (only multi-student discount applied)
        expect(result.totalDiscount).toBe(15);
      });
    });

    describe('Discount Calculation Precision', () => {
      it('should handle complex decimal calculations', async () => {
        const complexDiscount = {
          _id: '507f1f77bcf86cd799439020',
          studioId: '507f1f77bcf86cd799439001',
          category: 'multi-class',
          type: 'percent',
          isActive: true,
          discounts: { 1: 12.5789 }, // Complex decimal percentage
          excludedClasses: [],
        };

        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([complexDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Precision',
            lastName: 'Test',
            classPosition: 1,
            studentPosition: 1,
            tuitionFee: 123.45,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        const result = await discountService.calculateDiscount(params);

        console.log(complexDiscount);
        // Expected: 123.45 * 12.5789% = 15.53 (rounded to 2 decimal places)
        expect(result.totalDiscount).toBe(15.53);
      });

      it('should handle very small discount amounts', async () => {
        const smallDiscount = {
          _id: '507f1f77bcf86cd799439021',
          studioId: '507f1f77bcf86cd799439001',
          category: 'multi-class',
          type: 'percent',
          isActive: true,
          discounts: { 1: 0.01 }, // 0.01% discount
          excludedClasses: [],
        };

        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([smallDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Small',
            lastName: 'Discount',
            classPosition: 1,
            studentPosition: 1,
            tuitionFee: 1000,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        const result = await discountService.calculateDiscount(params);

        console.log(smallDiscount);
        // Expected: 1000 * 0.01% = 0.1
        expect(result.totalDiscount).toBe(0.1);
      });

      it('should handle rounding edge cases', async () => {
        const roundingDiscount = {
          _id: '507f1f77bcf86cd799439022',
          studioId: '507f1f77bcf86cd799439001',
          category: 'multi-class',
          type: 'percent',
          isActive: true,
          discounts: { 1: 66.666666 }, // Repeating decimal
          excludedClasses: [],
        };

        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([roundingDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Rounding',
            lastName: 'Test',
            classPosition: 1,
            studentPosition: 1,
            tuitionFee: 99.99,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        const result = await discountService.calculateDiscount(params);

        console.log(roundingDiscount);
        // Expected: 99.99 * 66.666666% = 66.66 (rounded to 2 decimal places)
        expect(result.totalDiscount).toBe(66.66);
      });
    });

    describe('Array Index Boundary Tests', () => {
      it('should handle 1-based indexing correctly for multi-class discounts', async () => {
        const zeroBoundaryDiscount = {
          _id: '507f1f77bcf86cd799439023',
          studioId: '507f1f77bcf86cd799439001',
          category: 'multi-class',
          type: 'percent',
          isActive: true,
          discounts: { 1: 50, 2: 25, 3: 10 }, // Only positions 1, 2, 3 defined
          excludedClasses: [],
        };

        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([zeroBoundaryDiscount]),
          }),
        });

        // Test class position 1 (direct lookup)
        const params1 = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Position',
            lastName: 'One',
            classPosition: 1, // This should map to key 1
            studentPosition: 1,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        const result1 = await discountService.calculateDiscount(params1);
        expect(result1.totalDiscount).toBe(50); // 100 * 50% = 50

        // Test class position 3 (direct lookup)
        const params2 = {
          ...params1,
          student: { ...params1.student, classPosition: 3 },
        };

        const result2 = await discountService.calculateDiscount(params2);
        expect(result2.totalDiscount).toBe(10); // 100 * 10% = 10

        // Test class position 4 (undefined key)
        const params3 = {
          ...params1,
          student: { ...params1.student, classPosition: 4 },
        };

        const result3 = await discountService.calculateDiscount(params3);
        expect(result3.totalDiscount).toBe(0); // undefined discount = 0
      });

      it('should handle 1-based indexing correctly for multi-student discounts', async () => {
        const studentBoundaryDiscount = {
          _id: '507f1f77bcf86cd799439024',
          studioId: '507f1f77bcf86cd799439001',
          category: 'multi-student',
          type: 'percent',
          isActive: true,
          byStudent: { 1: 0, 2: 20, 3: 30 }, // Only positions 1, 2, 3 defined
          excludedClasses: [],
        };

        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([studentBoundaryDiscount]),
          }),
        });

        // Test student position 1 (direct lookup)
        const params1 = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Student',
            lastName: 'One',
            classPosition: 1,
            studentPosition: 1, // This should map to key 1
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-student' as const,
        };

        const result1 = await discountService.calculateDiscount(params1);
        expect(result1.totalDiscount).toBe(0); // 100 * 0% = 0

        // Test student position 2 (direct lookup)
        const params2 = {
          ...params1,
          student: { ...params1.student, studentPosition: 2 },
        };

        const result2 = await discountService.calculateDiscount(params2);
        expect(result2.totalDiscount).toBe(20); // 100 * 20% = 20

        // Test student position 10 (undefined key)
        const params3 = {
          ...params1,
          student: { ...params1.student, studentPosition: 10 },
        };

        const result3 = await discountService.calculateDiscount(params3);
        expect(result3.totalDiscount).toBe(0); // undefined discount = 0
      });

      it('should handle mixed discount array definitions', async () => {
        const mixedDiscount = {
          _id: '507f1f77bcf86cd799439025',
          studioId: '507f1f77bcf86cd799439001',
          category: 'multi-class',
          type: 'dollars',
          isActive: true,
          discounts: { 2: 15, 4: 25, 6: 35 }, // Sparse array with gaps
          excludedClasses: [],
        };

        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([mixedDiscount]),
          }),
        });

        // Test position 1 (should get 0, no discount at key 1)
        const params1 = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Mixed',
            lastName: 'Array',
            classPosition: 1,
            studentPosition: 1,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        const result1 = await discountService.calculateDiscount(params1);
        expect(result1.totalDiscount).toBe(0); // No discount at key 1

        // Test position 2 (should get $15 from key 2)
        const params2 = {
          ...params1,
          student: { ...params1.student, classPosition: 2 },
        };

        const result2 = await discountService.calculateDiscount(params2);
        expect(result2.totalDiscount).toBe(15); // $15 discount at key 2

        // Test position 3 (should get 0, no discount at key 3)
        const params3 = {
          ...params1,
          student: { ...params1.student, classPosition: 3 },
        };

        const result3 = await discountService.calculateDiscount(params3);
        expect(result3.totalDiscount).toBe(0); // No discount at key 3

        // Test position 4 (should get $25 from key 4)
        const params4 = {
          ...params1,
          student: { ...params1.student, classPosition: 4 },
        };

        const result4 = await discountService.calculateDiscount(params4);
        expect(result4.totalDiscount).toBe(25); // $25 discount at key 4
      });
    });

    describe('Real-world Scenario Tests', () => {
      it('should handle family discount scenario correctly', async () => {
        // Family with 2 children, each taking 2 classes
        // First child: Class 1 (0% discount), Class 2 (10% discount)
        // Second child: Class 1 (0% discount + 15% family discount), Class 2 (10% discount + 15% family discount)

        // Test first child, first class (position 1, student 1)
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([
                mockDiscounts.multiClassPercentDiscount,
                mockDiscounts.multiStudentPercentDiscount,
              ]),
          }),
        });

        const firstChildFirstClass = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Child',
            lastName: 'One',
            classPosition: 1, // First class
            studentPosition: 1, // First student
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'all' as const,
        };

        const result1 =
          await discountService.calculateDiscount(firstChildFirstClass);
        expect(result1.totalDiscount).toBe(0); // 0% class + 0% student = 0

        // Test second child, first class (position 1, student 2)
        const secondChildFirstClass = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Child',
            lastName: 'Two',
            classPosition: 1, // First class
            studentPosition: 2, // Second student
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439021',
          },
          category: 'all' as const,
        };

        const result2 = await discountService.calculateDiscount(
          secondChildFirstClass,
        );
        expect(result2.totalDiscount).toBe(15); // 0% class + 15% student = 15

        // Test first child, second class (position 2, student 1)
        const firstChildSecondClass = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Child',
            lastName: 'One',
            classPosition: 2, // Second class
            studentPosition: 1, // First student
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439022',
          },
          category: 'all' as const,
        };

        const result3 = await discountService.calculateDiscount(
          firstChildSecondClass,
        );
        expect(result3.totalDiscount).toBe(10); // 10% class + 0% student = 10
      });

      it('should handle prorated amount scenarios', async () => {
        // Test with different tuition amounts (simulating prorated fees)
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([mockDiscounts.multiClassPercentDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Prorated',
            lastName: 'Student',
            classPosition: 2, // Second class gets 10% discount
            studentPosition: 1,
            tuitionFee: 75, // Prorated amount instead of full $100
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        const result = await discountService.calculateDiscount(params);

        // Expected: 75 * 10% = 7.5 (discount applied to prorated amount)
        expect(result.totalDiscount).toBe(7.5);
      });
    });

    describe('ObjectId Conversion Edge Cases', () => {
      it('should handle string studioId conversion correctly', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([mockDiscounts.multiClassPercentDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001', // String ID
          student: {
            firstName: 'String',
            lastName: 'ID',
            classPosition: 2,
            studentPosition: 1,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        const result = await discountService.calculateDiscount(params);
        console.log(mockDiscounts.multiClassPercentDiscount);
        expect(result.totalDiscount).toBe(10);

        // Verify the find method was called with correct query structure
        expect(mockDiscountModel.find).toHaveBeenCalledWith({
          studioId: expect.any(Object),
          isActive: true,
          category: 'multi-class',
        });
      });

      it('should handle different enrollmentId formats in exclusion check', async () => {
        const excludedDiscount = {
          ...mockDiscounts.multiClassPercentDiscount,
          excludedClasses: ['507f1f77bcf86cd799439020'], // String format
        };

        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([excludedDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Excluded',
            lastName: 'Test',
            classPosition: 2,
            studentPosition: 1,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020', // String format
          },
          category: 'multi-class' as const,
        };

        const result = await discountService.calculateDiscount(params);
        console.log(excludedDiscount);
        expect(result.totalDiscount).toBe(0); // Should be excluded
      });
    });

    describe('Invalid Data Structure Tests', () => {
      it('should handle discount with null discounts object', async () => {
        const nullDiscountData = {
          _id: '507f1f77bcf86cd799439026',
          studioId: '507f1f77bcf86cd799439001',
          category: 'multi-class',
          type: 'percent',
          isActive: true,
          discounts: null, // Null discounts
          excludedClasses: [],
        };

        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([nullDiscountData]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Null',
            lastName: 'Discount',
            classPosition: 2,
            studentPosition: 1,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        const result = await discountService.calculateDiscount(params);
        console.log(nullDiscountData);
        expect(result.totalDiscount).toBe(0);
      });

      it('should handle discount with undefined byStudent object', async () => {
        const undefinedByStudent = {
          _id: '507f1f77bcf86cd799439027',
          studioId: '507f1f77bcf86cd799439001',
          category: 'multi-student',
          type: 'percent',
          isActive: true,
          byStudent: undefined, // Undefined byStudent
          excludedClasses: [],
        };

        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([undefinedByStudent]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Undefined',
            lastName: 'ByStudent',
            classPosition: 1,
            studentPosition: 2,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-student' as const,
        };

        const result = await discountService.calculateDiscount(params);
        expect(result.totalDiscount).toBe(0);
      });

      it('should handle discount with missing amountByStudent for dollar type', async () => {
        const missingAmountByStudent = {
          _id: '507f1f77bcf86cd799439028',
          studioId: '507f1f77bcf86cd799439001',
          category: 'multi-student',
          type: 'dollars',
          isActive: true,
          // Missing amountByStudent property
          excludedClasses: [],
        };

        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([missingAmountByStudent]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Missing',
            lastName: 'Amount',
            classPosition: 1,
            studentPosition: 2,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-student' as const,
        };

        const result = await discountService.calculateDiscount(params);
        expect(result.totalDiscount).toBe(0);
      });

      it('should handle flat discount with null flat value', async () => {
        const nullFlatDiscount = {
          _id: '507f1f77bcf86cd799439034',
          studioId: '507f1f77bcf86cd799439001',
          category: 'multi-class',
          type: 'flat',
          isActive: true,
          flat: null, // Null flat value
          excludedClasses: [],
        };

        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([nullFlatDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Null',
            lastName: 'Flat',
            classPosition: 1,
            studentPosition: 1,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        const result = await discountService.calculateDiscount(params);
        console.log(nullFlatDiscount);
        expect(result.totalDiscount).toBe(0); // Should default to 0 when flat is null
      });

      it('should handle flat discount with undefined flat value', async () => {
        const undefinedFlatDiscount = {
          _id: '507f1f77bcf86cd799439035',
          studioId: '507f1f77bcf86cd799439001',
          category: 'multi-class',
          type: 'flat',
          isActive: true,
          // flat property is missing (undefined)
          excludedClasses: [],
        };

        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([undefinedFlatDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Undefined',
            lastName: 'Flat',
            classPosition: 1,
            studentPosition: 1,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        const result = await discountService.calculateDiscount(params);
        console.log(undefinedFlatDiscount);
        expect(result.totalDiscount).toBe(0); // Should default to 0 when flat is undefined
      });
    });

    describe('Large Value and Extreme Cases', () => {
      it('should handle very large tuition fees', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([mockDiscounts.multiClassPercentDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Large',
            lastName: 'Fee',
            classPosition: 2,
            studentPosition: 1,
            tuitionFee: 999999.99, // Very large fee
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        const result = await discountService.calculateDiscount(params);
        console.log(mockDiscounts.multiClassPercentDiscount);
        expect(result.totalDiscount).toBe(100000); // 999999.99 * 10% = 99999.999, rounded to 100000
      });

      it('should handle very large discount percentages', async () => {
        const largePercentageDiscount = {
          _id: '507f1f77bcf86cd799439029',
          studioId: '507f1f77bcf86cd799439001',
          category: 'multi-class',
          type: 'percent',
          isActive: true,
          discounts: { 1: 150 }, // 150% discount
          excludedClasses: [],
        };

        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([largePercentageDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Large',
            lastName: 'Percentage',
            classPosition: 1,
            studentPosition: 1,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        const result = await discountService.calculateDiscount(params);
        console.log(largePercentageDiscount);
        expect(result.totalDiscount).toBe(150); // 100 * 150% = 150
      });

      it('should handle very large dollar amounts', async () => {
        const largeDollarDiscount = {
          _id: '507f1f77bcf86cd799439030',
          studioId: '507f1f77bcf86cd799439001',
          category: 'multi-class',
          type: 'dollars',
          isActive: true,
          discounts: { 1: 50000 }, // $50,000 discount
          excludedClasses: [],
        };

        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([largeDollarDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Large',
            lastName: 'Dollar',
            classPosition: 1,
            studentPosition: 1,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        const result = await discountService.calculateDiscount(params);
        expect(result.totalDiscount).toBe(50000); // $50,000 flat discount
      });

      it('should handle maximum safe integer values', async () => {
        const maxIntegerDiscount = {
          _id: '507f1f77bcf86cd799439031',
          studioId: '507f1f77bcf86cd799439001',
          category: 'multi-class',
          type: 'dollars',
          isActive: true,
          discounts: { 1: Number.MAX_SAFE_INTEGER },
          excludedClasses: [],
        };

        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([maxIntegerDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Max',
            lastName: 'Integer',
            classPosition: 1,
            studentPosition: 1,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        const result = await discountService.calculateDiscount(params);
        expect(result.totalDiscount).toBe(Number.MAX_SAFE_INTEGER);
        expect(Number.isSafeInteger(result.totalDiscount)).toBe(true);
      });
    });

    describe('Database Error Simulation', () => {
      it('should handle database query errors gracefully', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.reject(new Error('Database connection failed')),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Database',
            lastName: 'Error',
            classPosition: 1,
            studentPosition: 1,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        await expect(discountService.calculateDiscount(params)).rejects.toThrow(
          'Database connection failed',
        );
      });

      it('should handle null database response', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve(null),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Null',
            lastName: 'Response',
            classPosition: 1,
            studentPosition: 1,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        const result = await discountService.calculateDiscount(params);
        expect(result.totalDiscount).toBe(0);
      });
    });

    describe('Discount Type Variations', () => {
      it('should handle unknown discount type gracefully', async () => {
        const unknownTypeDiscount = {
          _id: '507f1f77bcf86cd799439032',
          studioId: '507f1f77bcf86cd799439001',
          category: 'multi-class',
          type: 'unknown-type', // Unknown discount type
          isActive: true,
          discounts: { 1: 50 },
          excludedClasses: [],
        };

        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([unknownTypeDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Unknown',
            lastName: 'Type',
            classPosition: 1,
            studentPosition: 1,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        const result = await discountService.calculateDiscount(params);
        expect(result.totalDiscount).toBe(0); // Should default to 0 for unknown types
      });

      it('should handle discount with missing type field', async () => {
        const missingTypeDiscount = {
          _id: '507f1f77bcf86cd799439033',
          studioId: '507f1f77bcf86cd799439001',
          category: 'multi-class',
          // Missing type field
          isActive: true,
          discounts: { 1: 50 },
          excludedClasses: [],
        };

        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () => Promise.resolve([missingTypeDiscount]),
          }),
        });

        const params = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Missing',
            lastName: 'Type',
            classPosition: 1,
            studentPosition: 1,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        const result = await discountService.calculateDiscount(params);
        expect(result.totalDiscount).toBe(0);
      });
    });

    describe('Position Validation Tests', () => {
      it('should handle zero and negative class positions', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([mockDiscounts.multiClassPercentDiscount]),
          }),
        });

        // Test zero class position
        const zeroParams = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Zero',
            lastName: 'Position',
            classPosition: 0, // Zero position
            studentPosition: 1,
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-class' as const,
        };

        const zeroResult = await discountService.calculateDiscount(zeroParams);
        expect(zeroResult.totalDiscount).toBe(0); // Should handle gracefully

        // Test negative class position
        const negativeParams = {
          ...zeroParams,
          student: { ...zeroParams.student, classPosition: -1 },
        };

        const negativeResult =
          await discountService.calculateDiscount(negativeParams);
        expect(negativeResult.totalDiscount).toBe(0); // Should handle gracefully
      });

      it('should handle zero and negative student positions', async () => {
        mockDiscountModel.find.mockReturnValue({
          lean: () => ({
            exec: () =>
              Promise.resolve([mockDiscounts.multiStudentPercentDiscount]),
          }),
        });

        // Test zero student position
        const zeroParams = {
          studioId: '507f1f77bcf86cd799439001',
          student: {
            firstName: 'Zero',
            lastName: 'Student',
            classPosition: 1,
            studentPosition: 0, // Zero position
            tuitionFee: 100,
            enrollmentId: '507f1f77bcf86cd799439020',
          },
          category: 'multi-student' as const,
        };

        const zeroResult = await discountService.calculateDiscount(zeroParams);
        expect(zeroResult.totalDiscount).toBe(0); // Should handle gracefully

        // Test negative student position
        const negativeParams = {
          ...zeroParams,
          student: { ...zeroParams.student, studentPosition: -2 },
        };

        const negativeResult =
          await discountService.calculateDiscount(negativeParams);
        expect(negativeResult.totalDiscount).toBe(0); // Should handle gracefully
      });
    });

    describe('Complex Conditions and Advanced Edge Cases', () => {
      describe('Multiple Active Discounts with Conflicts', () => {
        it('should handle multiple overlapping discounts of same category', async () => {
          const overlappingDiscounts = [
            {
              ...mockDiscounts.multiClassPercentDiscount,
              _id: '507f1f77bcf86cd799439040',
              discounts: { 1: 10, 2: 20 }, // First discount
            },
            {
              ...mockDiscounts.multiClassPercentDiscount,
              _id: '507f1f77bcf86cd799439041',
              discounts: { 1: 15, 2: 25 }, // Second discount
            },
          ];

          mockDiscountModel.find.mockReturnValue({
            lean: () => ({
              exec: () => Promise.resolve(overlappingDiscounts),
            }),
          });

          const params = {
            studioId: '507f1f77bcf86cd799439001',
            student: {
              firstName: 'Overlapping',
              lastName: 'Discounts',
              classPosition: 2,
              studentPosition: 1,
              tuitionFee: 100,
              enrollmentId: '507f1f77bcf86cd799439020',
            },
            category: 'multi-class' as const,
          };

          const result = await discountService.calculateDiscount(params);

          // Expected: Both discounts should apply: 20% + 25% = 45% total
          expect(result.totalDiscount).toBe(45);
        });

        it('should handle mixed discount types with complex calculations', async () => {
          const mixedDiscounts = [
            {
              ...mockDiscounts.multiClassPercentDiscount,
              _id: '507f1f77bcf86cd799439042',
              discounts: { 1: 10, 2: 15, 3: 20 },
            },
            {
              ...mockDiscounts.multiClassDollarDiscount,
              _id: '507f1f77bcf86cd799439043',
              discounts: { 1: 5, 2: 10, 3: 15 },
            },
            {
              ...mockDiscounts.flatDiscount,
              _id: '507f1f77bcf86cd799439044',
              flat: 8, // 8% flat discount
            },
            {
              ...mockDiscounts.multiStudentPercentDiscount,
              _id: '507f1f77bcf86cd799439045',
              byStudent: { 1: 0, 2: 12, 3: 18 },
            },
          ];

          mockDiscountModel.find.mockReturnValue({
            lean: () => ({
              exec: () => Promise.resolve(mixedDiscounts),
            }),
          });

          const params = {
            studioId: '507f1f77bcf86cd799439001',
            student: {
              firstName: 'Complex',
              lastName: 'Mixed',
              classPosition: 3, // Third class
              studentPosition: 2, // Second student
              tuitionFee: 200,
              enrollmentId: '507f1f77bcf86cd799439020',
            },
            category: 'all' as const,
          };

          const result = await discountService.calculateDiscount(params);

          // Expected: 20% (class) + $15 (class dollar) + 8% (flat) + 12% (student) = 40% + $15 = $80 + $15 = $95
          expect(result.totalDiscount).toBe(95);
        });
      });

      describe('Extreme Discount Rule Scenarios', () => {
        it('should handle discounts with first-month vs all-months rules', async () => {
          const firstMonthDiscount = {
            ...mockDiscounts.multiClassPercentDiscount,
            _id: '507f1f77bcf86cd799439046',
            discountRules: 'first-month',
            discounts: { 1: 50 }, // 50% first month only
          };

          const allMonthsDiscount = {
            ...mockDiscounts.multiStudentPercentDiscount,
            _id: '507f1f77bcf86cd799439047',
            discountRules: 'all-months',
            byStudent: { 1: 0, 2: 25 }, // 25% all months
          };

          mockDiscountModel.find.mockReturnValue({
            lean: () => ({
              exec: () =>
                Promise.resolve([firstMonthDiscount, allMonthsDiscount]),
            }),
          });

          const params = {
            studioId: '507f1f77bcf86cd799439001',
            student: {
              firstName: 'Rule',
              lastName: 'Test',
              classPosition: 1,
              studentPosition: 2,
              tuitionFee: 100,
              enrollmentId: '507f1f77bcf86cd799439020',
            },
            category: 'all' as const,
          };

          const result = await discountService.calculateDiscount(params);

          // Expected: 50% (first month) + 25% (all months) = 75%
          expect(result.totalDiscount).toBe(75);
        });

        it('should handle discounts with sparse position definitions', async () => {
          const sparseDiscount = {
            _id: '507f1f77bcf86cd799439048',
            studioId: '507f1f77bcf86cd799439001',
            category: 'multi-class',
            type: 'percent',
            isActive: true,
            discounts: { 1: 10, 5: 30, 10: 50, 15: 70 }, // Only specific positions
            discountRules: 'all-months',
            excludedClasses: [],
          };

          mockDiscountModel.find.mockReturnValue({
            lean: () => ({
              exec: () => Promise.resolve([sparseDiscount]),
            }),
          });

          // Test position 1 (defined)
          const params1 = {
            studioId: '507f1f77bcf86cd799439001',
            student: {
              firstName: 'Sparse',
              lastName: 'Test1',
              classPosition: 1,
              studentPosition: 1,
              tuitionFee: 100,
              enrollmentId: '507f1f77bcf86cd799439020',
            },
            category: 'multi-class' as const,
          };

          const result1 = await discountService.calculateDiscount(params1);
          expect(result1.totalDiscount).toBe(10);

          // Test position 3 (undefined, should be 0)
          const params2 = {
            ...params1,
            student: { ...params1.student, classPosition: 3 },
          };
          const result2 = await discountService.calculateDiscount(params2);
          expect(result2.totalDiscount).toBe(0);

          // Test position 10 (defined)
          const params3 = {
            ...params1,
            student: { ...params1.student, classPosition: 10 },
          };
          const result3 = await discountService.calculateDiscount(params3);
          expect(result3.totalDiscount).toBe(50);
        });
      });

      describe('Data Type and Format Edge Cases', () => {
        it('should handle string numbers in discount configurations', async () => {
          const stringNumberDiscount = {
            _id: '507f1f77bcf86cd799439049',
            studioId: '507f1f77bcf86cd799439001',
            category: 'multi-class',
            type: 'percent',
            isActive: true,
            discounts: { '1': '15.5', '2': '25.75', '3': '30' }, // String numbers
            discountRules: 'all-months',
            excludedClasses: [],
          };

          mockDiscountModel.find.mockReturnValue({
            lean: () => ({
              exec: () => Promise.resolve([stringNumberDiscount]),
            }),
          });

          const params = {
            studioId: '507f1f77bcf86cd799439001',
            student: {
              firstName: 'String',
              lastName: 'Numbers',
              classPosition: 2,
              studentPosition: 1,
              tuitionFee: 100,
              enrollmentId: '507f1f77bcf86cd799439020',
            },
            category: 'multi-class' as const,
          };

          const result = await discountService.calculateDiscount(params);

          // Expected: 100 * 25.75% = 25.75
          expect(result.totalDiscount).toBe(25.75);
        });

        it('should handle decimal positions gracefully', async () => {
          mockDiscountModel.find.mockReturnValue({
            lean: () => ({
              exec: () =>
                Promise.resolve([mockDiscounts.multiClassPercentDiscount]),
            }),
          });

          const params = {
            studioId: '507f1f77bcf86cd799439001',
            student: {
              firstName: 'Decimal',
              lastName: 'Position',
              classPosition: 2.7, // Decimal position
              studentPosition: 1.3, // Decimal position
              tuitionFee: 100,
              enrollmentId: '507f1f77bcf86cd799439020',
            },
            category: 'multi-class' as const,
          };

          const result = await discountService.calculateDiscount(params);

          // Expected: Should handle decimal positions (likely floor to 2)
          expect(result.totalDiscount).toBe(0); // No discount at key 2.7
        });

        it('should handle NaN and Infinity values', async () => {
          const nanDiscount = {
            _id: '507f1f77bcf86cd799439050',
            studioId: '507f1f77bcf86cd799439001',
            category: 'multi-class',
            type: 'percent',
            isActive: true,
            discounts: { 1: NaN, 2: Infinity, 3: -Infinity }, // Invalid numbers
            discountRules: 'all-months',
            excludedClasses: [],
          };

          mockDiscountModel.find.mockReturnValue({
            lean: () => ({
              exec: () => Promise.resolve([nanDiscount]),
            }),
          });

          const params = {
            studioId: '507f1f77bcf86cd799439001',
            student: {
              firstName: 'NaN',
              lastName: 'Test',
              classPosition: 1,
              studentPosition: 1,
              tuitionFee: 100,
              enrollmentId: '507f1f77bcf86cd799439020',
            },
            category: 'multi-class' as const,
          };

          const result = await discountService.calculateDiscount(params);

          // Expected: Should handle NaN gracefully (likely 0)
          expect(isNaN(result.totalDiscount)).toBe(false);
          expect(isFinite(result.totalDiscount)).toBe(true);
        });
      });

      describe('Complex Exclusion Scenarios', () => {
        it('should handle multiple exclusion patterns', async () => {
          const multiExclusionDiscount = {
            _id: '507f1f77bcf86cd799439051',
            studioId: '507f1f77bcf86cd799439001',
            category: 'multi-class',
            type: 'percent',
            isActive: true,
            discounts: { 1: 25 },
            discountRules: 'all-months',
            excludedClasses: [
              '507f1f77bcf86cd799439020',
              '507f1f77bcf86cd799439021',
              '507f1f77bcf86cd799439022',
            ],
          };

          mockDiscountModel.find.mockReturnValue({
            lean: () => ({
              exec: () => Promise.resolve([multiExclusionDiscount]),
            }),
          });

          // Test excluded enrollment
          const excludedParams = {
            studioId: '507f1f77bcf86cd799439001',
            student: {
              firstName: 'Excluded',
              lastName: 'Student',
              classPosition: 1,
              studentPosition: 1,
              tuitionFee: 100,
              enrollmentId: '507f1f77bcf86cd799439021', // In exclusion list
            },
            category: 'multi-class' as const,
          };

          const excludedResult =
            await discountService.calculateDiscount(excludedParams);
          expect(excludedResult.totalDiscount).toBe(0);

          // Test non-excluded enrollment
          const includedParams = {
            ...excludedParams,
            student: {
              ...excludedParams.student,
              enrollmentId: '507f1f77bcf86cd799439099',
            },
          };

          const includedResult =
            await discountService.calculateDiscount(includedParams);
          expect(includedResult.totalDiscount).toBe(25);
        });

        it('should handle case-sensitive enrollment ID exclusions', async () => {
          const caseDiscount = {
            _id: '507f1f77bcf86cd799439052',
            studioId: '507f1f77bcf86cd799439001',
            category: 'multi-class',
            type: 'percent',
            isActive: true,
            discounts: { 1: 30 },
            discountRules: 'all-months',
            excludedClasses: ['507F1F77BCF86CD799439020'], // Uppercase
          };

          mockDiscountModel.find.mockReturnValue({
            lean: () => ({
              exec: () => Promise.resolve([caseDiscount]),
            }),
          });

          const params = {
            studioId: '507f1f77bcf86cd799439001',
            student: {
              firstName: 'Case',
              lastName: 'Test',
              classPosition: 1,
              studentPosition: 1,
              tuitionFee: 100,
              enrollmentId: '507f1f77bcf86cd799439020', // Lowercase
            },
            category: 'multi-class' as const,
          };

          const result = await discountService.calculateDiscount(params);

          // Should not match due to case sensitivity, discount should apply
          expect(result.totalDiscount).toBe(30);
        });
      });

      describe('Performance and Stress Test Scenarios', () => {
        it('should handle large numbers of discounts efficiently', async () => {
          const manyDiscounts = Array.from({ length: 100 }, (_, i) => ({
            _id: `507f1f77bcf86cd79943${String(i).padStart(4, '0')}`,
            studioId: '507f1f77bcf86cd799439001',
            category: 'multi-class',
            type: 'percent',
            isActive: true,
            discounts: { 1: 1 }, // 1% each
            discountRules: 'all-months',
            excludedClasses: [],
          }));

          mockDiscountModel.find.mockReturnValue({
            lean: () => ({
              exec: () => Promise.resolve(manyDiscounts),
            }),
          });

          const params = {
            studioId: '507f1f77bcf86cd799439001',
            student: {
              firstName: 'Stress',
              lastName: 'Test',
              classPosition: 1,
              studentPosition: 1,
              tuitionFee: 100,
              enrollmentId: '507f1f77bcf86cd799439020',
            },
            category: 'multi-class' as const,
          };

          const startTime = Date.now();
          const result = await discountService.calculateDiscount(params);
          const endTime = Date.now();

          // Expected: 100 discounts * 1% = 100% total
          expect(result.totalDiscount).toBe(100);
          expect(endTime - startTime).toBeLessThan(100); // Should complete quickly
        });

        it('should handle very deep discount position mappings', async () => {
          const deepPositions = {};
          for (let i = 1; i <= 1000; i++) {
            deepPositions[i] = i * 0.1; // 0.1%, 0.2%, 0.3%, etc.
          }

          const deepDiscount = {
            _id: '507f1f77bcf86cd799439053',
            studioId: '507f1f77bcf86cd799439001',
            category: 'multi-class',
            type: 'percent',
            isActive: true,
            discounts: deepPositions,
            discountRules: 'all-months',
            excludedClasses: [],
          };

          mockDiscountModel.find.mockReturnValue({
            lean: () => ({
              exec: () => Promise.resolve([deepDiscount]),
            }),
          });

          const params = {
            studioId: '507f1f77bcf86cd799439001',
            student: {
              firstName: 'Deep',
              lastName: 'Position',
              classPosition: 500, // Middle position
              studentPosition: 1,
              tuitionFee: 1000,
              enrollmentId: '507f1f77bcf86cd799439020',
            },
            category: 'multi-class' as const,
          };

          const result = await discountService.calculateDiscount(params);

          // Expected: 1000 * 50% = 500 (position 500 = 50%)
          expect(result.totalDiscount).toBe(500);
        });
      });

      describe('Concurrent Discount Application Edge Cases', () => {
        it('should handle discounts that could result in negative final amounts', async () => {
          const overDiscounts = [
            {
              _id: '507f1f77bcf86cd799439054',
              studioId: '507f1f77bcf86cd799439001',
              category: 'multi-class',
              type: 'percent',
              isActive: true,
              discounts: { 1: 200 }, // 200% discount
              discountRules: 'all-months',
              excludedClasses: [],
            },
            {
              _id: '507f1f77bcf86cd799439055',
              studioId: '507f1f77bcf86cd799439001',
              category: 'multi-student',
              type: 'dollars',
              isActive: true,
              amountByStudent: { 1: 50 }, // $50 discount
              discountRules: 'all-months',
              excludedClasses: [],
            },
          ];

          mockDiscountModel.find.mockReturnValue({
            lean: () => ({
              exec: () => Promise.resolve(overDiscounts),
            }),
          });

          const params = {
            studioId: '507f1f77bcf86cd799439001',
            student: {
              firstName: 'Over',
              lastName: 'Discount',
              classPosition: 1,
              studentPosition: 1,
              tuitionFee: 100,
              enrollmentId: '507f1f77bcf86cd799439020',
            },
            category: 'all' as const,
          };

          const result = await discountService.calculateDiscount(params);

          // Expected: 200% + $50 = $200 + $50 = $250 total discount
          expect(result.totalDiscount).toBe(250);
        });

        it('should handle fractional tuition fees with complex discount combinations', async () => {
          const complexDiscounts = [
            {
              _id: '507f1f77bcf86cd799439056',
              studioId: '507f1f77bcf86cd799439001',
              category: 'multi-class',
              type: 'percent',
              isActive: true,
              discounts: { 1: 33.333 }, // Repeating decimal
              discountRules: 'all-months',
              excludedClasses: [],
            },
            {
              _id: '507f1f77bcf86cd799439057',
              studioId: '507f1f77bcf86cd799439001',
              category: 'multi-student',
              type: 'dollars',
              isActive: true,
              amountByStudent: { 1: 12.567 }, // Precise decimal
              discountRules: 'all-months',
              excludedClasses: [],
            },
          ];

          mockDiscountModel.find.mockReturnValue({
            lean: () => ({
              exec: () => Promise.resolve(complexDiscounts),
            }),
          });

          const params = {
            studioId: '507f1f77bcf86cd799439001',
            student: {
              firstName: 'Fractional',
              lastName: 'Complex',
              classPosition: 1,
              studentPosition: 1,
              tuitionFee: 123.456789, // Precise decimal fee
              enrollmentId: '507f1f77bcf86cd799439020',
            },
            category: 'all' as const,
          };

          const result = await discountService.calculateDiscount(params);

          // Expected: 123.456789 * 33.333% + 12.567 = 41.15 + 12.567 = 53.72 (rounded)
          expect(result.totalDiscount).toBe(53.72);
        });
      });

      describe('Real-world Complex Scenarios', () => {
        it('should handle family with 5 children, multiple classes each, mixed discount types', async () => {
          const familyDiscounts = [
            {
              _id: '507f1f77bcf86cd799439058',
              studioId: '507f1f77bcf86cd799439001',
              category: 'multi-class',
              type: 'percent',
              isActive: true,
              discounts: { 1: 0, 2: 10, 3: 15, 4: 20, 5: 25 },
              discountRules: 'all-months',
              excludedClasses: [],
            },
            {
              _id: '507f1f77bcf86cd799439059',
              studioId: '507f1f77bcf86cd799439001',
              category: 'multi-student',
              type: 'by-student',
              isActive: true,
              byStudent: { 1: 0, 2: 20, 3: 30, 4: 40, 5: 50 },
              discountRules: 'all-months',
              excludedClasses: [],
            },
          ];

          mockDiscountModel.find.mockReturnValue({
            lean: () => ({
              exec: () => Promise.resolve(familyDiscounts),
            }),
          });

          // Test 5th child, 4th class
          const params = {
            studioId: '507f1f77bcf86cd799439001',
            student: {
              firstName: 'Child',
              lastName: 'Five',
              classPosition: 4, // 4th class = 20% discount
              studentPosition: 5, // 5th student = 50% discount
              tuitionFee: 150,
              enrollmentId: '507f1f77bcf86cd799439020',
            },
            category: 'all' as const,
          };

          const result = await discountService.calculateDiscount(params);

          // Expected: 150 * 20% + 150 * 50% = 30 + 75 = 105
          expect(result.totalDiscount).toBe(105);
        });

        it('should handle mid-year enrollment with prorated fees and complex discounts', async () => {
          const midYearDiscounts = [
            {
              _id: '507f1f77bcf86cd799439060',
              studioId: '507f1f77bcf86cd799439001',
              category: 'multi-class',
              type: 'flat',
              isActive: true,
              flat: 12.5, // 12.5% flat discount
              discountRules: 'first-month',
              excludedClasses: [],
            },
            {
              _id: '507f1f77bcf86cd799439061',
              studioId: '507f1f77bcf86cd799439001',
              category: 'multi-student',
              type: 'amount-by-student',
              isActive: true,
              amountByStudent: { 1: 0, 2: 25.5, 3: 45.75 },
              discountRules: 'all-months',
              excludedClasses: [],
            },
          ];

          mockDiscountModel.find.mockReturnValue({
            lean: () => ({
              exec: () => Promise.resolve(midYearDiscounts),
            }),
          });

          const params = {
            studioId: '507f1f77bcf86cd799439001',
            student: {
              firstName: 'MidYear',
              lastName: 'Enrollment',
              classPosition: 1,
              studentPosition: 3, // Third student
              tuitionFee: 87.33, // Prorated amount
              enrollmentId: '507f1f77bcf86cd799439020',
            },
            category: 'all' as const,
          };

          const result = await discountService.calculateDiscount(params);

          // Expected: 87.33 * 12.5% + 45.75 = 10.92 + 45.75 = 56.67
          expect(result.totalDiscount).toBe(56.67);
        });

        it('should handle scholarship students with maximum discount combinations', async () => {
          const scholarshipDiscounts = [
            {
              _id: '507f1f77bcf86cd799439062',
              studioId: '507f1f77bcf86cd799439001',
              category: 'multi-class',
              type: 'percent',
              isActive: true,
              discounts: { 1: 95 }, // 95% scholarship discount
              discountRules: 'all-months',
              excludedClasses: [],
            },
            {
              _id: '507f1f77bcf86cd799439063',
              studioId: '507f1f77bcf86cd799439001',
              category: 'multi-student',
              type: 'dollars',
              isActive: true,
              amountByStudent: { 1: 500 }, // Additional $500 grant
              discountRules: 'all-months',
              excludedClasses: [],
            },
          ];

          mockDiscountModel.find.mockReturnValue({
            lean: () => ({
              exec: () => Promise.resolve(scholarshipDiscounts),
            }),
          });

          const params = {
            studioId: '507f1f77bcf86cd799439001',
            student: {
              firstName: 'Scholarship',
              lastName: 'Student',
              classPosition: 1,
              studentPosition: 1,
              tuitionFee: 2000, // High tuition
              enrollmentId: '507f1f77bcf86cd799439020',
            },
            category: 'all' as const,
          };

          const result = await discountService.calculateDiscount(params);

          // Expected: 2000 * 95% + 500 = 1900 + 500 = 2400 (more than tuition!)
          expect(result.totalDiscount).toBe(2400);
        });
      });

      describe('Zero Dollar Class Business Rule Edge Cases', () => {
        it('should enforce zero dollar rule even with scholarship-level discounts', async () => {
          const extremeDiscounts = [
            {
              _id: '507f1f77bcf86cd799439065',
              studioId: '507f1f77bcf86cd799439001',
              category: 'multi-class',
              type: 'percent',
              isActive: true,
              discounts: { 1: 100 }, // 100% discount
              discountRules: 'all-months',
              excludedClasses: [],
            },
            {
              _id: '507f1f77bcf86cd799439066',
              studioId: '507f1f77bcf86cd799439001',
              category: 'multi-student',
              type: 'dollars',
              isActive: true,
              amountByStudent: { 1: 1000 }, // $1000 grant
              discountRules: 'all-months',
              excludedClasses: [],
            },
            {
              _id: '507f1f77bcf86cd799439067',
              studioId: '507f1f77bcf86cd799439001',
              category: 'multi-class',
              type: 'flat',
              isActive: true,
              flat: 50, // 50% flat discount
              discountRules: 'all-months',
              excludedClasses: [],
            },
          ];

          mockDiscountModel.find.mockReturnValue({
            lean: () => ({
              exec: () => Promise.resolve(extremeDiscounts),
            }),
          });

          const params = {
            studioId: '507f1f77bcf86cd799439001',
            student: {
              firstName: 'Zero',
              lastName: 'ExtremeDiscounts',
              classPosition: 1,
              studentPosition: 1,
              tuitionFee: 0, // Zero dollar class
              enrollmentId: '507f1f77bcf86cd799439020',
            },
            category: 'all' as const,
          };

          const result = await discountService.calculateDiscount(params);

          // Expected: 0 (zero dollar rule overrides ALL discount types)
          expect(result.totalDiscount).toBe(0);
        });

        it('should handle mixed zero and positive tuition scenarios in family', async () => {
          mockDiscountModel.find.mockReturnValue({
            lean: () => ({
              exec: () =>
                Promise.resolve([
                  mockDiscounts.multiClassPercentDiscount,
                  mockDiscounts.multiStudentPercentDiscount,
                ]),
            }),
          });

          // Test positive tuition first
          const positiveParams = {
            studioId: '507f1f77bcf86cd799439001',
            student: {
              firstName: 'Positive',
              lastName: 'Tuition',
              classPosition: 2, // 10% class discount
              studentPosition: 2, // 15% student discount
              tuitionFee: 100,
              enrollmentId: '507f1f77bcf86cd799439020',
            },
            category: 'all' as const,
          };

          const positiveResult =
            await discountService.calculateDiscount(positiveParams);
          expect(positiveResult.totalDiscount).toBe(25); // 10 + 15 = 25

          // Test zero tuition with same discount positions
          const zeroParams = {
            ...positiveParams,
            student: {
              ...positiveParams.student,
              firstName: 'Zero',
              lastName: 'Tuition',
              tuitionFee: 0, // Zero dollar class
            },
          };

          const zeroResult =
            await discountService.calculateDiscount(zeroParams);
          expect(zeroResult.totalDiscount).toBe(0); // Zero dollar rule applied
        });

        it('should handle exactly zero vs very small positive amounts consistently', async () => {
          mockDiscountModel.find.mockReturnValue({
            lean: () => ({
              exec: () =>
                Promise.resolve([mockDiscounts.multiClassDollarDiscount]),
            }),
          });

          // Test exactly zero
          const zeroParams = {
            studioId: '507f1f77bcf86cd799439001',
            student: {
              firstName: 'Exactly',
              lastName: 'Zero',
              classPosition: 3, // Would get $10 discount
              studentPosition: 1,
              tuitionFee: 0.0, // Exactly zero
              enrollmentId: '507f1f77bcf86cd799439020',
            },
            category: 'multi-class' as const,
          };

          const zeroResult =
            await discountService.calculateDiscount(zeroParams);
          expect(zeroResult.totalDiscount).toBe(0);

          // Test very small positive (should get discount)
          const smallParams = {
            ...zeroParams,
            student: {
              ...zeroParams.student,
              tuitionFee: 0.001, // Very small positive
            },
          };

          const smallResult =
            await discountService.calculateDiscount(smallParams);
          expect(smallResult.totalDiscount).toBe(10); // Dollar discount should apply
        });
      });

      describe('Memory and Performance Edge Cases', () => {
        it('should handle extremely large ObjectId arrays in exclusions', async () => {
          const largeExclusionList = Array.from(
            { length: 10000 },
            (_, i) => `507f1f77bcf86cd79943${String(i).padStart(4, '0')}`,
          );

          const largeExclusionDiscount = {
            _id: '507f1f77bcf86cd799439064',
            studioId: '507f1f77bcf86cd799439001',
            category: 'multi-class',
            type: 'percent',
            isActive: true,
            discounts: { 1: 40 },
            discountRules: 'all-months',
            excludedClasses: largeExclusionList,
          };

          mockDiscountModel.find.mockReturnValue({
            lean: () => ({
              exec: () => Promise.resolve([largeExclusionDiscount]),
            }),
          });

          const params = {
            studioId: '507f1f77bcf86cd799439001',
            student: {
              firstName: 'Large',
              lastName: 'Exclusion',
              classPosition: 1,
              studentPosition: 1,
              tuitionFee: 100,
              enrollmentId: '507f1f77bcf86cd799999999', // Not in exclusion list
            },
            category: 'multi-class' as const,
          };

          const startTime = Date.now();
          const result = await discountService.calculateDiscount(params);
          const endTime = Date.now();

          expect(result.totalDiscount).toBe(40);
          expect(endTime - startTime).toBeLessThan(200); // Should handle large arrays efficiently
        });
      });
    });
  });
});
