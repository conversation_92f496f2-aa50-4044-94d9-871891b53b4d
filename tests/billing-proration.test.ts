import { describe, it, expect } from 'vitest';
import {
  calculateProratedAmount,
  calculateProratedAmountFromEntity,
  type ProrationParams,
} from '../src/utils/helpers/billing-proration';

describe('Billing Proration Logic', () => {
  describe('Monthly Billing Scenarios', () => {
    it('should calculate proration for billing day 15th, join July 23rd', () => {
      const params: ProrationParams = {
        startDate: '2025-03-01',
        endDate: '2025-09-01',
        billingDay: 15,
        tuitionBillingCycle: 'monthly',
        tuitionFee: 30,
        today: '2025-07-23',
      };

      const result = calculateProratedAmount(params);

      console.log('\nCalculation Details:');
      console.log('-------------------');
      console.log(`Billing Cycle: ${params.tuitionBillingCycle}`);
      console.log(`Billing Day: ${params.billingDay}`);
      console.log(`Tuition Fee: $${params.tuitionFee}`);
      console.log(`Start Date: ${params.startDate}`);
      console.log(`End Date: ${params.endDate}`);
      console.log(`Join Date: ${params.today}`);
      console.log('\nMonthly Calculation:');
      console.log('Days in Month: 31 (July)');
      console.log('Daily Rate: $0.97 ($30/31 days)');
      console.log('Days to Charge: 23 (July 23 - Aug 15)');
      console.log('Expected Amount: $22.26');
      console.log(`Actual Amount: $${result.toFixed(2)}`);

      expect(result).toBe(22.26);
    });

    it('should calculate proration for billing day 2nd, join June 2nd', () => {
      const params: ProrationParams = {
        startDate: '2025-03-01',
        endDate: '2025-07-03',
        billingDay: 2,
        tuitionBillingCycle: 'monthly',
        tuitionFee: 30,
        today: '2025-06-02',
      };

      const result = calculateProratedAmount(params);

      console.log('\nCalculation Details:');
      console.log('-------------------');
      console.log(`Billing Cycle: ${params.tuitionBillingCycle}`);
      console.log(`Billing Day: ${params.billingDay}`);
      console.log(`Tuition Fee: $${params.tuitionFee}`);
      console.log(`Start Date: ${params.startDate}`);
      console.log(`End Date: ${params.endDate}`);
      console.log(`Join Date: ${params.today}`);
      console.log('\nMonthly Calculation:');
      console.log('Days in Month: 30 (June)');
      console.log('Daily Rate: $1.00 ($30/30 days)');
      console.log('Days to Charge: 30 (June 2 - July 2)');
      console.log('Expected Amount: $30.00');
      console.log(`Actual Amount: $${result.toFixed(2)}`);

      expect(result).toBe(30);
    });

    it('should calculate proration for billing day 1st, join June 2nd, end July 1st', () => {
      const params: ProrationParams = {
        startDate: '2025-03-01',
        endDate: '2025-07-01',
        billingDay: 1,
        tuitionBillingCycle: 'monthly',
        tuitionFee: 30,
        today: '2025-06-02',
      };

      const result = calculateProratedAmount(params);

      console.log('\nCalculation Details:');
      console.log('-------------------');
      console.log(`Billing Cycle: ${params.tuitionBillingCycle}`);
      console.log(`Billing Day: ${params.billingDay}`);
      console.log(`Tuition Fee: $${params.tuitionFee}`);
      console.log(`Start Date: ${params.startDate}`);
      console.log(`End Date: ${params.endDate}`);
      console.log(`Join Date: ${params.today}`);
      console.log('\nMonthly Calculation:');
      console.log('Days in Month: 30 (June)');
      console.log('Daily Rate: $1.00 ($30/30 days)');
      console.log('Days to Charge: 29 (June 2 - July 1)');
      console.log('Expected Amount: $29.00');
      console.log(`Actual Amount: $${result.toFixed(2)}`);

      expect(result).toBe(29);
    });

    it('should calculate proration for billing day 1st, join June 2nd, end June 29th', () => {
      const params: ProrationParams = {
        startDate: '2025-03-01',
        endDate: '2025-06-29',
        billingDay: 1,
        tuitionBillingCycle: 'monthly',
        tuitionFee: 30,
        today: '2025-06-02',
      };

      const result = calculateProratedAmount(params);

      console.log('\nCalculation Details:');
      console.log('-------------------');
      console.log(`Billing Cycle: ${params.tuitionBillingCycle}`);
      console.log(`Billing Day: ${params.billingDay}`);
      console.log(`Tuition Fee: $${params.tuitionFee}`);
      console.log(`Start Date: ${params.startDate}`);
      console.log(`End Date: ${params.endDate}`);
      console.log(`Join Date: ${params.today}`);
      console.log('\nMonthly Calculation:');
      console.log('Days in Month: 30 (June)');
      console.log('Daily Rate: $1.00 ($30/30 days)');
      console.log('Days to Charge: 28 (June 2 - June 29)');
      console.log('Expected Amount: $28.00');
      console.log(`Actual Amount: $${result.toFixed(2)}`);

      expect(result).toBe(28);
    });

    it('should charge full amount when joining before class starts', () => {
      const params: ProrationParams = {
        startDate: '2025-07-15',
        endDate: '2025-12-31',
        billingDay: 10,
        tuitionBillingCycle: 'monthly',
        tuitionFee: 30,
        today: '2025-06-20',
      };

      const result = calculateProratedAmount(params);

      console.log('\nCalculation Details:');
      console.log('-------------------');
      console.log(`Billing Cycle: ${params.tuitionBillingCycle}`);
      console.log(`Billing Day: ${params.billingDay}`);
      console.log(`Tuition Fee: $${params.tuitionFee}`);
      console.log(`Start Date: ${params.startDate}`);
      console.log(`End Date: ${params.endDate}`);
      console.log(`Join Date: ${params.today}`);
      console.log('\nMonthly Calculation:');
      console.log('Days in Month: 30 (June)');
      console.log('Daily Rate: $1.00 ($30/30 days)');
      console.log(
        'Days to Charge: 30 (Full month - joining before class starts)',
      );
      console.log('Expected Amount: $30.00');
      console.log(`Actual Amount: $${result.toFixed(2)}`);

      expect(result).toBe(30);
    });

    it('should calculate proration for Monthly 5: Three invoices with mid-month start', () => {
      const params: ProrationParams = {
        startDate: '2025-06-15',
        endDate: '2025-08-16',
        billingDay: 15,
        tuitionBillingCycle: 'monthly',
        tuitionFee: 30,
        today: '2025-06-15',
      };

      const result = calculateProratedAmount(params);

      console.log('\nCalculation Details:');
      console.log('-------------------');
      console.log(`Billing Cycle: ${params.tuitionBillingCycle}`);
      console.log(`Billing Day: ${params.billingDay}`);
      console.log(`Tuition Fee: $${params.tuitionFee}`);
      console.log(`Start Date: ${params.startDate}`);
      console.log(`End Date: ${params.endDate}`);
      console.log(`Join Date: ${params.today}`);
      console.log('\nMonthly Calculation:');
      console.log('Days in Month: 30 (June)');
      console.log('Daily Rate: $1.00 ($30/30 days)');
      console.log('Days to Charge: 30 (Full month)');
      console.log('Expected Amount: $30.00');
      console.log(`Actual Amount: $${result.toFixed(2)}`);

      expect(result).toBe(30);
    });

    it('should calculate proration for Monthly 6: Single month with partial days', () => {
      const params: ProrationParams = {
        startDate: '2025-06-28',
        endDate: '2025-07-03',
        billingDay: 1,
        tuitionBillingCycle: 'monthly',
        tuitionFee: 30,
        today: '2025-06-28',
      };

      const result = calculateProratedAmount(params);

      console.log('\nCalculation Details:');
      console.log('-------------------');
      console.log(`Billing Cycle: ${params.tuitionBillingCycle}`);
      console.log(`Billing Day: ${params.billingDay}`);
      console.log(`Tuition Fee: $${params.tuitionFee}`);
      console.log(`Start Date: ${params.startDate}`);
      console.log(`End Date: ${params.endDate}`);
      console.log(`Join Date: ${params.today}`);
      console.log('\nMonthly Calculation:');
      console.log('Days in Month: 30 (June)');
      console.log('Daily Rate: $1.00 ($30/30 days)');
      console.log('Days to Charge: 3 (June 28-30)');
      console.log('Expected Amount: $3.00');
      console.log(`Actual Amount: $${result.toFixed(2)}`);

      expect(result).toBe(3);
    });

    it('should calculate proration for Monthly 8: Long term subscription', () => {
      const params: ProrationParams = {
        startDate: '2025-09-15',
        endDate: '2026-07-07',
        billingDay: 1,
        tuitionBillingCycle: 'monthly',
        tuitionFee: 30,
        today: '2025-09-15',
      };

      const result = calculateProratedAmount(params);

      console.log('\nCalculation Details:');
      console.log('-------------------');
      console.log(`Billing Cycle: ${params.tuitionBillingCycle}`);
      console.log(`Billing Day: ${params.billingDay}`);
      console.log(`Tuition Fee: $${params.tuitionFee}`);
      console.log(`Start Date: ${params.startDate}`);
      console.log(`End Date: ${params.endDate}`);
      console.log(`Join Date: ${params.today}`);
      console.log('\nMonthly Calculation:');
      console.log('Days in Month: 30 (September)');
      console.log('Daily Rate: $1.00 ($30/30 days)');
      console.log('Days to Charge: 16 (Sep 15-30)');
      console.log('Expected Amount: $16.00');
      console.log(`Actual Amount: $${result.toFixed(2)}`);

      expect(result).toBe(16);
    });

    it('should calculate proration for Monthly 9: Multiple months with partial start', () => {
      const params: ProrationParams = {
        startDate: '2025-06-28',
        endDate: '2025-10-03',
        billingDay: 1,
        tuitionBillingCycle: 'monthly',
        tuitionFee: 30,
        today: '2025-06-28',
      };

      const result = calculateProratedAmount(params);

      console.log('\nCalculation Details:');
      console.log('-------------------');
      console.log(`Billing Cycle: ${params.tuitionBillingCycle}`);
      console.log(`Billing Day: ${params.billingDay}`);
      console.log(`Tuition Fee: $${params.tuitionFee}`);
      console.log(`Start Date: ${params.startDate}`);
      console.log(`End Date: ${params.endDate}`);
      console.log(`Join Date: ${params.today}`);
      console.log('\nMonthly Calculation:');
      console.log('Days in Month: 30 (June)');
      console.log('Daily Rate: $1.00 ($30/30 days)');
      console.log('Days to Charge: 3 (June 28-30)');
      console.log('Expected Amount: $3.00');
      console.log(`Actual Amount: $${result.toFixed(2)}`);

      expect(result).toBe(3);
    });
  });

  describe('Weekly Billing Scenarios', () => {
    it('should calculate weekly proration for billing day 2nd (Tuesday), join Wednesday', () => {
      const params: ProrationParams = {
        startDate: '2025-06-02', // Monday
        endDate: '2025-07-15',
        billingDay: 2, // 2nd of month (falls on Tuesday in June 2025)
        tuitionBillingCycle: 'weekly',
        tuitionFee: 7,
        today: '2025-06-04', // Wednesday
      };

      const result = calculateProratedAmount(params);

      console.log('\nCalculation Details:');
      console.log('-------------------');
      console.log(`Billing Cycle: ${params.tuitionBillingCycle}`);
      console.log(`Billing Day: ${params.billingDay}`);
      console.log(`Tuition Fee: $${params.tuitionFee}`);
      console.log(`Start Date: ${params.startDate}`);
      console.log(`End Date: ${params.endDate}`);
      console.log(`Join Date: ${params.today}`);
      console.log('\nWeekly Calculation:');
      console.log('Daily Rate: $1.00 ($7/7 days)');
      console.log('Days to Charge: 5 (Wed-Sun)');
      console.log('Expected Amount: $5.00');
      console.log(`Actual Amount: $${result.toFixed(2)}`);

      expect(result).toBe(5);
    });

    it('should calculate weekly proration for billing day 6th (Friday), join Monday', () => {
      const params: ProrationParams = {
        startDate: '2025-06-01', // Sunday
        endDate: '2025-07-31',
        billingDay: 6, // 6th of month (falls on Friday in June 2025)
        tuitionBillingCycle: 'weekly',
        tuitionFee: 7,
        today: '2025-06-09', // Monday
      };

      const result = calculateProratedAmount(params);

      console.log('\nCalculation Details:');
      console.log('-------------------');
      console.log(`Billing Cycle: ${params.tuitionBillingCycle}`);
      console.log(`Billing Day: ${params.billingDay}`);
      console.log(`Tuition Fee: $${params.tuitionFee}`);
      console.log(`Start Date: ${params.startDate}`);
      console.log(`End Date: ${params.endDate}`);
      console.log(`Join Date: ${params.today}`);
      console.log('\nWeekly Calculation:');
      console.log('Daily Rate: $1.00 ($7/7 days)');
      console.log('Days to Charge: 4 (Mon-Thu)');
      console.log('Expected Amount: $4.00');
      console.log(`Actual Amount: $${result.toFixed(2)}`);

      expect(result).toBe(4);
    });

    it('should calculate weekly proration for billing day 1st (Sunday), join Friday', () => {
      const params: ProrationParams = {
        startDate: '2025-06-01', // Sunday
        endDate: '2025-08-01',
        billingDay: 1, // 1st of month (falls on Sunday in June 2025)
        tuitionBillingCycle: 'weekly',
        tuitionFee: 7,
        today: '2025-06-13', // Friday
      };

      const result = calculateProratedAmount(params);

      console.log('\nCalculation Details:');
      console.log('-------------------');
      console.log(`Billing Cycle: ${params.tuitionBillingCycle}`);
      console.log(`Billing Day: ${params.billingDay}`);
      console.log(`Tuition Fee: $${params.tuitionFee}`);
      console.log(`Start Date: ${params.startDate}`);
      console.log(`End Date: ${params.endDate}`);
      console.log(`Join Date: ${params.today}`);
      console.log('\nWeekly Calculation:');
      console.log('Daily Rate: $1.00 ($7/7 days)');
      console.log('Days to Charge: 2 (Fri-Sat)');
      console.log('Expected Amount: $2.00');
      console.log(`Actual Amount: $${result.toFixed(2)}`);

      expect(result).toBe(2);
    });

    it('should charge full amount when joining before class starts', () => {
      const params: ProrationParams = {
        startDate: '2025-07-01', // Tuesday
        endDate: '2025-08-15',
        billingDay: 3, // 3rd of month (falls on Wednesday in July 2025)
        tuitionBillingCycle: 'weekly',
        tuitionFee: 7,
        today: '2025-06-25', // Wednesday
      };

      const result = calculateProratedAmount(params);

      console.log('\nCalculation Details:');
      console.log('-------------------');
      console.log(`Billing Cycle: ${params.tuitionBillingCycle}`);
      console.log(`Billing Day: ${params.billingDay}`);
      console.log(`Tuition Fee: $${params.tuitionFee}`);
      console.log(`Start Date: ${params.startDate}`);
      console.log(`End Date: ${params.endDate}`);
      console.log(`Join Date: ${params.today}`);
      console.log('\nWeekly Calculation:');
      console.log('Daily Rate: $1.00 ($7/7 days)');
      console.log(
        'Days to Charge: 7 (Full week - joining before class starts)',
      );
      console.log('Expected Amount: $7.00');
      console.log(`Actual Amount: $${result.toFixed(2)}`);

      expect(result).toBe(7);
    });

    it('should calculate proration for Weekly 3: Partial week at start and end', () => {
      const params: ProrationParams = {
        startDate: '2025-06-03',
        endDate: '2025-06-17',
        billingDay: 2,
        tuitionBillingCycle: 'weekly',
        tuitionFee: 7,
        today: '2025-06-03',
      };

      const result = calculateProratedAmount(params);

      console.log('\nCalculation Details:');
      console.log('-------------------');
      console.log(`Billing Cycle: ${params.tuitionBillingCycle}`);
      console.log(`Billing Day: ${params.billingDay}`);
      console.log(`Tuition Fee: $${params.tuitionFee}`);
      console.log(`Start Date: ${params.startDate}`);
      console.log(`End Date: ${params.endDate}`);
      console.log(`Join Date: ${params.today}`);
      console.log('\nWeekly Calculation:');
      console.log('Daily Rate: $1.00 ($7/7 days)');
      console.log('Days to Charge: 7 (Full week)');
      console.log('Expected Amount: $7.00');
      console.log(`Actual Amount: $${result.toFixed(2)}`);

      expect(result).toBe(7);
    });
  });

  describe('Bi-Weekly Billing Scenarios', () => {
    it('should calculate bi-weekly proration for billing day 2nd (Monday), join Thursday', () => {
      const params: ProrationParams = {
        startDate: '2025-06-02', // Monday
        endDate: '2025-08-15',
        billingDay: 2, // 2nd of month (falls on Monday in June 2025)
        tuitionBillingCycle: 'bi-weekly',
        tuitionFee: 14,
        today: '2025-06-05', // Thursday
      };

      const result = calculateProratedAmount(params);

      console.log('\nCalculation Details:');
      console.log('-------------------');
      console.log(`Billing Cycle: ${params.tuitionBillingCycle}`);
      console.log(`Billing Day: ${params.billingDay}`);
      console.log(`Tuition Fee: $${params.tuitionFee}`);
      console.log(`Start Date: ${params.startDate}`);
      console.log(`End Date: ${params.endDate}`);
      console.log(`Join Date: ${params.today}`);
      console.log('\nBi-Weekly Calculation:');
      console.log('Daily Rate: $1.00 ($14/14 days)');
      console.log('Days to Charge: 11 (Thu-Sun)');
      console.log('Expected Amount: $11.00');
      console.log(`Actual Amount: $${result.toFixed(2)}`);

      expect(result).toBe(11);
    });

    it('should calculate bi-weekly proration for billing day 6th (Friday), join Sunday', () => {
      const params: ProrationParams = {
        startDate: '2025-06-01', // Sunday
        endDate: '2025-09-01',
        billingDay: 6, // 6th of month (falls on Friday in June 2025)
        tuitionBillingCycle: 'bi-weekly',
        tuitionFee: 14,
        today: '2025-06-08', // Sunday
      };

      const result = calculateProratedAmount(params);

      console.log('\nCalculation Details:');
      console.log('-------------------');
      console.log(`Billing Cycle: ${params.tuitionBillingCycle}`);
      console.log(`Billing Day: ${params.billingDay}`);
      console.log(`Tuition Fee: $${params.tuitionFee}`);
      console.log(`Start Date: ${params.startDate}`);
      console.log(`End Date: ${params.endDate}`);
      console.log(`Join Date: ${params.today}`);
      console.log('\nBi-Weekly Calculation:');
      console.log('Daily Rate: $1.00 ($14/14 days)');
      console.log('Days to Charge: 12 (Sun-Fri)');
      console.log('Expected Amount: $12.00');
      console.log(`Actual Amount: $${result.toFixed(2)}`);

      expect(result).toBe(12);
    });

    it('should calculate bi-weekly proration when joining on billing day', () => {
      const params: ProrationParams = {
        startDate: '2025-06-01', // Sunday
        endDate: '2025-08-30',
        billingDay: 11, // 11th of month (falls on Wednesday in June 2025)
        tuitionBillingCycle: 'bi-weekly',
        tuitionFee: 14,
        today: '2025-06-11', // Wednesday (billing day)
      };

      const result = calculateProratedAmount(params);

      console.log('\nCalculation Details:');
      console.log('-------------------');
      console.log(`Billing Cycle: ${params.tuitionBillingCycle}`);
      console.log(`Billing Day: ${params.billingDay}`);
      console.log(`Tuition Fee: $${params.tuitionFee}`);
      console.log(`Start Date: ${params.startDate}`);
      console.log(`End Date: ${params.endDate}`);
      console.log(`Join Date: ${params.today}`);
      console.log('\nBi-Weekly Calculation:');
      console.log('Daily Rate: $1.00 ($14/14 days)');
      console.log('Days to Charge: 14 (Full bi-weekly cycle)');
      console.log('Expected Amount: $14.00');
      console.log(`Actual Amount: $${result.toFixed(2)}`);

      expect(result).toBe(14);
    });

    it('should calculate proration for Bi-Weekly 3: Partial periods at start and end', () => {
      const params: ProrationParams = {
        startDate: '2025-06-03',
        endDate: '2025-06-25',
        billingDay: 2,
        tuitionBillingCycle: 'bi-weekly',
        tuitionFee: 14,
        today: '2025-06-03',
      };

      const result = calculateProratedAmount(params);

      console.log('\nCalculation Details:');
      console.log('-------------------');
      console.log(`Billing Cycle: ${params.tuitionBillingCycle}`);
      console.log(`Billing Day: ${params.billingDay}`);
      console.log(`Tuition Fee: $${params.tuitionFee}`);
      console.log(`Start Date: ${params.startDate}`);
      console.log(`End Date: ${params.endDate}`);
      console.log(`Join Date: ${params.today}`);
      console.log('\nBi-Weekly Calculation:');
      console.log('Daily Rate: $1.00 ($14/14 days)');
      console.log('Days to Charge: 14 (Full bi-weekly cycle)');
      console.log('Expected Amount: $14.00');
      console.log(`Actual Amount: $${result.toFixed(2)}`);

      expect(result).toBe(14);
    });
  });

  describe('One-Time Billing Scenarios', () => {
    it('should charge full amount regardless of join date', () => {
      const params: ProrationParams = {
        startDate: '2025-06-01',
        endDate: '2025-12-31',
        billingDay: 15,
        tuitionBillingCycle: 'one-time',
        tuitionFee: 150,
        today: '2025-07-15',
      };

      const result = calculateProratedAmount(params);
      expect(result).toBe(150);
    });

    it('should charge full amount when joining before class starts', () => {
      const params: ProrationParams = {
        startDate: '2025-08-01',
        endDate: '2025-12-15',
        billingDay: 1,
        tuitionBillingCycle: 'one-time',
        tuitionFee: 200,
        today: '2025-07-20',
      };

      const result = calculateProratedAmount(params);
      expect(result).toBe(200);
    });
  });

  describe('Edge Cases', () => {
    it('should handle billing day before class start day in same month', () => {
      const params: ProrationParams = {
        startDate: '2025-06-15', // Class starts 15th
        endDate: '2025-08-15',
        billingDay: 5, // Billing on 5th (before class start)
        tuitionBillingCycle: 'monthly',
        tuitionFee: 100,
        today: '2025-06-20',
      };

      const result = calculateProratedAmount(params);
      // Should move billing to next month (July 5th)
      expect(result).toBeGreaterThan(0);
    });

    it('should return 0 when total days is 0 or negative', () => {
      const params: ProrationParams = {
        startDate: '2025-06-01',
        endDate: '2025-06-01', // Same day end
        billingDay: 15,
        tuitionBillingCycle: 'monthly',
        tuitionFee: 100,
        today: '2025-06-02', // After end date
      };

      const result = calculateProratedAmount(params);
      expect(result).toBe(0);
    });

    it('should throw error when no valid billing date found within 12 months', () => {
      const params: ProrationParams = {
        startDate: '2025-01-01',
        endDate: '2025-12-31',
        billingDay: 31, // Invalid day for some months
        tuitionBillingCycle: 'monthly',
        tuitionFee: 100,
        today: '2025-06-01',
      };

      expect(() => calculateProratedAmount(params)).not.toThrow();
    });

    it('should handle minimum tuition fee', () => {
      const params: ProrationParams = {
        startDate: '2025-06-01',
        endDate: '2025-12-31',
        billingDay: 15,
        tuitionBillingCycle: 'weekly',
        tuitionFee: 0.01, // Minimum fee
        today: '2025-06-10',
      };

      const result = calculateProratedAmount(params);
      expect(result).toBeGreaterThan(0);
    });
  });

  describe('calculateProratedAmountFromEntity', () => {
    it('should work with entity object format', () => {
      const entity = {
        startDate: '2025-06-01',
        endDate: '2025-12-31',
        billingDay: 15,
        tuitionBillingCycle: 'monthly',
        tuitionFee: 30,
      };

      const result = calculateProratedAmountFromEntity(entity, '2025-06-20');
      expect(result).toBeGreaterThan(0);
      expect(typeof result).toBe('number');
    });

    it('should use current date when no override provided', () => {
      const entity = {
        startDate: '2025-01-01',
        endDate: '2025-12-31',
        billingDay: 15,
        tuitionBillingCycle: 'one-time' as const,
        tuitionFee: 100,
      };

      const result = calculateProratedAmountFromEntity(entity);
      expect(result).toBe(100);
    });
  });

  describe('Daily Rate Calculations', () => {
    it('should calculate correct daily rates for different cycles', () => {
      // Weekly: $28/7 = $4/day
      const weeklyParams: ProrationParams = {
        startDate: '2025-06-02',
        endDate: '2025-07-15',
        billingDay: 2,
        tuitionBillingCycle: 'weekly',
        tuitionFee: 28,
        today: '2025-06-04',
      };

      const weeklyResult = calculateProratedAmount(weeklyParams);
      expect(weeklyResult).toBe(20); // 5 days * $4

      // Bi-weekly: $84/14 = $6/day
      const biWeeklyParams: ProrationParams = {
        startDate: '2025-06-01',
        endDate: '2025-09-01',
        billingDay: 6,
        tuitionBillingCycle: 'bi-weekly',
        tuitionFee: 84,
        today: '2025-06-08',
      };

      const biWeeklyResult = calculateProratedAmount(biWeeklyParams);
      expect(biWeeklyResult).toBe(72); // 12 days * $6
    });
  });

  describe('Class End Date Handling', () => {
    it('should respect class end date in monthly billing', () => {
      const params: ProrationParams = {
        startDate: '2025-06-01',
        endDate: '2025-06-15', // Class ends before next billing
        billingDay: 1,
        tuitionBillingCycle: 'monthly',
        tuitionFee: 30,
        today: '2025-06-05',
      };

      const result = calculateProratedAmount(params);
      // Should only charge until class end date
      expect(result).toBeLessThan(30);
    });
  });

  describe('Precision and Rounding', () => {
    it('should round to 2 decimal places correctly', () => {
      const params: ProrationParams = {
        startDate: '2025-06-01',
        endDate: '2025-12-31',
        billingDay: 15,
        tuitionBillingCycle: 'monthly',
        tuitionFee: 33.33,
        today: '2025-06-20',
      };

      const result = calculateProratedAmount(params);
      // Should be rounded to 2 decimal places
      expect(result.toString()).toMatch(/^\d+\.\d{2}$/);
    });

    it('should handle large amounts correctly', () => {
      const params: ProrationParams = {
        startDate: '2025-06-01',
        endDate: '2025-12-31',
        billingDay: 15,
        tuitionBillingCycle: 'monthly',
        tuitionFee: 1000,
        today: '2025-06-20',
      };

      const result = calculateProratedAmount(params);
      expect(result).toBeGreaterThan(0);
      expect(result).toBeLessThanOrEqual(1000);
    });
  });

  describe('Date Format Handling', () => {
    it('should handle Date objects as input', () => {
      const params: ProrationParams = {
        startDate: new Date('2025-06-01'),
        endDate: new Date('2025-12-31'),
        billingDay: 15,
        tuitionBillingCycle: 'monthly',
        tuitionFee: 30,
        today: new Date('2025-06-20'),
      };

      const result = calculateProratedAmount(params);
      expect(result).toBeGreaterThan(0);
    });

    it('should handle mixed string and Date inputs', () => {
      const params: ProrationParams = {
        startDate: '2025-06-01',
        endDate: new Date('2025-12-31'),
        billingDay: 15,
        tuitionBillingCycle: 'one-time',
        tuitionFee: 100,
        today: new Date('2025-06-20'),
      };

      const result = calculateProratedAmount(params);
      expect(result).toBe(100);
    });
  });

  describe('Boundary Conditions', () => {
    it('should handle billing on last day of month', () => {
      const params: ProrationParams = {
        startDate: '2025-01-01',
        endDate: '2025-12-31',
        billingDay: 31, // Last day of month
        tuitionBillingCycle: 'monthly',
        tuitionFee: 30,
        today: '2025-01-15',
      };

      expect(() => calculateProratedAmount(params)).not.toThrow();
    });

    it('should handle February 29th in leap year', () => {
      const params: ProrationParams = {
        startDate: '2024-02-01', // 2024 is a leap year
        endDate: '2024-12-31',
        billingDay: 29,
        tuitionBillingCycle: 'monthly',
        tuitionFee: 30,
        today: '2024-02-15',
      };

      expect(() => calculateProratedAmount(params)).not.toThrow();
    });

    it('should handle minimum tuition fee', () => {
      const params: ProrationParams = {
        startDate: '2025-06-01',
        endDate: '2025-12-31',
        billingDay: 15,
        tuitionBillingCycle: 'weekly',
        tuitionFee: 0.01, // Minimum fee
        today: '2025-06-10',
      };

      const result = calculateProratedAmount(params);
      expect(result).toBeGreaterThan(0);
    });
  });
});
