steps:
  # 2. Install Dependencies using Node.js 22 image
  - name: 'gcr.io/cloud-builders/npm:latest' # Uses npm image, we specify Node version via .nvmrc or rely on latest if compatible
    entrypoint: 'npm'
    args: ['ci']
    id: Install

  # 2.1 Rebuild bcrypt from source
  - name: 'gcr.io/cloud-builders/npm:latest'
    entrypoint: 'npm'
    args: ['rebuild', 'bcrypt', '--build-from-source']
    waitFor: ['Install']
    id: RebuildBcrypt

  # 3. Build Application (creates dist/ folder)
  - name: 'node:18'
    entrypoint: 'npm'
    args: ['run', 'build']
    waitFor: ['RebuildBcrypt'] # Updated to wait for bcrypt rebuild
    id: Build

  # 4. Deploy to App Engine
  # Uses the gcloud builder image
  # Deploys using the file specified by the _APP_YAML_FILE substitution variable
  # The Cloud Build service account needs App Engine Deployer, SA User, Secret Accessor roles.
  - name: 'gcr.io/cloud-builders/gcloud'
    entrypoint: 'gcloud'
    args:
      - 'app'
      - 'deploy'
      - '${_APP_YAML_FILE}'
      - '--project=$PROJECT_ID'
      - '--quiet'
    waitFor: ['Build']
    id: Deploy

# Timeout reduced from 20 minutes to 10 minutes
timeout: '600s'

options:
  machineType: 'E2_HIGHCPU_8'  # 8 vCPU, 8 GB RAM
  logging: CLOUD_LOGGING_ONLY

#gcloud builds submit --config cloudbuild.yaml .