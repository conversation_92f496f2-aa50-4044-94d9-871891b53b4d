runtime: nodejs20

env: standard

instance_class: F1

handlers:
  - url: /.*
    script: auto
    secure: always
  # - url: .* # This handler is redundant as the one above covers all URLs
  #   script: auto

env_variables:
  NODE_ENV: 'production'
  GCS_ENV_BUCKET: 'enrollio_env'
  GCS_ENV_FILE: 'env.staging'
  REDIS_HOST: redis-16026.c82.us-east-1-2.ec2.redns.redis-cloud.com
  REDIS_PORT: 16026
  REDIS_PASSWORD: yuGNm6MXKEYEoxFmGHRkOzvtXcDkvFtp
  REDIS_USERNAME: default
  CLIENT_ID: 67e92e3443252fde19e6cbd2-mb97z7n5
  CLIENT_SECRET: 8f023df8-402d-4a4d-8208-1f539dc4d426
  
automatic_scaling:
  min_idle_instances: automatic
  max_idle_instances: automatic
  min_pending_latency: automatic
  max_pending_latency: automatic
  target_cpu_utilization: 0.65
  max_instances: 3

# Modified entrypoint for diagnostics
entrypoint: npm run start:prod

service: enrollio-be-staging

service_account: <EMAIL>