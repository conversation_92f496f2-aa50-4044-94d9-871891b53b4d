runtime: custom
env: flex

service: enrollio-be-staging

# Use the Docker image from the Dockerfile in the same directory
# No need to specify it as App Engine Flex will use the Dockerfile by default

liveness_check:
  path: "/health"
  check_interval_sec: 30
  timeout_sec: 4
  failure_threshold: 2
  success_threshold: 2
  initial_delay_sec: 60

readiness_check:
  path: "/health"
  check_interval_sec: 5
  timeout_sec: 4
  failure_threshold: 2
  success_threshold: 2
  app_start_timeout_sec: 300

automatic_scaling:
  min_num_instances: 1
  max_num_instances: 3
  cool_down_period_sec: 180
  cpu_utilization:
    target_utilization: 0.65

resources:
  cpu: 1
  memory_gb: 1
  disk_size_gb: 10

env_variables:
  NODE_ENV: 'production'
  GCS_ENV_BUCKET: 'enrollio_env'
  GCS_ENV_FILE: 'env.staging'
  REDIS_HOST: 'redis-16026.c82.us-east-1-2.ec2.redns.redis-cloud.com'
  REDIS_PORT: '16026'
  REDIS_USERNAME: 'default'
  # REDIS_PASSWORD is set using Secret Manager

beta_settings:
  # Enable Cloud SQL connections if needed
  # cloud_sql_instances: your-project:region:instance-name

# Set up App Engine service account
service_account: <EMAIL>

# For deploying with App Engine Flex Docker:
# gcloud app deploy app.docker.yaml 