# Dependencies
node_modules/
npm-debug.log
yarn-error.log
yarn-debug.log
pnpm-debug.log

# Build outputs
dist/
build/

# Environment files
.env
.env.*
!.env.example

# Version control
.git/
.gitignore

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Test files
coverage/
.nyc_output/

# Documentation
README.md
docs/

# CI/CD
.github/
.gitlab-ci.yml
cloudbuild.yaml
app.*.yaml

# Docker files (avoid recursive copy)
Dockerfile
docker-compose*.yml
.dockerignore

# Logs
logs/
*.log

# Temporary files
tmp/
temp/ 