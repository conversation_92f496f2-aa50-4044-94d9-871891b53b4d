# Docker Deployment Guide for Enrollio Backend

This guide explains how to build and deploy the Enrollio backend using Docker.

## Prerequisites

- Docker installed locally
- Google Cloud SDK (`gcloud`) configured
- Access to Google Cloud Project
- Google Cloud Build API enabled
- Container Registry or Artifact Registry enabled

## Files Created

1. **Dockerfile** - Multi-stage Docker build for the NestJS application
2. **.dockerignore** - Excludes unnecessary files from Docker context
3. **cloudbuild.docker.yaml** - Cloud Build configuration for Docker deployment
4. **docker-compose.yml** - Local development environment
5. **docker-compose.prod.yml** - Production-like testing environment

## Local Development

### Running with Docker Compose

```bash
# Start development environment with hot reload
docker-compose up

# Or run in background
docker-compose up -d

# View logs
docker-compose logs -f app

# Stop services
docker-compose down
```

### Building Production Image Locally

```bash
# Build the production image
docker build -t enrollio-be:latest .

# Run the production image
docker run -p 3000:3000 \
  -e NODE_ENV=production \
  -e REDIS_HOST=your-redis-host \
  -e REDIS_PORT=6379 \
  enrollio-be:latest
```

### Testing Production Build Locally

```bash
# Set Redis password in environment
export REDIS_PASSWORD=yuGNm6MXKEYEoxFmGHRkOzvtXcDkvFtp

# Run production-like environment
docker-compose -f docker-compose.prod.yml up
```

## Deployment Options

### Option 1: Google Cloud Run (Recommended)

Cloud Run is a serverless platform that automatically scales your containerized applications.

#### First-time setup:

```bash
# Create Redis password secret
echo -n "yuGNm6MXKEYEoxFmGHRkOzvtXcDkvFtp" | gcloud secrets create redis-password --data-file=-

# Create Redis username secret
echo -n "default" | gcloud secrets create redis-username --data-file=-

# Grant Secret Accessor permission to Cloud Run service account
gcloud secrets add-iam-policy-binding redis-password \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/secretmanager.secretAccessor"

gcloud secrets add-iam-policy-binding redis-username \
  --member="serviceAccount:<EMAIL>" \
  --role="roles/secretmanager.secretAccessor"
```

#### Deploy using Cloud Build:

```bash
# Deploy to staging
gcloud builds submit \
  --config cloudbuild.docker.yaml \
  --substitutions=_ENV=staging

# Deploy to production
gcloud builds submit \
  --config cloudbuild.docker.yaml \
  --substitutions=_ENV=production,_REDIS_HOST=your-prod-redis-host,_REDIS_PORT=your-prod-redis-port
```

### Option 2: Google Kubernetes Engine (GKE)

For more control and complex deployments, you can use GKE.

1. Uncomment the GKE deployment section in `cloudbuild.docker.yaml`
2. Create Kubernetes deployment and service manifests
3. Update the substitution variables for your GKE cluster

### Option 3: App Engine with Custom Runtime

You can still use App Engine with Docker:

1. Create `app.yaml` with `runtime: custom`
2. App Engine will use your Dockerfile automatically

Example `app.yaml`:

```yaml
runtime: custom
env: flex

service: enrollio-be-staging

automatic_scaling:
  min_num_instances: 1
  max_num_instances: 3
  cpu_utilization:
    target_utilization: 0.65

env_variables:
  NODE_ENV: 'production'
  GCS_ENV_BUCKET: 'enrollio_env'
  GCS_ENV_FILE: 'env.staging'
  REDIS_HOST: 'redis-16026.c82.us-east-1-2.ec2.redns.redis-cloud.com'
  REDIS_PORT: '16026'
```

## Environment Variables

The application uses environment variables that can be:

1. Set directly in the deployment configuration
2. Loaded from Google Cloud Storage (GCS_ENV_BUCKET and GCS_ENV_FILE)
3. Stored in Google Secret Manager (recommended for sensitive data)

## Monitoring and Logs

### Cloud Run

```bash
# View logs
gcloud run services logs read enrollio-be-staging --region=us-central1

# Stream logs
gcloud run services logs tail enrollio-be-staging --region=us-central1
```

### Docker Compose

```bash
# View logs
docker-compose logs -f app
```

## Troubleshooting

1. **Port Issues**: Ensure your application listens on the port specified in the Dockerfile (3000)
2. **Memory Issues**: Adjust memory limits in Cloud Run deployment if needed
3. **Environment Variables**: Verify all required environment variables are set
4. **Redis Connection**: Check Redis connectivity and credentials

## CI/CD Pipeline

The `cloudbuild.docker.yaml` file:

1. Builds the Docker image with multiple tags
2. Pushes to Google Container Registry
3. Deploys to Cloud Run (or GKE if configured)

Tags created:

- `gcr.io/PROJECT_ID/enrollio-be:latest`
- `gcr.io/PROJECT_ID/enrollio-be:SHORT_SHA`
- `gcr.io/PROJECT_ID/enrollio-be:ENV` (staging/production)

## Security Considerations

1. The Dockerfile runs as a non-root user (`nestjs`)
2. Sensitive data should use Google Secret Manager
3. Use least-privilege service accounts
4. Enable vulnerability scanning in Container Registry
5. Regularly update base images

## Performance Optimization

1. Multi-stage build reduces final image size
2. Production dependencies only in final stage
3. Layer caching optimizes build times
4. `dumb-init` ensures proper signal handling

## Next Steps

1. Choose your deployment platform (Cloud Run recommended)
2. Set up secrets in Google Secret Manager
3. Configure Cloud Build triggers for automatic deployments
4. Set up monitoring and alerting
5. Configure custom domain if needed
