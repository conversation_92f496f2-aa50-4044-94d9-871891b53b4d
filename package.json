{"name": "enrollio-be", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main.js", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "vitest run tests", "test:watch": "vitest", "test:cov": "vitest run --coverage", "test:debug": "vitest --inspect-brk", "test:e2e": "vitest run --config ./vitest.e2e.config.ts", "prepare": "if [ \"$CI\" != \"true\" ]; then husky install; fi", "lint-staged": "lint-staged"}, "lint-staged": {"*.{js,ts,tsx}": ["prettier --write"], "*.{json,md}": ["prettier --write"]}, "dependencies": {"@google-cloud/storage": "^7.16.0", "@nestjs/axios": "^3.0.3", "@nestjs/bullmq": "^10.2.3", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "*", "@nestjs/mongoose": "^10.0.10", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.1.2", "@sendgrid/mail": "^8.1.4", "@sendinblue/client": "^3.3.1", "@types/multer": "^1.4.12", "@types/pdfkit": "^0.13.9", "@types/sharp": "^0.31.1", "axios": "^1.9.0", "bcrypt": "^6.0.0", "bullmq": "^5.41.3", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "csv-parse": "^5.6.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "express": "^5.1.0", "fast-csv": "^5.0.2", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "mailgun-js": "^0.22.0", "mailgun.js": "^10.2.3", "mongodb": "^6.9.0", "mongoose": "^8.14.1", "multer": "^1.4.5-lts.1", "pdfkit": "^0.16.0", "redis": "^4.7.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "sharp": "^0.34.2", "stripe": "^17.4.0", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^9.4.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.4.17", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "@vitest/coverage-istanbul": "^3.1.2", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.0", "jest": "^29.5.0", "jsdom": "^26.1.0", "lint-staged": "^15.4.3", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}