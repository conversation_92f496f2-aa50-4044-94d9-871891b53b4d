steps:
  # 1. Build Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'build'
      - '-t'
      - 'gcr.io/$PROJECT_ID/enrollio-be:$SHORT_SHA'
      - '-t'
      - 'gcr.io/$PROJECT_ID/enrollio-be:latest'
      - '-t'
      - 'gcr.io/$PROJECT_ID/enrollio-be:${_ENV}'
      - '.'
    id: 'docker-build'

  # 2. Push Docker image to Google Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args:
      - 'push'
      - '--all-tags'
      - 'gcr.io/$PROJECT_ID/enrollio-be'
    waitFor: ['docker-build']
    id: 'docker-push'

  # 3. Deploy to Cloud Run (Option 1)
  # Uncomment this section if deploying to Cloud Run
  - name: 'gcr.io/cloud-builders/gcloud'
    args:
      - 'run'
      - 'deploy'
      - 'enrollio-be-${_ENV}'
      - '--image=gcr.io/$PROJECT_ID/enrollio-be:$SHORT_SHA'
      - '--region=${_REGION}'
      - '--platform=managed'
      - '--memory=512Mi'
      - '--cpu=1'
      - '--min-instances=1'
      - '--max-instances=3'
      - '--port=3000'
      - '--allow-unauthenticated'
      - '--set-env-vars=NODE_ENV=production,GCS_ENV_BUCKET=enrollio_env,GCS_ENV_FILE=env.${_ENV}'
      - '--set-env-vars=REDIS_HOST=${_REDIS_HOST},REDIS_PORT=${_REDIS_PORT}'
      - '--set-secrets=REDIS_PASSWORD=redis-password:latest,REDIS_USERNAME=redis-username:latest'
      - '--service-account=enrollio-${_ENV}@${PROJECT_ID}.iam.gserviceaccount.com'
    waitFor: ['docker-push']
    id: 'deploy-cloud-run'

  # 4. Deploy to GKE (Option 2)
  # Uncomment this section if deploying to GKE
  # - name: 'gcr.io/cloud-builders/kubectl'
  #   args:
  #     - 'set'
  #     - 'image'
  #     - 'deployment/enrollio-be'
  #     - 'enrollio-be=gcr.io/$PROJECT_ID/enrollio-be:$SHORT_SHA'
  #     - '--namespace=${_NAMESPACE}'
  #   env:
  #     - 'CLOUDSDK_COMPUTE_ZONE=${_GKE_ZONE}'
  #     - 'CLOUDSDK_CONTAINER_CLUSTER=${_GKE_CLUSTER}'
  #   waitFor: ['docker-push']
  #   id: 'deploy-gke'

# Substitution variables (customize these)
substitutions:
  _ENV: 'staging' # Can be 'staging' or 'production'
  _REGION: 'us-central1' # Cloud Run region
  _REDIS_HOST: 'redis-16026.c82.us-east-1-2.ec2.redns.redis-cloud.com'
  _REDIS_PORT: '16026'
  # For GKE deployment
  # _GKE_ZONE: 'us-central1-a'
  # _GKE_CLUSTER: 'enrollio-cluster'
  # _NAMESPACE: 'default'

# Build timeout
timeout: '1200s'

options:
  logging: CLOUD_LOGGING_ONLY
  # Use machine type with more CPU for faster builds
  machineType: 'N1_HIGHCPU_8'

# To run this build:
# gcloud builds submit --config cloudbuild.docker.yaml --substitutions=_ENV=staging . 