import { Test, TestingModule } from '@nestjs/testing';
import { EnrollmentHistoryController } from './enrollment-history.controller';
import { EnrollmentHistoryService } from './enrollment-history.service';

describe('EnrollmentHistoryController', () => {
  let controller: EnrollmentHistoryController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [EnrollmentHistoryController],
      providers: [EnrollmentHistoryService],
    }).compile();

    controller = module.get<EnrollmentHistoryController>(
      EnrollmentHistoryController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
