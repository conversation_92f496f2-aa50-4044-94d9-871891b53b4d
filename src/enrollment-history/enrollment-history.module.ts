import { Module } from '@nestjs/common';
import { EnrollmentHistoryService } from './enrollment-history.service';
import { EnrollmentHistoryController } from './enrollment-history.controller';
import { MongooseModule } from '@nestjs/mongoose';
import {
  EnrollmentHistorySchema,
  EnrollmentHistory,
} from 'src/database/schema/enrollmentHistory';
import { JwtModule } from '@nestjs/jwt';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: EnrollmentHistory.name, schema: EnrollmentHistorySchema },
    ]),
    JwtModule,
  ],
  controllers: [EnrollmentHistoryController],
  providers: [EnrollmentHistoryService],
  exports: [EnrollmentHistoryService],
})
export class EnrollmentHistoryModule {}
