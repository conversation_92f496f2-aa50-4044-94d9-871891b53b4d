import {
  IsOptional,
  IsString,
  IsArray,
  ValidateNested,
  IsMongoId,
} from 'class-validator';
import { Type } from 'class-transformer';

class EnrollmentRecordDto {
  @IsOptional()
  @IsMongoId()
  enrollmentId?: string;

  @IsOptional()
  @IsString()
  status?: string;
}

export class CreateEnrollmentHistoryDto {
  @IsOptional()
  @IsMongoId()
  studentId?: string;

  @IsOptional()
  @IsMongoId()
  parentId?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EnrollmentRecordDto)
  enrollments?: EnrollmentRecordDto[];
}
