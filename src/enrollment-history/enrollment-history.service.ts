import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CreateEnrollmentHistoryDto } from './dto/create-enrollment-history.dto';
import { UpdateEnrollmentHistoryDto } from './dto/update-enrollment-history.dto';
import {
  EnrollmentHistorySchema,
  EnrollmentHistory,
  EnrollmentHistoryDocument,
} from 'src/database/schema/enrollmentHistory';

@Injectable()
export class EnrollmentHistoryService {
  constructor(
    @InjectModel(EnrollmentHistory.name)
    private enrollmentHistoryModel: Model<EnrollmentHistory>,
  ) {}

  async create(
    createEnrollmentHistoryDto: CreateEnrollmentHistoryDto,
    studioIdString: string,
  ) {
    try {
      if (!studioIdString) {
        throw new Error('Studio ID is required');
      }

      const studioId = Types.ObjectId.createFromHexString(studioIdString);
      let studentId;

      if (createEnrollmentHistoryDto.studentId) {
        try {
          studentId = Types.ObjectId.createFromHexString(
            createEnrollmentHistoryDto.studentId,
          );
        } catch (error) {
          throw new Error('Invalid student ID format');
        }
      }

      // Check if record already exists for this student and studio
      const existingHistory = await this.enrollmentHistoryModel.findOne({
        studentId,
        studioId,
      });

      if (existingHistory) {
        // If record exists, push new enrollments to existing record
        try {
          const updatedHistory =
            await this.enrollmentHistoryModel.findOneAndUpdate(
              { studentId, studioId },
              {
                $push: {
                  enrollments: {
                    $each:
                      createEnrollmentHistoryDto.enrollments?.map(
                        (enrollment) => {
                          try {
                            return {
                              ...enrollment,
                              ...(enrollment.enrollmentId && {
                                enrollmentId:
                                  Types.ObjectId.createFromHexString(
                                    enrollment.enrollmentId,
                                  ),
                              }),
                            };
                          } catch (error) {
                            throw new Error(
                              `Invalid enrollment ID format in enrollment data`,
                            );
                          }
                        },
                      ) || [],
                  },
                },
              },
              { new: true },
            );
          if (!updatedHistory) {
            throw new Error('Failed to update enrollment history');
          }
          return updatedHistory;
        } catch (error) {
          throw new Error(
            `Error updating enrollment history: ${error.message}`,
          );
        }
      } else {
        // If no record exists, create new one
        try {
          const convertedData = {
            ...(studentId && { studentId }),
            studioId,
            ...(createEnrollmentHistoryDto.parentId && {
              parentId: Types.ObjectId.createFromHexString(
                createEnrollmentHistoryDto.parentId,
              ),
            }),
            enrollments: createEnrollmentHistoryDto.enrollments?.map(
              (enrollment) => {
                try {
                  return {
                    ...enrollment,
                    ...(enrollment.enrollmentId && {
                      enrollmentId: Types.ObjectId.createFromHexString(
                        enrollment.enrollmentId,
                      ),
                    }),
                  };
                } catch (error) {
                  throw new Error(
                    `Invalid enrollment ID format in enrollment data`,
                  );
                }
              },
            ),
          };

          const newEnrollmentHistory = new this.enrollmentHistoryModel(
            convertedData,
          );
          const savedHistory = await newEnrollmentHistory.save();
          if (!savedHistory) {
            throw new Error('Failed to create enrollment history');
          }
          return savedHistory;
        } catch (error) {
          throw new Error(
            `Error creating enrollment history: ${error.message}`,
          );
        }
      }
    } catch (error) {
      // Re-throw the error with a more specific message if it's not already a custom error
      if (error.message.includes('hex string')) {
        throw new Error('Invalid ID format provided');
      }
      throw error;
    }
  }

  async findAll(
    locationId: string,
    studentId: string,
    page: number = 1,
    limit: number = 10,
  ) {
    const skip = (page - 1) * limit;

    const [items, total] = await Promise.all([
      this.enrollmentHistoryModel
        .find({
          studioId: Types.ObjectId.createFromHexString(locationId),
          studentId: Types.ObjectId.createFromHexString(studentId),
        })
        .populate({
          path: 'enrollments.enrollmentId',
          select: 'title tuitionFee startDate endDate',
          populate: {
            path: 'session',
            select: 'name',
            model: 'Session',
          },
        })
        .skip(skip)
        .limit(limit)
        .exec(),

      this.enrollmentHistoryModel.countDocuments({
        studioId: Types.ObjectId.createFromHexString(locationId),
        studentId: Types.ObjectId.createFromHexString(studentId),
      }),
    ]);

    return {
      items,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string, locationId: string) {
    return await this.enrollmentHistoryModel
      .findOne({
        _id: Types.ObjectId.createFromHexString(id),
        studioId: Types.ObjectId.createFromHexString(locationId),
      })
      .exec();
  }

  async update(
    id: string,
    updateEnrollmentHistoryDto: UpdateEnrollmentHistoryDto,
    locationId: string,
  ) {
    const updateQuery: any = {
      $push: {
        enrollments: {
          $each:
            updateEnrollmentHistoryDto.enrollments?.map((enrollment) => ({
              ...enrollment,
              ...(enrollment.enrollmentId && {
                enrollmentId: Types.ObjectId.createFromHexString(
                  enrollment.enrollmentId,
                ),
              }),
            })) || [],
        },
      },
    };

    // Add other fields if they exist
    if (updateEnrollmentHistoryDto.studentId) {
      updateQuery.studentId = Types.ObjectId.createFromHexString(
        updateEnrollmentHistoryDto.studentId,
      );
    }
    if (updateEnrollmentHistoryDto.parentId) {
      updateQuery.parentId = Types.ObjectId.createFromHexString(
        updateEnrollmentHistoryDto.parentId,
      );
    }

    return await this.enrollmentHistoryModel
      .findOneAndUpdate(
        {
          _id: Types.ObjectId.createFromHexString(id),
          studioId: Types.ObjectId.createFromHexString(locationId),
        },
        updateQuery,
        { new: true },
      )
      .exec();
  }

  async remove(id: string, locationId: string) {
    return await this.enrollmentHistoryModel
      .findOneAndDelete({
        _id: Types.ObjectId.createFromHexString(id),
        studioId: Types.ObjectId.createFromHexString(locationId),
      })
      .exec();
  }
}
