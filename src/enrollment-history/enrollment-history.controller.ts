import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { Request } from 'express';
import { EnrollmentHistoryService } from './enrollment-history.service';
import { CreateEnrollmentHistoryDto } from './dto/create-enrollment-history.dto';
import { UpdateEnrollmentHistoryDto } from './dto/update-enrollment-history.dto';

@Controller('enrollment-history')
@UseGuards(JwtAuthGuard)
export class EnrollmentHistoryController {
  constructor(
    private readonly enrollmentHistoryService: EnrollmentHistoryService,
  ) {}

  @Post()
  create(
    @Body() createEnrollmentHistoryDto: CreateEnrollmentHistoryDto,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return this.enrollmentHistoryService.create(
      createEnrollmentHistoryDto,
      locationId,
    );
  }

  @Get('student/:studentId')
  findAll(
    @Req() request: Request,
    @Param('studentId') studentId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    const locationId = request['locationId'];
    return this.enrollmentHistoryService.findAll(
      locationId,
      studentId,
      page,
      limit,
    );
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Req() request: Request) {
    const locationId = request['locationId'];
    return this.enrollmentHistoryService.findOne(id, locationId);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateEnrollmentHistoryDto: UpdateEnrollmentHistoryDto,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return this.enrollmentHistoryService.update(
      id,
      updateEnrollmentHistoryDto,
      locationId,
    );
  }

  @Delete(':id')
  remove(@Param('id') id: string, @Req() request: Request) {
    const locationId = request['locationId'];
    return this.enrollmentHistoryService.remove(id, locationId);
  }
}
