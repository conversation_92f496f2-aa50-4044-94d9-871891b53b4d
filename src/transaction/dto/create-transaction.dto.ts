import { IsString, IsOptional, IsObject, IsNumber } from 'class-validator';

export class CreateTransactionDto {
  @IsString()
  @IsOptional()
  entityType: string;

  @IsString()
  @IsOptional()
  entityId: string;

  @IsObject()
  @IsOptional()
  details: Record<string, any>;

  @IsString()
  @IsOptional()
  transactionType: string;

  @IsNumber()
  @IsOptional()
  amount: number;

  @IsString()
  @IsOptional()
  status: string;

  @IsString()
  @IsOptional()
  stripeSessionId: string;

  @IsString()
  @IsOptional()
  paymentIntentId: string;

  @IsObject()
  @IsOptional()
  discountOptions: Record<string, any>;
}
