import { Body, Controller, Post, Get, Param, Query } from '@nestjs/common';
import { CreateTransactionDto } from './dto/create-transaction.dto';
import { TransactionService } from './transaction.service';

@Controller('transaction')
export class TransactionController {
  constructor(private transactionService: TransactionService) {}
  @Post()
  async createTransaction(@Body() createTransactionDto: CreateTransactionDto) {
    return await this.transactionService.createTransaction(
      createTransactionDto,
    );
  }

  @Get('invoices/parent')
  async getInvoicesByParentId(@Query('parentId') parentId: string) {
    console.log(parentId);
    return await this.transactionService.getInvoicesByParentId(parentId);
  }

  @Get('invoices/student')
  async getInvoicesByStudentId(@Query('studentId') studentId: string) {
    return await this.transactionService.getInvoicesByStudentId(studentId);
  }
}
