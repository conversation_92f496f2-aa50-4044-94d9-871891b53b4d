import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Student, StudentDocument } from 'src/database/schema/student';
import { Invoice, InvoiceDocument } from 'src/database/schema/invoice';
import {
  Transaction,
  TransactionDocument,
} from 'src/database/schema/transaction';
import { CreateTransactionDto } from './dto/create-transaction.dto';
import { CreateInvoiceDto } from './dto/create-invoice.dto';
import { InvoiceStatus } from 'src/stripe/type';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import { SubscriptionInvoiceDocument } from 'src/database/schema/subscriptionInvoice';

@Injectable()
export class TransactionService {
  constructor(
    @InjectModel(Transaction.name)
    private transactionModel: Model<TransactionDocument>,
    @InjectModel(Invoice.name) private invoiceModel: Model<InvoiceDocument>,
    @InjectModel(Student.name) private studentModel: Model<StudentDocument>,
    @InjectModel(SubscriptionInvoice.name)
    private subscriptionInvoiceModel: Model<SubscriptionInvoiceDocument>,
  ) {}
  async createTransaction(createTransactionDto: CreateTransactionDto) {
    const transaction = new this.transactionModel(createTransactionDto);
    await transaction.save();

    return transaction;
  }

  async findbyId(id: string) {
    return await this.transactionModel.findById(id);
  }

  async findOneByStripeSessionId(stripeSessionId: string) {
    return await this.transactionModel.findOne({ stripeSessionId });
  }

  async findOneBypaymentIntentId(paymentIntentId: string) {
    return await this.transactionModel.findOne({
      paymentIntentId,
    });
  }

  async retryFindTransactionByPaymentIntentId(
    paymentIntentId: string,
    maxRetries = 5,
    initialDelay = 500,
  ): Promise<any> {
    let transaction = null;
    let retryCount = 0;

    // First attempt
    transaction = await this.findOneBypaymentIntentId(paymentIntentId);

    // If found on first try, return immediately
    if (transaction) return transaction;

    // Retry with exponential backoff
    while (!transaction && retryCount < maxRetries) {
      // Calculate delay with exponential backoff
      const delay = initialDelay * Math.pow(2, retryCount);

      // Log retry attempt
      console.log(
        `Retry ${retryCount + 1}/${maxRetries} finding transaction for payment intent ${paymentIntentId}. Waiting ${delay}ms...`,
      );

      // Wait before next attempt
      await new Promise((resolve) => setTimeout(resolve, delay));

      // Try to find the transaction again
      transaction = await this.findOneBypaymentIntentId(paymentIntentId);

      retryCount++;
    }

    if (!transaction) {
      console.log(
        `Failed to find transaction for payment intent ${paymentIntentId} after ${maxRetries} retries.`,
      );
    } else {
      console.log(
        `Found transaction for payment intent ${paymentIntentId} on retry ${retryCount}.`,
      );
    }

    return transaction;
  }

  async createInvoice(createInvoiceDto: CreateInvoiceDto) {
    const invoice = new this.invoiceModel(createInvoiceDto);
    await invoice.save();

    return invoice;
  }

  async getInvoicesByParentId(parentId: string) {
    const students = await this.studentModel.find({
      parentId: Types.ObjectId.createFromHexString(parentId),
    });
    const studentIds = students.map((student) => student._id.toString());
    const invoices = await this.invoiceModel
      .find({
        entityId: { $in: studentIds },
      })
      .sort({ createdAt: -1 });

    // add student name and class to the invoice
    for (const invoice of invoices) {
      const student = students.find(
        (student) => student._id.toString() === invoice.entityId,
      );
      invoice.name = student.name;
    }

    const totalOutstanding = invoices.reduce((acc, invoice) => {
      return invoice.status !== 'paid' &&
        invoice.status !== 'cancelled' &&
        invoice.status !== 'applied'
        ? acc + invoice.amount
        : acc;
    }, 0);

    // Get the latest payment details
    const lastPayment =
      invoices.find(
        (inv) =>
          inv.status !== 'scheduled' &&
          inv.status !== 'applied' &&
          inv.status !== 'cancelled',
      )?.createdAt ?? '';
    const lastPaymentAmount =
      invoices.find(
        (inv) =>
          inv.status !== 'scheduled' &&
          inv.status !== 'applied' &&
          inv.status !== 'cancelled',
      )?.amount ?? 0;

    return {
      data: {
        totalOutstanding,
        lastPayment,
        lastPaymentAmount,
        invoices: invoices,
      },
      pagination: {
        page: 1,
        limit: 10,
        totalCount: invoices.length,
        totalPages: 1,
      },
    };
  }

  async getInvoicesByStudentId(studentId: string) {
    const outstandingBalances = await this.subscriptionInvoiceModel
      .find({
        studentId: Types.ObjectId.createFromHexString(studentId),
        status: {
          $in: [
            InvoiceStatus.PENDING,
            InvoiceStatus.FAILED,
            InvoiceStatus.SCHEDULED,
          ],
        },
      })
      .lean()
      .exec();

    const totalOutstanding = outstandingBalances.reduce((acc, invoice) => {
      return acc + invoice.finalAmount;
    }, 0);

    //max 2 decimal places
    const totalOutstandingFormatted = Number(totalOutstanding.toFixed(2));

    return {
      data: {
        totalOutstanding: totalOutstandingFormatted,
      },
    };
  }

  async getInvoicesByTransactionId(transactionId: string) {
    return this.invoiceModel.find({ transactionId });
  }

  async updateByProperty(property: string, value: any, updateData: any) {
    const result = await this.transactionModel.findOneAndUpdate(
      { [property]: value },
      updateData,
      { new: true },
    );

    return result;
  }
}
