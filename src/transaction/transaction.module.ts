import { Modu<PERSON> } from '@nestjs/common';
import { TransactionService } from './transaction.service';
import { TransactionController } from './transaction.controller';
import { MongooseModule } from '@nestjs/mongoose';
import {
  Transaction,
  TransactionSchema,
} from 'src/database/schema/transaction';
import { Invoice, InvoiceSchema } from 'src/database/schema/invoice';
import { Student, StudentSchema } from 'src/database/schema/student';
import {
  SubscriptionInvoice,
  SubscriptionInvoiceSchema,
} from 'src/database/schema/subscriptionInvoice';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Transaction.name, schema: TransactionSchema },
    ]),
    MongooseModule.forFeature([{ name: Invoice.name, schema: InvoiceSchema }]),
    MongooseModule.forFeature([{ name: Student.name, schema: StudentSchema }]),
    MongooseModule.forFeature([
      { name: SubscriptionInvoice.name, schema: SubscriptionInvoiceSchema },
    ]),
  ],
  providers: [TransactionService],
  controllers: [TransactionController],
  exports: [TransactionService],
})
export class TransactionModule {}
