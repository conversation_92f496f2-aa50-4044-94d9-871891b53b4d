import { Injectable } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import { HttpService } from '@nestjs/axios';

@Injectable()
export class GhlApiService {
  private httpService: HttpService;
  async getSubaccount(subaccountId: string) {
    try {
      const response = await firstValueFrom(
        this.httpService.get(
          `https://services.leadconnectorhq.com/locations/${subaccountId}`,
          {
            headers: {
              Authorization: `Bearer ${process.env.GHL_API_KEY}`,
            },
          },
        ),
      );
      console.log('Response from api call: ', response);
      return response.data;
    } catch (error) {
      throw new Error(`Failed to exchange code for token: ${error.message}`);
    }
  }
}
