// create-event.dto.ts
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsNumber,
  IsArray,
  IsDate,
  IsMongoId,
} from 'class-validator';
import { Types } from 'mongoose';

export class CreateEventDto {
  @IsString()
  title: string;

  @IsMongoId()
  studio: Types.ObjectId;

  @IsArray()
  @IsMongoId({ each: true })
  students: Types.ObjectId[];

  @IsArray()
  @IsDate({ each: true })
  schedules: Date[];

  @IsOptional()
  @IsString()
  location?: string;

  @IsOptional()
  @IsString()
  room?: string;

  @IsArray()
  @IsOptional()
  @IsMongoId({ each: true })
  instructor?: string[];

  @IsArray()
  @IsString({ each: true })
  days: string[];

  @IsOptional()
  @IsString()
  startTime?: string;

  @IsOptional()
  @IsString()
  endTime?: string;

  @IsOptional()
  @IsNumber()
  duration?: number;

  @IsOptional()
  @IsDate()
  registrationStartDate?: Date;

  @IsOptional()
  @IsDate()
  registrationEndDate?: Date;

  @IsOptional()
  @IsDate()
  startDate?: Date;

  @IsOptional()
  @IsDate()
  endDate?: Date;

  @IsNumber()
  tuitionFee: number;

  @IsOptional()
  @IsNumber()
  maxSize?: number;

  @IsOptional()
  @IsNumber()
  maxWait?: number;

  @IsOptional()
  @IsBoolean()
  oneTime?: boolean;

  @IsOptional()
  @IsBoolean()
  allowOnlineRegistration?: boolean;

  @IsOptional()
  @IsBoolean()
  allowPortalEnrollment?: boolean;

  @IsOptional()
  @IsBoolean()
  allowTrialEnrollment?: boolean;

  @IsOptional()
  @IsBoolean()
  displayOnWebsite?: boolean;

  @IsOptional()
  @IsString()
  tuitionBillingMethod?: string;

  @IsOptional()
  @IsString()
  tuitionBillingCycle?: string;

  @IsOptional()
  @IsNumber()
  billingDay?: number;

  @IsOptional()
  @IsString()
  tuitionDiscountRule?: string;

  @IsOptional()
  @IsBoolean()
  prorateTuition?: boolean;

  @IsOptional()
  @IsBoolean()
  registrationFee?: boolean;

  @IsOptional()
  @IsNumber()
  costumeFee?: number;

  @IsOptional()
  @IsNumber()
  registrationFeeAmount?: number;

  @IsArray()
  @IsOptional()
  @IsMongoId({ each: true })
  tags?: string[];

  @IsOptional()
  @IsMongoId()
  session?: string;

  @IsArray()
  @IsOptional()
  @IsMongoId({ each: true })
  group?: string[];

  @IsArray()
  @IsMongoId({ each: true })
  policyGroup: string[];

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  mainTeacher?: string;

  @IsOptional()
  @IsString()
  subTeacher?: string;

  @IsOptional()
  @IsNumber()
  startYear?: number;

  @IsOptional()
  @IsNumber()
  endYear?: number;

  @IsOptional()
  @IsNumber()
  startMonth?: number;

  @IsOptional()
  @IsNumber()
  endMonth?: number;

  @IsOptional()
  @IsString()
  calendarId_ghl?: string;

  @IsOptional()
  @IsString()
  productId_ghl?: string;

  @IsOptional()
  @IsArray()
  availability?: [
    {
      day: string;
      startTime: string;
      endTime: string;
    },
  ];

  @IsOptional()
  @IsString()
  timeConfig?: 'same' | 'different';

  @IsOptional()
  @IsBoolean()
  hide?: boolean;

  @IsOptional()
  @IsString()
  color?: string;

  @IsOptional()
  @IsString()
  imageUrl?: string;

  @IsOptional()
  @IsString()
  imageName?: string;

  @IsOptional()
  image?: Buffer;

  @IsOptional()
  @IsString()
  defaultImageUrl?: string;
}
