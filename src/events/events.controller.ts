import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
  UseInterceptors,
  UploadedFile,
  NotFoundException,
  Inject,
} from '@nestjs/common';
import { EventsService } from './events.service';
import { CreateEventDto } from './dto/create-event.dto';
import { UpdateEventDto } from './dto/update-event.dto';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { FileInterceptor } from '@nestjs/platform-express';
import { Logger } from '@nestjs/common';
import type { Redis } from 'ioredis';

@Controller('events')
export class EventsController {
  private readonly logger = new Logger(EventsController.name);

  constructor(
    private readonly eventsService: EventsService,
    @Inject('REDIS_CLIENT') private readonly redis: Redis,
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(FileInterceptor('file'))
  async create(
    @UploadedFile() file: Express.Multer.File,
    @Body('payload') payload: string,
    @Body('fileUrl') fileUrl: string,
    @Body('fileType') fileType: 'file' | 'url',
    @Req() request: Request,
  ) {
    try {
      const locationId = request['locationId'];
      const createEventDto: CreateEventDto = JSON.parse(payload);

      if (file && fileType === 'file') {
        createEventDto.image = file.buffer;
        createEventDto.imageName = file.originalname;
      } else if (fileType === 'url') {
        createEventDto.imageUrl = fileUrl;
      }

      const result = await this.eventsService.create(
        createEventDto,
        locationId,
      );

      // Invalidate search cache after creating event
      await this.invalidateSearchCache(locationId);

      return result;
    } catch (error) {
      this.logger.error('Failed to create event', {
        error: error.message,
        stack: error.stack,
        fileType,
        hasFile: !!file,
      });
      throw new NotFoundException('Event could not be created');
    }
  }

  @Get()
  findAll() {
    return this.eventsService.findAll();
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  findOne(@Param('id') id: string, @Req() request: Request) {
    const locationId = request['locationId'];
    return this.eventsService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(FileInterceptor('file'))
  async update(
    @Param('id') id: string,
    @UploadedFile() file: Express.Multer.File,
    @Body('payload') payload: string,
    @Body('fileUrl') fileUrl: string,
    @Body('fileType') fileType: 'file' | 'url',
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    const updateEventDto: UpdateEventDto = JSON.parse(payload);
    if (file && fileType === 'file') {
      updateEventDto.image = file.buffer;
      updateEventDto.imageName = file.originalname;
    } else if (fileType === 'url') {
      updateEventDto.imageUrl = fileUrl;
    }

    const result = await this.eventsService.update(
      id,
      updateEventDto,
      locationId,
    );

    // Invalidate search cache after updating event
    await this.invalidateSearchCache(locationId);

    return result;
  }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.eventsService.remove(id);
  // }

  @Get('/studio/all')
  @UseGuards(JwtAuthGuard)
  async findByStudio(@Req() request: Request) {
    const locationId = request['locationId'];
    return this.eventsService.findByStudio(locationId);
  }

  @Get('/studio/all/csv')
  @UseGuards(JwtAuthGuard)
  async getAllEventsCsv(@Req() request: Request) {
    const locationId = request['locationId'];
    return await this.eventsService.findAllByStudioIdCsv(locationId);
  }

  @Get('student/:studentId')
  @UseGuards(JwtAuthGuard)
  async findByStudent(
    @Param('studentId') studentId: string,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return this.eventsService.findByStudent(studentId);
  }

  @Get('search/event')
  @UseGuards(JwtAuthGuard)
  async searchEvents(
    @Req() request: Request,
    @Query('name') name?: string,
    @Query('room') room?: string,
    @Query('age') age?: string,
    @Query('days') days?: string,
    @Query('instructor') instructor?: string,
    @Query('session') session?: string,
    @Query('sessionId') sessionId?: string,
    @Query('location') location?: string,
    @Query('isStudio') isStudio?: string,
    @Query('tags') tags?: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    const locationId = request['locationId'];

    // Create cache key based on all search parameters
    const cacheKey = this.generateCacheKey(locationId, {
      name,
      room,
      age,
      days,
      instructor,
      session,
      sessionId,
      location,
      isStudio,
      tags,
      page,
      limit,
    });

    try {
      // Check cache first
      const cachedResult = await this.redis.get(cacheKey);
      if (cachedResult) {
        return JSON.parse(cachedResult);
      }
    } catch (error) {
      // Log cache read error but continue with normal flow
      console.warn('Redis cache read error:', error.message);
    }

    const result = await this.eventsService.searchEventsByProperties(
      locationId,
      {
        name,
        room,
        age,
        days,
        instructor,
        session,
        sessionId,
        location,
        tags,
        isStudio: isStudio === 'true',
        pagination: {
          page: Number(page),
          limit: Number(limit),
        },
      },
    );

    // Cache the result with dynamic TTL
    try {
      const ttl = this.getCacheTTL({
        name,
        room,
        age,
        days,
        instructor,
        session,
        sessionId,
        location,
        isStudio,
        tags,
        page,
        limit,
      });
      await this.redis.set(cacheKey, JSON.stringify(result), 'EX', ttl);
    } catch (error) {
      // Log cache write error but continue with normal flow
      console.warn('Redis cache write error:', error.message);
    }

    return result;
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  async remove(@Param('id') id: string, @Req() request: Request) {
    const studioId = request['locationId'];
    const result = await this.eventsService.remove(id, studioId);

    // Invalidate search cache after removing event
    await this.invalidateSearchCache(studioId);

    return result;
  }

  @Delete('batch/all')
  @UseGuards(JwtAuthGuard)
  async removeBatch(@Body() ids: string[], @Req() request: Request) {
    const studioId = request['locationId'];
    const result = await this.eventsService.removeBatch(ids, studioId);

    // Invalidate search cache after batch removing events
    await this.invalidateSearchCache(studioId);

    return result;
  }

  @Get('/calendar/all')
  @UseGuards(JwtAuthGuard)
  async getCalendarEvents(
    @Req() request: Request,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const locationId = request['locationId'];
    return this.eventsService.getCalendarEvents(locationId, startDate, endDate);
  }

  // Cache management methods
  private generateCacheKey(locationId: string, filters: any): string {
    // Create a deterministic cache key from the filters
    const sortedFilters = Object.keys(filters)
      .sort()
      .reduce((result, key) => {
        if (filters[key] !== undefined && filters[key] !== null) {
          // Handle arrays by sorting and joining
          if (Array.isArray(filters[key])) {
            result[key] = filters[key].sort().join(',');
          } else {
            result[key] = filters[key];
          }
        }
        return result;
      }, {});

    const filtersString = JSON.stringify(sortedFilters);
    return `event:search:${locationId}:${Buffer.from(filtersString).toString('base64')}`;
  }

  private async invalidateSearchCache(locationId: string): Promise<void> {
    try {
      // Use Redis SCAN to find all cache keys for this location
      const pattern = `event:search:${locationId}:*`;
      const keys = await this.redis.keys(pattern);

      if (keys.length > 0) {
        await this.redis.del(...keys);
        console.log(
          `Invalidated ${keys.length} event cache entries for location ${locationId}`,
        );
      }
    } catch (error) {
      console.warn('Redis cache invalidation error:', error.message);
    }
  }

  private getCacheTTL(filters: any): number {
    // Base TTL of 5 minutes (300 seconds)
    let ttl = 300;

    // For searches with many filters, use longer TTL since they're more specific
    const filterCount = Object.values(filters).filter(
      (v) => v !== undefined && v !== null,
    ).length;
    if (filterCount > 5) {
      ttl = 600; // 10 minutes for complex searches
    }

    // For paginated results beyond page 1, use shorter TTL since they're less frequently accessed
    if (filters.page && filters.page > 1) {
      ttl = 180; // 3 minutes for later pages
    }

    return ttl;
  }

  // Cache warming endpoint for frequently accessed searches
  @Get('search/warm-cache')
  @UseGuards(JwtAuthGuard)
  async warmSearchCache(@Req() request: Request) {
    const locationId = request['locationId'];

    // Warm cache for common search patterns
    const commonSearches = [
      { isStudio: 'false', page: 1, limit: 10 }, // Default search
      { isStudio: 'true', page: 1, limit: 10 }, // Studio search
      { page: 1, limit: 20 }, // Larger page size
    ];

    const results = await Promise.allSettled(
      commonSearches.map(async (filters) => {
        const cacheKey = this.generateCacheKey(locationId, filters);
        const cached = await this.redis.get(cacheKey);

        if (!cached) {
          const result = await this.eventsService.searchEventsByProperties(
            locationId,
            {
              ...filters,
              isStudio: filters.isStudio === 'true',
              pagination: { page: filters.page, limit: filters.limit },
            },
          );
          await this.redis.set(
            cacheKey,
            JSON.stringify(result),
            'EX',
            this.getCacheTTL(filters),
          );
          return { warmed: true, filters };
        }
        return { warmed: false, filters };
      }),
    );

    return {
      message: 'Event cache warming completed',
      results: results.map((r) =>
        r.status === 'fulfilled' ? r.value : { error: true },
      ),
    };
  }
}
