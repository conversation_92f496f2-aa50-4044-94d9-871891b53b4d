import { Module, forwardRef } from '@nestjs/common';
import { EventsService } from './events.service';
import { EventsController } from './events.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Event, EventSchema } from 'src/database/schema/event';
import { StudiosModule } from 'src/studios/studios.module';
import { GohighlevelModule } from 'src/gohighlevel/gohighlevel.module';
import { JwtModule } from '@nestjs/jwt';
import { CustomFormSchema } from 'src/database/schema/customForm';
import { CustomForm } from 'src/database/schema/customForm';
import { PoliciesModule } from 'src/policies/policies.module';
import { CurrencyModule } from 'src/currency/currency.module';
import { Proration, ProrationSchema } from 'src/database/schema/prorations';
import { Session, SessionSchema } from 'src/database/schema/session';
import { StudentsModule } from 'src/students/students.module';
import { Tag, TagSchema } from 'src/database/schema/tags';
import { GcpStorageModule } from 'src/gcp-storage/gcp-storage.module';
import { RedisModule } from 'src/redis/redis.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Event.name, schema: EventSchema },
      { name: CustomForm.name, schema: CustomFormSchema },
      { name: Proration.name, schema: ProrationSchema },
      { name: Session.name, schema: SessionSchema },
      { name: Tag.name, schema: TagSchema },
    ]),
    RedisModule,
    forwardRef(() => StudiosModule),
    GohighlevelModule,
    JwtModule,
    PoliciesModule,
    CurrencyModule,
    forwardRef(() => StudentsModule),
    forwardRef(() => GcpStorageModule),
  ],
  controllers: [EventsController],
  providers: [EventsService],
  exports: [EventsService],
})
export class EventsModule {}
