import {
  Injectable,
  NotFoundException,
  forwardRef,
  Inject,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CreateEventDto } from './dto/create-event.dto';
import { UpdateEventDto } from './dto/update-event.dto';
import { Event } from 'src/database/schema/event';
import { StudiosService } from 'src/studios/studios.service';
import { GohighlevelService } from 'src/gohighlevel/gohighlevel.service';
import { CustomForm } from 'src/database/schema/customForm';
import {
  CalendarDto,
  CalendarType,
  MeetingLocationType,
  RecurrenceFrequency,
  RecurrenceBookingOption,
  CalendarDayArrayIndexMap,
} from 'src/gohighlevel/dto/createCalendarDto';
import {
  convertTo24HourTime,
  createBodyToCreatePriceForAProductInGHL,
  mapIntervalUpperCase,
  calculateSlotDuration,
} from 'src/utils/helperFunction';
import { PoliciesService } from 'src/policies/policies.service';
import { Proration } from 'src/database/schema/prorations';
import { CurrencyService } from 'src/currency/currency.service';
import { Session } from 'src/database/schema/session';
import { StudentsService } from 'src/students/students.service';
import { Tag } from 'src/database/schema/tags';
import { GcpStorageService } from 'src/gcp-storage/gcp-storage.service';
import type { Redis } from 'ioredis';

@Injectable()
export class EventsService {
  private readonly logger = new Logger(EventsService.name);
  constructor(
    @InjectModel(Event.name) private readonly eventModel: Model<Event>,
    @InjectModel(Proration.name) private prorationModel: Model<Proration>,
    @InjectModel(CustomForm.name) private customFormModel: Model<CustomForm>,
    @Inject(forwardRef(() => StudiosService))
    private readonly studioService: StudiosService,
    @Inject(forwardRef(() => GohighlevelService))
    private readonly ghlService: GohighlevelService,
    @Inject(forwardRef(() => PoliciesService))
    private readonly policyService: PoliciesService,
    @Inject(forwardRef(() => CurrencyService))
    private readonly currencyService: CurrencyService,
    @InjectModel(Session.name) private sessionModel: Model<Session>,
    @Inject(forwardRef(() => StudentsService))
    private readonly studentService: StudentsService,
    @InjectModel(Tag.name) private tagModel: Model<Tag>,
    @Inject(forwardRef(() => GcpStorageService))
    private readonly gcpStorageService: GcpStorageService,
    @Inject('REDIS_CLIENT') private readonly redis: Redis,
  ) {}

  async createProduct(
    newEnrollmentData,
    createEnrollmentDto,
    locationId_ghl,
    studio,
  ) {
    let ghlProductId_ghl = '';
    try {
      const ghl_product_details = await this.ghlService.createProduct(
        newEnrollmentData.title,
        studio.locationId,
        createEnrollmentDto.description,
      );
      ghlProductId_ghl = ghl_product_details._id;
      const currency = await this.currencyService.findByStudioId(studio._id);
      const price_body = await createBodyToCreatePriceForAProductInGHL(
        createEnrollmentDto,
        studio.locationId,
        'event',
        ghlProductId_ghl,
        this.prorationModel,
        currency.name,
      );
      await this.ghlService.createPriceForAProduct(
        studio.locationId,
        ghlProductId_ghl,
        price_body,
      );
      console.log('Product created successfully in ghl.');
    } catch (error) {
      console.error('error creating product in ghl: ', error);
    }
    console.log('Returning product ID:', ghlProductId_ghl);
    return ghlProductId_ghl;
  }

  async createCalendar(
    newEnrollmentData,
    createEnrollmentDto,
    locationId_ghl,
    studio,
  ) {
    try {
      const slotDuration = calculateSlotDuration(
        newEnrollmentData.availability[0].startTime,
        newEnrollmentData.availability[0].endTime,
      );
      const dayIndexes = newEnrollmentData.days.map(
        (day) =>
          CalendarDayArrayIndexMap[
            day as keyof typeof CalendarDayArrayIndexMap
          ],
      );

      const openHours = newEnrollmentData.availability.map((day) => {
        const { hour: openHour, minute: openMinute } = convertTo24HourTime(
          day.startTime,
        );
        const { hour: closeHour, minute: closeMinute } = convertTo24HourTime(
          day.endTime,
        );
        return {
          daysOfTheWeek: [
            CalendarDayArrayIndexMap[
              day.day as keyof typeof CalendarDayArrayIndexMap
            ],
          ], // Map day to index
          hours: [
            {
              openHour,
              openMinute,
              closeHour,
              closeMinute,
            },
          ],
        };
      });

      const recurringFrequency = await mapIntervalUpperCase(
        createEnrollmentDto.tuitionBillingCycle,
      );

      // Define CalendarDto object with mapped and hardcoded values
      const createCalendarDetails: Partial<CalendarDto> = {
        description: newEnrollmentData.description,
        calendarType: CalendarType.CLASS_BOOKING,
        appoinmentPerSlot: newEnrollmentData.maxSize,
        allowReschedule: false,
        enableRecurring: true,
        recurring: {
          freq: RecurrenceFrequency.WEEKLY,
          count: 24,
          bookingOption: RecurrenceBookingOption.SKIP,
        },
        teamMembers: [
          {
            userId: studio.userId,
            meetingLocation: newEnrollmentData.location.toString(),
            meetingLocationType: MeetingLocationType.CUSTOM,
          },
        ],
        openHours: openHours,
        slotDuration: slotDuration,
        slotDurationUnit: 'mins',
      };
      return await this.ghlService.createCalendar(
        newEnrollmentData.title,
        studio.locationId,
        createCalendarDetails,
      );
      console.log('Calendar created successfully in ghl.');
    } catch (error) {
      console.error('error creating calendar in ghl:', error);
    }
  }

  async create(
    createEventDto: CreateEventDto,
    locationId,
  ): Promise<Partial<Event> & { imageUrl?: string }> {
    const response: any = { status: 'success', message: '', data: {} };

    try {
      createEventDto.studio = Types.ObjectId.createFromHexString(locationId);
      // Step 1: Retrieve Studio Information
      const studio = await this.studioService.findByLocationId(
        createEventDto.studio,
      );
      const locationId_ghl = studio.locationId;

      // Step 2: Convert Related IDs to ObjectId
      try {
        (createEventDto.policyGroup as any) = createEventDto.policyGroup.map(
          (policyId: string) => Types.ObjectId.createFromHexString(policyId),
        );
        if (createEventDto.tags && createEventDto.tags.length > 0) {
          (createEventDto.tags as any) = createEventDto.tags.map(
            (tagId: string) => Types.ObjectId.createFromHexString(tagId),
          );
        } else {
          (createEventDto.tags as any) = [];
        }
        if (createEventDto.instructor && createEventDto.instructor.length > 0) {
          (createEventDto.instructor as any) = createEventDto.instructor.map(
            (instructorId: string) =>
              Types.ObjectId.createFromHexString(instructorId),
          );
        } else {
          (createEventDto.instructor as any) = [];
        }
        if (createEventDto.group && createEventDto.group.length > 0) {
          (createEventDto.group as any) = createEventDto.group.map(
            (groupId: string) => Types.ObjectId.createFromHexString(groupId),
          );
        } else {
          (createEventDto.group as any) = [];
        }
        if (createEventDto.room == '') {
          (createEventDto.room as any) = null;
        } else if (createEventDto.room && createEventDto.room !== '') {
          (createEventDto.room as any) = Types.ObjectId.createFromHexString(
            createEventDto.room,
          );
        }
        if (createEventDto.location == '') {
          (createEventDto.location as any) = null;
        } else if (createEventDto.location && createEventDto.location !== '') {
          (createEventDto.location as any) = Types.ObjectId.createFromHexString(
            createEventDto.location,
          );
        }
        if (createEventDto.session == '') {
          (createEventDto.session as any) = null;
        } else if (createEventDto.session && createEventDto.session !== '') {
          (createEventDto.session as any) = Types.ObjectId.createFromHexString(
            createEventDto.session,
          );
        }
      } catch (error) {
        this.logger.error(
          `Error converting string IDs to ObjectIds: ${locationId}`,
          error,
        );
        response.status = 'partial_success';
        response.message += ' ID conversion issues occurred.';
      }

      // Step 3: Save the Event in Database
      const newEvent = new this.eventModel(createEventDto);
      const savedEvent = await newEvent.save();
      response.data.eventId = savedEvent._id;
      response.message = 'Event created successfully.';

      try {
        const newEventDataId = Types.ObjectId.createFromHexString(
          savedEvent._id.toString(),
        );
        await Promise.all(
          createEventDto.policyGroup.map(async (policyId) => {
            const policy = await this.policyService.findOne(policyId);
            if (!policy.event.includes(newEventDataId)) {
              policy.event.push(newEventDataId);
              await policy.save();
            }
          }),
        );
      } catch (error) {
        this.logger.error(
          `error updating policies with enrollment id: ${locationId}`,
          error,
        );
      }

      // Update GHL related fields
      const ghl_product_id = await this.createProduct(
        savedEvent,
        createEventDto,
        locationId_ghl,
        studio,
      );
      savedEvent.productId_ghl = ghl_product_id;

      const ghl_calendar_id = await this.createCalendar(
        savedEvent,
        createEventDto,
        locationId_ghl,
        studio,
      );
      savedEvent.calendarId_ghl = ghl_calendar_id;

      // Handle image logic
      let imageUrl: string | undefined;

      if (createEventDto.image) {
        try {
          this.logger.log(
            `Attempting to upload event preview image for event ${savedEvent._id}`,
          );

          await this.gcpStorageService.uploadFileToGCP(
            undefined,
            createEventDto.image,
            createEventDto.imageName,
            createEventDto.studio.toString(),
            'event-preview-image',
            savedEvent._id.toString(),
          );

          imageUrl = await this.gcpStorageService.getPublicImage(
            createEventDto.studio.toString(),
            'event-preview-image',
            savedEvent._id.toString(),
          );

          this.logger.log(
            `Successfully uploaded event preview image for event ${savedEvent._id}`,
          );
        } catch (error) {
          this.logger.error(
            `Failed to upload event preview image for event ${savedEvent._id}`,
            {
              error: error.message,
              stack: error.stack,
              eventId: savedEvent._id,
              studioId: locationId,
            },
          );
        }
      } else if (createEventDto.imageUrl) {
        imageUrl = createEventDto.imageUrl;
        savedEvent.defaultImageUrl = createEventDto.imageUrl;
      }

      // Single save operation at the end
      await savedEvent.save();

      return { ...savedEvent.toObject(), imageUrl };
    } catch (error) {
      this.logger.error(`Error creating event: ${locationId}`, error);
      throw new Error('Failed to create event. Please try again.');
    }
  }

  async findAll(): Promise<Event[]> {
    return await this.eventModel.find({ isDeleted: false }).exec();
  }

  async findOne(id: string): Promise<any> {
    const event = await this.eventModel
      .findOne({ _id: id, isDeleted: false })
      .populate({
        path: 'room',
        model: 'CustomForm', // Specify the model to populate from
        match: { fieldType: 'room' },
        select: 'fieldName',
      })
      .populate({
        path: 'instructor',
        model: 'CustomForm',
        match: { fieldType: 'instructor' },
        select: 'fieldName',
      })
      .populate({ path: 'tags', model: 'Tag', select: 'fieldName' })
      .populate({
        path: 'group',
        model: 'CustomForm',
        match: { fieldType: 'group' },
        select: 'fieldName',
      })
      .populate({
        path: 'location',
        model: 'CustomForm',
        match: { fieldType: 'location' },
        select: 'fieldName',
      })
      .populate({
        path: 'policyGroup',
        model: 'Policy',
        // match: { fieldType: 'group' },
        select: 'name description',
      })
      .populate({
        path: 'session',
        model: 'Session',
        select:
          'name endDate startDate isRegistrationFee registrationFeeAmount billingDate',
      })
      .populate('students')
      .lean()
      .exec();
    if (!event) {
      throw new NotFoundException(`Event with ID ${id} not found`);
    }
    const locationId = event.studio.toString();
    let imageUrl: string | undefined;
    try {
      this.logger.log(
        `Attempting to retrieve class preview image for enrollment ${id}`,
        {
          enrollmentId: id,
          studioId: event.studio,
        },
      );

      try {
        imageUrl = await this.gcpStorageService.getPublicImage(
          event.studio.toString(),
          'event-preview-image',
          id,
        );
        if (imageUrl === null) {
          imageUrl = event.defaultImageUrl;
        }
      } catch (error) {
        this.logger.error(
          `Failed to retrieve event preview image for enrollment ${id}`,
          {
            error: error.message,
            stack: error.stack,
            enrollmentId: id,
            studioId: locationId,
          },
        );
      }

      if (!imageUrl) {
        imageUrl = event.defaultImageUrl;
      }

      this.logger.log(
        `Successfully retrieved event preview image URL for enrollment ${id}`,
        {
          enrollmentId: id,
          studioId: locationId,
          hasImageUrl: !!imageUrl,
        },
      );
    } catch (error) {
      this.logger.error(
        `Failed to retrieve event preview image for enrollment ${id}`,
        {
          error: error.message,
          stack: error.stack,
          enrollmentId: id,
          studioId: locationId,
        },
      );
    }
    return {
      ...event,
      remainingSeats: event.maxSize - (event.students?.length || 0),
      imageUrl,
    };
  }

  async update(
    id: string,
    updateEventDto: UpdateEventDto,
    locationId,
  ): Promise<Partial<Event> & { imageUrl?: string }> {
    updateEventDto.studio = Types.ObjectId.createFromHexString(locationId);
    // Ensure necessary transformations for specific fields
    if (updateEventDto.policyGroup) {
      updateEventDto.policyGroup = updateEventDto.policyGroup.map(
        (policyId: string) => Types.ObjectId.createFromHexString(policyId),
      ) as any;
    }

    if (updateEventDto.tags) {
      updateEventDto.tags = updateEventDto.tags.map((tagId: string) =>
        Types.ObjectId.createFromHexString(tagId),
      ) as any;
    }

    if (updateEventDto.location) {
      updateEventDto.location = Types.ObjectId.createFromHexString(
        updateEventDto.location,
      ) as any;
    }

    if (updateEventDto.group) {
      updateEventDto.group = updateEventDto.group.map((groupId: string) =>
        Types.ObjectId.createFromHexString(groupId),
      ) as any;
    }

    if (updateEventDto.instructor) {
      updateEventDto.instructor = updateEventDto.instructor.map(
        (instructorId: string) =>
          Types.ObjectId.createFromHexString(instructorId),
      ) as any;
    }

    // Add room conversion
    if (updateEventDto.room) {
      if (updateEventDto.room === '') {
        updateEventDto.room = null;
      } else {
        updateEventDto.room = Types.ObjectId.createFromHexString(
          updateEventDto.room,
        ) as any;
      }
    }

    // Add session conversion
    if (updateEventDto.session) {
      if (updateEventDto.session === '') {
        updateEventDto.session = null;
      } else {
        updateEventDto.session = Types.ObjectId.createFromHexString(
          updateEventDto.session,
        ) as any;
      }
    }

    // Update the event in the database
    const updatedEvent = await this.eventModel
      .findByIdAndUpdate(id, updateEventDto, { new: true, runValidators: true })
      .populate({
        path: 'room',
        model: 'CustomForm', // Specify the model to populate from
        match: { fieldType: 'room' },
        select: 'fieldName',
      })
      .populate({
        path: 'instructor',
        model: 'CustomForm',
        match: { fieldType: 'instructor' },
        select: 'fieldName',
      })
      .populate({ path: 'tags', model: 'Tag', select: 'fieldName' })
      .populate({
        path: 'group',
        model: 'CustomForm',
        match: { fieldType: 'group' },
        select: 'fieldName',
      })
      .populate({
        path: 'location',
        model: 'CustomForm',
        match: { fieldType: 'location' },
        select: 'fieldName',
      })
      .populate({
        path: 'policyGroup',
        model: 'Policy',
        // match: { fieldType: 'group' },
        select: 'name',
      })
      .exec();

    // Handle case where the document is not found
    if (!updatedEvent) {
      throw new NotFoundException(`Event with ID ${id} not found`);
    }

    try {
      const newEventDataId = Types.ObjectId.createFromHexString(
        updatedEvent._id.toString(),
      );
      await Promise.all(
        updateEventDto.policyGroup.map(async (policyId) => {
          const policy = await this.policyService.findOne(policyId);
          if (!policy.event.includes(newEventDataId)) {
            policy.event.push(newEventDataId);
            await policy.save();
          }
        }),
      );
    } catch (error) {
      console.error('error updating policies with enrollment id:', error);
    }

    if (updatedEvent.calendarId_ghl) {
      await this.ghlService.updateCalendar(
        updatedEvent.calendarId_ghl,
        locationId,
        updateEventDto,
      );
    }

    let imageUrl: string | undefined;

    if (updateEventDto.image) {
      imageUrl = await this.gcpStorageService.uploadFileToGCP(
        undefined,
        updateEventDto.image,
        updateEventDto.imageName,
        updateEventDto.studio.toString(),
        'event-preview-image',
        updatedEvent._id.toString(),
      );
      updatedEvent.defaultImageUrl = '';
    } else if (updateEventDto.imageUrl) {
      imageUrl = updateEventDto.imageUrl;
      updatedEvent.defaultImageUrl = updateEventDto.imageUrl;
      await this.gcpStorageService.deleteFileFromGCP(
        updateEventDto.studio.toString(),
        'event-preview-image',
        'image',
        updatedEvent._id.toString(),
      );
    }
    await updatedEvent.save();
    return { ...updatedEvent.toObject(), imageUrl };
  }

  // async remove(id: string): Promise<Event> {
  //   const deletedEvent = await this.eventModel.findByIdAndDelete(id).exec();
  //   if (!deletedEvent) {
  //     throw new NotFoundException(`Event with ID ${id} not found`);
  //   }
  //   return deletedEvent;
  // }

  async findByStudio(locationId): Promise<Event[]> {
    const locationId_object = Types.ObjectId.createFromHexString(locationId);
    const events = await this.eventModel
      .find({ studio: locationId_object, isDeleted: false })
      .populate({
        path: 'room',
        model: 'CustomForm', // Specify the model to populate from
        match: { fieldType: 'room' },
        select: 'fieldName',
      })
      .populate({
        path: 'instructor',
        model: 'CustomForm',
        match: { fieldType: 'instructor' },
        select: 'fieldName',
      })
      .populate({ path: 'tags', model: 'Tag', select: 'fieldName' })
      .populate({
        path: 'group',
        model: 'CustomForm',
        match: { fieldType: 'group' },
        select: 'fieldName',
      })
      .populate({
        path: 'location',
        model: 'CustomForm',
        match: { fieldType: 'location' },
        select: 'fieldName',
      })
      // .populate({
      //   path: 'policyGroup',
      //   model: 'Policy',
      //   select: 'name',
      // })
      .populate({
        path: 'session',
        model: 'Session',
        select:
          'name endDate startDate isRegistrationFee registrationFeeAmount',
      })
      .populate({
        path: 'studio',
        model: 'Studio',
        select: 'subaccountName',
      })
      .exec();
    // if (!events.length) {
    //   throw new NotFoundException(`No events found for studio ID ${locationId}`);
    // }
    return events;
  }

  async findByStudent(studentId: string): Promise<Event[]> {
    const events = await this.eventModel
      .find({ students: studentId, isDeleted: false })
      .exec();
    if (!events.length) {
      throw new NotFoundException(
        `No events found for student ID ${studentId}`,
      );
    }
    return events;
  }

  async searchEventsByProperties(
    locationId: string,
    filters: {
      name?: string;
      room?: string;
      days?: string;
      age?: string;
      instructor?: string;
      session?: string;
      sessionId?: string;
      location?: string;
      tags?: string;
      isStudio?: boolean;
      pagination?: {
        page: number;
        limit: number;
      };
    },
  ) {
    const locationObjectId = Types.ObjectId.createFromHexString(locationId);
    const searchConditions: any = {
      studio: locationObjectId,
      isDeleted: false,
    };
    const { page = 1, limit = 10 } = filters.pagination || {};
    const skip = (page - 1) * limit;
    // Apply filters before pipeline
    if (filters.room) {
      const roomDocs = await this.customFormModel.find({
        studio: locationObjectId,
        fieldType: 'room',
        fieldName: { $regex: new RegExp(filters.room, 'i') },
      });
      if (roomDocs.length > 0) {
        searchConditions.room = { $in: roomDocs.map((room) => room._id) };
      }
    }
    // check if the api call is not from studio, in that case show only the unhidden ones
    // studio portal will show all
    if (!filters.isStudio) {
      searchConditions.hide = false;
    }

    if (filters.instructor) {
      const instructorDocs = await this.customFormModel.find({
        studio: locationObjectId,
        fieldType: 'instructor',
        fieldName: { $regex: new RegExp(filters.instructor, 'i') },
      });
      if (instructorDocs.length > 0) {
        searchConditions.instructor = { $in: instructorDocs.map((i) => i._id) };
      }
    }

    if (filters.name) {
      searchConditions.title = { $regex: new RegExp(filters.name, 'i') };
    }

    if (filters.days) {
      searchConditions.days = {
        $elemMatch: { $regex: new RegExp(filters.days, 'i') },
      };
    }
    // Add age filter
    if (filters.age) {
      const age = parseInt(filters.age);
      if (!isNaN(age)) {
        const enrollmentsWithAge = await this.eventModel
          .find({
            studio: locationObjectId,
            startYear: { $lte: age },
            endYear: { $gte: age },
            isDeleted: false,
          })
          .select('_id');
        if (enrollmentsWithAge.length > 0) {
          searchConditions._id = {
            $in: enrollmentsWithAge.map((enrollment) => enrollment._id),
          };
        } else {
          // If no enrollments found for this age, return empty result
          return {
            data: {
              classes: [],
              rooms: [],
              instructors: [],
              tags: [],
              ages: {},
            },
            pagination: {
              page,
              limit,
              totalCount: 0,
              totalPages: 0,
            },
          };
        }
      }
    }

    // Modify session filter
    if (filters.session || filters.sessionId) {
      try {
        const sessionQuery: any = {
          studioId: locationObjectId,
          isArchive: false,
        };

        // If sessionId is provided, use it directly
        if (filters.sessionId) {
          sessionQuery._id = Types.ObjectId.createFromHexString(
            filters.sessionId,
          );
        }
        // Otherwise, use session name search if provided
        else if (filters.session) {
          sessionQuery.name = { $regex: new RegExp(filters.session, 'i') };
        }

        const sessionDocs = await this.sessionModel.find(sessionQuery);

        if (sessionDocs.length > 0) {
          searchConditions.session = {
            $in: sessionDocs.map((session) => session._id),
          };
        } else {
          // Return empty result if no matching sessions found
          return {
            data: {
              events: [],
              rooms: [],
              instructors: [],
              tags: [],
              sessions: [],
              locations: [],
            },
            pagination: {
              page,
              limit,
              totalCount: 0,
              totalPages: 0,
            },
          };
        }
      } catch (error) {
        console.error('Error in session filter:', error);
      }
    }

    // Add location filter
    if (filters.location) {
      const locationDocs = await this.customFormModel.find({
        studio: locationObjectId,
        fieldType: 'location',
        fieldName: { $regex: new RegExp(filters.location, 'i') },
      });
      if (locationDocs.length > 0) {
        searchConditions.location = { $in: locationDocs.map((loc) => loc._id) };
      }
    }

    if (filters.tags) {
      const tagsDocs = await this.tagModel.find({
        studioId: locationObjectId,
        fieldName: { $regex: new RegExp(filters.tags, 'i') },
      });
      if (tagsDocs.length > 0) {
        searchConditions.tags = { $in: tagsDocs.map((tag) => tag._id) };
      }
    }

    console.log(
      'Search conditions:',
      JSON.stringify(searchConditions, null, 2),
    ); // Debug log

    // Prepare the aggregation pipeline
    const pipeline: any[] = [
      { $match: searchConditions },
      { $sort: { createdAt: -1 } },

      // Add facet for total count
      {
        $facet: {
          metadata: [{ $count: 'totalCount' }],
          data: [
            { $skip: skip },
            { $limit: limit },
            // Lookups
            {
              $lookup: {
                from: 'tags',
                localField: 'tags',
                foreignField: '_id',
                as: 'tagsDocs',
              },
            },
            {
              $lookup: {
                from: 'customforms',
                localField: 'room',
                foreignField: '_id',
                as: 'roomDocs',
              },
            },
            {
              $unwind: {
                path: '$roomDocs',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $lookup: {
                from: 'customforms',
                localField: 'instructor',
                foreignField: '_id',
                as: 'instructorDocs',
              },
            },
            {
              $lookup: {
                from: 'sessions',
                localField: 'session',
                foreignField: '_id',
                as: 'sessionDocs',
              },
            },
            {
              $unwind: {
                path: '$sessionDocs',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $lookup: {
                from: 'customforms',
                localField: 'location',
                foreignField: '_id',
                as: 'locationDocs',
              },
            },
            {
              $unwind: {
                path: '$locationDocs',
                preserveNullAndEmptyArrays: true,
              },
            },
            // Project stage
            {
              $project: {
                _id: 1,
                title: 1,
                description: 1,
                days: 1,
                startTime: 1,
                endTime: 1,
                startYear: 1,
                endYear: 1,
                startMonth: 1,
                endMonth: 1,
                duration: 1,
                maxSize: 1,
                maxWait: 1,
                oneTime: 1,
                allowOnlineRegistration: 1,
                allowPortalEnrollment: 1,
                allowTrialEnrollment: 1,
                displayOnWebsite: 1,
                tuitionBillingMethod: 1,
                tuitionBillingCycle: 1,
                tuitionDiscountRule: 1,
                prorateTuition: 1,
                registrationFee: 1,
                registrationFeeAmount: 1,
                tuitionFee: 1,
                registrationStartDate: 1,
                registrationEndDate: 1,
                startDate: 1,
                endDate: 1,
                schedules: 1,
                availability: 1,
                timeConfig: 1,
                calendarId_ghl: 1,
                productId_ghl: 1,
                color: 1,
                costumeFee: 1,
                defaultImageUrl: 1,
                room: {
                  _id: '$roomDocs._id',
                  name: '$roomDocs.fieldName',
                  fieldType: '$roomDocs.fieldType',
                },
                instructor: {
                  $map: {
                    input: '$instructorDocs',
                    as: 'inst',
                    in: {
                      _id: '$$inst._id',
                      name: '$$inst.fieldName',
                      fieldType: '$$inst.fieldType',
                    },
                  },
                },
                session: {
                  _id: '$sessionDocs._id',
                  name: '$sessionDocs.name',
                  // fieldType: '$sessionDocs.fieldType',
                },
                location: {
                  _id: '$locationDocs._id',
                  name: '$locationDocs.fieldName',
                  fieldType: '$locationDocs.fieldType',
                },
                tags: {
                  $map: {
                    input: '$tagsDocs',
                    as: 'tag',
                    in: {
                      _id: '$$tag._id',
                      name: '$$tag.fieldName',
                    },
                  },
                },
                group: {
                  $map: {
                    input: '$groupDocs',
                    as: 'grp',
                    in: {
                      _id: '$$grp._id',
                      name: '$$grp.fieldName',
                      fieldType: '$$grp.fieldType',
                    },
                  },
                },
              },
            },
          ],
        },
      },
    ];

    // console.log('Pipeline:', JSON.stringify(pipeline, null, 2)); // Debug log

    const getAllPossibleValues = async () => {
      const rooms = await this.customFormModel
        .find({
          studio: locationObjectId,
          fieldType: 'room',
        })
        .select('_id fieldName fieldType');

      const instructors = await this.customFormModel
        .find({
          studio: locationObjectId,
          fieldType: 'instructor',
        })
        .select('_id fieldName fieldType');

      const tags = await this.tagModel
        .find({
          studioId: locationObjectId,
        })
        .select('_id fieldName');

      const ageStats = await this.eventModel.aggregate([
        {
          $match: {
            studio: locationObjectId,
            isDeleted: false,
            startYear: { $exists: true },
            endYear: { $exists: true },
          },
        },
        {
          $group: {
            _id: null,
            startYear: { $min: '$startYear' },
            endYear: { $max: '$endYear' },
          },
        },
        {
          $project: {
            _id: new Types.ObjectId(),
            startYear: '$startYear',
            endYear: '$endYear',
            fieldType: 'age',
          },
        },
      ]);

      // Format the age group
      const ageGroups =
        ageStats.length > 0
          ? {
              minAge: ageStats[0].startYear,
              maxAge: ageStats[0].endYear,
            }
          : {};

      // Add sessions to possible values
      const sessions = await this.sessionModel
        .find({
          studioId: locationObjectId,
          // fieldType: 'session',
        })
        .select(
          '_id name endDate startDate isRegistrationFee registrationFeeAmount',
        );

      // Add locations to possible values
      const locations = await this.customFormModel
        .find({
          studio: locationObjectId,
          fieldType: 'location',
        })
        .select('_id fieldName fieldType');

      return {
        rooms: rooms.map((room) => ({
          _id: room._id,
          name: room.fieldName,
          fieldType: room.fieldType,
        })),
        instructors: instructors.map((instructor) => ({
          _id: instructor._id,
          name: instructor.fieldName,
          fieldType: instructor.fieldType,
        })),
        tags: tags.map((tag) => ({
          _id: tag._id,
          name: tag.fieldName,
        })),
        age: ageGroups,
        sessions: sessions.map((session) => ({
          _id: session._id,
          name: session.name,
          // fieldType: session.fieldType,
        })),
        locations: locations.map((location) => ({
          _id: location._id,
          name: location.fieldName,
          fieldType: location.fieldType,
        })),
      };
    };

    // Execute the aggregation and get possible values in parallel
    const [results, possibleValues] = await Promise.all([
      this.eventModel.aggregate(pipeline),
      getAllPossibleValues(),
    ]);

    // console.log('Aggregation results:', JSON.stringify(results, null, 2)); // Debug log

    // Extract total count and data
    const totalCount = results[0]?.metadata[0]?.totalCount || 0;
    let eventData = results[0]?.data || [];

    // Get active student counts for each event
    const activeStudentCounts = await Promise.all(
      eventData.map(async (eventItem) => {
        const students = await this.studentService.findAllByEventId(
          eventItem._id.toString(),
        );
        return students.filter((student) =>
          student.events.some(
            (e) =>
              e.eventId.toString() === eventItem._id.toString() &&
              (e.subscriptionStatus === 'active' ||
                e.subscriptionStatus === 'scheduled'),
          ),
        ).length;
      }),
    );

    // Calculate remaining seats
    eventData = await Promise.all(
      eventData.map(async (eventItem, index) => {
        let imageUrl: string | undefined;

        try {
          if (eventItem.defaultImageUrl && eventItem.defaultImageUrl !== '') {
            imageUrl = eventItem.defaultImageUrl;
          } else {
            imageUrl = await this.gcpStorageService.getPublicImage(
              locationId,
              'event-preview-image',
              eventItem._id.toString(),
            );
            if (imageUrl === null) {
              imageUrl = eventItem.defaultImageUrl;
            }
          }
        } catch (error) {
          this.logger.warn('Failed to get image URL for event', {
            eventId: eventItem._id.toString(),
            studioId: locationId,
            error: error.message,
          });
        }

        return {
          ...eventItem,
          remainingSeats: eventItem.maxSize - (activeStudentCounts[index] || 0),
          imageUrl,
        };
      }),
    );

    return {
      data: {
        events: eventData,
        ...possibleValues,
      },
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    };
  }

  async findAllByStudioIdCsv(locationId: string) {
    const locationId_object = Types.ObjectId.createFromHexString(locationId);
    const events = await this.eventModel
      .find({ studio: locationId_object, isDeleted: false })
      .populate({
        path: 'room',
        model: 'CustomForm',
        match: { fieldType: 'room' },
        select: 'fieldName',
      })
      .populate({
        path: 'instructor',
        model: 'CustomForm',
        match: { fieldType: 'instructor' },
        select: 'fieldName',
      })
      .populate({ path: 'tags', model: 'Tag', select: 'fieldName' })
      .populate({
        path: 'group',
        model: 'CustomForm',
        match: { fieldType: 'group' },
        select: 'fieldName',
      })
      .populate({
        path: 'policyGroup',
        model: 'Policy',
        select: 'name',
      })
      .populate({
        path: 'location',
        model: 'CustomForm',
        match: { fieldType: 'location' },
        select: 'fieldName',
      })
      .exec();

    return events.map((event) => {
      const plainEvent = event.toObject();

      return {
        title: plainEvent.title,
        location: plainEvent.location,
        days: plainEvent.days,
        startTime: plainEvent.startTime,
        endTime: plainEvent.endTime,
        duration: plainEvent.duration,
        startDate: plainEvent.startDate,
        endDate: plainEvent.endDate,
        maxSize: plainEvent.maxSize,
        oneTime: plainEvent.oneTime,
        registrationFeeAmount: plainEvent.registrationFeeAmount || 0,
        description: plainEvent.description,
        startYear: plainEvent.startYear,
        endYear: plainEvent.endYear,
        startMonth: plainEvent.startMonth,
        endMonth: plainEvent.endMonth,
        room: plainEvent.room
          ? typeof plainEvent.room === 'object' &&
            'fieldName' in plainEvent.room
            ? plainEvent.room.fieldName
            : plainEvent.room.toString()
          : '',
        instructor: plainEvent.instructor?.map((i: any) => i.fieldName) || [],
        tags: plainEvent.tags?.map((t: any) => t.fieldName) || [],
        group: plainEvent.group?.map((g: any) => g.fieldName) || [],
        policyGroup: plainEvent.policyGroup?.map((p: any) => p.name) || [],
      };
    });
  }

  async remove(id: string, studioId: string): Promise<boolean> {
    const studioObjectId = Types.ObjectId.createFromHexString(studioId);
    const eventId = Types.ObjectId.createFromHexString(id);
    const result = await this.eventModel.updateOne(
      {
        _id: eventId,
        studio: studioObjectId,
      },
      {
        $set: { isDeleted: true },
      },
    );
    return result.modifiedCount > 0;
  }

  async removeBatch(
    ids: string[],
    studioId: string,
  ): Promise<{ deletedCount: number }> {
    const studioObjectId = Types.ObjectId.createFromHexString(studioId);
    const eventIds = ids.map((id) => Types.ObjectId.createFromHexString(id));
    const result = await this.eventModel.updateMany(
      {
        _id: { $in: eventIds },
        studio: studioObjectId,
      },
      {
        $set: { isDeleted: true },
      },
    );
    return { deletedCount: result.modifiedCount };
  }

  private parseCustomDateTime(dateTimeStr: string) {
    if (!dateTimeStr) {
      return null;
    }

    const parts = dateTimeStr.split(', ');
    const year = parts[1];
    const timeWithAmPm = parts[2];

    const [timeStr, meridiem] = timeWithAmPm.split(' ');
    let [hours, minutes] = timeStr.split(':').map(Number);

    if (meridiem === 'PM' && hours !== 12) {
      hours += 12;
    } else if (meridiem === 'AM' && hours === 12) {
      hours = 0;
    }

    const formattedHours = hours.toString().padStart(2, '0');
    const formattedMinutes = minutes.toString().padStart(2, '0');
    const time = `${formattedHours}:${formattedMinutes}:00`;

    const date = new Date(`${parts[0]}, ${year}`);
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');

    return `${year}-${month}-${day}T${time}`;
  }

  async getCalendarEvents(
    locationId: string,
    startDate?: string,
    endDate?: string,
    page = 1,
    limit = 50,
  ) {
    try {
      const locationObjectId = Types.ObjectId.createFromHexString(locationId);
      const startRange = startDate
        ? new Date(startDate)
        : new Date(new Date().setDate(1));
      const endRange = endDate
        ? new Date(endDate)
        : new Date(new Date().setMonth(startRange.getMonth() + 1));

      const dateFilter = {
        studio: locationObjectId,
        isDeleted: false,
        $or: [
          // Events that start within our range
          { startDate: { $lte: endRange } },
          // AND end after our range starts
          { endDate: { $gte: startRange } },
        ],
      };

      const [events, total] = await Promise.all([
        this.eventModel
          .find(dateFilter)
          .select('title startDate endDate color _id availability')
          .skip((page - 1) * limit)
          .limit(limit)
          .exec(),
        this.eventModel.countDocuments(dateFilter),
      ]);

      const expandedEvents = [];

      for (const event of events) {
        for (const slot of event.availability) {
          const slotDay = slot.day;
          const slotStartTime = this.parseCustomDateTime(slot.startTime);
          const slotEndTime = this.parseCustomDateTime(slot.endTime)?.split(
            'T',
          )[1];

          if (!slotStartTime || !slotEndTime) continue;

          const occurrences = this.getOccurrencesInRange(
            event.startDate,
            event.endDate,
            slotDay,
          );

          occurrences.forEach((date) => {
            expandedEvents.push({
              title: event.title,
              start: `${date.toISOString().split('T')[0]}T${slotStartTime.split('T')[1]}`,
              end: `${date.toISOString().split('T')[0]}T${slotEndTime}`,
              resourceId: event._id.toString(),
              color: event.color,
              day: slotDay,
            });
          });
        }
      }

      return {
        data: expandedEvents,
        total: expandedEvents.length,
        page,
        lastPage: Math.ceil(expandedEvents.length / limit),
      };
    } catch (error) {
      console.error('Error in getCalendarEvents:', error);
      return {
        data: [],
        total: 0,
        page,
        lastPage: 0,
      };
    }
  }

  // Helper method to get occurrences (same as enrollment service)
  getOccurrencesInRange(startDate, endDate, targetDay) {
    const dayMap = {
      Sun: 0,
      Mon: 1,
      Tue: 2,
      Wed: 3,
      Thu: 4,
      Fri: 5,
      Sat: 6,
    };

    // Convert string to number if needed
    const targetDayNum =
      typeof targetDay === 'string' ? dayMap[targetDay] : targetDay;

    if (targetDayNum === undefined) {
      console.error('Invalid targetDay:', targetDay);
      return [];
    }

    const occurrences = [];
    const current = new Date(startDate);
    current.setUTCHours(0, 0, 0, 0);
    endDate = new Date(endDate);
    endDate.setUTCHours(23, 59, 59, 999);

    // Move forward to the next correct targetDay if needed
    while (current.getUTCDay() !== targetDayNum) {
      current.setUTCDate(current.getUTCDate() + 1);
    }

    while (current <= endDate) {
      if (current.getUTCDay() === targetDayNum) {
        occurrences.push(new Date(current));
      }
      current.setUTCDate(current.getUTCDate() + 7); // Move directly to the next same weekday
    }
    return occurrences;
  }

  // Cache image URLs separately with longer TTL since they change less frequently
  private async getCachedImageUrl(
    locationId: string,
    eventId: string,
    defaultImageUrl?: string,
  ): Promise<string | undefined> {
    const cacheKey = `event:image:${locationId}:${eventId}`;

    try {
      const cached = await this.redis.get(cacheKey);
      if (cached) {
        return cached === 'null' ? defaultImageUrl : cached;
      }

      // Fetch from GCP if not cached
      let imageUrl = await this.gcpStorageService.getPublicImage(
        locationId,
        'event-preview-image',
        eventId,
      );

      if (imageUrl === null) {
        imageUrl = defaultImageUrl;
      }

      // Cache for 1 hour since images don't change often
      await this.redis.set(cacheKey, imageUrl || 'null', 'EX', 3600);
      return imageUrl;
    } catch (error) {
      this.logger.debug(`Error fetching image for event ${eventId}`, error);
      return defaultImageUrl;
    }
  }

  // Cache reference data (rooms, instructors, tags, etc.) with longer TTL
  private async getCachedReferenceData(locationId: string): Promise<any> {
    const cacheKey = `event:refdata:${locationId}`;

    try {
      const cached = await this.redis.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      const locationObjectId = Types.ObjectId.createFromHexString(locationId);

      // Fetch all reference data in parallel
      const [rooms, instructors, sessions, locations, tags] = await Promise.all(
        [
          this.customFormModel
            .find({
              studio: locationObjectId,
              fieldType: 'room',
            })
            .select('_id fieldName fieldType'),

          this.customFormModel
            .find({
              studio: locationObjectId,
              fieldType: 'instructor',
            })
            .select('_id fieldName fieldType'),

          this.sessionModel
            .find({
              studioId: locationObjectId,
            })
            .select('_id name'),

          this.customFormModel
            .find({
              studio: locationObjectId,
              fieldType: 'location',
            })
            .select('_id fieldName fieldType'),

          this.tagModel
            .find({
              studioId: locationObjectId,
            })
            .select('_id fieldName'),
        ],
      );

      const referenceData = {
        rooms: rooms.map((room) => ({
          _id: room._id,
          name: room.fieldName,
          fieldType: room.fieldType,
        })),
        instructors: instructors.map((instructor) => ({
          _id: instructor._id,
          name: instructor.fieldName,
          fieldType: instructor.fieldType,
        })),
        sessions: sessions.map((session) => ({
          _id: session._id,
          name: session.name,
        })),
        locations: locations.map((location) => ({
          _id: location._id,
          name: location.fieldName,
          fieldType: location.fieldType,
        })),
        tags: tags.map((tag) => ({
          _id: tag._id,
          name: tag.fieldName,
        })),
      };

      // Cache for 30 minutes since reference data changes less frequently
      await this.redis.set(cacheKey, JSON.stringify(referenceData), 'EX', 1800);
      return referenceData;
    } catch (error) {
      this.logger.debug(
        `Error fetching reference data for location ${locationId}`,
        error,
      );
      return {
        rooms: [],
        instructors: [],
        sessions: [],
        locations: [],
        tags: [],
      };
    }
  }

  // Invalidate related caches when data changes
  async invalidateRelatedCaches(
    locationId: string,
    eventIds: string[] = [],
  ): Promise<void> {
    try {
      const patterns = [
        `event:search:${locationId}:*`,
        `event:refdata:${locationId}`,
      ];

      // Add event-specific cache patterns
      for (const eventId of eventIds) {
        patterns.push(`event:image:${locationId}:${eventId}`);
      }

      // Delete all matching keys
      for (const pattern of patterns) {
        const keys = await this.redis.keys(pattern);
        if (keys.length > 0) {
          await this.redis.del(...keys);
        }
      }
    } catch (error) {
      this.logger.warn('Error invalidating related caches:', error);
    }
  }
}
