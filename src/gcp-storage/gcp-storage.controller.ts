import {
  Controller,
  Post,
  Body,
  Get,
  Query,
  UseGuards,
  Req,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { GcpStorageService } from './gcp-storage.service';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('cloud')
@UseGuards(JwtAuthGuard)
export class GcpStorageController {
  constructor(private readonly gcpService: GcpStorageService) {}

  @Post('generate-signed-url')
  async generateSignedUrl(
    @Body('userId') userId: string,
    @Body('filename') filename: string,
    @Req() request: Request,
  ): Promise<{ signedUrl: string }> {
    const studioId = request['locationId'];
    const signedUrl = await this.gcpService.generateUploadSignedUrl(
      userId,
      filename,
      studioId,
    );
    return { signedUrl };
  }

  @Get('get-signed-url')
  async getSignedUrl(
    @Query('fileName') fileName: string,
    @Query('userId') userId: string,
    @Req() request: Request,
  ) {
    const studioId = request['locationId'];
    const signedUrl = await this.gcpService.getSignedUrl(
      fileName,
      userId,
      studioId,
    );
    return { signedUrl };
  }

  @Post('upload-file')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @Body('filename') filename: string,
    @UploadedFile() file: Express.Multer.File,
    @Req() request: Request,
    @Body('type') type?: string,
    @Body('entityId') entityId?: string,
    @Body('userId') userId?: string,
  ): Promise<{ signedUrl: string }> {
    const studioId = request['locationId'];
    const signedUrl = await this.gcpService.uploadFileToGCP(
      userId,
      file.buffer,
      filename,
      studioId,
      type,
      entityId,
    );
    return { signedUrl };
  }

  @Get('public-image')
  async getPublicImage(
    @Req() request: Request,
    @Query('type') type?: string,
    @Query('entityId') entityId?: string,
  ): Promise<{ url: string }> {
    const id = request['locationId'];
    try {
      const url = await this.gcpService.getPublicImage(id, type, entityId);
      return { url };
    } catch (error) {
      throw new Error(`Failed to get public image: ${error.message}`);
    }
  }
}
