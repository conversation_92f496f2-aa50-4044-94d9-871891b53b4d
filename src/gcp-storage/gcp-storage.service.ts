import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { Storage } from '@google-cloud/storage';
import { v4 as uuidv4 } from 'uuid';
import { GetSignedUrlConfig } from '@google-cloud/storage';
import * as path from 'path';
import { StudentsService } from 'src/students/students.service';
import { ParentsService } from 'src/parents/parents.service';
import { EmailTemplateConfigService } from 'src/email-template-config/email-template-config.service';
import { CreateEmailTemplateConfigDto } from 'src/email-template-config/dto/create-email-template-config.dto';
import { Logger } from '@nestjs/common';
import {
  ImageProcessingService,
  ProcessedImage,
} from '../utils/image-processing.service';
@Injectable()
export class GcpStorageService {
  private storage: Storage;
  private readonly defaultBucket: string = 'enrollio-portal-profile-pictures';
  private readonly logger = new Logger(GcpStorageService.name);

  constructor(
    @Inject(forwardRef(() => StudentsService))
    private readonly studentService: StudentsService,
    @Inject(forwardRef(() => ParentsService))
    private readonly parentService: ParentsService,
    private readonly emailTemplateConfigService: EmailTemplateConfigService,
    private readonly imageProcessingService: ImageProcessingService,
  ) {
    if (process.env.NODE_ENV !== 'production') {
      this.storage = new Storage({
        keyFilename: './src/config/service_key.json',
        projectId: 'enrollio-portal',
      });
    } else {
      this.storage = new Storage();
    }
  }

  private getBucketName(type?: string): string {
    switch (type) {
      case 'email-template':
        return 'email-template-image-config';
      case 'class-preview-image':
        return 'class-preview-image';
      case 'event-preview-image':
        return 'event-preview-image';
      case 'studio-logo':
        return 'studio_logo';
      default:
        return this.defaultBucket;
    }
  }

  // Helper method to get the content type
  private getContentType(filename: string): string {
    const extension = filename.split('.').pop()?.toLowerCase();
    if (extension === 'png') return 'image/png';
    if (extension === 'jpg' || extension === 'jpeg') return 'image/jpeg';
    return 'application/octet-stream';
  }

  async generateSignedUrl(
    parentId: string,
    filename: string,
    type?: string,
  ): Promise<string> {
    const bucketName = this.getBucketName(type);
    const bucket = this.storage.bucket(bucketName);

    // Define the file path in the bucket
    const uniqueFilename = `${parentId}/${filename}`;
    const file = bucket.file(uniqueFilename);

    // Define options with proper type annotation
    const options: GetSignedUrlConfig = {
      version: 'v4',
      action: 'write', // Correctly typed action
      expires: Date.now() + 15 * 60 * 1000, // URL valid for 15 minutes
      contentType: this.getContentType(filename), // Specify the expected content type
    };

    try {
      // Generate the signed URL for uploading
      const signedUrls = await file.getSignedUrl(options); // Returns [string]
      const signedUrl = signedUrls[0]; // Extract the first element
      return signedUrl;
    } catch (error) {
      console.error('Error generating signed URL:', error);
      throw new Error('Failed to generate signed URL');
    }
  }

  async uploadImage(
    base64: string,
    parentId: string,
    filename: string,
    type?: string,
  ): Promise<string> {
    const bucketName = this.getBucketName(type);
    const bucket = this.storage.bucket(bucketName);
    const uniqueFilename = `${parentId}/${filename}`;
    const file = bucket.file(uniqueFilename);
    // Convert Base64 string to a Buffer
    const buffer = Buffer.from(base64, 'base64');

    const extension = filename.split('.').pop().toLowerCase();
    let contentType: string;

    if (extension === 'png') {
      contentType = 'image/png';
    } else if (extension === 'jpg' || extension === 'jpeg') {
      contentType = 'image/jpeg';
    } else {
      contentType = 'application/octet-stream';
    }

    try {
      await file.save(buffer, {
        metadata: { contentType },
        // public: true,
      });

      const [signedUrl] = await file.getSignedUrl({
        action: 'read', // Allow only read access
        expires: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days from now
      });

      return signedUrl;
    } catch (error) {
      console.error('Error uploading file:', error);
      throw new Error('Failed to upload image');
    }
  }

  async generateUploadSignedUrl(
    userId: string | undefined,
    filename: string,
    studioId: string,
    type?: string,
  ): Promise<string> {
    const bucketName = this.getBucketName(type);
    const bucket = this.storage.bucket(bucketName);

    const id = type === 'email-template' ? studioId : userId;
    if (!id) {
      throw new Error('No valid ID provided for file upload');
    }

    const uniqueFilename =
      type === 'email-template' ? `${id}/template` : `${id}/profile`;
    const file = bucket.file(uniqueFilename);

    const extension = filename.split('.').pop()?.toLowerCase();
    const contentTypeMap: Record<string, string> = {
      png: 'image/png',
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      gif: 'image/gif',
      bmp: 'image/bmp',
      webp: 'image/webp',
    };

    const contentType =
      contentTypeMap[extension || ''] || 'application/octet-stream';

    try {
      const [signedUrl] = await file.getSignedUrl({
        action: 'write',
        expires: Date.now() + 15 * 60 * 1000,
        contentType,
      });

      return signedUrl;
    } catch (error) {
      console.error('Error generating signed URL:', error);
      throw new Error('Failed to generate upload signed URL');
    }
  }

  private async updateEntity(
    user: string,
    userId: string,
    url: string,
    studioId: string,
    type?: string,
  ): Promise<void> {
    switch (user) {
      case 'email-template':
        const createDto: CreateEmailTemplateConfigDto = {
          imageUrl: url,
          urlGeneratedAt: new Date(),
        };
        await this.emailTemplateConfigService.create(createDto, studioId);
        break;
      case 'student':
        await this.studentService.uploadProfilePic(userId, url);
        break;
      case 'parent':
        await this.parentService.uploadProfilePic(userId, url);
        break;
      default:
        throw new Error('Invalid entity type');
    }
  }

  async getSignedUrl(
    user: string,
    userId: string,
    studioId: string,
    type?: string,
  ): Promise<string> {
    const bucketName = this.getBucketName(type);
    const bucket = this.storage.bucket(bucketName);
    const uniqueFilename = `${userId}/profile`;
    const file = bucket.file(uniqueFilename);

    const [url] = await file.getSignedUrl({
      action: 'read',
      expires: Date.now() + 2 * 24 * 60 * 60 * 1000,
    });

    await this.updateEntity(user, userId, url, studioId);
    return url;
  }

  private getUniqueFilename(
    id: string,
    filename?: string,
    type?: string,
    entityId?: string,
  ): string {
    switch (type) {
      case 'email-template':
        return filename ? `${id}/template/${filename}` : `${id}/template`;
      case 'class-preview-image':
        return filename ? `${id}/${entityId}/image` : `${id}/${entityId}`;
      case 'event-preview-image':
        return filename ? `${id}/${entityId}/image` : `${id}/${entityId}`;
      case 'studio-logo':
        return filename ? `${id}/image` : `${id}/image`;
      default:
        return `${id}/profile`;
    }
  }

  async uploadFileToGCP(
    userId: string | undefined,
    file: Buffer | string,
    filename: string,
    studioId: string,
    type?: string,
    entityId?: string,
  ): Promise<string> {
    const bucketName = this.getBucketName(type);
    const bucket = this.storage.bucket(bucketName);

    const id = [
      'email-template',
      'class-preview-image',
      'event-preview-image',
      'studio-logo',
    ].includes(type || '')
      ? studioId
      : userId || studioId;

    const uniqueFilename = this.getUniqueFilename(id, filename, type, entityId);
    const fileObject = bucket.file(uniqueFilename);

    const extension = filename.split('.').pop()?.toLowerCase();
    const contentTypeMap: Record<string, string> = {
      png: 'image/png',
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      gif: 'image/gif',
      bmp: 'image/bmp',
      webp: 'image/webp',
    };

    const contentType =
      contentTypeMap[extension || ''] || 'application/octet-stream';

    try {
      // Convert base64 to Buffer if string is provided
      const fileBuffer = Buffer.isBuffer(file)
        ? file
        : Buffer.from(file.replace(/^data:.*?;base64,/, ''), 'base64');

      // Upload file directly
      await fileObject.save(fileBuffer, {
        metadata: { contentType },
      });

      // Generate a read URL for the uploaded file
      const [signedUrl] = await fileObject.getSignedUrl({
        action: 'read',
        expires: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days from now
      });

      return signedUrl;
      // return 'uploaded file successfully';
    } catch (error) {
      this.logger.error('Error uploading file:', error);
      throw new Error('Failed to upload file');
    }
  }

  /**
   * Upload WebP and original image versions
   */
  async uploadImageVariantsToGCP(
    userId: string | undefined,
    file: Buffer | string,
    filename: string,
    studioId: string,
    type?: string,
    entityId?: string,
  ): Promise<{
    webpUrl: string;
    originalUrl: string;
  }> {
    try {
      this.logger.log(`Starting image processing for ${filename}`, {
        type,
        studioId,
        entityId,
      });

      // Process image to WebP and original versions
      const processedImages =
        await this.imageProcessingService.processImageToWebPAndOriginal(
          file,
          filename,
        );

      this.logger.log(`Processed ${processedImages.length} image versions`, {
        types: processedImages.map((img) => img.type),
      });

      const bucketName = this.getBucketName(type);
      const bucket = this.storage.bucket(bucketName);

      const id = [
        'email-template',
        'class-preview-image',
        'event-preview-image',
        'studio-logo',
      ].includes(type || '')
        ? studioId
        : userId || studioId;

      const uploadPromises = processedImages.map(async (processedImage) => {
        const uniqueFilename = this.getUniqueFilenameForVersion(
          id,
          processedImage.filename,
          type,
          entityId,
          processedImage.type,
        );
        const fileObject = bucket.file(uniqueFilename);

        // Upload the processed image
        await fileObject.save(processedImage.buffer, {
          metadata: {
            contentType: processedImage.contentType,
            cacheControl: 'public, max-age=31536000', // 1 year cache for images
            customMetadata: {
              originalFilename: filename,
              type: processedImage.type,
              processedAt: new Date().toISOString(),
            },
          },
        });

        // Generate signed URL
        const [signedUrl] = await fileObject.getSignedUrl({
          action: 'read',
          expires: Date.now() + 7 * 24 * 60 * 60 * 1000, // 7 days
        });

        return {
          type: processedImage.type,
          url: signedUrl,
          filename: processedImage.filename,
        };
      });

      const uploadResults = await Promise.all(uploadPromises);

      // Find WebP and original variants
      const webpVariant = uploadResults.find(
        (result) => result.type === 'webp',
      );
      const originalVariant = uploadResults.find(
        (result) => result.type === 'original',
      );

      this.logger.log(
        `Successfully uploaded ${uploadResults.length} image versions`,
        {
          type,
          studioId,
          entityId,
          types: uploadResults.map((r) => r.type),
        },
      );

      return {
        webpUrl: webpVariant?.url || '',
        originalUrl: originalVariant?.url || '',
      };
    } catch (error) {
      this.logger.error('Error uploading image versions:', {
        error: error.message,
        stack: error.stack,
        filename,
        type,
        studioId,
        entityId,
      });
      throw new Error(`Failed to upload image versions: ${error.message}`);
    }
  }

  /**
   * Generate unique filename for image versions (webp/original)
   */
  private getUniqueFilenameForVersion(
    id: string,
    filename: string,
    type?: string,
    entityId?: string,
    version?: string,
  ): string {
    switch (type) {
      case 'email-template':
        return `${id}/template/${version}/${filename}`;
      case 'class-preview-image':
        return `${id}/${entityId}/${version}/${filename}`;
      case 'event-preview-image':
        return `${id}/${entityId}/${version}/${filename}`;
      case 'studio-logo':
        return `${id}/${version}/${filename}`;
      default:
        return `${id}/${version}/${filename}`;
    }
  }

  async getEmailTemplateImageUrl(
    studioId: string,
    filename: string,
  ): Promise<string> {
    const bucketName = 'email-template-image-config';
    const bucket = this.storage.bucket(bucketName);
    const filePath = `${studioId}/template/${filename}`;
    const file = bucket.file(filePath);

    try {
      const [signedUrl] = await file.getSignedUrl({
        action: 'read',
        expires: Date.now() + 2 * 24 * 60 * 60 * 1000,
      });

      return signedUrl;
    } catch (error) {
      console.error('Error generating email template image URL:', error);
      throw new Error('Failed to generate email template image URL');
    }
  }

  async deleteFileFromGCP(
    id: string,
    type: string,
    filename?: string,
    entityId?: string,
  ): Promise<void> {
    const bucketName = this.getBucketName(type);
    const bucket = this.storage.bucket(bucketName);

    const studioId = [
      'email-template',
      'class-preview-image',
      'event-preview-image',
    ].includes(type)
      ? id
      : id;

    const filePath = this.getUniqueFilename(studioId, filename, type, entityId);

    try {
      if (type !== 'email-template') {
        // Handle directory-based deletion for preview images and other types
        const [files] = await bucket.getFiles({
          prefix: filePath,
        });

        if (files.length > 0) {
          await Promise.all(files.map((file) => file.delete()));
          this.logger.log(`Successfully deleted files from GCP`, {
            filePath,
            type,
            studioId,
            entityId,
            filesDeleted: files.length,
          });
        } else {
          this.logger.debug(`No files exist in directory in GCP`, {
            filePath,
            type,
            studioId,
            entityId,
          });
        }
      } else {
        // Original single-file deletion logic for email templates
        const file = bucket.file(filePath);
        const [exists] = await file.exists();

        if (exists) {
          await file.delete();
          this.logger.log(`Successfully deleted file from GCP`, {
            filePath,
            type,
            studioId,
            entityId,
          });
        } else {
          this.logger.debug(`File does not exist in GCP`, {
            filePath,
            type,
            studioId,
            entityId,
          });
        }
      }
    } catch (error) {
      this.logger.error(`Failed to delete from GCP`, {
        error: error.message,
        stack: error.stack,
        filePath,
        type,
        studioId,
        entityId,
      });
      // Don't throw error to prevent blocking operations
    }
  }

  async getPublicImage(
    id: string,
    type?: string,
    entityId?: string,
  ): Promise<string> {
    const bucketName = this.getBucketName(type);
    const bucket = this.storage.bucket(bucketName);

    const prefix = entityId ? `${id}/${entityId}/` : `${id}/`;

    try {
      const [files] = await bucket.getFiles({ prefix });

      if (!files || files.length === 0) {
        this.logger.error('Image not found', {
          studioId: id,
          type,
        });
        // throw new Error('Image not found');
        return null;
      }

      const file = files[0];

      const [signedUrl] = await file.getSignedUrl({
        action: 'read',
        expires: Date.now() + 24 * 60 * 60 * 1000,
      });

      return signedUrl;
    } catch (error) {
      throw new Error('Failed to generate public image URL');
    }
  }

  /**
   * Get a specific image variant by size
   */
  async getImageVariant(
    id: string,
    type: string,
    entityId: string,
    size: string = 'original',
  ): Promise<string | null> {
    const bucketName = this.getBucketName(type);
    const bucket = this.storage.bucket(bucketName);

    const prefix = `${id}/${entityId}/${size}/`;

    try {
      const [files] = await bucket.getFiles({ prefix });

      if (!files || files.length === 0) {
        this.logger.debug(`Image variant not found for size: ${size}`, {
          studioId: id,
          type,
          entityId,
          size,
        });
        return null;
      }

      const file = files[0];

      const [signedUrl] = await file.getSignedUrl({
        action: 'read',
        expires: Date.now() + 24 * 60 * 60 * 1000,
      });

      return signedUrl;
    } catch (error) {
      this.logger.error('Failed to get image variant', {
        error: error.message,
        studioId: id,
        type,
        entityId,
        size,
      });
      return null;
    }
  }

  /**
   * Get all available image variants for an entity
   */
  async getAllImageVariants(
    id: string,
    type: string,
    entityId: string,
  ): Promise<{ size: string; url: string }[]> {
    const bucketName = this.getBucketName(type);
    const bucket = this.storage.bucket(bucketName);

    const prefix = `${id}/${entityId}/`;

    try {
      const [files] = await bucket.getFiles({ prefix });

      if (!files || files.length === 0) {
        this.logger.debug('No image variants found', {
          studioId: id,
          type,
          entityId,
        });
        return [];
      }

      const variants = await Promise.all(
        files.map(async (file) => {
          // Extract size from file path (format: studioId/entityId/size/filename)
          const pathParts = file.name.split('/');
          const size = pathParts[pathParts.length - 2]; // Second to last part is the size

          const [signedUrl] = await file.getSignedUrl({
            action: 'read',
            expires: Date.now() + 24 * 60 * 60 * 1000,
          });

          return {
            size,
            url: signedUrl,
          };
        }),
      );

      return variants;
    } catch (error) {
      this.logger.error('Failed to get all image variants', {
        error: error.message,
        studioId: id,
        type,
        entityId,
      });
      return [];
    }
  }
}
