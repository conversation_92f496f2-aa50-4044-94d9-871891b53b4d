// import { Test, TestingModule } from '@nestjs/testing';
// import { GcpStorageController } from './gcp-storage.controller';
// import { GcpStorageService } from './gcp-storage.service';

// describe('GcpStorageController', () => {
//   let controller: GcpStorageController;

//   beforeEach(async () => {
//     const module: TestingModule = await Test.createTestingModule({
//       controllers: [GcpStorageController],
//       providers: [GcpStorageService],
//     }).compile();

//     controller = module.get<GcpStorageController>(GcpStorageController);
//   });

//   it('should be defined', () => {
//     expect(controller).toBeDefined();
//   });
// });
