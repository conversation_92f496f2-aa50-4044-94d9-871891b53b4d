import { forwardRef, Module } from '@nestjs/common';
import { GcpStorageService } from './gcp-storage.service';
import { GcpStorageController } from './gcp-storage.controller';
import { StudentsModule } from 'src/students/students.module';
import { ParentsModule } from 'src/parents/parents.module';
import { EmailTemplateConfigModule } from 'src/email-template-config/email-template-config.module';
import { AuthModule } from 'src/auth/auth.module';
import { ImageProcessingModule } from '../utils/image-processing.module';

@Module({
  imports: [
    forwardRef(() => StudentsModule),
    forwardRef(() => ParentsModule),
    EmailTemplateConfigModule,
    AuthModule,
    ImageProcessingModule,
  ],
  controllers: [GcpStorageController],
  providers: [GcpStorageService],
  exports: [GcpStorageService],
})
export class GcpStorageModule {}
