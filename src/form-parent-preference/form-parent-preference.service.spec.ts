import { Test, TestingModule } from '@nestjs/testing';
import { FormParentPreferenceService } from './form-parent-preference.service';

describe('FormParentPreferenceService', () => {
  let service: FormParentPreferenceService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [FormParentPreferenceService],
    }).compile();

    service = module.get<FormParentPreferenceService>(
      FormParentPreferenceService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
