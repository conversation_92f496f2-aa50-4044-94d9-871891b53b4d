import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Put,
  Delete,
  UseGuards,
  Req,
} from '@nestjs/common';
import { FormParentPreferenceService } from './form-parent-preference.service';
import { CreateFormParentPreferenceDto } from './dto/create-form-parent-preference.dto';
import { JwtAuthGuard } from 'src/auth/auth.guard';

@Controller('custom-form-config')
export class FormParentPreferenceController {
  constructor(
    private readonly formParentPreferenceService: FormParentPreferenceService,
  ) {}

  @UseGuards(JwtAuthGuard)
  @Post()
  create(
    @Body() createDto: CreateFormParentPreferenceDto,
    @Req() request: Request,
  ) {
    const studioId = request['locationId'];
    return this.formParentPreferenceService.createOrUpdate(createDto, studioId);
  }

  @UseGuards(JwtAuthGuard)
  @Get(':entity')
  findOne(@Param('entity') entity: string, @Req() request: Request) {
    const studioId = request['locationId'];
    return this.formParentPreferenceService.findOne(entity, studioId);
  }

  @Get('/studio/:entity/:id')
  findOneByLocationId(
    @Param('id') id: string,
    @Param('entity') entity: string,
    @Req() request: Request,
  ) {
    return this.formParentPreferenceService.findOneByLocation(id, entity);
  }

  @UseGuards(JwtAuthGuard)
  @Get('studio/:studioId')
  findByStudio(@Req() request: Request) {
    const studioId = request['locationId'];
    return this.formParentPreferenceService.findByStudio(studioId);
  }

  @UseGuards(JwtAuthGuard)
  @Put(':id')
  update(
    @Param('id') id: string,
    @Body() updateDto: CreateFormParentPreferenceDto,
    @Req() request: Request,
  ) {
    const studioId = request['locationId'];
    return this.formParentPreferenceService.update(id, updateDto, studioId);
  }

  @UseGuards(JwtAuthGuard)
  @Delete(':id')
  remove(@Param('id') id: string, @Req() request: Request) {
    const studioId = request['locationId'];
    return this.formParentPreferenceService.remove(id, studioId);
  }
}
