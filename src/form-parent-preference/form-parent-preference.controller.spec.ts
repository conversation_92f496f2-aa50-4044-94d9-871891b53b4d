import { Test, TestingModule } from '@nestjs/testing';
import { FormParentPreferenceController } from './form-parent-preference.controller';
import { FormParentPreferenceService } from './form-parent-preference.service';

describe('FormParentPreferenceController', () => {
  let controller: FormParentPreferenceController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [FormParentPreferenceController],
      providers: [FormParentPreferenceService],
    }).compile();

    controller = module.get<FormParentPreferenceController>(
      FormParentPreferenceController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
