import { Types } from 'mongoose';

interface OptionType {
  label: string;
  value: string;
}

export class CreateFormParentPreferenceDto {
  studioId?: Types.ObjectId;
  formFields: {
    fieldId: string;
    label: string;
    type:
      | 'text'
      | 'number'
      | 'date'
      | 'boolean'
      | 'select'
      | 'multiselect'
      | 'time'
      | 'checkbox'
      | 'text-area'
      | 'radio';
    required: boolean;
    options?: string[] | OptionType[]; // Now accepts either format
    defaultValue?: any;
    order: number;
    visible: boolean;
    section?: string;
  }[];
  isActive?: boolean;
  entity: string;
}
