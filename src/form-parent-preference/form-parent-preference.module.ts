// form-parent-preference.module.ts
import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { FormParentPreferenceController } from './form-parent-preference.controller';
import { FormParentPreferenceService } from './form-parent-preference.service';
import {
  FormParentPreference,
  FormParentPreferenceSchema,
} from 'src/database/schema/customParentForm';
import { AuthModule } from 'src/auth/auth.module';
import { StudiosModule } from 'src/studios/studios.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: FormParentPreference.name, schema: FormParentPreferenceSchema },
    ]),
    AuthModule,
    StudiosModule,
  ],
  controllers: [FormParentPreferenceController],
  providers: [FormParentPreferenceService],
  exports: [FormParentPreferenceService],
})
export class FormParentPreferenceModule {}
