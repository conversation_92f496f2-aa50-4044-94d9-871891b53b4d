import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { FormParentPreference } from 'src/database/schema/customParentForm';
import { CreateFormParentPreferenceDto } from './dto/create-form-parent-preference.dto';
import { StudiosService } from 'src/studios/studios.service';

@Injectable()
export class FormParentPreferenceService {
  constructor(
    @InjectModel(FormParentPreference.name)
    private formParentPreferenceModel: Model<FormParentPreference>,
    private readonly studioService: StudiosService,
  ) {}

  async createOrUpdate(
    dto: CreateFormParentPreferenceDto,
    studioId: string,
  ): Promise<FormParentPreference> {
    try {
      const studioObjectId = Types.ObjectId.createFromHexString(studioId);
      dto.studioId = studioObjectId;

      return await this.formParentPreferenceModel.findOneAndUpdate(
        { studioId: studioObjectId, entity: dto.entity },
        dto,
        { upsert: true, new: true },
      );
    } catch (error) {
      console.error('error creating or updating custom form.', error);
    }
  }

  async findOne(id: string, studioId: string): Promise<FormParentPreference> {
    const studioObjectId = Types.ObjectId.createFromHexString(studioId);
    const form = await this.formParentPreferenceModel.findOne({
      entity: id,
      studioId: studioObjectId,
    });
    if (!form) throw new NotFoundException();
    return form;
  }

  private getDefaultFormStructure(): CreateFormParentPreferenceDto {
    return {
      formFields: [
        {
          fieldId: 'firstName',
          label: 'First Name',
          section: 'General Info',
          order: 1,
          required: true,
          type: 'text',
          visible: true,
        },
        {
          fieldId: 'lastName',
          label: 'Last Name',
          section: 'General Info',
          order: 2,
          required: true,
          type: 'text',
          visible: true,
        },
        {
          fieldId: 'email',
          label: 'Email',
          section: 'General Info',
          order: 3,
          required: true,
          type: 'text',
          visible: true,
        },
        {
          fieldId: 'relation',
          label: 'Relation',
          section: 'General Info',
          order: 4,
          required: true,
          type: 'select',
          options: [
            { value: 'father', label: 'Father' },
            { value: 'mother', label: 'Mother' },
            { value: 'guardian', label: 'Guardian' },
            { value: 'other', label: 'Other' },
          ],
          visible: true,
        },
        {
          fieldId: 'primaryPhone',
          label: 'Primary Phone',
          section: 'General Info',
          order: 5,
          required: true,
          type: 'text',
          visible: true,
        },
        {
          fieldId: 'street',
          label: 'Street',
          section: 'Address',
          order: 1,
          required: false,
          type: 'text',
          visible: true,
        },
        {
          fieldId: 'apartment',
          label: 'Apartment',
          section: 'Address',
          order: 2,
          required: false,
          type: 'text',
          visible: true,
        },
        {
          fieldId: 'city',
          label: 'City/Suburb',
          section: 'Address',
          order: 3,
          required: false,
          type: 'text',
          visible: true,
        },
        {
          fieldId: 'country',
          label: 'Country',
          section: 'Address',
          order: 4,
          required: true,
          type: 'text',
          visible: true,
        },
        {
          fieldId: 'zip',
          label: 'Zip/Postcode',
          section: 'Address',
          order: 5,
          required: true,
          type: 'text',
          visible: true,
        },
        {
          fieldId: 'state',
          label: 'State/Region',
          section: 'Address',
          order: 6,
          required: true,
          type: 'text',
          visible: true,
        },
        {
          fieldId: 'firstName',
          label: 'First Name',
          section: 'Emergency Contacts',
          order: 1,
          required: false,
          type: 'text',
          visible: true,
        },
        {
          fieldId: 'lastName',
          label: 'Last Name',
          section: 'Emergency Contacts',
          order: 2,
          required: false,
          type: 'text',
          visible: true,
        },
        {
          fieldId: 'primaryPhone',
          label: 'Primary Phone',
          section: 'Emergency Contacts',
          order: 3,
          required: false,
          type: 'text',
          visible: true,
        },
        {
          fieldId: 'email',
          label: 'Email',
          section: 'Emergency Contacts',
          order: 4,
          required: false,
          type: 'text',
          visible: true,
        },
        {
          fieldId: 'authorizePickup',
          label: 'Authorize Pickup',
          section: 'Emergency Contacts',
          order: 5,
          required: false,
          type: 'checkbox',
          visible: true,
        },
        {
          fieldId: 'firstName',
          label: 'First Name',
          section: 'Students',
          order: 1,
          required: true,
          type: 'text',
          visible: true,
        },
        {
          fieldId: 'lastName',
          label: 'Last Name',
          section: 'Students',
          order: 2,
          required: true,
          type: 'text',
          visible: true,
        },
        {
          fieldId: 'dob',
          label: 'Date of Birth',
          section: 'Students',
          order: 3,
          required: true,
          type: 'date',
          visible: true,
        },
        {
          fieldId: 'gender',
          label: 'Gender',
          section: 'Students',
          order: 4,
          required: true,
          type: 'select',
          visible: true,
          options: [
            { value: 'male', label: 'Male' },
            { value: 'female', label: 'Female' },
            { value: 'other', label: 'Other' },
          ],
        },
        {
          fieldId: 'email',
          label: 'Student Email',
          section: 'Students',
          order: 5,
          required: false,
          type: 'text',
          visible: true,
        },
        {
          fieldId: 'primaryPhone',
          label: 'Student Phone',
          section: 'Students',
          order: 6,
          required: false,
          type: 'text',
          visible: true,
        },
        {
          fieldId: 'costumeSize',
          label: 'Costume Size',
          section: 'Students',
          order: 7,
          required: false,
          type: 'select',
          visible: true,
          options: [
            { value: 'child-xs', label: 'Child XS (2-4)' },
            { value: 'child-s', label: 'Child S (4-6)' },
            { value: 'child-m', label: 'Child M (6-8)' },
            { value: 'child-l', label: 'Child L (8-10)' },
            { value: 'child-xl', label: 'Child XL (12-14)' },
            { value: 'adult-xs', label: 'Adult XS' },
            { value: 'adult-s', label: 'Adult S' },
            { value: 'adult-m', label: 'Adult M' },
            { value: 'need-sizing', label: 'Not Sure, Need Sizing Help' },
          ],
        },
        {
          fieldId: 'transportation',
          label: 'Transportation',
          section: 'Students',
          order: 8,
          required: false,
          type: 'radio',
          visible: true,
        },
        {
          fieldId: 'healthInsuranceCarrier',
          label: 'Health Insurance Carrier',
          section: 'Students',
          order: 9,
          required: false,
          type: 'text',
          visible: true,
        },
        {
          fieldId: 'primaryDoctor',
          label: 'Primary Doctor',
          section: 'Students',
          order: 10,
          required: false,
          type: 'text',
          visible: true,
        },
        {
          fieldId: 'allergies',
          label: 'Allergies',
          section: 'Students',
          order: 11,
          required: false,
          type: 'text-area',
          visible: true,
        },
        {
          fieldId: 'disabilities',
          label: 'Disabilities',
          section: 'Students',
          order: 12,
          required: false,
          type: 'text-area',
          visible: true,
        },
        {
          fieldId: 'medications',
          label: 'Medications',
          section: 'Students',
          order: 13,
          required: false,
          type: 'text-area',
          visible: true,
        },
        {
          fieldId: 'specialNeeds',
          label: 'Special Needs',
          section: 'Students',
          order: 14,
          required: false,
          type: 'text-area',
          visible: true,
        },
        {
          fieldId: 'enrollments',
          label: 'Enrollments',
          section: 'Students',
          order: 15,
          required: true,
          type: 'select',
          visible: true,
        },
      ],
      isActive: true,
      entity: 'parent',
    };
  }

  async findOneByLocation(
    id: string,
    entity: string,
  ): Promise<FormParentPreference> {
    try {
      const studio = await this.studioService.findByLocationIdString(id);
      const studioId = studio._id;
      const form = await this.formParentPreferenceModel.findOne({
        entity: entity,
        studioId: studioId,
      });

      if (!form) {
        console.log('no form found so return default form.');
        return new this.formParentPreferenceModel(
          this.getDefaultFormStructure(),
        );
      }
      return form;
    } catch (error) {
      console.error('error getting the custom form config.', error);
      return new this.formParentPreferenceModel(this.getDefaultFormStructure());
    }
  }

  async findByStudio(studioId: string): Promise<FormParentPreference> {
    const studioObjectId = Types.ObjectId.createFromHexString(studioId);
    const form = await this.formParentPreferenceModel.findOne({
      studioId: studioObjectId,
    });
    if (!form) throw new NotFoundException();
    return form;
  }

  async update(
    id: string,
    updateDto: CreateFormParentPreferenceDto,
    studioId: string,
  ): Promise<FormParentPreference> {
    const studioObjectId = Types.ObjectId.createFromHexString(studioId);
    const updated = await this.formParentPreferenceModel.findOneAndUpdate(
      { _id: id, studioId: studioObjectId },
      updateDto,
      { new: true },
    );
    if (!updated) throw new NotFoundException();
    return updated;
  }

  async remove(id: string, studioId: string): Promise<FormParentPreference> {
    const studioObjectId = Types.ObjectId.createFromHexString(studioId);
    const deleted = await this.formParentPreferenceModel.findOneAndDelete({
      _id: id,
      studioId: studioObjectId,
    });
    if (!deleted) throw new NotFoundException();
    return deleted;
  }
}
