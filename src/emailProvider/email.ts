import axios from 'axios';

export async function sendTemporaryPasswordEmail(
  emailTo: string,
  password: string,
  studio: string,
  parent: string,
  paragraph1: string,
  paragraph2: string,
  subject: string,
  studioLocationId: string,
  studioLogoUrl: string,
) {
  try {
    if (!process.env.SENDGRID_API_KEY) {
      throw new Error('SendGrid API key is not configured');
    }

    console.log('Sending temporary password email with config:', {
      to: emailTo,
      studio,
      parent,
      apiKeyPresent: !!process.env.SENDGRID_API_KEY,
    });

    const msg = {
      personalizations: [
        {
          to: [
            {
              email: emailTo,
              name: parent,
            },
          ],
          dynamic_template_data: {
            password: password,
            studio_name: studio,
            studio_logo: studioLogoUrl,
            paragraph1: paragraph1,
            paragraph2: paragraph2,
            subject: subject,
            year: new Date().getFullYear(),
            parentPortalUrl: `${process.env.PARENT_PORTAL_URL}/login?studioId=${studioLocationId}`,
          },
        },
      ],
      from: {
        email: '<EMAIL>',
        name: '<PERSON><PERSON><PERSON>',
      },
      subject: subject,
      template_id: 'd-f5a9245d6c154c0ba369b8259e1c80a1',
      mail_settings: {
        sandbox_mode: {
          enable: false,
        },
      },
      tracking_settings: {
        click_tracking: {
          enable: true,
          enable_text: true,
        },
        open_tracking: {
          enable: true,
        },
      },
    };

    const config = {
      method: 'post',
      url: 'https://api.sendgrid.com/v3/mail/send',
      headers: {
        Authorization: `Bearer ${process.env.SENDGRID_API_KEY}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      data: msg,
    };

    console.log('Full request config:', {
      url: config.url,
      headers: { ...config.headers, Authorization: 'Bearer [REDACTED]' },
      data: {
        ...config.data,
        personalizations: [
          {
            ...config.data.personalizations[0],
            dynamic_template_data: {
              ...config.data.personalizations[0].dynamic_template_data,
              password: '[REDACTED]',
            },
          },
        ],
      },
    });

    const response = await axios(config);

    console.log('Email sent successfully:', {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
    });

    return true;
  } catch (error) {
    console.error('Detailed error information:', {
      error: error.message,
      response: error.response?.data,
      status: error.response?.status,
      headers: error.response?.headers,
    });

    if (axios.isAxiosError(error)) {
      console.error('Axios error details:', {
        config: {
          url: error.config?.url,
          method: error.config?.method,
          headers: {
            ...error.config?.headers,
            Authorization: '[REDACTED]',
          },
          data: {
            ...JSON.parse(error.config?.data || '{}'),
            personalizations: [
              {
                ...JSON.parse(error.config?.data || '{}').personalizations[0],
                dynamic_template_data: {
                  ...JSON.parse(error.config?.data || '{}').personalizations[0]
                    .dynamic_template_data,
                  password: '[REDACTED]',
                },
              },
            ],
          },
        },
        response: error.response?.data,
      });
    }

    return false;
  }
}

export async function sendResetPasswordEmail(
  emailTo: string,
  reset_url: string,
  studio: string,
  parent: string,
  studioLogoUrl: string,
) {
  try {
    if (!process.env.SENDGRID_API_KEY) {
      throw new Error('SendGrid API key is not configured');
    }

    console.log('Sending email with config:', {
      to: emailTo,
      reset_url,
      studio,
      parent,
      apiKeyPresent: !!process.env.SENDGRID_API_KEY,
    });

    const msg = {
      personalizations: [
        {
          to: [
            {
              email: emailTo,
              name: parent,
            },
          ],

          dynamic_template_data: {
            reset_url: reset_url,
            studio_name: studio,
            parentName: parent,
            year: new Date().getFullYear(),
            studio_logo: studioLogoUrl,
          },
        },
      ],
      from: {
        email: '<EMAIL>',
        name: 'Enrollio',
      },
      template_id: 'd-13922e4d9d14416d8ca6664da2e48804',
    };

    const config = {
      method: 'post',
      url: 'https://api.sendgrid.com/v3/mail/send',
      headers: {
        Authorization: `Bearer ${process.env.SENDGRID_API_KEY}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      data: msg,
    };

    const response = await axios(config);

    return response;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Axios error details:', {
        config: {
          url: error.config?.url,
          method: error.config?.method,
          headers: {
            ...error.config?.headers,
            Authorization: '[REDACTED]',
          },
          data: error.config?.data,
        },
        response: error.response?.data,
      });
    }

    return false;
  }
}

export async function sendTransactionEmail(
  emailTo: string,
  transactions: any[],
  currency: string,
  parentName: string,
  studioName: string,
  studioLogoUrl: string,
) {
  try {
    if (!process.env.SENDGRID_API_KEY) {
      throw new Error('SendGrid API key is not configured');
    }

    let currencySymbol = '';
    switch (currency) {
      case 'USD':
        currencySymbol = 'US$';
        break;
      case 'EUR':
        currencySymbol = '€';
        break;
      case 'GBP':
        currencySymbol = '£';
        break;
      case 'CAD':
        currencySymbol = 'CA$';
        break;
      case 'AUD':
        currencySymbol = 'A$';
        break;
      case 'NZD':
        currencySymbol = 'NZ$';
        break;
      default:
        currencySymbol = 'US$';
    }

    // Get regular items (non-discounts)
    const items = transactions
      .filter((t) => t.type !== 'Discount')
      .map((transaction) => ({
        type: transaction.type,
        product: `${transaction.name} - ${transaction.product}`,
        amount: `${currencySymbol}${transaction.amount.toFixed(2)}`,
      }));

    // Get unique student names
    const studentNames = [...new Set(transactions.map((t) => t.name))].join(
      ', ',
    );

    // Calculate total before discount
    const subtotal = transactions
      .filter((t) => t.status === 'paid')
      .reduce((sum, t) => sum + t.amount, 0);

    // Get total discount amount
    const totalDiscount = transactions
      .filter((t) => t.type === 'Discount')
      .reduce((sum, t) => sum + t.amount, 0);

    const msg = {
      personalizations: [
        {
          to: [
            {
              email: emailTo,
              name: parentName,
            },
          ],
          dynamic_template_data: {
            studio: studioLogoUrl ? null : studioName,
            studentName: studentNames,
            studio_logo: studioLogoUrl,
            items,
            subtotal: subtotal.toFixed(2),
            discount: totalDiscount > 0 ? totalDiscount.toFixed(2) : null,
            total: (subtotal - totalDiscount).toFixed(2),
            transactionId:
              transactions.length > 0 ? transactions[0].transactionId : '',
            parentPortalUrl: process.env.PARENT_PORTAL_URL,
            year: new Date().getFullYear(),
            subject: 'Invoice from ' + studioName,
          },
        },
      ],
      from: {
        email: '<EMAIL>',
        name: 'Enrollio',
      },
      subject: 'Invoice from ' + studioName,
      template_id: 'd-a04eb4e37f414114a80f8fd81e8adac2',
      mail_settings: {
        sandbox_mode: {
          enable: false,
        },
      },
      tracking_settings: {
        click_tracking: {
          enable: true,
          enable_text: true,
        },
        open_tracking: {
          enable: true,
        },
      },
    };

    const config = {
      method: 'post',
      url: 'https://api.sendgrid.com/v3/mail/send',
      headers: {
        Authorization: `Bearer ${process.env.SENDGRID_API_KEY}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      data: msg,
    };

    console.log('Full request config:', {
      url: config.url,
      headers: { ...config.headers, Authorization: 'Bearer [REDACTED]' },
      data: config.data,
    });

    const response = await axios(config);

    console.log('Transaction email sent successfully:', {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
    });

    return true;
  } catch (error) {
    console.error('Detailed error information:', {
      error: error.message,
      response: error.response?.data,
      status: error.response?.status,
      headers: error.response?.headers,
    });

    if (axios.isAxiosError(error)) {
      console.error('Axios error details:', {
        config: {
          url: error.config?.url,
          method: error.config?.method,
          headers: {
            ...error.config?.headers,
            Authorization: '[REDACTED]',
          },
          data: error.config?.data,
        },
        response: error.response?.data,
      });
    }

    return false;
  }
}

export async function sendDropClassEmail(
  emailTo: string,
  parentName: string,
  studioName: string,
  studioLogoUrl: string,
  classDetails: {
    className: string;
    session: string;
    startDate: string;
    endDate: string;
    tuitionFee: number;
    location: string;
    instructor: string;
    currency: string;
  },
) {
  try {
    if (!process.env.SENDGRID_API_KEY) {
      throw new Error('SendGrid API key is not configured');
    }

    let currencySymbol = '';
    switch (classDetails.currency) {
      case 'USD':
        currencySymbol = 'US$';
        break;
      case 'EUR':
        currencySymbol = '€';
        break;
      case 'GBP':
        currencySymbol = '£';
        break;
      case 'CAD':
        currencySymbol = 'CA$';
        break;
      case 'AUD':
        currencySymbol = 'A$';
        break;
      case 'NZD':
        currencySymbol = 'NZ$';
        break;
      default:
        currencySymbol = 'US$';
    }

    const msg = {
      personalizations: [
        {
          to: [
            {
              email: emailTo,
              name: parentName,
            },
          ],
          dynamic_template_data: {
            studio_name: studioName,
            studio_logo: studioLogoUrl,
            className: classDetails.className,
            session: classDetails.session,
            startDate: classDetails.startDate,
            endDate: classDetails.endDate,
            tuitionFee: `${currencySymbol}${classDetails.tuitionFee.toFixed(2)}`,
            location: classDetails.location,
            instructor: classDetails.instructor,
            year: new Date().getFullYear(),
          },
        },
      ],
      from: {
        email: '<EMAIL>',
        name: 'Enrollio',
      },
      subject: 'Program Update Confirmation',
      template_id: 'd-5a35c804c9ef4f99a3dc4b10a4ac08ac',
      mail_settings: {
        sandbox_mode: {
          enable: false,
        },
      },
      tracking_settings: {
        click_tracking: {
          enable: true,
          enable_text: true,
        },
        open_tracking: {
          enable: true,
        },
      },
    };

    const config = {
      method: 'post',
      url: 'https://api.sendgrid.com/v3/mail/send',
      headers: {
        Authorization: `Bearer ${process.env.SENDGRID_API_KEY}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      data: msg,
    };

    const response = await axios(config);
    return true;
  } catch (error) {
    console.error('Error sending drop class email:', error);
    return false;
  }
}

export async function sendClassTransferEmail(
  emailTo: string,
  parentName: string,
  studioName: string,
  studioLogoUrl: string,
  classDetails: {
    oldClass: {
      className: string;
      session: string;
      startDate: string;
      endDate: string;
      tuitionFee: number;
      location: string;
      instructor: string;
    };
    newClass: {
      className: string;
      session: string;
      startDate: string;
      endDate: string;
      tuitionFee: number;
      location: string;
      instructor: string;
    };
    currency: string;
  },
) {
  try {
    if (!process.env.SENDGRID_API_KEY) {
      throw new Error('SendGrid API key is not configured');
    }

    let currencySymbol = '';
    switch (classDetails.currency) {
      case 'USD':
        currencySymbol = 'US$';
        break;
      case 'EUR':
        currencySymbol = '€';
        break;
      case 'GBP':
        currencySymbol = '£';
        break;
      case 'CAD':
        currencySymbol = 'CA$';
        break;
      case 'AUD':
        currencySymbol = 'A$';
        break;
      case 'NZD':
        currencySymbol = 'NZ$';
        break;
      default:
        currencySymbol = 'US$';
    }

    const msg = {
      personalizations: [
        {
          to: [
            {
              email: emailTo,
              name: parentName,
            },
          ],
          dynamic_template_data: {
            studio_name: studioName,
            studio_logo: studioLogoUrl,
            oldClassName: classDetails.oldClass.className,
            oldSession: classDetails.oldClass.session,
            oldStartDate: classDetails.oldClass.startDate,
            oldEndDate: classDetails.oldClass.endDate,
            oldTuitionFee: `${currencySymbol}${classDetails.oldClass.tuitionFee.toFixed(2)}`,
            oldLocation: classDetails.oldClass.location,
            oldInstructor: classDetails.oldClass.instructor,
            newClassName: classDetails.newClass.className,
            newSession: classDetails.newClass.session,
            newStartDate: classDetails.newClass.startDate,
            newEndDate: classDetails.newClass.endDate,
            newTuitionFee: `${currencySymbol}${classDetails.newClass.tuitionFee.toFixed(2)}`,
            newLocation: classDetails.newClass.location,
            newInstructor: classDetails.newClass.instructor,
            year: new Date().getFullYear(),
          },
        },
      ],
      from: {
        email: '<EMAIL>',
        name: 'Enrollio',
      },
      subject: 'Program Transfer Confirmation',
      template_id: 'd-d3189a2218f341969322d1b1167df3c8',
      mail_settings: {
        sandbox_mode: {
          enable: false,
        },
      },
      tracking_settings: {
        click_tracking: {
          enable: true,
          enable_text: true,
        },
        open_tracking: {
          enable: true,
        },
      },
    };

    const config = {
      method: 'post',
      url: 'https://api.sendgrid.com/v3/mail/send',
      headers: {
        Authorization: `Bearer ${process.env.SENDGRID_API_KEY}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      data: msg,
    };

    const response = await axios(config);
    return true;
  } catch (error) {
    console.error('Error sending class transfer email:', error);
    return false;
  }
}

export async function sendPaymentMethodReminderEmail(
  emailTo: string,
  parentName: string,
  studioName: string,
  portalUrl: string,
) {
  try {
    if (!process.env.SENDGRID_API_KEY) {
      throw new Error('SendGrid API key is not configured');
    }

    const msg = {
      personalizations: [
        {
          to: [{ email: emailTo, name: parentName }],
          dynamic_template_data: {
            studio: studioName,
            parentPortalUrl: portalUrl,
            year: new Date().getFullYear(),
          },
        },
      ],
      from: {
        email: '<EMAIL>',
        name: 'Enrollio',
      },
      subject: 'Add Your Payment Method',
      template_id: 'd-9ab3de34620747c098990298eedad7ae',
    };

    const response = await axios.post(
      'https://api.sendgrid.com/v3/mail/send',
      msg,
      {
        headers: {
          Authorization: `Bearer ${process.env.SENDGRID_API_KEY}`,
          'Content-Type': 'application/json',
        },
      },
    );

    return true;
  } catch (error) {
    console.error('Error sending payment method reminder:', error);
    return false;
  }
}

interface ClassDetail {
  name: string;
  startDate: string;
  endDate: string;
  days: string;
  time: string;
  location: string;
}

interface StudentEnrollment {
  studentName: string;
  classes: ClassDetail[];
}

export interface PolicyDetail {
  name: string;
  applicableClasses: string;
  description: string;
}

export async function sendPoliciesEmail(
  emailTo: string,
  parentName: string,
  studioName: string,
  studioLogoUrl: string,
  studentEnrollments: StudentEnrollment[],
  policies: PolicyDetail[],
) {
  try {
    if (!process.env.SENDGRID_API_KEY) {
      throw new Error('SendGrid API key is not configured');
    }

    // Process policies to convert ## to <br/> and format text
    const formattedPolicies = policies.map((policy) => ({
      ...policy,
      description: policy.description
        .replace(/\n/g, '<br/>') // Convert newlines to HTML line breaks
        .trim(),
    }));

    const msg = {
      personalizations: [
        {
          to: [
            {
              email: emailTo,
              name: parentName,
            },
          ],
          dynamic_template_data: {
            studio_name: studioName,
            studio_logo: studioLogoUrl,
            parentName: parentName,
            studentEnrollments: studentEnrollments,
            policies: formattedPolicies,
            year: new Date().getFullYear(),
            subject: 'Studio Policies and Enrollment Confirmation',
          },
        },
      ],
      from: {
        email: '<EMAIL>',
        name: 'Enrollio',
      },
      subject: 'Studio Policies and Enrollment Confirmation',
      template_id: 'd-b29d73cc86824b709cfe98c4fe3f4e9b',
      mail_settings: {
        sandbox_mode: {
          enable: false,
        },
      },
      tracking_settings: {
        click_tracking: {
          enable: true,
          enable_text: true,
        },
        open_tracking: {
          enable: true,
        },
      },
    };

    const config = {
      method: 'post',
      url: 'https://api.sendgrid.com/v3/mail/send',
      headers: {
        Authorization: `Bearer ${process.env.SENDGRID_API_KEY}`,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      data: msg,
    };

    const response = await axios(config);
    return true;
  } catch (error) {
    console.error('Error sending policies email:', error);
    return false;
  }
}
