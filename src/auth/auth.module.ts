import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { AuthService } from './auth.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Parent, ParentSchema } from 'src/database/schema/parent';
import { AuthController } from './auth.controller';
import { Studio, StudioSchema } from 'src/database/schema/studio';

@Module({
  imports: [
    JwtModule.register({
      secret: 'yourSecretKey**huy67541!1@#543%&^8', // Replace with your secret key
      signOptions: { expiresIn: '2d' }, // Token expiration time
    }),
    MongooseModule.forFeature([{ name: Parent.name, schema: ParentSchema }]),
    MongooseModule.forFeature([{ name: Studio.name, schema: StudioSchema }]),
  ],
  providers: [AuthService],
  controllers: [AuthController],
  exports: [AuthService, JwtModule],
})
export class AuthModule {}
