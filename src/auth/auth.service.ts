import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Parent, ParentDocument } from 'src/database/schema/parent';
import { LoginDto } from 'src/parents/dto/login_dto';
import * as bcrypt from 'bcrypt';
import { Request } from 'express';
import { Studio, StudioDocument } from 'src/database/schema/studio';

@Injectable()
export class AuthService {
  constructor(
    private readonly jwtService: JwtService,
    @InjectModel(Parent.name) private parentModel: Model<ParentDocument>,
    @InjectModel(Studio.name) private studioModel: Model<StudioDocument>,
  ) {}

  async validateUser(
    email: string,
    password: string,
    studioId,
  ): Promise<ParentDocument> {
    const parent = await this.parentModel
      .findOne({ email: { $regex: new RegExp(`^${email}$`, 'i') }, studioId })
      .populate({
        path: 'studioId',
        model: 'Studio',
        select: 'subaccountName locationId',
      })
      .exec();
    if (!parent) {
      throw new UnauthorizedException('User does not exist. Please register!');
    }

    // Validate the stored password if OTP is not active
    const isPasswordMatching = await bcrypt.compare(
      password.trim(),
      parent.password,
    );
    if (!isPasswordMatching) {
      throw new UnauthorizedException('Invalid email or password');
    }

    return parent;
  }

  async login(loginDto: LoginDto, req) {
    const { email, password, locationId } = loginDto;

    try {
      const studio = await this.studioModel.findOne({ locationId: locationId });
      const studioId = studio._id;
      const parent = await this.validateUser(email, password, studioId);

      if (!parent) {
        throw new Error('Invalid credentials');
      }
      const updatedParent = parent;

      const parentData = updatedParent.toObject();
      delete parentData.password;
      // delete parentData.oneTimePassword;
      // delete parentData.oneTimePasswordExpires;

      const payload = {
        email: parent.email,
        sub: parent.id,
        locationId: parent.studioId._id.toString(),
        userAgent: req.headers['user-agent'],
        ip: req.ip,
      };
      const token = this.jwtService.sign(payload);

      return {
        access_token: token,
        parent: parentData,
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'An error occurred during login',
        error,
      );
    }
  }

  async resetPassword(resetPasswordDto: LoginDto, req) {
    const { email, password, locationId } = resetPasswordDto;
    const studio = await this.studioModel.findOne({ locationId: locationId });
    const studioId = studio._id;

    const parent = await this.parentModel.findOne({ email, studioId }).exec();
    if (!parent) {
      throw new BadRequestException('User with this email does not exist');
    }

    const isSamePassword = await bcrypt.compare(password, parent.password);
    if (isSamePassword) {
      throw new BadRequestException(
        'The new password must be different from your current password.',
      );
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    const updatedParent = await this.parentModel
      .findOneAndUpdate(
        { email, studioId },
        { $set: { password: hashedPassword, isFirstLogin: false } },
        { new: true }, // Return the updated document
      )
      .exec();

    const payload = {
      email: updatedParent.email,
      sub: updatedParent.id,
      locationId: parent.studioId.toString(),
      userAgent: req.headers['user-agent'],
      ip: req.ip,
    };
    const token = this.jwtService.sign(payload);

    const parentData = updatedParent.toObject();
    delete parentData.password;

    return {
      access_token: token,
      parent: parentData,
    };
  }
}
