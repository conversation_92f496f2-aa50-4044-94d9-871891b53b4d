import { Body, Controller, Post, Req } from '@nestjs/common';
import { AuthService } from './auth.service';
import { LoginDto } from 'src/parents/dto/login_dto';

@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post()
  async auth(@Body() login: LoginDto, @Req() request: Request) {
    return await this.authService.login(login, request);
  }

  @Post('reset-password')
  async resetPassword(
    @Body() resetPasswordDto: LoginDto,
    @Req() request: Request,
  ) {
    return await this.authService.resetPassword(resetPasswordDto, request);
  }
}
