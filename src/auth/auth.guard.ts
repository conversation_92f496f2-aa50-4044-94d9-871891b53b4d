import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';

@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(private readonly jwtService: JwtService) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest<Request>();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      return false; // Block access if no token is present
    }

    try {
      const payload = this.jwtService.verify(token, {
        secret: 'yourSecretKey**huy67541!1@#543%&^8',
      }); // Verify and decode JWT

      // const isUserAgentValid = payload.userAgent === request.headers['user-agent'];
      // const isIpValid = payload.ip === request.ip;

      // if (!isUserAgentValid || !isIpValid) {
      //     return false;
      // }
      request['locationId'] = payload.locationId;
      request['email'] = payload.email;
      return true; // Allow access
    } catch (error) {
      return false; // Block access if token is invalid or expired
    }
  }

  // Helper function to extract JWT from the Authorization header
  private extractTokenFromHeader(request: Request): string | null {
    const authHeader = request.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.split(' ')[1];
  }
}
