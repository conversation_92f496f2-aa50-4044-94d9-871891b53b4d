import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  WebhookErrorLog,
  WebhookErrorLogDocument,
} from '../database/schema/webhook-error-log.schema';

@Injectable()
export class WebhookErrorLogsService {
  constructor(
    @InjectModel(WebhookErrorLog.name)
    private webhookErrorLogModel: Model<WebhookErrorLogDocument>,
  ) {}

  async create(
    createWebhookErrorLogDto: Partial<WebhookErrorLog>,
  ): Promise<WebhookErrorLog> {
    const createdLog = new this.webhookErrorLogModel(createWebhookErrorLogDto);
    return createdLog.save();
  }

  async findAll(): Promise<WebhookErrorLog[]> {
    return this.webhookErrorLogModel.find().sort({ timestamp: -1 }).exec();
  }

  async findByLocationId(locationId: string): Promise<WebhookErrorLog[]> {
    return this.webhookErrorLogModel
      .find({ locationId })
      .sort({ timestamp: -1 })
      .exec();
  }

  async markAsResolved(id: string, notes?: string): Promise<WebhookErrorLog> {
    return this.webhookErrorLogModel.findByIdAndUpdate(
      id,
      {
        resolved: true,
        resolvedAt: new Date(),
        notes: notes,
      },
      { new: true },
    );
  }
}
