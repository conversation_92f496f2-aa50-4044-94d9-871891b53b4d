import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { WebhookErrorLogsService } from './webhook-error-logs.service';
import {
  WebhookErrorLog,
  WebhookErrorLogSchema,
} from '../database/schema/webhook-error-log.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: WebhookErrorLog.name, schema: WebhookErrorLogSchema },
    ]),
  ],
  providers: [WebhookErrorLogsService],
  exports: [WebhookErrorLogsService],
})
export class WebhookErrorLogsModule {}
