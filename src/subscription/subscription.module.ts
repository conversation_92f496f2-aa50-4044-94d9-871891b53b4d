import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SubscriptionService } from './subscription.service';
import {
  Subscription,
  SubscriptionSchema,
} from '../database/schema/subscription';
import { PaymentTransactionSchema } from 'src/database/schema/paymentTransaction';
import { StudentSchema } from 'src/database/schema/student';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import { SubscriptionInvoiceSchema } from 'src/database/schema/subscriptionInvoice';
import { PaymentTransaction } from 'src/database/schema/paymentTransaction';
import { Student } from 'src/database/schema/student';
import { SubscriptionController } from './subscription.controller';
import { CurrencyModule } from 'src/currency/currency.module';
import { SubscriptionInvoiceModule } from 'src/subscription-invoice/subscription-invoice.module';
import { PaymentTransactionModule } from 'src/payment-transaction/payment-transaction.module';
import { TransactionModule } from 'src/transaction/transaction.module';
import { PaymentProcessorModule } from 'src/payment-processor/payment-processor.module';
import { AuthModule } from 'src/auth/auth.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Subscription.name, schema: SubscriptionSchema },
      { name: SubscriptionInvoice.name, schema: SubscriptionInvoiceSchema },
      { name: PaymentTransaction.name, schema: PaymentTransactionSchema },
      { name: Student.name, schema: StudentSchema },
    ]),
    AuthModule,
    forwardRef(() => SubscriptionInvoiceModule),
    forwardRef(() => TransactionModule),
    forwardRef(() => PaymentTransactionModule),
    forwardRef(() => CurrencyModule),
    forwardRef(() => PaymentProcessorModule),
  ],
  controllers: [SubscriptionController],
  providers: [SubscriptionService],
  exports: [SubscriptionService],
})
export class SubscriptionModule {}
