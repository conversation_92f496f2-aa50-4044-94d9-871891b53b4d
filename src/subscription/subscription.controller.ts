import {
  Body,
  Controller,
  Get,
  Param,
  Put,
  Req,
  UseGuards,
  Query,
  Post,
} from '@nestjs/common';
import { SubscriptionService } from './subscription.service';
import { JwtAuthGuard } from 'src/auth/auth.guard';

@UseGuards(JwtAuthGuard)
@Controller('subscription')
export class SubscriptionController {
  constructor(private readonly subscriptionService: SubscriptionService) {}

  @Put('pause/:studentId')
  async pauseSubscription(
    @Param('studentId') studentId: string,
    @Body() body: { entityType: string; entityId: string[] },
  ) {
    return this.subscriptionService.pauseSubscription(
      studentId,
      body.entityType,
      body.entityId,
    );
  }

  @Put('resume/:studentId')
  async resumeSubscription(
    @Param('studentId') studentId: string,
    @Body() body: { entityType: string; entityId: string[] },
  ) {
    return this.subscriptionService.resumeSubscription(
      studentId,
      body.entityType,
      body.entityId,
    );
  }
}
