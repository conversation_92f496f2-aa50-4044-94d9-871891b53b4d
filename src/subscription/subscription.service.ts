import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Subscription } from '../database/schema/subscription';
import { SubscriptionStatus } from '../database/schema/subscription';
import { Student } from '../database/schema/student';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import { PaymentTransaction } from 'src/database/schema/paymentTransaction';
import { InvoiceStatus, PaymentTransactionType } from '../stripe/type/index';

@Injectable()
export class SubscriptionService {
  constructor(
    @InjectModel(Subscription.name)
    private readonly subscriptionModel: Model<Subscription>,
    @InjectModel(Student.name)
    private readonly studentModel: Model<Student>,
    @InjectModel(SubscriptionInvoice.name)
    private readonly subscriptionInvoiceModel: Model<SubscriptionInvoice>,
    @InjectModel(PaymentTransaction.name)
    private readonly paymentTransactionModel: Model<PaymentTransaction>,
  ) {}

  async create(createDto: Partial<Subscription>): Promise<Subscription> {
    const created = new this.subscriptionModel(createDto);
    return created.save();
  }

  async findAll(studioId: string): Promise<Subscription[]> {
    return this.subscriptionModel.find({ studioId }).exec();
  }

  async findOne(id: string): Promise<Subscription> {
    return this.subscriptionModel.findById(id).exec();
  }

  async update(
    id: string,
    updateDto: Partial<Subscription>,
  ): Promise<Subscription> {
    return this.subscriptionModel
      .findByIdAndUpdate(id, updateDto, { new: true })
      .exec();
  }

  async remove(id: string): Promise<Subscription> {
    return this.subscriptionModel.findByIdAndDelete(id).exec();
  }

  async fetchSubscriptionsByStudentId(
    studentId: string,
    locationId: string,
  ): Promise<Subscription[]> {
    return this.subscriptionModel
      .find({
        studentId: Types.ObjectId.createFromHexString(studentId),
        studioId: Types.ObjectId.createFromHexString(locationId),
      })
      .exec();
  }

  async pauseSubscription(
    studentId: string,
    entityType: string,
    entityIds: string[],
  ) {
    entityIds.forEach(async (entityId) => {
      const subscription = await this.subscriptionModel
        .findOneAndUpdate(
          {
            studentId: Types.ObjectId.createFromHexString(studentId),
            entityType: entityType === 'enrollment' ? 'class' : 'event',
            entityId: Types.ObjectId.createFromHexString(entityId),
          },
          { status: SubscriptionStatus.PAUSED },
        )
        .exec();
      if (entityType === 'enrollment') {
        await this.studentModel.findOneAndUpdate(
          {
            _id: Types.ObjectId.createFromHexString(studentId),
            'enrollments.enrollmentId': entityId,
          },
          {
            $set: {
              'enrollments.$.subscriptionStatus': SubscriptionStatus.PAUSED,
            },
          },
        );
      } else if (entityType === 'event') {
        await this.studentModel.findOneAndUpdate(
          {
            _id: Types.ObjectId.createFromHexString(studentId),
            'events.eventId': entityId,
          },
          {
            $set: {
              'events.$.subscriptionStatus': SubscriptionStatus.PAUSED,
            },
          },
        );
      }

      await this.subscriptionInvoiceModel.updateMany(
        {
          subscriptionId: Types.ObjectId.createFromHexString(
            subscription._id.toString(),
          ),
          status: InvoiceStatus.UPCOMING,
        },
        { status: SubscriptionStatus.PAUSED },
      );

      await this.paymentTransactionModel.findOneAndUpdate(
        {
          studentId: Types.ObjectId.createFromHexString(studentId),
          type: {
            $in: [
              entityType as PaymentTransactionType,
              PaymentTransactionType.REGISTRATION,
            ],
          },
          typeId: Types.ObjectId.createFromHexString(entityId),
        },
        { status: SubscriptionStatus.PAUSED },
      );
    });

    return {
      message: 'Subsription successfully paused.',
    };
  }

  async resumeSubscription(
    studentId: string,
    entityType: string,
    entityIds: string[],
  ) {
    entityIds.forEach(async (entityId) => {
      const subscription = await this.subscriptionModel
        .findOneAndUpdate(
          {
            studentId: Types.ObjectId.createFromHexString(studentId),
            entityType: entityType === 'enrollment' ? 'class' : 'event',
            entityId: Types.ObjectId.createFromHexString(entityId),
          },
          { status: SubscriptionStatus.ACTIVE },
        )
        .exec();
      if (entityType === 'enrollment') {
        await this.studentModel.findOneAndUpdate(
          {
            _id: Types.ObjectId.createFromHexString(studentId),
            'enrollments.enrollmentId': entityId,
          },
          {
            $set: {
              'enrollments.$.subscriptionStatus': SubscriptionStatus.ACTIVE,
            },
          },
        );
      } else if (entityType === 'event') {
        await this.studentModel.findOneAndUpdate(
          {
            _id: Types.ObjectId.createFromHexString(studentId),
            'events.eventId': entityId,
          },
          {
            $set: {
              'events.$.subscriptionStatus': SubscriptionStatus.ACTIVE,
            },
          },
        );
      }

      await this.subscriptionInvoiceModel.updateMany(
        {
          subscriptionId: Types.ObjectId.createFromHexString(
            subscription._id.toString(),
          ),
          status: InvoiceStatus.PAUSED,
        },
        { status: InvoiceStatus.UPCOMING },
      );

      await this.paymentTransactionModel.findOneAndUpdate(
        {
          studentId: Types.ObjectId.createFromHexString(studentId),
          type: {
            $in: [
              entityType as PaymentTransactionType,
              PaymentTransactionType.REGISTRATION,
            ],
          },
          typeId: Types.ObjectId.createFromHexString(entityId),
        },
        { status: SubscriptionStatus.SCHEDULED },
      );
    });

    return {
      message: 'Subsription successfully resumed.',
    };
  }
}
