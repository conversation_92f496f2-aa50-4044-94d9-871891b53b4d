import { <PERSON><PERSON><PERSON>, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { StudiosModule } from './studios/studios.module';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { GohighlevelModule } from './gohighlevel/gohighlevel.module';
import { ParentsModule } from './parents/parents.module';
import { StudentsModule } from './students/students.module';
import { AuthModule } from './auth/auth.module';
import { EnrollmentModule } from './enrollment/enrollment.module';
import { StripeModule } from './stripe/stripe.module';
import { PoliciesModule } from './policies/policies.module';
import { AttendanceModule } from './attendance/attendance.module';
import { CustomFormModule } from './custom-form/custom-form.module';
import { GcpStorageModule } from './gcp-storage/gcp-storage.module';
import { EventsModule } from './events/events.module';
import { TransactionModule } from './transaction/transaction.module';
import { ScheduleModule } from '@nestjs/schedule';
import { CurrencyModule } from './currency/currency.module';
import { FormParentPreferenceModule } from './form-parent-preference/form-parent-preference.module';
import { BullModule } from '@nestjs/bullmq';
import { ReleaseNotificationModule } from './release-notifications/release-notification.module';
import { EmailTemplateModule } from './email-template/email-template.module';
import { Logger } from '@nestjs/common';
import { EmailTemplateConfigModule } from './email-template-config/email-template-config.module';
import { LeadsModule } from './leads/leads.module';
import { DiscountModule } from './discount/discount.module';
import { SessionModule } from './session/session.module';
import { TagsModule } from './tags/tags.module';
import { EnrollmentHistoryModule } from './enrollment-history/enrollment-history.module';
import { EventHistoryModule } from './event-history/event-history.module';
import { TrialstudentsModule } from './trialstudents/trialstudents.module';
import { DefaultImageModule } from './default-image/default-image.module';
import { WebhookErrorLogsModule } from './webhook-error-logs/webhook-error-logs.module';
import { RedisModule } from './redis/redis.module';
import { ClassHistoryModule } from './class-history/class-history.module';
import { DashboardModule } from './dashboard/dashboard.module';
import { Inject } from '@nestjs/common';
import type { Redis } from 'ioredis';
import { WebhooksModule } from './webhooks/webhooks.module';
import { PaymentProcessorModule } from './payment-processor/payment-processor.module';
import { ReportingModule } from './reporting/reporting.module';
import { TransactionCodeModule } from './transaction-code/transaction-code.module';
import { PaymentTransactionModule } from './payment-transaction/payment-transaction.module';
import { NotesModule } from './notes/notes.module';
import { CronModule } from './cron/cron.module';
import { GhlModule } from './ghl/ghl.module';

@Module({
  imports: [
    StudiosModule,
    ConfigModule.forRoot({
      isGlobal: true, // Make ConfigModule globally available
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: () => ({
        uri: process.env.MONGODB_URI,
      }),
    }),
    GohighlevelModule,
    PaymentProcessorModule,
    ParentsModule,
    StudentsModule,
    AuthModule,
    EnrollmentModule,
    StripeModule,
    PoliciesModule,
    AttendanceModule,
    CustomFormModule,
    GcpStorageModule,
    EventsModule,
    TransactionModule,
    ScheduleModule.forRoot(),
    CurrencyModule,
    FormParentPreferenceModule,
    ReleaseNotificationModule,
    EmailTemplateModule,
    DashboardModule,
    BullModule.forRoot({
      connection: {
        host: process.env.REDIS_HOST,
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
        username: process.env.REDIS_USERNAME,
        enableReadyCheck: true,
        maxRetriesPerRequest: null,
        retryStrategy: (times) => {
          Logger.log(`Redis connection attempt ${times}`);
          if (times > 3) return null;
          return Math.min(times * 100, 2000);
        },
      },
    }),
    EmailTemplateConfigModule,
    LeadsModule,
    DiscountModule,
    SessionModule,
    TagsModule,
    EnrollmentHistoryModule,
    EventHistoryModule,
    TrialstudentsModule,
    DefaultImageModule,
    WebhookErrorLogsModule,
    WebhooksModule,
    ReportingModule,
    TransactionCodeModule,
    PaymentTransactionModule,
    ClassHistoryModule,
    RedisModule,
    NotesModule,
    CronModule,
    GhlModule,
    // BullModule.registerQueue({
    //   name: 'enrollment-queue'
    // }),
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(AppModule.name);

  constructor(@Inject('REDIS_CLIENT') private readonly redis: Redis) {}

  async onModuleInit() {
    this.logger.log('App module initialized');
  }

  async onModuleDestroy() {
    await this.redis.quit();
  }
}
