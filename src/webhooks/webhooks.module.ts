import { forwardRef, Module, Session } from '@nestjs/common';
import { WebhooksController } from './webhooks.controller';
import { WebhooksService } from './webhooks.service';
import { StripeWebhookHandler } from './handlers/stripe-webhook.handler';
import { StripeEventHandlerRegistry } from './handlers/stripe/stripe-event-handler.registry';
import { PaymentIntentSucceededHandler } from '../stripe/services/event-handlers/payment-intent-succeeded.handler';
import { PaymentIntentFailedHandler } from '../stripe/services/event-handlers/payment-intent-failed.handler';
import { CheckoutSessionCompletedHandler } from '../stripe/services/event-handlers/checkout-session-completed.handler';
import { ProductCreatedHandler } from 'src/stripe/services/event-handlers/product-created.handler';
import { MongooseModule } from '@nestjs/mongoose';
import {
  Credential,
  CredentialSchema,
} from '../database/schema/stripeCredential';
import { Proration, ProrationSchema } from 'src/database/schema/prorations';
import { Discount, DiscountSchema } from 'src/database/schema/discount';
import {
  DiscountCoupon,
  DiscountCouponSchema,
} from 'src/database/schema/discountCoupon';
import { Enrollment, EnrollmentSchema } from 'src/database/schema/enrollment';
import { Event, EventSchema } from 'src/database/schema/event';
import { BullModule } from '@nestjs/bullmq';
import { StripeModule } from '../stripe/stripe.module';
import { TransactionModule } from '../transaction/transaction.module';
import { ParentsModule } from '../parents/parents.module';
import { StudentsModule } from '../students/students.module';
import { WebhookErrorLogsModule } from '../webhook-error-logs/webhook-error-logs.module';
import { StudiosModule } from '../studios/studios.module';
import { DiscountModule } from '../discount/discount.module';
import { DiscountCouponModule } from 'src/discount-coupon/discount-coupon.module';
import { EnrollmentModule } from 'src/enrollment/enrollment.module';
import { EventsModule } from 'src/events/events.module';
import { GcpStorageModule } from '../gcp-storage/gcp-storage.module';
import { GohighlevelModule } from 'src/gohighlevel/gohighlevel.module';
import { CurrencyModule } from 'src/currency/currency.module';
import { PoliciesModule } from 'src/policies/policies.module';
import { TriggersModule } from 'src/triggers/triggers.module';
import { SubscriptionModule } from 'src/subscription/subscription.module';
import { SubscriptionInvoiceModule } from 'src/subscription-invoice/subscription-invoice.module';
import { StripeCouponService } from 'src/stripe/services/stripe-coupon.service';
import { CouponModule } from 'src/coupon/coupon.module';
import { PaymentTransactionModule } from 'src/payment-transaction/payment-transaction.module';
import {
  ClassHistory,
  ClassHistorySchema,
} from 'src/database/schema/classHistory';
import { ClassHistoryModule } from 'src/class-history/class-history.module';
import { PaymentTransaction } from 'src/database/schema/paymentTransaction';
import { PaymentTransactionSchema } from 'src/database/schema/paymentTransaction';
import {
  SubscriptionInvoice,
  SubscriptionInvoiceSchema,
} from 'src/database/schema/subscriptionInvoice';
import { Studio, StudioSchema } from 'src/database/schema/studio';
import { Parent, ParentSchema } from 'src/database/schema/parent';
import { Student, StudentSchema } from 'src/database/schema/student';
// GHL webhook handlers
import { GhlWebhookHandler } from './handlers/ghl-webhook.handler';
import { GhlEventHandlerRegistry } from './handlers/ghl/ghl-event-handler.registry';
import { InvoiceCreateHandler } from 'src/ghl/services/event-handlers/invoice-create.handler';
import { InvoicePaidHandler } from 'src/ghl/services/event-handlers/invoice-paid.handler';
import { InvoiceUpdateHandler } from 'src/ghl/services/event-handlers/invoice-update.handler';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Credential.name, schema: CredentialSchema },
      { name: Proration.name, schema: ProrationSchema },
      { name: Discount.name, schema: DiscountSchema },
      { name: DiscountCoupon.name, schema: DiscountCouponSchema },
      { name: Enrollment.name, schema: EnrollmentSchema },
      { name: Event.name, schema: EventSchema },
      { name: ClassHistory.name, schema: ClassHistorySchema },
      { name: PaymentTransaction.name, schema: PaymentTransactionSchema },
      { name: SubscriptionInvoice.name, schema: SubscriptionInvoiceSchema },
      { name: Studio.name, schema: StudioSchema },
      { name: Parent.name, schema: ParentSchema },
      { name: Student.name, schema: StudentSchema },
    ]),
    BullModule.registerQueue(
      {
        name: 'parent-remove-tags',
      },
      {
        name: 'invoice-status-update',
      },
    ),
    forwardRef(() => StripeModule),
    forwardRef(() => TransactionModule),
    forwardRef(() => ParentsModule),
    forwardRef(() => StudentsModule),
    forwardRef(() => WebhookErrorLogsModule),
    forwardRef(() => StudiosModule),
    forwardRef(() => DiscountModule),
    forwardRef(() => DiscountCouponModule),
    forwardRef(() => EnrollmentModule),
    forwardRef(() => EventsModule),
    forwardRef(() => GcpStorageModule),
    forwardRef(() => GohighlevelModule),
    forwardRef(() => CurrencyModule),
    forwardRef(() => PoliciesModule),
    forwardRef(() => TriggersModule),
    forwardRef(() => SubscriptionModule),
    forwardRef(() => SubscriptionInvoiceModule),
    forwardRef(() => CouponModule),
    forwardRef(() => PaymentTransactionModule),
    ClassHistoryModule,
  ],
  controllers: [WebhooksController],
  providers: [
    WebhooksService,
    StripeWebhookHandler,
    StripeEventHandlerRegistry,
    PaymentIntentSucceededHandler,
    PaymentIntentFailedHandler,
    CheckoutSessionCompletedHandler,
    ProductCreatedHandler,
    StripeCouponService,
    // GHL webhook providers
    GhlWebhookHandler,
    GhlEventHandlerRegistry,
    InvoiceCreateHandler,
    InvoicePaidHandler,
    InvoiceUpdateHandler,
  ],
  exports: [
    WebhooksService,
    PaymentIntentSucceededHandler,
    PaymentIntentFailedHandler,
    CheckoutSessionCompletedHandler,
    ProductCreatedHandler,
  ],
})
export class WebhooksModule {}
