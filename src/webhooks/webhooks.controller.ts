import { <PERSON>, Post, Req, Res, Query } from '@nestjs/common';
import { Request, Response } from 'express';
import { WebhooksService } from './webhooks.service';

// Define the extended request interface with rawBody
interface RequestWithRawBody extends Request {
  rawBody?: Buffer;
}

@Controller('webhooks')
export class WebhooksController {
  constructor(private readonly webhooksService: WebhooksService) {}

  @Post('stripe')
  async stripeWebhook(
    @Req() req: RequestWithRawBody,
    @Query('locationId') locationId: string,
    @Res() res: Response,
  ) {
    return this.webhooksService.handleStripeWebhook(req, res, locationId);
  }

  @Post('ghl')
  async ghlWebhook(
    @Req() req: RequestWithRawBody,
    @Res() res: Response,
  ) {
    return this.webhooksService.handleGhlWebhook(req, res);
  }
}
