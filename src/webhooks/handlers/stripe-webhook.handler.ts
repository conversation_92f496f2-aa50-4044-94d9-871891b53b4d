import { Injectable, Logger } from '@nestjs/common';
import { Request, Response } from 'express';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import Stripe from 'stripe';
import { Credential } from '../../database/schema/stripeCredential';
import { StripeEventHandlerRegistry } from './stripe/stripe-event-handler.registry';
import { WebhookErrorLogsService } from '../../webhook-error-logs/webhook-error-logs.service';

// Define the extended request interface with rawBody
interface RequestWithRawBody extends Request {
  rawBody?: Buffer;
}

@Injectable()
export class StripeWebhookHandler {
  private readonly logger = new Logger(StripeWebhookHandler.name);

  constructor(
    @InjectModel(Credential.name) private credentialModel: Model<Credential>,
    private readonly stripeEventHandlerRegistry: StripeEventHandlerRegistry,
    private readonly webhookErrorLogsService: WebhookErrorLogsService,
  ) {}

  async handleWebhook(
    req: RequestWithRawBody,
    res: Response,
    locationId: string,
  ) {
    try {
      let event: Stripe.Event;
      let credential;
      let stripe;

      let parsedBody = null;
      try {
        // If rawBody is available, use it directly
        const rawBody = req.rawBody || req.body;

        // If body is a buffer or string, try to parse it
        if (Buffer.isBuffer(rawBody)) {
          parsedBody = JSON.parse(rawBody.toString('utf8'));
        } else if (typeof rawBody === 'string') {
          parsedBody = JSON.parse(rawBody);
        } else {
          // If it's already an object, use it directly
          parsedBody = rawBody;
        }
      } catch (parseError) {
        this.logger.error('Failed to parse webhook body:', parseError);
        // Keep the raw body as fallback
        parsedBody = null;
      }

      try {
        // Get credentials
        credential = await this.credentialModel.findOne({
          studioId: Types.ObjectId.createFromHexString(locationId),
        });

        if (!credential) {
          await this.webhookErrorLogsService.create({
            type: 'credential_error',
            message: `No credentials found for studio ID: ${locationId}`,
            payload: req.body,
            headers: req.headers,
            resolved: false,
            timestamp: new Date(),
            locationId: locationId,
          });
          return res
            .status(200)
            .send({ received: true, error: 'Invalid studio ID' });
        }

        stripe = new Stripe(credential.apiSecret, {
          apiVersion: '2025-02-24.acacia',
        });

        const sig = req.headers['stripe-signature'] as string;
        this.logger.log('Webhook received:', sig);

        // Verify signature
        try {
          // Verify signature and parse the event using the raw body
          event = stripe.webhooks.constructEvent(
            req.rawBody || req.body,
            sig,
            credential.webhookSecret,
          );
        } catch (err) {
          this.logger.error(
            `Error verifying webhook signature: ${err.message}`,
          );

          // Log the error with parsed body if available
          await this.webhookErrorLogsService.create({
            type: 'signature_error',
            message: err.message,
            payload: parsedBody,
            rawPayload: req.rawBody || req.body,
            headers: req.headers,
            timestamp: new Date(),
            resolved: false,
            locationId: locationId,
          });

          return res
            .status(200)
            .send({ received: true, error: 'Signature verification failed' });
        }

        // Process the event
        try {
          // Get the appropriate handler from the registry
          const handler = this.stripeEventHandlerRegistry.getHandler(
            event.type,
          );

          if (handler) {
            // Use the dedicated handler from the registry
            this.logger.log(`Processing ${event.type} with dedicated handler`);
            await handler.handleEvent(event.data.object, stripe);
            this.logger.log(`Successfully processed ${event.type}`);
          } else {
            // Log unhandled event type
            this.logger.warn(`Unhandled event type: ${event.type}`);
          }
        } catch (error) {
          // Log the processing error
          this.logger.error(`Error processing ${event.type} event:`, error);

          // Log with the parsed event data
          await this.webhookErrorLogsService.create({
            type: 'processing_error',
            message: error.message,
            stack: error.stack,
            eventId: event.id,
            eventType: event.type,
            payload: parsedBody,
            rawPayload: req.rawBody || req.body,
            headers: req.headers,
            resolved: false,
            timestamp: new Date(),
            locationId: locationId,
          });
        }

        // Always return 200
        return res.status(200).send({ received: true });
      } catch (error) {
        // Catch any other unexpected errors
        this.logger.error('Unexpected error in webhook handler:', error);

        // Log the unexpected error
        try {
          await this.webhookErrorLogsService.create({
            type: 'unexpected_error',
            message: error.message,
            stack: error.stack,
            payload: parsedBody,
            rawPayload: req.rawBody || req.body,
            headers: req.headers,
            resolved: false,
            timestamp: new Date(),
            locationId: locationId,
          });
        } catch (logError) {
          this.logger.error('Failed to log webhook error:', logError);
        }

        // Always return 200
        return res
          .status(200)
          .send({ received: true, error: 'Internal server error' });
      }
    } catch (error) {
      this.logger.error(`Error handling webhook: ${error.message}`);
      return res.status(500).send(`Webhook Error: ${error.message}`);
    }
  }
}
