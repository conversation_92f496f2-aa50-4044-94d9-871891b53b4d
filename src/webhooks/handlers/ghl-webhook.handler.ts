import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { Request, Response } from 'express';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Studio } from 'src/database/schema/studio';
import { GhlEventHandlerRegistry } from './ghl/ghl-event-handler.registry';
import { GhlWebhookDto, GhlWebhookEventType, GhlInvoiceWebhookDto } from '../dto/ghl-webhook.dto';
import { WebhookErrorLogsService } from 'src/webhook-error-logs/webhook-error-logs.service';
import { validate } from 'class-validator';
import { plainToClass } from 'class-transformer';

// Define the extended request interface with rawBody
interface RequestWithRawBody extends Request {
  rawBody?: Buffer;
}

@Injectable()
export class GhlWebhookHandler {
  private readonly logger = new Logger(GhlWebhookHandler.name);

  constructor(
    @InjectModel(Studio.name)
    private studioModel: Model<Studio>,
    private readonly ghlEventHandlerRegistry: GhlEventHandlerRegistry,
    private readonly webhookErrorLogsService: WebhookErrorLogsService,
  ) {}

  async handleWebhook(
    req: RequestWithRawBody,
    res: Response,
  ): Promise<Response> {
    let locationId = req.body.locationId;
    this.logger.log(`Received GHL webhook for location: ${locationId}`);



    try {
      // Validate locationId
      if (!locationId) {
        this.logger.error('Missing locationId in GHL webhook request');
        return res.status(400).json({ error: 'Missing locationId parameter' });
      }

      // Find studio by locationId
      const studio = await this.studioModel.findOne({ locationId }).exec();
      if (!studio) {
        this.logger.error(`Studio not found for locationId: ${locationId}`);
        return res.status(404).json({ error: 'Studio not found' });
      }

      // Parse webhook payload
      const webhookData = req.body;
      if (!webhookData) {
        this.logger.error('Empty webhook payload received');
        return res.status(400).json({ error: 'Empty payload' });
      }

      this.logger.log(`GHL webhook payload: ${JSON.stringify(webhookData)}`);

      // Determine if this is a direct invoice webhook or a general webhook with invoice data
      let eventType: string;
      let invoiceData: any;

      if (webhookData._id && webhookData.status && webhookData.total !== undefined) {
        invoiceData = webhookData;
        eventType = webhookData.type;
      } else if (webhookData.type && webhookData.data) {
        // This is a general webhook structure
        eventType = webhookData.type;
        invoiceData = webhookData.data;
      } else {
        this.logger.error('Invalid webhook payload structure');
        return res.status(400).json({ error: 'Invalid payload structure' });
      }

      // Verify event type is supported
      if (!Object.values(GhlWebhookEventType).includes(eventType as GhlWebhookEventType)) {
        this.logger.warn(`Unsupported GHL webhook event type: ${eventType}`);
        return res.status(200).json({
          message: 'Event type not supported',
          eventType: eventType
        });
      }

      // Validate invoice data
      const ghlInvoiceDto = plainToClass(GhlInvoiceWebhookDto, invoiceData);
      const validationErrors = await validate(ghlInvoiceDto);

      if (validationErrors.length > 0) {
        this.logger.error(`Invoice data validation failed: ${JSON.stringify(validationErrors)}`);
        return res.status(400).json({
          error: 'Invalid invoice data',
          details: validationErrors
        });
      }

      // Process the event
      try {
        // Get the appropriate handler from the registry
        const handler = this.ghlEventHandlerRegistry.getHandler(eventType);

        if (handler) {
          // Use the dedicated handler from the registry
          this.logger.log(`Processing ${eventType} with dedicated handler`);
          await handler.handleEvent(ghlInvoiceDto);
          this.logger.log(`Successfully processed ${eventType}`);
        } else {
          // Log unhandled event type
          this.logger.warn(`Unhandled GHL event type: ${eventType}`);
        }

        return res.status(200).json({
          message: 'Webhook processed successfully',
          eventType: eventType,
          processed: !!handler
        });

      } catch (processingError) {
        this.logger.error(
          `Error processing GHL webhook event ${eventType}: ${processingError.message}`,
          processingError.stack,
        );

        // Log the error to webhook error logs
        await this.webhookErrorLogsService.create({
          type: 'processing_error',
          webhookType: 'GHL',
          eventType: eventType,
          locationId: locationId,
          message: processingError.message,
          payload: webhookData,
          stack: processingError.stack,
          timestamp: new Date(),
          headers: req.headers,
        });

        return res.status(500).json({
          error: 'Error processing webhook',
          eventType: eventType
        });
      }

    } catch (error) {
      this.logger.error(`GHL webhook handler error: ${error.message}`, error.stack);

      // Log the error to webhook error logs
      try {
        await this.webhookErrorLogsService.create({
          type: 'unexpected_error',
          webhookType: 'GHL',
          eventType: 'unknown',
          locationId: locationId || 'unknown',
          message: error.message,
          payload: req.body,
          stack: error.stack,
          timestamp: new Date(),
          headers: req.headers,
        });
      } catch (logError) {
        this.logger.error(`Failed to log webhook error: ${logError.message}`);
      }

      return res.status(500).json({ error: 'Internal server error' });
    }
  }
}
