import { Injectable, Logger } from '@nestjs/common';
import { StripeEventHandler } from '../../interfaces/stripe-event-handler.interface';
import { PaymentIntentSucceededHandler } from 'src/stripe/services/event-handlers/payment-intent-succeeded.handler';
import { PaymentIntentFailedHandler } from 'src/stripe/services/event-handlers/payment-intent-failed.handler';
import { CheckoutSessionCompletedHandler } from 'src/stripe/services/event-handlers/checkout-session-completed.handler';
import { ProductCreatedHandler } from 'src/stripe/services/event-handlers/product-created.handler';
import { ChargeRefundedHandler } from 'src/stripe/services/event-handlers/charge-refunded.handler';
@Injectable()
export class StripeEventHandlerRegistry {
  private readonly logger = new Logger(StripeEventHandlerRegistry.name);
  private handlers: Map<string, StripeEventHandler<any>> = new Map();

  constructor(
    private readonly paymentIntentSucceededHandler: PaymentIntentSucceededHandler,
    private readonly paymentIntentFailedHandler: PaymentIntentFailedHandler,
    private readonly checkoutSessionCompletedHandler: CheckoutSessionCompletedHandler,
    private readonly productCreatedHandler: ProductCreatedHandler,
    private readonly chargeRefundedHandler: ChargeRefundedHandler,
    // Inject other handlers here
  ) {
    this.registerHandlers();
    this.logRegisteredHandlers();
  }

  private registerHandlers(): void {
    this.handlers.set(
      'payment_intent.succeeded',
      this.paymentIntentSucceededHandler,
    );
    this.handlers.set(
      'payment_intent.payment_failed',
      this.paymentIntentFailedHandler,
    );
    // this.handlers.set(
    //   'checkout.session.completed',
    //   this.checkoutSessionCompletedHandler,
    // );
    this.handlers.set('product.created', this.productCreatedHandler);
    this.handlers.set('charge.refunded', this.chargeRefundedHandler);
  }

  private logRegisteredHandlers(): void {
    this.logger.log(
      `Registered handlers for event types: ${Array.from(this.handlers.keys()).join(', ')}`,
    );
  }

  getHandler(eventType: string): StripeEventHandler<any> | undefined {
    const handler = this.handlers.get(eventType);
    this.logger.debug(
      `Handler lookup for ${eventType}: ${handler ? 'Found' : 'Not found'}`,
    );
    return handler;
  }

  hasHandler(eventType: string): boolean {
    return this.handlers.has(eventType);
  }
}
