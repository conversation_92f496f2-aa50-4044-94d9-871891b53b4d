import { Injectable, Logger } from '@nestjs/common';
import { GhlEventHandler } from '../../interfaces/ghl-event-handler.interface';
import { GhlWebhookEventType } from '../../dto/ghl-webhook.dto';
import { InvoiceCreateHandler } from 'src/ghl/services/event-handlers/invoice-create.handler';
import { InvoicePaidHandler } from 'src/ghl/services/event-handlers/invoice-paid.handler';
import { InvoiceUpdateHandler } from 'src/ghl/services/event-handlers/invoice-update.handler';

@Injectable()
export class GhlEventHandlerRegistry {
  private readonly logger = new Logger(GhlEventHandlerRegistry.name);
  private handlers: Map<string, GhlEventHandler<any>> = new Map();

  constructor(
    private readonly invoiceCreateHandler: InvoiceCreateHandler,
    private readonly invoicePaidHandler: InvoicePaidHandler,
    private readonly invoiceUpdateHandler: InvoiceUpdateHandler,
  ) {
    this.registerHandlers();
    this.logRegisteredHandlers();
  }

  private registerHandlers(): void {
    // Invoice events
    this.handlers.set(
      GhlWebhookEventType.INVOICE_CREATE,
      this.invoiceCreateHandler,
    );
    this.handlers.set(
      GhlWebhookEventType.INVOICE_PAID,
      this.invoicePaidHandler,
    );
    this.handlers.set(
      GhlWebhookEventType.INVOICE_PARTIAL_PAID,
      this.invoicePaidHandler,
    );
    this.handlers.set(
      GhlWebhookEventType.INVOICE_SENT,
      this.invoiceUpdateHandler,
    );
    this.handlers.set(
      GhlWebhookEventType.INVOICE_VOID,
      this.invoiceUpdateHandler,
    );
    this.handlers.set(
      GhlWebhookEventType.INVOICE_DELETE,
      this.invoiceUpdateHandler,
    );
    this.handlers.set(
      GhlWebhookEventType.INVOICE_UPDATE,
      this.invoiceUpdateHandler,
    );
  }

  private logRegisteredHandlers(): void {
    this.logger.log(
      `Registered GHL handlers for event types: ${Array.from(this.handlers.keys()).join(', ')}`,
    );
  }

  getHandler(eventType: string): GhlEventHandler<any> | undefined {
    return this.handlers.get(eventType);
  }

  hasHandler(eventType: string): boolean {
    return this.handlers.has(eventType);
  }

  getAllEventTypes(): string[] {
    return Array.from(this.handlers.keys());
  }
}
