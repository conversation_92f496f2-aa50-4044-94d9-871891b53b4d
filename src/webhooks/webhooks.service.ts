import { Injectable } from '@nestjs/common';
import { Request, Response } from 'express';
import { StripeWebhookHandler } from './handlers/stripe-webhook.handler';
import { GhlWebhookHandler } from './handlers/ghl-webhook.handler';

// Define the extended request interface with rawBody
interface RequestWithRawBody extends Request {
  rawBody?: Buffer;
}

@Injectable()
export class WebhooksService {
  constructor(
    private readonly stripeWebhookHandler: StripeWebhookHandler,
    private readonly ghlWebhookHandler: GhlWebhookHandler,
  ) {}

  async handleStripeWebhook(
    req: RequestWithRawBody,
    res: Response,
    locationId: string,
  ) {
    return this.stripeWebhookHandler.handleWebhook(req, res, locationId);
  }

  async handleGhlWebhook(
    req: RequestWithRawBody,
    res: Response,
  ) {
    return this.ghlWebhookHandler.handleWebhook(req, res);
  }
}
