import { IsString, IsNotEmpty, IsObject, IsOptional, IsEnum, IsN<PERSON><PERSON> } from 'class-validator';

export enum GhlWebhookEventType {
  // Invoice events
  INVOICE_CREATE = 'InvoiceCreate',
  INVOICE_SENT = 'InvoiceSent',
  INVOICE_VOID = 'InvoiceVoid',
  INVOICE_PAID = 'InvoicePaid',
  INVOICE_PARTIAL_PAID = 'InvoicePartialPaid',
  INVOICE_DELETE = 'InvoiceDelete',
  INVOICE_UPDATE = 'InvoiceUpdate',

  // Contact events
  CONTACT_CREATE = 'ContactCreate',
  CONTACT_DELETE = 'ContactDelete',
  CONTACT_DND_UPDATE = 'ContactDndUpdate',
  CONTACT_TAG_UPDATE = 'ContactTagUpdate',

  // Note events
  NOTE_CREATE = 'NoteCreate',
  NOTE_DELETE = 'NoteDelete',

  // Task events
  TASK_CREATE = 'TaskCreate',
  TASK_DELETE = 'TaskDelete',

  // Opportunity events
  OPPORTUNITY_CREATE = 'OpportunityCreate',
  OPPORTUNITY_DELETE = 'OpportunityDelete',
  OPPORTUNITY_STAGE_UPDATE = 'OpportunityStageUpdate',
  OPPORTUNITY_STATUS_UPDATE = 'OpportunityStatusUpdate',
  OPPORTUNITY_MONETARY_VALUE_UPDATE = 'OpportunityMonetaryValueUpdate',

  // Conversation events
  CONVERSATION_UNREAD = 'ConversationUnreadWebhook',
  INBOUND_MESSAGE = 'InboundMessage',
  OUTBOUND_MESSAGE = 'OutboundMessage',

  // Campaign events
  CAMPAIGN_STATUS_UPDATE = 'CampaignStatusUpdate',

  // Location events
  LOCATION_CREATE = 'LocationCreate',
  LOCATION_UPDATE = 'LocationUpdate',
}

export enum GhlInvoiceStatus {
  DRAFT = 'draft',
  SENT = 'sent',
  PAID = 'paid',
  PARTIALLY_PAID = 'partially_paid',
  VOID = 'void',
  DELETE = 'delete',
}

export enum GhlDiscountType {
  PERCENTAGE = 'percentage',
  FIXED = 'fixed',
}

export class GhlBusinessDetailsDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsOptional()
  address?: string;

  @IsString()
  @IsOptional()
  phoneNo?: string;

  @IsString()
  @IsOptional()
  website?: string;

  @IsString()
  @IsOptional()
  logoUrl?: string;

  @IsOptional()
  customValues?: string[];
}

export class GhlAddressDto {
  @IsString()
  @IsOptional()
  countryCode?: string;

  @IsString()
  @IsOptional()
  addressLine1?: string;

  @IsString()
  @IsOptional()
  addressLine2?: string;

  @IsString()
  @IsOptional()
  city?: string;

  @IsString()
  @IsOptional()
  state?: string;

  @IsString()
  @IsOptional()
  postalCode?: string;
}

export class GhlAdditionalEmailDto {
  @IsString()
  @IsNotEmpty()
  email: string;
}

export class GhlContactDetailsDto {
  @IsString()
  @IsNotEmpty()
  id: string;

  @IsString()
  @IsOptional()
  phoneNo?: string;

  @IsString()
  @IsOptional()
  email?: string;

  @IsOptional()
  customFields?: string[];

  @IsString()
  @IsOptional()
  name?: string;

  @IsObject()
  @IsOptional()
  address?: GhlAddressDto;

  @IsOptional()
  additionalEmails?: GhlAdditionalEmailDto[];

  @IsString()
  @IsOptional()
  companyName?: string;
}

export class GhlDiscountDto {
  @IsEnum(GhlDiscountType)
  type: GhlDiscountType;

  @IsNumber()
  value: number;
}

export class GhlInvoiceItemDto {
  @IsOptional()
  taxes?: any[];

  @IsString()
  @IsNotEmpty()
  _id: string;

  @IsString()
  @IsOptional()
  productId?: string;

  @IsString()
  @IsOptional()
  priceId?: string;

  @IsString()
  @IsOptional()
  currency?: string;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsNumber()
  qty: number;

  @IsNumber()
  amount: number;
}

export class GhlTotalSummaryDto {
  @IsNumber()
  subTotal: number;

  @IsNumber()
  discount: number;
}

// Base GHL webhook structure
export class GhlWebhookDto {
  @IsString()
  @IsNotEmpty()
  type: string; // Event type (e.g., 'InvoiceCreate', 'InvoicePaid', etc.)

  @IsString()
  @IsNotEmpty()
  locationId: string;

  @IsObject()
  @IsNotEmpty()
  data: any; // The actual event data (invoice, contact, opportunity, etc.)

  @IsString()
  @IsOptional()
  id?: string; // Event ID

  @IsString()
  @IsOptional()
  timestamp?: string; // Event timestamp

  @IsObject()
  @IsOptional()
  meta?: any; // Additional metadata
}

// GHL Invoice webhook DTO (based on actual webhook payload structure)
export class GhlInvoiceWebhookDto {
  @IsString()
  @IsNotEmpty()
  _id: string;

  @IsEnum(GhlInvoiceStatus)
  status: GhlInvoiceStatus;

  @IsOptional()
  liveMode?: boolean;

  @IsNumber()
  amountPaid: number;

  @IsString()
  @IsNotEmpty()
  altId: string;

  @IsString()
  @IsOptional()
  altType?: string;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsObject()
  @IsOptional()
  businessDetails?: GhlBusinessDetailsDto;

  @IsString()
  @IsOptional()
  invoiceNumber?: string;

  @IsString()
  @IsOptional()
  currency?: string;

  @IsObject()
  @IsOptional()
  contactDetails?: GhlContactDetailsDto;

  @IsString()
  @IsOptional()
  issueDate?: string;

  @IsString()
  @IsOptional()
  dueDate?: string;

  @IsObject()
  @IsOptional()
  discount?: GhlDiscountDto;

  @IsOptional()
  invoiceItems?: GhlInvoiceItemDto[];

  @IsNumber()
  total: number;

  @IsString()
  @IsOptional()
  title?: string;

  @IsNumber()
  amountDue: number;

  @IsString()
  @IsOptional()
  createdAt?: string;

  @IsString()
  @IsOptional()
  updatedAt?: string;

  @IsObject()
  @IsOptional()
  totalSummary?: GhlTotalSummaryDto;
}
