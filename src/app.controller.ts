import { Controller, Get, Post, Body, Query } from '@nestjs/common';
import { AppService } from './app.service';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) {}

  @Get()
  getHello(@Query('code') code: string): string {
    return this.appService.getHello();
  }

  @Get('health')
  healthCheck() {
    return {
      status: 'ok - ' + new Date().toISOString(),
      timestamp: new Date().toISOString(),
      service: 'enrollio-be',
    };
  }

  @Post('log-body')
  logBody(@Body() body: any) {
    console.log('Incoming Body:', JSON.stringify(body, null, 2));
  }
}
