import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateEmailTemplateConfigDto } from './dto/create-email-template-config.dto';
import { UpdateEmailTemplateConfigDto } from './dto/update-email-template-config.dto';
import { InjectModel } from '@nestjs/mongoose';
import { EmailTemplateConfig } from 'src/database/schema/email-template-config';
import { Model, Types } from 'mongoose';

@Injectable()
export class EmailTemplateConfigService {
  constructor(
    @InjectModel(EmailTemplateConfig.name)
    private emailTemplateModel: Model<EmailTemplateConfig>,
  ) {}

  async create(
    createEmailTemplateConfigDto: CreateEmailTemplateConfigDto,
    locationId: string,
  ) {
    const studioId = Types.ObjectId.createFromHexString(locationId);
    const emailTemplate = new this.emailTemplateModel({
      ...createEmailTemplateConfigDto,
      studio: studioId,
    });
    return await emailTemplate.save();
  }

  async findAll(locationId: string) {
    const studioId = Types.ObjectId.createFromHexString(locationId);
    return await this.emailTemplateModel.find({ studio: studioId }).exec();
  }

  async findOne(id: string, locationId: string) {
    const studioId = Types.ObjectId.createFromHexString(locationId);
    const emailTemplate = await this.emailTemplateModel
      .findOne({
        _id: id,
        studio: studioId,
      })
      .exec();

    if (!emailTemplate) {
      throw new NotFoundException('Email template not found');
    }

    return emailTemplate;
  }

  async update(
    id: string,
    updateEmailTemplateConfigDto: UpdateEmailTemplateConfigDto,
    locationId: string,
  ) {
    const studioId = Types.ObjectId.createFromHexString(locationId);
    const updatedTemplate = await this.emailTemplateModel
      .findOneAndUpdate(
        { _id: id, studio: studioId },
        { $set: updateEmailTemplateConfigDto },
        { new: true },
      )
      .exec();

    if (!updatedTemplate) {
      throw new NotFoundException('Email template not found');
    }

    return updatedTemplate;
  }

  async remove(id: string, locationId: string) {
    const studioId = Types.ObjectId.createFromHexString(locationId);
    const result = await this.emailTemplateModel
      .deleteOne({
        _id: id,
        studio: studioId,
      })
      .exec();

    if (result.deletedCount === 0) {
      throw new NotFoundException('Email template not found');
    }

    return { deleted: true };
  }

  async updateImageUrl(studioId: string, imageUrl: string) {
    const locationId = Types.ObjectId.createFromHexString(studioId);
    return this.emailTemplateModel
      .findOneAndUpdate(
        { studio: locationId },
        {
          imageUrl,
          urlGeneratedAt: new Date(),
        },
        { upsert: true, new: true },
      )
      .exec();
  }

  async findByStudio(studioId: string): Promise<string> {
    const locationObjectId = Types.ObjectId.createFromHexString(studioId);

    const template = await this.emailTemplateModel
      .findOne({ studio: locationObjectId })
      .select('imageUrl -_id')
      .exec();

    if (!template) {
      throw new NotFoundException(
        'No email template configuration found for this studio',
      );
    }

    return template.imageUrl;
  }
}
