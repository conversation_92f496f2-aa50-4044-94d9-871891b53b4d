import { Test, TestingModule } from '@nestjs/testing';
import { EmailTemplateConfigService } from './email-template-config.service';

describe('EmailTemplateConfigService', () => {
  let service: EmailTemplateConfigService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [EmailTemplateConfigService],
    }).compile();

    service = module.get<EmailTemplateConfigService>(
      EmailTemplateConfigService,
    );
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
