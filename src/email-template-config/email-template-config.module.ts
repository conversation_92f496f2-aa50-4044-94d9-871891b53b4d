import { Modu<PERSON> } from '@nestjs/common';
import { EmailTemplateConfigService } from './email-template-config.service';
import { EmailTemplateConfigController } from './email-template-config.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { EmailTemplateConfig } from './entities/email-template-config.entity';
import { EmailTemplateConfigSchema } from 'src/database/schema/email-template-config';
import { JwtModule } from '@nestjs/jwt';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: EmailTemplateConfig.name, schema: EmailTemplateConfigSchema },
    ]),
    JwtModule,
  ],
  controllers: [EmailTemplateConfigController],
  providers: [EmailTemplateConfigService],
  exports: [EmailTemplateConfigService],
})
export class EmailTemplateConfigModule {}
