import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Req,
  UseGuards,
} from '@nestjs/common';
import { EmailTemplateConfigService } from './email-template-config.service';
import { CreateEmailTemplateConfigDto } from './dto/create-email-template-config.dto';
import { UpdateEmailTemplateConfigDto } from './dto/update-email-template-config.dto';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { Request } from 'express';

@Controller('email-template-config')
export class EmailTemplateConfigController {
  constructor(
    private readonly emailTemplateConfigService: EmailTemplateConfigService,
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  async create(
    @Body() createEmailTemplateConfigDto: CreateEmailTemplateConfigDto,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return this.emailTemplateConfigService.create(
      createEmailTemplateConfigDto,
      locationId,
    );
  }

  @Get('studio')
  @UseGuards(JwtAuthGuard)
  async findByStudio(@Req() request: Request) {
    const locationId = request['locationId'];
    return this.emailTemplateConfigService.findByStudio(locationId);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  async findAll(@Req() request: Request) {
    const locationId = request['locationId'];
    return this.emailTemplateConfigService.findAll(locationId);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  async findOne(@Param('id') id: string, @Req() request: Request) {
    const locationId = request['locationId'];
    return this.emailTemplateConfigService.findOne(id, locationId);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  async update(
    @Param('id') id: string,
    @Body() updateEmailTemplateConfigDto: UpdateEmailTemplateConfigDto,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return this.emailTemplateConfigService.update(
      id,
      updateEmailTemplateConfigDto,
      locationId,
    );
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  async remove(@Param('id') id: string, @Req() request: Request) {
    const locationId = request['locationId'];
    return this.emailTemplateConfigService.remove(id, locationId);
  }
}
