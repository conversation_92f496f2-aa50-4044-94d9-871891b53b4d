import { Test, TestingModule } from '@nestjs/testing';
import { EmailTemplateConfigController } from './email-template-config.controller';
import { EmailTemplateConfigService } from './email-template-config.service';

describe('EmailTemplateConfigController', () => {
  let controller: EmailTemplateConfigController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [EmailTemplateConfigController],
      providers: [EmailTemplateConfigService],
    }).compile();

    controller = module.get<EmailTemplateConfigController>(
      EmailTemplateConfigController,
    );
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
