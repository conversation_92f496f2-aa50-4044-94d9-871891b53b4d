import {
  ConflictException,
  Inject,
  forwardRef,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CreateLeadDto } from './dto/create-lead.dto';
import { UpdateLeadDto } from './dto/update-lead.dto';
import { Lead } from '../database/schema/lead';
import { StudiosService } from 'src/studios/studios.service';
import { GohighlevelService } from 'src/gohighlevel/gohighlevel.service';
import { Parent } from 'src/database/schema/parent';
import { ParentsService } from 'src/parents/parents.service';

interface FindAllOptions {
  studioId: string;
  page?: number;
  limit?: number;
  sort?: string;
}

@Injectable()
export class LeadsService {
  constructor(
    @InjectModel(Lead.name) private readonly leadModel: Model<Lead>,
    @InjectModel(Parent.name) private readonly parentModel: Model<Parent>,
    @Inject(forwardRef(() => StudiosService))
    private readonly studiosService: StudiosService,
    @Inject(forwardRef(() => GohighlevelService))
    private readonly gohighlevelService: GohighlevelService,
    @Inject(forwardRef(() => ParentsService))
    private readonly parentsService: ParentsService,
  ) {}

  async create(createLeadDto: CreateLeadDto): Promise<Lead> {
    const { locationId, ...leadData } = createLeadDto;
    const studio = await this.studiosService.findByLocationIdString(locationId);
    const existingParent = await this.parentModel
      .findOne({
        email: leadData.email,
        studioId: studio._id,
      })
      .exec();

    if (existingParent) {
      throw new ConflictException(
        'This email is already associated with a parent portal account.',
      );
    }

    const existingLead = await this.leadModel
      .findOne({
        email: leadData.email,
        studioId: studio._id,
      })
      .exec();

    if (existingLead) {
      return await this.leadModel
        .findByIdAndUpdate(existingLead._id, { ...leadData }, { new: true })
        .exec();
    }

    let ghlContactId;
    if (createLeadDto.ghlContactId) {
      ghlContactId = createLeadDto.ghlContactId;
    } else {
      const { firstName, lastName, email, primaryPhone } = createLeadDto;
      const ghlContactResponse = await this.gohighlevelService.createContact(
        {
          name: `${firstName} ${lastName}`,
          email: email.toLowerCase(),
          phone: primaryPhone,
        },
        studio.locationId,
      );
      ghlContactId = ghlContactResponse.contact?.id ?? ghlContactResponse.id;
    }

    try {
      const tagAdded = await this.gohighlevelService.addTagToContactByEmail(
        studio._id.toString(),
        leadData.email,
        'portal_lead',
      );
      if (!tagAdded) {
        console.warn(
          `Failed to add "portal_lead" tag - contact not found for email: ${leadData.email}`,
        );
      }
    } catch (error) {
      console.error(
        `Error adding "portal_lead" tag to contact: ${error.message}`,
      );
    }

    const createdLead = new this.leadModel({
      ...leadData,
      studioId: studio._id,
      ghlContactId,
    });
    return await createdLead.save();
  }

  async findAll(options: FindAllOptions): Promise<Lead[]> {
    const { studioId, page = 1, limit = 10, sort } = options;

    const query = this.leadModel.find({ studioId });

    if (sort) {
      query.sort(sort);
    }

    if (page && limit) {
      query.skip((page - 1) * limit).limit(limit);
    }

    return query.exec();
  }

  async findOne(id: string): Promise<Lead> {
    const lead = await this.leadModel.findById(id).exec();
    if (!lead) {
      throw new NotFoundException(`Lead with ID ${id} not found`);
    }
    return lead;
  }

  async update(id: string, updateLeadDto: UpdateLeadDto): Promise<Lead> {
    const updatedLead = await this.leadModel
      .findByIdAndUpdate(id, updateLeadDto, { new: true })
      .exec();

    if (!updatedLead) {
      throw new NotFoundException(`Lead with ID ${id} not found`);
    }
    return updatedLead;
  }

  async remove(id: string): Promise<void> {
    const result = await this.leadModel.findByIdAndDelete(id).exec();
    if (!result) {
      throw new NotFoundException(`Lead with ID ${id} not found`);
    }
  }

  async findByEmail(email: string, locationId: string): Promise<Lead> {
    const studio = await this.studiosService.findByLocationIdString(locationId);
    const lead = await this.leadModel
      .findOne({
        email,
        studioId: studio._id,
      })
      .exec();

    if (!lead) {
      throw new NotFoundException(
        `Lead with email ${email} not found for this location`,
      );
    }
    return lead;
  }
}
