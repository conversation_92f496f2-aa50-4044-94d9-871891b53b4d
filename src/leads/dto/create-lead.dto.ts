import {
  IsString,
  IsEmail,
  IsNot<PERSON>mpty,
  IsMongoId,
  IsO<PERSON>al,
} from 'class-validator';
import { Types } from 'mongoose';

export class CreateLeadDto {
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @IsString()
  @IsNotEmpty()
  lastName: string;

  @IsString()
  @IsNotEmpty()
  primaryPhone: string;

  @IsString()
  @IsNotEmpty()
  relation: string;

  @IsEmail()
  @IsNotEmpty()
  email: string;

  @IsString()
  @IsNotEmpty()
  locationId: string;

  @IsString()
  @IsOptional()
  ghlContactId?: string;
}
