import { forwardRef, Module } from '@nestjs/common';
import { LeadsService } from './leads.service';
import { LeadsController } from './leads.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Lead, LeadSchema } from 'src/database/schema/lead';
import { StudiosModule } from 'src/studios/studios.module';
import { GohighlevelModule } from 'src/gohighlevel/gohighlevel.module';
import { Parent, ParentSchema } from 'src/database/schema/parent';
import { ParentsModule } from 'src/parents/parents.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Lead.name, schema: LeadSchema },
      { name: Parent.name, schema: ParentSchema },
    ]),
    forwardRef(() => StudiosModule),
    forwardRef(() => GohighlevelModule),
    forwardRef(() => ParentsModule),
  ],
  controllers: [LeadsController],
  providers: [LeadsService],
  exports: [LeadsService, MongooseModule], // Export MongooseModule to share model registrations
})
export class LeadsModule {}
