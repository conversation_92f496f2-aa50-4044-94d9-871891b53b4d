import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  PaymentTransaction,
  PaymentTransactionDocument,
} from 'src/database/schema/paymentTransaction';
import { PaymentTransactionService } from 'src/payment-transaction/payment-transaction.service';
import {
  PaymentTransactionStatus,
  PaymentMethod,
  InvoiceStatus,
  PaymentProcessingMethod,
} from 'src/stripe/type';
import { StripeService } from 'src/stripe/stripe.service';
import { StripeWalletService } from 'src/stripe/services/wallet.service';
import { ParentsService } from 'src/parents/parents.service';
import { ReasonType } from 'src/database/schema/walletTransaction';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import * as dayjs from 'dayjs';
import { ConfigService } from '@nestjs/config';
import { ParentDocument } from 'src/database/schema/parent';

@Injectable()
export class ScheduledPaymentsCronService {
  private readonly logger = new Logger(ScheduledPaymentsCronService.name);

  constructor(
    @InjectModel(PaymentTransaction.name)
    private paymentTransactionModel: Model<PaymentTransaction>,
    private readonly paymentTransactionService: PaymentTransactionService,
    private readonly stripeService: StripeService,
    private readonly walletService: StripeWalletService,
    private readonly parentsService: ParentsService,
    @InjectModel(SubscriptionInvoice.name)
    private subscriptionInvoiceModel: Model<SubscriptionInvoice>,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Cron job that runs daily at 9 AM to process scheduled payments
   * Finds transactions with status "scheduled" and billing date >= today
   */
  @Cron('0 0 16 * * *', {
    // 14:00 UTC = 09:00 EST (9:00 AM EST) = 19:30 IST (7:30 PM IST)
    name: 'process-scheduled-payments',
    timeZone: 'UTC',
  })
  async processScheduledPayments(): Promise<void> {
    try {
      // Check if cron jobs should run in this environment
      const environment = this.configService.get<string>('NODE_ENV', 'local');

      if (environment === 'local') {
        this.logger.log(
          'Scheduled payments cron job skipped - disabled in current environment',
        );
        return;
      }
      this.logger.log('Starting scheduled payments processing...');

      // Get current date (start of day)
      const today = dayjs().endOf('day').toDate();

      this.logger.log(`Processing payments for date: ${today.toISOString()}`);

      // Find scheduled transactions with billing date today or after today
      const scheduledTransactions = await this.findScheduledTransactions(today);

      this.logger.log(
        `Found ${scheduledTransactions.length} scheduled transactions to process`,
      );

      if (scheduledTransactions.length === 0) {
        this.logger.log('No scheduled transactions found for processing');
        return;
      }

      // Process each transaction sequentially to prevent connection pool exhaustion
      let successful = 0;
      let failed = 0;
      const errors: string[] = [];

      for (const transaction of scheduledTransactions) {
        try {
          const studio = transaction.studioId as any;

          if (
            studio.paymentProcessingMethod === PaymentProcessingMethod.MANUAL
          ) {
            continue;
          }

          await this.processTransaction(
            transaction as PaymentTransactionDocument,
          );
          successful++;
          this.logger.log(
            `✅ Successfully processed transaction ${(transaction as any)._id}`,
          );
        } catch (error) {
          failed++;
          const errorMessage = `❌ Failed to process transaction ${(transaction as any)._id}: ${error.message}`;
          errors.push(errorMessage);
          this.logger.error(errorMessage);
        }
      }

      this.logger.log(
        `Processed ${scheduledTransactions.length} transactions - Success: ${successful}, Failed: ${failed}`,
      );

      // Log any failures
      if (errors.length > 0) {
        this.logger.error(`Transaction processing errors:`, errors);
      }
    } catch (error) {
      this.logger.error(
        `Error in scheduled payments cron job: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Find scheduled transactions that are due today or after today
   */
  private async findScheduledTransactions(
    toDate: Date,
  ): Promise<PaymentTransaction[]> {
    try {
      const transactions = await this.paymentTransactionModel
        .find({
          status: PaymentTransactionStatus.SCHEDULED,
          amount: { $gt: 0 },
          'metadata.billingDate': {
            $lte: toDate,
          },
        })
        .populate({
          path: 'studioId',
          model: 'Studio',
          select: 'paymentProcessingMethod',
        })
        .populate({
          path: 'parentId',
          model: 'Parent',
        })
        .lean()
        .exec();

      this.logger.log(
        `Query found ${transactions.length} scheduled transactions`,
      );

      return transactions as PaymentTransaction[];
    } catch (error) {
      this.logger.error(
        `Error finding scheduled transactions: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Process a single scheduled transaction with wallet logic
   */
  private async processTransaction(
    transaction: PaymentTransactionDocument,
  ): Promise<void> {
    try {
      this.logger.log(
        `Processing transaction ${(transaction as any)._id} for amount $${transaction.amount}`,
      );

      // Only apply wallet logic if we have valid parentId and studentId
      let walletCalculation = null;
      const hasValidParentId =
        transaction.parentId &&
        Types.ObjectId.isValid(transaction.parentId.toString());

      if (hasValidParentId) {
        // Calculate wallet usage for this transaction with timeout
        try {
          // Add timeout to prevent hanging
          walletCalculation = await Promise.race([
            this.walletService.calculateWalletUsageForTransaction({
              parentId: transaction.parentId.toString(),
              transactionAmount: transaction.amount,
              studioId: transaction.studioId.toString(),
              studentId: transaction.studentId?.toString(),
            }),
            new Promise((_, reject) =>
              setTimeout(
                () => reject(new Error('Wallet calculation timeout')),
                10000,
              ),
            ),
          ]);
        } catch (error) {
          this.logger.error(
            `Wallet calculation failed for transaction ${(transaction as any)._id}: ${error.message}`,
          );
          // Continue without wallet calculation if it fails
          walletCalculation = null;
        }

        // If wallet can pay fully, skip Stripe and mark as paid
        if (walletCalculation?.canPayFully) {
          await this.processFullWalletPayment(transaction, walletCalculation);
          return;
        }

        // Store wallet payment info in transaction metadata if wallet will be used
        if (walletCalculation?.walletAmountUsed > 0) {
          if (!transaction.metadata) {
            transaction.metadata = {} as any;
          }
          (transaction.metadata as any).walletAmountUsed =
            walletCalculation.walletAmountUsed;
          (transaction.metadata as any).originalAmount = transaction.amount;
          (transaction.metadata as any).paymentIntentAmount =
            walletCalculation.paymentIntentAmount;
          (transaction.metadata as any).appliedAt = new Date();
        }
      }

      const parent = transaction.parentId as unknown as ParentDocument;

      // Check if payment method is available
      const paymentMethodOnFile =
        await this.stripeService.checkPaymentMethodOnFile(
          transaction.studioId._id.toString(),
          parent.stripeCustomerId,
          transaction.paymentMethod,
        );

      if (paymentMethodOnFile.hasPaymentMethod) {
        // Create payment intent for remaining amount after wallet usage
        const metadata = {
          groupId: transaction.groupId,
          isScheduledPayment: true,
        };
        const paymentIntentId =
          await this.stripeService.captureManualTransaction({
            transactionId: (transaction as any)._id.toString(),
            studioId: transaction.studioId._id.toString(),
            metadata: metadata,
          });

        // Update transaction status to pending
        await this.paymentTransactionService.update(
          (transaction as any)._id.toString(),
          {
            status: PaymentTransactionStatus.PENDING,
            metadata: {
              ...transaction.metadata,
              paymentIntentId: paymentIntentId,
            },
          },
        );

        const invoice =
          await this.paymentTransactionService.findOldestInvoiceByStatus(
            (transaction as any)._id.toString(),
            PaymentTransactionStatus.SCHEDULED,
          );

        if (invoice) {
          invoice.status = InvoiceStatus.PENDING;
          invoice.metadata.paymentIntentId = paymentIntentId;
          await invoice.save();
        }

        this.logger.log(
          `Successfully processed transaction ${(transaction as any)._id}${
            walletCalculation
              ? ` - Amount: $${walletCalculation.paymentIntentAmount} (Wallet: $${walletCalculation.walletAmountUsed})`
              : ''
          }`,
        );
      } else {
        await this.handleTransactionError(transaction);
        return;
      }
    } catch (error) {
      this.logger.error(
        `Error processing transaction ${(transaction as any)._id}: ${error.message}`,
        error.stack,
      );
      await this.handleTransactionError(transaction);
    }
  }

  /**
   * Process full wallet payment (skip Stripe entirely)
   */
  private async processFullWalletPayment(
    transaction: PaymentTransactionDocument,
    walletCalculation: any,
  ): Promise<void> {
    try {
      // Deduct wallet balance immediately for full wallet payments
      await this.parentsService.removeWalletBalance({
        parentId: transaction.parentId.toString(),
        amount: walletCalculation.walletAmountUsed,
        reason: ReasonType.CLASS_BUY,
        paymentMethod: PaymentMethod.WALLET,
        studioId: transaction.studioId.toString(),
        studentId: transaction.studentId?.toString(),
      });

      // Update transaction status to completed
      await this.paymentTransactionService.update(
        (transaction as any)._id.toString(),
        {
          status: PaymentTransactionStatus.PAID,
          paymentMethod: PaymentMethod.WALLET,
        },
      );

      const invoice = await this.subscriptionInvoiceModel.findOne({
        metadata: {
          internalTransactionId: transaction._id.toString(),
        },
      });

      if (invoice) {
        invoice.status = InvoiceStatus.PAID;
        invoice.paymentMethod = PaymentMethod.WALLET;
        await invoice.save();
      }

      this.logger.log(
        `Transaction ${(transaction as any)._id} paid fully with wallet - Amount: $${walletCalculation.walletAmountUsed}`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing full wallet payment for transaction ${(transaction as any)._id}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Handle transaction processing errors
   */
  private async handleTransactionError(
    transaction: PaymentTransactionDocument,
  ): Promise<void> {
    await this.paymentTransactionService.update(
      (transaction as any)._id.toString(),
      {
        status: PaymentTransactionStatus.FAILED,
      },
    );

    await this.subscriptionInvoiceModel.updateOne(
      {
        status: InvoiceStatus.PENDING,
        metadata: {
          internalTransactionId: transaction._id.toString(),
        },
      },
      {
        status: InvoiceStatus.FAILED,
      },
    );
  }
}
