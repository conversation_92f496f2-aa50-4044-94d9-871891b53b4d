import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Student } from 'src/database/schema/student';
import { Event } from 'src/database/schema/event';
import { CronExpression } from '@nestjs/schedule';
import { Cron } from '@nestjs/schedule';
import { StripeHistoryService } from 'src/stripe/services/remove-and-create-history.service';
import {
  PaymentTransactionStatus,
  InvoiceStatus,
  SubscriptionStatus,
} from 'src/stripe/type';

@Injectable()
export class ExpiredEventsCronService {
  private readonly logger = new Logger(ExpiredEventsCronService.name);

  constructor(
    @InjectModel(Student.name) private studentModel: Model<Student>,
    @InjectModel(Event.name) private eventModel: Model<Event>,
    @Inject(forwardRef(() => StripeHistoryService))
    private readonly StripeHistoryService: StripeHistoryService,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT, {
    name: 'expired-events',
    disabled: true,
    timeZone: 'America/New_York',
  })
  async handleExpiredEvents() {
    try {
      this.logger.log('Starting expired one-time events check');
      const currentDate = new Date();

      // Find all students with events
      const studentsWithEvents = await this.studentModel
        .find({
          'events.eventId': { $exists: true, $ne: [] },
        })
        .populate({
          path: 'events.eventId',
          model: 'Event',
          select: 'oneTime startDate endDate',
        });

      for (const student of studentsWithEvents) {
        const expiredEvents = [];

        // Process each event for the student
        for (const eventEntry of student.events) {
          const event = await this.eventModel.findById(eventEntry.eventId);

          // Skip if event not found or not a one-time event
          if (!event) continue;

          // Check if event has expired
          if (event.oneTime) {
            if (event.startDate && new Date(event.startDate) < currentDate) {
              expiredEvents.push(eventEntry.eventId);

              await this.StripeHistoryService.removeEventAndCreateHistory(
                student._id as Types.ObjectId,
                student.parentId as Types.ObjectId,
                eventEntry.eventId.toString(),
                student.studioId.toString(),
                SubscriptionStatus.TERM_ENDED,
              );
            }
          } else {
            if (event.endDate && new Date(event.endDate) < currentDate) {
              expiredEvents.push(eventEntry.eventId);
              await this.StripeHistoryService.removeEventAndCreateHistory(
                student._id as Types.ObjectId,
                student.parentId as Types.ObjectId,
                eventEntry.eventId.toString(),
                student.studioId.toString(),
                SubscriptionStatus.TERM_ENDED,
              );
            }
          }
        }

        // Remove expired events from student document
        if (expiredEvents.length > 0) {
          await this.studentModel.updateOne(
            { _id: student._id },
            {
              $pull: {
                events: {
                  eventId: { $in: expiredEvents },
                },
              },
            },
          );

          this.logger.log(
            `Removed ${expiredEvents.length} expired events from student ${student._id}`,
          );
        }
      }

      this.logger.log('Completed expired one-time events check');
    } catch (error) {
      this.logger.error('Error in handleExpiredOneTimeEvents:', error);
    }
  }
}
