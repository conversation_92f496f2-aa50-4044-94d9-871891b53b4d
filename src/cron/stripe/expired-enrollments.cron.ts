import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import Stripe from 'stripe';
import { Credential } from 'src/database/schema/stripeCredential';
import { StudiosService } from 'src/studios/studios.service';
import { ParentsService } from 'src/parents/parents.service';
import { StudentsService } from 'src/students/students.service';
import { Enrollment } from 'src/database/schema/enrollment';
import { Student } from 'src/database/schema/student';
import { CurrencyService } from 'src/currency/currency.service';
import { DiscountService } from 'src/discount/discount.service';
import { CronExpression } from '@nestjs/schedule';
import { Cron } from '@nestjs/schedule';
import { StripeHistoryService } from 'src/stripe/services/remove-and-create-history.service';
import { StripeCommonService } from 'src/stripe/services/stripe-common.service';
import {
  PaymentTransactionStatus,
  InvoiceStatus,
  SubscriptionStatus,
} from 'src/stripe/type';
import { DiscountCouponService } from 'src/discount-coupon/discount-coupon.service';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ExpiredEnrollmentsCronService {
  private readonly logger = new Logger(ExpiredEnrollmentsCronService.name);

  constructor(
    @InjectModel(Credential.name) private credentialModel: Model<Credential>,
    @Inject(forwardRef(() => ParentsService))
    private readonly parentsService: ParentsService,
    @Inject(forwardRef(() => StudentsService))
    private readonly studentsService: StudentsService,
    @Inject(forwardRef(() => StudiosService))
    private readonly studioService: StudiosService,
    @InjectModel(Student.name) private studentModel: Model<Student>,
    @InjectModel(Enrollment.name) private enrollmentModel: Model<Enrollment>,
    @Inject(forwardRef(() => CurrencyService))
    private readonly currencyService: CurrencyService,
    @Inject(forwardRef(() => DiscountService))
    private readonly discountService: DiscountService,
    @Inject(forwardRef(() => StripeHistoryService))
    private readonly StripeHistoryService: StripeHistoryService,
    @Inject(forwardRef(() => StripeCommonService))
    private readonly StripeCommonService: StripeCommonService,
    @Inject(forwardRef(() => DiscountCouponService))
    private readonly discountCouponService: DiscountCouponService,
    @Inject(forwardRef(() => SubscriptionService))
    private readonly subscriptionService: SubscriptionService,
    private readonly configService: ConfigService,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT, {
    name: 'expired-enrollments',
    disabled: true,
    timeZone: 'America/New_York',
  })
  async handleExpiredEnrollments() {
    const environment = this.configService.get<string>('NODE_ENV', 'local');

    if (environment === 'local') {
      this.logger.log(
        'Expired enrollments cron job skipped - disabled in current environment',
      );
      return;
    }
    this.logger.log('Starting expired enrollments check...');

    try {
      // Get all students with their enrollments populated
      const students = await this.studentModel
        .find({
          'enrollments.enrollmentId': { $exists: true, $ne: [] },
        })
        .populate({
          path: 'enrollments.enrollmentId',
          model: 'Enrollment',
          select: 'endDate studioId',
        })
        .exec();

      const now = new Date();

      for (const student of students) {
        if (!student.enrollments?.length) continue;

        for (const enrollment of student.enrollments) {
          // Skip if enrollment is invalid or already processed
          if (
            !enrollment?.enrollmentId ||
            enrollment.subscriptionStatus === SubscriptionStatus.TERM_ENDED
          )
            continue;

          const enrollmentDoc = await this.enrollmentModel.findById(
            enrollment.enrollmentId.id,
          ); // Type assertion for populated enrollment
          if (!enrollmentDoc?.endDate) continue;

          // Check if enrollment has expired
          if (new Date(enrollmentDoc.endDate) < now) {
            await this.StripeHistoryService.removeEnrollmentAndCreateHistory(
              student._id as Types.ObjectId,
              student.parentId as Types.ObjectId,
              enrollmentDoc._id.toString(),
              enrollmentDoc.studio.toString(),
              SubscriptionStatus.TERM_ENDED,
            );

            // Recalculate discounts for family
            const parent = await this.parentsService.findOne(
              student.parentId.toString(),
            );
            const allStudents =
              await this.studentsService.getStudentsByParentId(
                parent._id.toString(),
              );

            // Get credentials for Stripe
            const credential = await this.credentialModel.findOne({
              studioId: student.studioId.toString(),
            });
            const stripe = new Stripe(credential.apiSecret, {
              apiVersion: '2025-02-24.acacia',
            });

            // Get and sort all active students by their earliest enrollment date
            const activeStudents = allStudents
              .filter((s) => {
                const activeEnrollments = s.enrollments.filter(
                  (e) => e.subscriptionStatus === 'active' && e.subscriptionId,
                );
                return activeEnrollments.length > 0;
              })
              .sort((a, b) => {
                const aDate = Math.min(
                  ...a.enrollments
                    .filter(
                      (e) =>
                        e.subscriptionStatus === 'active' && e.subscriptionId,
                    )
                    .map((e) => new Date(e.enrolledDate).getTime()),
                );
                const bDate = Math.min(
                  ...b.enrollments
                    .filter(
                      (e) =>
                        e.subscriptionStatus === 'active' && e.subscriptionId,
                    )
                    .map((e) => new Date(e.enrolledDate).getTime()),
                );
                return aDate - bDate;
              });

            // Recalculate discounts for each active student
            for (let i = 0; i < activeStudents.length; i++) {
              const currentStudent = activeStudents[i];
              const studentPosition = i + 1;

              const activeEnrollments = currentStudent.enrollments
                .filter(
                  (e) => e.subscriptionStatus === 'active' && e.subscriptionId,
                )
                .sort(
                  (a, b) =>
                    new Date(a.enrolledDate).getTime() -
                    new Date(b.enrolledDate).getTime(),
                );

              // Process each enrollment for the current student
              for (let j = 0; j < activeEnrollments.length; j++) {
                const activeEnrollment = activeEnrollments[j];
                const classPosition = j + 1;

                // Calculate new discounts
                const multiClassDiscount =
                  await this.discountService.calculateDiscount({
                    studioId: student.studioId.toString(),
                    student: {
                      firstName: currentStudent.firstName,
                      lastName: currentStudent.lastName,
                      classPosition,
                      studentPosition: 0,
                      tuitionFee: (activeEnrollment.enrollmentId as any)
                        .tuitionFee,
                      enrollmentId: activeEnrollment.enrollmentId.toString(),
                    },
                    category: 'multi-class',
                  });

                const multiStudentDiscount =
                  await this.discountService.calculateDiscount({
                    studioId: student.studioId.toString(),
                    student: {
                      firstName: currentStudent.firstName,
                      lastName: currentStudent.lastName,
                      classPosition: 0,
                      studentPosition,
                      tuitionFee: (activeEnrollment.enrollmentId as any)
                        .tuitionFee,
                      enrollmentId: activeEnrollment.enrollmentId.toString(),
                    },
                    category: 'multi-student',
                  });

                const totalDiscount =
                  multiClassDiscount.totalDiscount +
                  multiStudentDiscount.totalDiscount;

                if (totalDiscount > 0) {
                  const coupon = await this.discountCouponService.createCoupon({
                    type: 'fixed',
                    value: totalDiscount,
                    name: `Fixed Discount of ${totalDiscount}`,
                    studioId: Types.ObjectId.createFromHexString(
                      student.studioId.toString(),
                    ),
                    category: 'enrollment',
                  });

                  // Update subscription with new coupon ID
                  try {
                    await this.subscriptionService.update(
                      activeEnrollment.subscriptionId,
                      {
                        appliedCouponId: Types.ObjectId.createFromHexString(
                          coupon._id.toString(),
                        ),
                      },
                    );
                  } catch (error) {
                    this.logger.error(
                      'Error updating subscription with new coupon:',
                      error,
                    );
                  }
                }
              }
            }

            this.logger.log(
              `Processed term end for student ${student._id} and enrollment ${enrollment.enrollmentId}`,
            );
          }
        }
      }

      this.logger.log('Completed expired enrollments check');
    } catch (error) {
      this.logger.error('Error processing expired enrollments:', error);
    }
  }
}
