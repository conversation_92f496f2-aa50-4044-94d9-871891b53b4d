import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import Stripe from 'stripe';
import { CronExpression } from '@nestjs/schedule';
import { Cron } from '@nestjs/schedule';
import { Credential } from 'src/database/schema/stripeCredential';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { ParentsService } from 'src/parents/parents.service';
import { PaymentTransactionService } from 'src/payment-transaction/payment-transaction.service';
import {
  PaymentTransactionStatus,
  InvoiceStatus,
  PaymentMethod,
} from 'src/stripe/type';
import { StudiosService } from 'src/studios/studios.service';
import { CurrencyService } from 'src/currency/currency.service';
import { ConfigService } from '@nestjs/config';
import { getGcpIpAddress } from 'src/utils/helperFunction';

@Injectable()
export class RetryFailedPaymentsCronService {
  private readonly logger = new Logger(RetryFailedPaymentsCronService.name);
  private readonly DEFAULT_MAX_RETRY_ATTEMPTS = 5;
  private readonly RETRY_INTERVAL_DAYS = 3;

  constructor(
    @InjectModel(Credential.name) private credentialModel: Model<Credential>,
    private readonly paymentTransactionService: PaymentTransactionService,
    @Inject(forwardRef(() => ParentsService))
    private readonly parentsService: ParentsService,
    @Inject(forwardRef(() => StudiosService))
    private readonly studiosService: StudiosService,
    @Inject(forwardRef(() => CurrencyService))
    private readonly currencyService: CurrencyService,
    private readonly configService: ConfigService,
    @InjectQueue('invoice-status-update') private invoiceStatusQueue: Queue,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_2AM, {
    name: 'retry-failed-payments',
    disabled: true,
    timeZone: 'America/New_York',
  })
  async retryFailedPayments() {
    // Check if cron jobs should run in this environment
    const environment = this.configService.get<string>('NODE_ENV', 'local');

    if (environment === 'local') {
      this.logger.log(
        'Token refresh cron job skipped - disabled in current environment',
      );
      return;
    }

    this.logger.log('Starting failed payments retry job...');

    try {
      // Find failed payment transactions that are eligible for retry
      const failedTransactions =
        await this.paymentTransactionService.findTransactionsByStatus(
          PaymentTransactionStatus.FAILED,
        );

      this.logger.log(`Found ${failedTransactions.length} failed transactions`);

      for (const transaction of failedTransactions) {
        // Get the studio configuration
        let maxRetryAttempts = this.DEFAULT_MAX_RETRY_ATTEMPTS;
        try {
          const studio = await this.studiosService.findOne(
            transaction.studioId.toString(),
          );
          if (studio) {
            // Use studio-specific setting if available, otherwise use default
            if (
              studio.failedPaymentRetryCount !== undefined &&
              studio.failedPaymentRetryCount >= 0
            ) {
              maxRetryAttempts = studio.failedPaymentRetryCount;
              this.logger.log(
                `Using studio-specific retry count (${maxRetryAttempts}) for transaction ${transaction._id}, studio ${studio._id}`,
              );
            } else {
              this.logger.log(
                `Using default retry count (${maxRetryAttempts}) for transaction ${transaction._id}, studio ${studio._id} has no setting`,
              );
            }
          }
        } catch (error) {
          this.logger.warn(
            `Could not retrieve studio settings for transaction ${transaction._id}, using default retry count (${maxRetryAttempts}):`,
            error.message,
          );
        }

        // Skip if already reached max retry attempts
        if (transaction.retryAttempts >= maxRetryAttempts) {
          this.logger.log(
            `Skipping transaction ${transaction._id}: Maximum retry attempts reached (${transaction.retryAttempts}/${maxRetryAttempts})`,
          );
          continue;
        }

        // Check if enough time has passed since the last retry
        // Only use lastRetryDate as the reference point to avoid issues with updatedAt changes
        // If lastRetryDate is not set, initialize it to transaction creation time
        if (!transaction.lastRetryDate) {
          // Initialize lastRetryDate if not set
          transaction.lastRetryDate =
            (transaction as any).createdAt || new Date();
          await transaction.save();
        }

        const daysSinceLastRetry = this.calculateDaysBetween(
          transaction.lastRetryDate,
          new Date(),
        );

        if (daysSinceLastRetry < this.RETRY_INTERVAL_DAYS) {
          this.logger.log(
            `Skipping transaction ${transaction._id}: Not enough time passed since last retry (${daysSinceLastRetry}/${this.RETRY_INTERVAL_DAYS} days)`,
          );
          continue;
        }

        // Transaction is eligible for retry
        this.logger.log(
          `Attempting to retry payment for transaction ${transaction._id}`,
        );

        try {
          // Get the parent
          const parent = await this.parentsService.findOne(
            transaction.parentId.toString(),
          );
          if (!parent || !parent.stripeCustomerId) {
            this.logger.error(
              `Parent not found or no Stripe customer ID for transaction ${transaction._id}`,
            );
            continue;
          }

          // Get the studio and currency
          const studio = await this.studiosService.findOne(
            transaction.studioId.toString(),
          );
          if (!studio) {
            this.logger.error(
              `Studio not found for transaction ${transaction._id}`,
            );
            continue;
          }

          const currency = await this.currencyService.findByStudioId(
            new Types.ObjectId(transaction.studioId.toString()),
          );

          // Get Stripe credentials
          const credential = await this.credentialModel.findOne({
            studioId: transaction.studioId.toString(),
          });

          if (!credential) {
            this.logger.error(
              `No Stripe credentials found for studio ${transaction.studioId}`,
            );
            continue;
          }

          const stripe = new Stripe(credential.apiSecret, {
            apiVersion: '2025-02-24.acacia',
          });

          // Get the customer's payment methods
          // Use proper typing with PaymentMethod enum
          let paymentMethodType: Stripe.PaymentMethodListParams.Type = 'card';

          // Convert payment method to Stripe-compatible type
          if (
            transaction.paymentMethod === PaymentMethod.US_BANK_ACCOUNT ||
            transaction.paymentMethod === PaymentMethod.CASH ||
            transaction.paymentMethod === PaymentMethod.CHECK ||
            transaction.paymentMethod === PaymentMethod.WALLET ||
            transaction.paymentMethod === PaymentMethod.CARD ||
            transaction.paymentMethod === PaymentMethod.LINK
          ) {
            paymentMethodType =
              transaction.paymentMethod as Stripe.PaymentMethodListParams.Type;
          } else {
            this.logger.error(
              `Unsupported payment method type for transaction ${transaction._id}: ${transaction.paymentMethod}`,
            );
            continue;
          }

          const paymentMethods = await stripe.paymentMethods.list({
            customer: parent.stripeCustomerId,
            type: paymentMethodType,
          });

          if (!paymentMethods.data.length) {
            this.logger.error(
              `No payment methods found for customer ${parent.stripeCustomerId}`,
            );
            continue;
          }

          // Get transaction description from metadata if available
          const description =
            'Payment retry for failed payment: ' +
            (transaction.metadata?.description || 'Enrollio');

          let paymentIntent;
          try {
            // For US bank accounts, we need to handle mandates
            if (paymentMethodType === PaymentMethod.US_BANK_ACCOUNT) {
              // First create without confirming
              paymentIntent = await stripe.paymentIntents.create({
                amount: Math.floor(transaction.amount * 100), // Convert to cents
                currency: currency.name.toLowerCase(),
                customer: parent.stripeCustomerId,
                payment_method: paymentMethods.data[0].id,
                payment_method_types: [paymentMethodType],
                off_session: false,
                confirm: false,
                description: `Retry ${transaction.retryAttempts + 1}/${maxRetryAttempts}: ${description}`,
                metadata: {
                  originalTransactionId: transaction._id.toString(),
                  retryNumber: (transaction.retryAttempts + 1).toString(),
                  maxRetries: maxRetryAttempts.toString(),
                },
              });

              // Then confirm with mandate data
              const serverIp = await getGcpIpAddress();
              paymentIntent = await stripe.paymentIntents.confirm(
                paymentIntent.id,
                {
                  payment_method: paymentMethods.data[0].id,
                  mandate_data: {
                    customer_acceptance: {
                      type: 'online',
                      online: {
                        ip_address: serverIp || '127.0.0.1',
                        user_agent: 'Enrollio Server Process',
                      },
                    },
                  },
                },
              );
            } else if (
              paymentMethodType === PaymentMethod.CARD ||
              paymentMethodType === PaymentMethod.LINK
            ) {
              const paymentMethodTypes = [paymentMethodType];
              if (paymentMethodType === PaymentMethod.LINK) {
                paymentMethodTypes.push(PaymentMethod.CARD);
              }
              // For cards and link, create and confirm in one step
              paymentIntent = await stripe.paymentIntents.create({
                amount: Math.floor(transaction.amount * 100), // Convert to cents
                currency: currency.name.toLowerCase(),
                customer: parent.stripeCustomerId,
                payment_method: paymentMethods.data[0].id,
                payment_method_types: paymentMethodTypes,
                off_session: true,
                confirm: true,
                description: `Retry ${transaction.retryAttempts + 1}/${maxRetryAttempts}: ${description}`,
                metadata: {
                  originalTransactionId: transaction._id.toString(),
                  retryNumber: (transaction.retryAttempts + 1).toString(),
                  maxRetries: maxRetryAttempts.toString(),
                },
              });
            }

            // Update the transaction with retry information
            transaction.retryAttempts = (transaction.retryAttempts || 0) + 1;
            transaction.lastRetryDate = new Date();

            // Store the payment intent ID in metadata
            if (!transaction.metadata) {
              transaction.metadata = {};
            }
            transaction.metadata.paymentIntentId = paymentIntent.id;

            transaction.status = PaymentTransactionStatus.PENDING;
            await transaction.save();

            this.logger.log(
              `Payment retry ${transaction.retryAttempts}/${maxRetryAttempts} initiated for transaction ${transaction._id}`,
            );
          } catch (error) {
            this.logger.error(
              `Error retrying payment for transaction ${transaction._id}:`,
              error,
            );

            // Update retry attempt count even if it failed
            transaction.retryAttempts = (transaction.retryAttempts || 0) + 1;
            transaction.lastRetryDate = new Date(); // Always update lastRetryDate to properly track intervals
            transaction.lastError = error.message;
            // Set the status back to FAILED since the retry attempt didn't succeed
            transaction.status = PaymentTransactionStatus.FAILED;
            if (transaction.metadata) {
              transaction.metadata.paymentIntentId = null;
            }
            await transaction.save();
          }
        } catch (error) {
          this.logger.error(
            `Error retrying payment for transaction ${transaction._id}:`,
            error,
          );

          // Update retry attempt count even if it failed
          transaction.retryAttempts = (transaction.retryAttempts || 0) + 1;
          transaction.lastRetryDate = new Date(); // Always update lastRetryDate to properly track intervals
          transaction.lastError = error.message;
          // Set the status back to FAILED since the retry attempt didn't succeed
          transaction.status = PaymentTransactionStatus.FAILED;
          if (transaction.metadata) {
            transaction.metadata.paymentIntentId = null;
          }
          await transaction.save();
        }
      }

      this.logger.log('Completed failed payments retry job');
    } catch (error) {
      this.logger.error('Error in retryFailedPayments cron job:', error);
    }
  }

  private calculateDaysBetween(startDate: Date, endDate: Date): number {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  }
}
