import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  SubscriptionInvoice,
  SubscriptionInvoiceDocument,
} from 'src/database/schema/subscriptionInvoice';
import { SubscriptionInvoiceService } from 'src/subscription-invoice/subscription-invoice.service';
import {
  InvoiceStatus,
  PaymentMethod,
  PaymentProcessingMethod,
} from 'src/stripe/type';
import { StripeService } from 'src/stripe/stripe.service';
import { StripeWalletService } from 'src/stripe/services/wallet.service';
import { ParentsService } from 'src/parents/parents.service';
import { ReasonType } from 'src/database/schema/walletTransaction';
import * as dayjs from 'dayjs';
import { ConfigService } from '@nestjs/config';
import { ParentDocument } from 'src/database/schema/parent';

@Injectable()
export class UpcomingPaymentsCronService {
  private readonly logger = new Logger(UpcomingPaymentsCronService.name);

  constructor(
    @InjectModel(SubscriptionInvoice.name)
    private subscriptionInvoiceModel: Model<SubscriptionInvoice>,
    private readonly subscriptionInvoiceService: SubscriptionInvoiceService,
    private readonly stripeService: StripeService,
    private readonly walletService: StripeWalletService,
    private readonly parentsService: ParentsService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Cron job that runs daily at 9 AM to process upcoming payments
   * Finds invoices with status "upcoming" and dueDate >= today
   */
  @Cron(CronExpression.EVERY_DAY_AT_9AM, {
    name: 'process-upcoming-payments',
    disabled: true,
    timeZone: 'America/New_York',
  })
  async processUpcomingPayments(): Promise<void> {
    try {
      // Check if cron jobs should run in this environment
      // Uncomment if you want to enable environment-based checks
      const environment = this.configService.get<string>('NODE_ENV', 'local');
      if (environment === 'local') {
        this.logger.log(
          'Token refresh cron job skipped - disabled in current environment',
        );
        return;
      }

      this.logger.log('Starting upcoming payments processing...');

      // Get current date (start of day)
      const endOfToday = dayjs().endOf('day').toDate();

      this.logger.log(
        `Processing payments for date: ${endOfToday.toISOString()}`,
      );

      // Find upcoming invoices with dueDate today or earlier
      const upcomingInvoices = await this.findUpcomingInvoices(endOfToday);

      this.logger.log(
        `Found ${upcomingInvoices.length} upcoming invoices to process`,
      );

      if (upcomingInvoices.length === 0) {
        this.logger.log('No upcoming invoices found for processing');
        return;
      }

      // Process each invoice sequentially to prevent connection pool exhaustion
      let successful = 0;
      let failed = 0;
      const errors: string[] = [];

      for (const invoice of upcomingInvoices) {
        try {
          const studio = invoice.studioId as any;

          if (
            studio.paymentProcessingMethod === PaymentProcessingMethod.MANUAL
          ) {
            continue;
          }

          await this.processInvoice(invoice as SubscriptionInvoiceDocument);
          successful++;
          this.logger.log(
            `✅ Successfully processed invoice ${(invoice as any)._id}`,
          );
        } catch (error) {
          failed++;
          const errorMessage = `❌ Failed to process invoice ${(invoice as any)._id}: ${error.message}`;
          errors.push(errorMessage);
          this.logger.error(errorMessage);
        }
      }

      this.logger.log(
        `Processed ${upcomingInvoices.length} invoices - Success: ${successful}, Failed: ${failed}`,
      );

      // Log any failures
      if (errors.length > 0) {
        this.logger.error(`Invoice processing errors:`, errors);
      }
    } catch (error) {
      this.logger.error(
        `Error in upcoming payments cron job: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Find all unprocessed invoices that are due up to today
   * This ensures we catch any invoices that might have been missed in previous runs
   */
  private async findUpcomingInvoices(
    toDate: Date,
  ): Promise<SubscriptionInvoice[]> {
    try {
      this.logger.log(
        `Finding unprocessed invoices due up to ${toDate.toISOString()}`,
      );

      const invoices = await this.subscriptionInvoiceModel
        .find({
          status: InvoiceStatus.UPCOMING,
          finalAmount: { $gt: 0 },
          dueDate: {
            $lte: toDate,
          },
        })
        .populate({
          path: 'studioId',
          model: 'Studio',
          select: 'paymentProcessingMethod',
        })
        .populate({
          path: 'parentId',
          model: 'Parent',
        })
        .populate({
          path: 'studentId',
          model: 'Student',
        })
        .populate({
          path: 'subscriptionId',
          model: 'Subscription',
        })
        .sort({ dueDate: 1 }) // Sort by due date ascending
        .lean()
        .exec();

      this.logger.log(
        `Query found ${invoices.length} unprocessed invoices to handle`,
      );

      return invoices as SubscriptionInvoice[];
    } catch (error) {
      this.logger.error(`Error finding unprocessed invoices: ${error.message}`);
      throw error;
    }
  }

  /**
   * Process a single invoice with wallet logic
   */
  private async processInvoice(
    invoice: SubscriptionInvoiceDocument,
  ): Promise<void> {
    try {
      this.logger.log(
        `Processing invoice ${invoice._id} for student ${invoice.studentId} - Amount: $${invoice.finalAmount}`,
      );

      // Calculate wallet usage for this invoice with timeout
      let walletCalculation = null;
      try {
        // Add timeout to prevent hanging
        walletCalculation = await Promise.race([
          this.walletService.calculateWalletUsageForInvoice({
            parentId: invoice.parentId._id.toString(),
            invoiceAmount: invoice.finalAmount,
            studioId: invoice.studioId._id.toString(),
            studentId: invoice.studentId._id.toString(),
          }),
          new Promise((_, reject) =>
            setTimeout(
              () => reject(new Error('Wallet calculation timeout')),
              10000,
            ),
          ),
        ]);
      } catch (error) {
        this.logger.error(
          `Wallet calculation failed for invoice ${invoice._id}: ${error.message}`,
        );
        // Continue without wallet calculation if it fails
        walletCalculation = {
          canPayFully: false,
          walletAmountUsed: 0,
          paymentIntentAmount: invoice.finalAmount,
        };
      }

      // If wallet can pay fully, skip Stripe and mark as paid
      if (walletCalculation.canPayFully) {
        await this.processFullWalletPayment(invoice, walletCalculation);
        return;
      }

      // Store wallet payment info in invoice metadata if wallet will be used
      if (walletCalculation.walletAmountUsed > 0) {
        if (!invoice.metadata) {
          invoice.metadata = {} as any;
        }
        (invoice.metadata as any).walletAmountUsed =
          walletCalculation.walletAmountUsed;
        (invoice.metadata as any).originalAmount = invoice.finalAmount;
        (invoice.metadata as any).paymentIntentAmount =
          walletCalculation.paymentIntentAmount;
        (invoice.metadata as any).appliedAt = new Date();
      }

      const parent = invoice.parentId as unknown as ParentDocument;
      const paymentMethod =
        (invoice.paymentMethod as string) === 'unknown' ||
        !invoice.paymentMethod
          ? (invoice.subscriptionId as any)?.metadata?.paymentMethod
          : invoice.paymentMethod;

      // Check if payment method is available for remaining amount
      const paymentMethodOnFile =
        await this.stripeService.checkPaymentMethodOnFile(
          invoice.studioId._id.toString(),
          parent.stripeCustomerId,
          paymentMethod,
        );

      if (paymentMethodOnFile.hasPaymentMethod) {
        // Create payment intent for remaining amount after wallet usage
        const paymentIntent = await this.stripeService.captureManualPayment({
          invoiceId: invoice._id.toString(),
          studioId: invoice.studioId._id.toString(),
        });

        await this.subscriptionInvoiceService.update(invoice._id.toString(), {
          status: InvoiceStatus.PENDING,
          metadata: {
            ...invoice.metadata, // Preserve existing metadata
            paymentIntentId: paymentIntent,
          },
        });

        this.logger.log(
          `Successfully processed invoice ${invoice._id}${
            walletCalculation.walletAmountUsed > 0
              ? ` - Amount: $${walletCalculation.paymentIntentAmount} (Wallet: $${walletCalculation.walletAmountUsed})`
              : ` - Amount: $${invoice.finalAmount}`
          }`,
        );
      } else {
        await this.handlePaymentMethodError(invoice);
        return;
      }
    } catch (error) {
      this.logger.error(
        `Error processing invoice ${invoice._id}: ${error.message}`,
        error.stack,
      );
      await this.handlePaymentMethodError(invoice);
    }
  }

  /**
   * Process full wallet payment (skip Stripe entirely)
   */
  private async processFullWalletPayment(
    invoice: SubscriptionInvoiceDocument,
    walletCalculation: any,
  ): Promise<void> {
    try {
      // Deduct wallet balance immediately for full wallet payments
      await this.parentsService.removeWalletBalance({
        parentId: invoice.parentId.toString(),
        amount: walletCalculation.walletAmountUsed,
        reason: ReasonType.INVOICE_PAYMENT,
        paymentMethod: PaymentMethod.WALLET,
        studioId: invoice.studioId.toString(),
        studentId: invoice.studentId.toString(),
      });

      // Update invoice with wallet payment
      const walletPayment = {
        method: PaymentMethod.WALLET,
        amount: walletCalculation.walletAmountUsed,
        date: new Date(),
      };

      await this.subscriptionInvoiceService.update(invoice._id.toString(), {
        status: InvoiceStatus.PAID,
        paymentDate: new Date(),
        payments: [...(invoice.payments || []), walletPayment],
      });

      this.logger.log(
        `Invoice ${invoice._id} paid fully with wallet - Amount: $${walletCalculation.walletAmountUsed}`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing full wallet payment for invoice ${invoice._id}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Handle payment method errors
   */
  private async handlePaymentMethodError(
    invoice: SubscriptionInvoiceDocument,
  ): Promise<void> {
    // Use service to update with proper type handling
    await this.subscriptionInvoiceService.update(
      (invoice as any)._id.toString(),
      {
        status: InvoiceStatus.FAILED,
      },
    );
  }
}
