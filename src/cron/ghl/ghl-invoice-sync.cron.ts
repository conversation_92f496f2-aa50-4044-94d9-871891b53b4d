// import { Injectable, Logger } from '@nestjs/common';
// import { Cron, CronExpression } from '@nestjs/schedule';
// import { InjectModel } from '@nestjs/mongoose';
// import { Model } from 'mongoose';
// import { Studio } from 'src/database/schema/studio';
// import { GhlInvoiceService } from 'src/ghl/services/ghl-invoice.service';

// @Injectable()
// export class GhlInvoiceSyncCronService {
//   private readonly logger = new Logger(GhlInvoiceSyncCronService.name);

//   constructor(
//     @InjectModel(Studio.name)
//     private studioModel: Model<Studio>,
//     private readonly ghlInvoiceService: GhlInvoiceService,
//   ) {}

//   /**
//    * Sync GHL invoices every 15 minutes
//    */
//   @Cron(CronExpression.EVERY_15_MINUTES)
//   async syncGhlInvoices(): Promise<void> {
//     this.logger.log('Starting GHL invoice sync cron job');

//     try {
//       // Get all studios that use GHL
//       const studios = await this.studioModel.find({
//         paymentProvider: 'ghl',
//       }).exec();

//       this.logger.log(`Found ${studios.length} studios using GHL`);

//       // Sync invoices for each studio
//       const syncPromises = studios.map(studio => 
//         this.syncStudioInvoices(studio.locationId)
//       );

//       await Promise.allSettled(syncPromises);

//       this.logger.log('Completed GHL invoice sync cron job');
//     } catch (error) {
//       this.logger.error(`Error in GHL invoice sync cron job: ${error.message}`, error.stack);
//     }
//   }

//   /**
//    * Sync invoices for a specific studio
//    */
//   private async syncStudioInvoices(locationId: string): Promise<void> {
//     try {
//       await this.ghlInvoiceService.syncInvoicesFromGhl(locationId);
//     } catch (error) {
//       this.logger.error(`Error syncing invoices for studio ${locationId}: ${error.message}`);
//       // Don't throw error to prevent stopping other studio syncs
//     }
//   }

//   /**
//    * Manual sync trigger (can be called via API endpoint)
//    */
//   async triggerManualSync(locationId?: string): Promise<void> {
//     this.logger.log(`Triggering manual GHL invoice sync${locationId ? ` for location: ${locationId}` : ''}`);

//     try {
//       if (locationId) {
//         // Sync specific studio
//         await this.syncStudioInvoices(locationId);
//       } else {
//         // Sync all studios
//         await this.syncGhlInvoices();
//       }
//     } catch (error) {
//       this.logger.error(`Error in manual GHL invoice sync: ${error.message}`, error.stack);
//       throw error;
//     }
//   }
// }
