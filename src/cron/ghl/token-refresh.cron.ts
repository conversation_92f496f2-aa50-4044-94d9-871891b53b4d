import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ConfigService } from '@nestjs/config';
import { Studio, StudioDocument } from 'src/database/schema/studio';
import { GohighlevelService } from 'src/gohighlevel/gohighlevel.service';
import {
  TokenRefreshHistory,
  TokenRefreshHistoryDocument,
} from 'src/database/schema/tokenRefreshHistory';

@Injectable()
export class TokenRefreshCronService {
  private readonly logger = new Logger(TokenRefreshCronService.name);

  constructor(
    @InjectModel(Studio.name) private studioModel: Model<StudioDocument>,
    @InjectModel(TokenRefreshHistory.name)
    private tokenHistoryModel: Model<TokenRefreshHistoryDocument>,
    private readonly ghlService: GohighlevelService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Runs every 12 hours to proactively refresh tokens
   * This helps prevent expired/invalid refresh tokens by refreshing them before they expire
   */
  @Cron('0 */12 * * *')
  async handleTokenRefresh() {
    // Check if cron jobs should run in this environment
    const environment = this.configService.get<string>('NODE_ENV', 'local');

    if (environment === 'local') {
      this.logger.log(
        'Token refresh cron job skipped - disabled in current environment',
      );
      return;
    }

    this.logger.log('Starting proactive token refresh for all studios');

    try {
      // Get all studios from the database
      const studios = await this.studioModel.find().exec();
      this.logger.log(`Found ${studios.length} studios to process`);

      let successCount = 0;
      let failureCount = 0;

      // Process studios in batches to avoid overwhelming the API
      const batchSize = 5;
      for (let i = 0; i < studios.length; i += batchSize) {
        const batch = studios.slice(i, i + batchSize);

        // Process each studio in the batch concurrently
        const refreshPromises = batch.map(async (studio) => {
          try {
            // Attempt to refresh the token
            this.logger.log(
              `Refreshing token for studio: ${studio.subaccountName} (${studio.locationId})`,
            );
            await this.ghlService.refereshToken(studio.locationId);
            successCount++;

            // Record successful refresh
            await this.tokenHistoryModel.create({
              locationId: studio.locationId,
              studioName: studio.subaccountName,
              success: true,
              refreshedBy: 'daily-cron',
            });

            return { success: true, locationId: studio.locationId };
          } catch (error) {
            failureCount++;
            this.logger.error(
              `Failed to refresh token for studio ${studio.locationId}: ${error.message}`,
            );

            // Record failed refresh
            await this.tokenHistoryModel.create({
              locationId: studio.locationId,
              studioName: studio.subaccountName,
              success: false,
              errorMessage: error.message,
              refreshedBy: 'daily-cron',
            });

            return {
              success: false,
              locationId: studio.locationId,
              error: error.message,
            };
          }
        });

        // Wait for all promises in this batch to resolve
        const results = await Promise.all(refreshPromises);

        // Add a small delay between batches to avoid rate limiting
        if (i + batchSize < studios.length) {
          await new Promise((resolve) => setTimeout(resolve, 5000));
        }
      }

      this.logger.log(
        `Token refresh completed. Success: ${successCount}, Failures: ${failureCount}`,
      );
    } catch (error) {
      this.logger.error(`Error in token refresh cron job: ${error.message}`);
    }
  }
}
