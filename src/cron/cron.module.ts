import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ScheduleModule } from '@nestjs/schedule';
import { ConfigModule } from '@nestjs/config';
import {
  Credential,
  CredentialSchema,
} from 'src/database/schema/stripeCredential';
import { Student, StudentSchema } from 'src/database/schema/student';
import { Enrollment, EnrollmentSchema } from 'src/database/schema/enrollment';
import { Event, EventSchema } from 'src/database/schema/event';
import {
  EnrollmentHistory,
  EnrollmentHistorySchema,
} from 'src/database/schema/enrollmentHistory';
import { StudiosModule } from 'src/studios/studios.module';
import { ParentsModule } from 'src/parents/parents.module';
import { StudentsModule } from 'src/students/students.module';
import { CurrencyModule } from 'src/currency/currency.module';
import { DiscountModule } from 'src/discount/discount.module';
import { StripeModule } from 'src/stripe/stripe.module';
import { ExpiredEnrollmentsCronService } from './stripe/expired-enrollments.cron';
import { ExpiredEventsCronService } from './stripe/expired-events.cron';
import { RetryFailedPaymentsCronService } from './stripe/retry-failed-payments.cron';
import {
  PaymentTransaction,
  PaymentTransactionSchema,
} from 'src/database/schema/paymentTransaction';
import { BullModule } from '@nestjs/bullmq';
import { PaymentTransactionModule } from 'src/payment-transaction/payment-transaction.module';
import { UpcomingPaymentsCronService } from './stripe/upcoming-payments.cron';
import { ScheduledPaymentsCronService } from './stripe/scheduled-payments.cron';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import { SubscriptionInvoiceSchema } from 'src/database/schema/subscriptionInvoice';
import { SubscriptionInvoiceModule } from 'src/subscription-invoice/subscription-invoice.module';
import { TokenRefreshCronService } from './ghl/token-refresh.cron';
import { GohighlevelModule } from 'src/gohighlevel/gohighlevel.module';
import { Studio, StudioSchema } from 'src/database/schema/studio';
import {
  TokenRefreshHistory,
  TokenRefreshHistorySchema,
} from 'src/database/schema/tokenRefreshHistory';
import { Subscription } from 'src/database/schema/subscription';
import { SubscriptionSchema } from 'src/database/schema/subscription';
import { SubscriptionModule } from 'src/subscription/subscription.module';
import { DiscountCouponModule } from 'src/discount-coupon/discount-coupon.module';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    ConfigModule,
    MongooseModule.forFeature([
      { name: Credential.name, schema: CredentialSchema },
      { name: Student.name, schema: StudentSchema },
      { name: Enrollment.name, schema: EnrollmentSchema },
      { name: Event.name, schema: EventSchema },
      { name: EnrollmentHistory.name, schema: EnrollmentHistorySchema },
      { name: SubscriptionInvoice.name, schema: SubscriptionInvoiceSchema },
      { name: PaymentTransaction.name, schema: PaymentTransactionSchema },
      { name: Studio.name, schema: StudioSchema },
      { name: TokenRefreshHistory.name, schema: TokenRefreshHistorySchema },
      { name: Subscription.name, schema: SubscriptionSchema },
    ]),
    BullModule.registerQueue({
      name: 'invoice-status-update',
    }),
    forwardRef(() => StudiosModule),
    forwardRef(() => ParentsModule),
    forwardRef(() => StudentsModule),
    forwardRef(() => CurrencyModule),
    forwardRef(() => DiscountModule),
    forwardRef(() => StripeModule),
    forwardRef(() => PaymentTransactionModule),
    forwardRef(() => SubscriptionInvoiceModule),
    forwardRef(() => GohighlevelModule),
    forwardRef(() => SubscriptionModule),
    forwardRef(() => DiscountCouponModule),
  ],
  providers: [
    ExpiredEnrollmentsCronService,
    ExpiredEventsCronService,
    RetryFailedPaymentsCronService,
    ScheduledPaymentsCronService,
    UpcomingPaymentsCronService,
    TokenRefreshCronService,
  ],
  exports: [
    ExpiredEnrollmentsCronService,
    ExpiredEventsCronService,
    RetryFailedPaymentsCronService,
    ScheduledPaymentsCronService,
    UpcomingPaymentsCronService,
    TokenRefreshCronService,
  ],
})
export class CronModule {}
