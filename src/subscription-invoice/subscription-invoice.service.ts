import { Injectable, forwardRef, Inject } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Document } from 'mongoose';
import { SubscriptionInvoice } from '../database/schema/subscriptionInvoice';
import { Subscription } from '../database/schema/subscription';
import {
  InvoiceStatus,
  PaymentProvider,
  InvoiceType,
  PaymentTransactionStatus,
  PaymentProcessingMethod,
  PaymentMethod,
} from 'src/stripe/type';
import { Types } from 'mongoose';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { PaymentTransactionService } from 'src/payment-transaction/payment-transaction.service';
import { CurrencyService } from 'src/currency/currency.service';
import { currencySymbols, generateId } from '../utils/helperFunction';
import { UpdateLineItemsDto } from './dto/update.dto';
import { PaymentProcessorService } from '../payment-processor/payment-processor.service';
import {
  DailyInvoiceDetail,
  DailyPaymentStats,
  DailySummary,
  DailyPendingPaymentStats,
  DateDto,
} from './dto/invoices.dto';
import { generateInvoiceDates } from 'src/utils/invoiceDates';
import { PaymentTransaction } from 'src/database/schema/paymentTransaction';
import * as dayjs from 'dayjs';
import { StudiosService } from 'src/studios/studios.service';
import { DiscountService } from '../discount/discount.service';

interface BulkChargeGroup {
  date: string;
  totalAmount: number;
  studentsCount: number;
  chargeName: string;
  bulkPaymentId: string;
}

@Injectable()
export class SubscriptionInvoiceService {
  constructor(
    @InjectModel(SubscriptionInvoice.name)
    private readonly subscriptionInvoiceModel: Model<SubscriptionInvoice>,
    @Inject()
    private readonly subscriptionService: SubscriptionService,
    private readonly paymentTransactionService: PaymentTransactionService,
    private readonly currencyService: CurrencyService,
    @Inject(forwardRef(() => PaymentProcessorService))
    private readonly paymentProcessorService: PaymentProcessorService,
    @Inject(forwardRef(() => StudiosService))
    private readonly studioService: StudiosService,
    private readonly discountService: DiscountService,
  ) {}

  async create(createDto: Partial<SubscriptionInvoice>) {
    const created = new this.subscriptionInvoiceModel({
      ...createDto,
    }) as SubscriptionInvoice & Document;
    return created.save();
  }

  async findAll(studioId: string): Promise<SubscriptionInvoice[]> {
    return this.subscriptionInvoiceModel.find({ studioId }).exec();
  }

  async findOne(id: string): Promise<SubscriptionInvoice | null> {
    return this.subscriptionInvoiceModel.findById(id).exec();
  }

  async findOnePopulated(id: string): Promise<SubscriptionInvoice | null> {
    return this.subscriptionInvoiceModel
      .findById(id)
      .populate({
        path: 'subscriptionId',
        model: 'Subscription',
      })
      .exec();
  }

  async update(id: string, updateDto: Partial<SubscriptionInvoice>) {
    // First get the existing invoice
    const existingInvoice = await this.subscriptionInvoiceModel.findById(id);
    if (!existingInvoice) {
      throw new Error('Invoice not found');
    }

    const updatedData = { ...updateDto };

    // If baseAmount is being updated but finalAmount isn't, recalculate finalAmount
    if (
      updatedData.baseAmount !== undefined &&
      updatedData.finalAmount === undefined
    ) {
      // Get appliedDiscount from metadata or calculate from existing values
      const appliedDiscount =
        existingInvoice.metadata?.appliedDiscount !== undefined
          ? existingInvoice.metadata.appliedDiscount
          : existingInvoice.baseAmount - existingInvoice.finalAmount;

      // Calculate new finalAmount
      updatedData.finalAmount = updatedData.baseAmount - appliedDiscount;
    }
    // If finalAmount is being updated but baseAmount isn't, ensure metadata.appliedDiscount is updated
    else if (
      updatedData.finalAmount !== undefined &&
      updatedData.baseAmount === undefined
    ) {
      const baseAmount = existingInvoice.baseAmount;
      const newAppliedDiscount = baseAmount - updatedData.finalAmount;

      // Update metadata.appliedDiscount
      if (!updatedData.metadata) {
        updatedData.metadata = {
          ...existingInvoice.metadata,
          appliedDiscount: newAppliedDiscount,
        };
      } else {
        updatedData.metadata.appliedDiscount = newAppliedDiscount;
      }
    }
    // If both are updated, calculate and update metadata.appliedDiscount
    else if (
      updatedData.baseAmount !== undefined &&
      updatedData.finalAmount !== undefined
    ) {
      const newAppliedDiscount =
        updatedData.baseAmount - updatedData.finalAmount;

      // Update metadata.appliedDiscount
      if (!updatedData.metadata) {
        updatedData.metadata = {
          ...existingInvoice.metadata,
          appliedDiscount: newAppliedDiscount,
        };
      } else {
        updatedData.metadata.appliedDiscount = newAppliedDiscount;
      }
    }

    return this.subscriptionInvoiceModel
      .findByIdAndUpdate(id, updatedData, { new: true })
      .exec();
  }

  async remove(id: string): Promise<SubscriptionInvoice | null> {
    return this.subscriptionInvoiceModel.findByIdAndDelete(id).exec();
  }

  async generateSubscriptionInvoices(params: {
    subscription: Subscription;
    childTransaction: PaymentTransaction;
    discountAmount: number;
    enrollment: any;
    transactionId: string;
    transactionCodeId: string;
    applyDiscountTo?: 'first' | 'all';
    noPaymentMethod?: boolean;
    paymentIntentSent?: boolean;
    discountCategory?: 'discount' | 'scholarship';
    paymentProcessingMethod?: PaymentProcessingMethod;
  }): Promise<SubscriptionInvoice[]> {
    const {
      subscription,
      childTransaction,
      discountAmount,
      enrollment,
      transactionId,
      transactionCodeId,
      applyDiscountTo = 'all', // Default to 'all' for backward compatibility
      noPaymentMethod,
      paymentIntentSent,
      discountCategory,
      paymentProcessingMethod,
    } = params;

    const invoiceDates = generateInvoiceDates(
      subscription.startDate,
      subscription.endDate,
      subscription.billingCycle,
      enrollment.billingDay,
    );

    // Create invoices sequentially instead of in parallel
    const invoices: SubscriptionInvoice[] = [];
    for (let index = 0; index < invoiceDates.length; index++) {
      const date = invoiceDates[index];
      // Apply discount based on applyDiscountTo parameter
      const shouldApplyDiscount =
        applyDiscountTo === 'all' ||
        (applyDiscountTo === 'first' && index === 0);

      const invoiceDiscountAmount = shouldApplyDiscount ? discountAmount : 0;

      const enrollmentTuitionFee = enrollment.tuitionFee;

      let status = null;
      const payments = [];
      const invoiceTotal =
        (index === 0 ? childTransaction.amount : subscription.baseAmount) -
        invoiceDiscountAmount;

      // Wallet logic for first invoice
      if (
        index === 0 &&
        childTransaction.metadata &&
        childTransaction.metadata.walletAmountUsed > 0
      ) {
        const walletAmount = childTransaction.metadata.walletAmountUsed;
        payments.push({
          method: PaymentMethod.WALLET,
          amount: walletAmount,
          date: new Date(), // Optionally use transaction date if available
        });

        if (walletAmount >= invoiceTotal) {
          status = InvoiceStatus.PAID;
        } else if (walletAmount > 0) {
          status = InvoiceStatus.PARTIALLY_PAID;
        }
      }

      if (!status) {
        if (noPaymentMethod) {
          if (index === 0) {
            status = InvoiceStatus.FAILED;
          } else {
            status = InvoiceStatus.UPCOMING;
          }
        } else if (paymentIntentSent) {
          if (index === 0) {
            status = InvoiceStatus.PENDING;
          } else {
            status = InvoiceStatus.UPCOMING;
          }
        } else {
          if (index === 0) {
            status =
              childTransaction.status == PaymentTransactionStatus.SCHEDULED
                ? InvoiceStatus.SCHEDULED
                : InvoiceStatus.FAILED;
          } else {
            status = InvoiceStatus.UPCOMING;
          }
        }
      }

      const lineItems =
        index === 0
          ? childTransaction.metadata.line_items.map((item) => ({
              name: item.price_data.product_data.name,
              amount:
                item.price_data.product_data.description === 'Tuition Fee'
                  ? enrollmentTuitionFee
                  : item.price_data.unit_amount / 100,
              quantity: item.quantity,
              type: item.price_data.product_data.description,
              total:
                item.price_data.product_data.description === 'Tuition Fee'
                  ? enrollmentTuitionFee * item.quantity
                  : (item.price_data.unit_amount * item.quantity) / 100,
            })) //only include the tuition fee line item
          : childTransaction.metadata.line_items
              .filter(
                (item) =>
                  item.price_data.product_data.description === 'Tuition Fee',
              )
              .map((item) => ({
                name: item.price_data.product_data.name,
                amount: enrollmentTuitionFee,
                quantity: item.quantity,
                type: item.price_data.product_data.description,
                total: enrollmentTuitionFee * item.quantity,
              }));

      const invoice = await this.create({
        studioId: subscription.studioId,
        subscriptionId: Types.ObjectId.createFromHexString(
          subscription._id.toString(),
        ),
        parentId: subscription.parentId,
        studentId: subscription.studentId,
        entityId: subscription.entityId,
        entityType: subscription.entityType,
        baseAmount: subscription.baseAmount,
        status: status,
        paymentProvider: childTransaction.paymentProvider,
        paymentMethod: childTransaction.paymentMethod,
        line_items: lineItems,
        type: InvoiceType.SUBSCRIPTION,
        dueDate:
          index === 0 ? new Date(childTransaction.metadata.billingDate) : date,
        appliedCouponId: subscription.appliedCouponId,
        finalAmount: invoiceTotal,
        startDate: date,
        endDate:
          invoiceDates[index + 1] ||
          new Date(date.getTime() + 24 * 60 * 60 * 1000),
        metadata: {
          attemptCount: 0,
          internalTransactionId: index === 0 ? transactionId : null,
          appliedDiscount: invoiceDiscountAmount,
          applyDiscountTo: applyDiscountTo,
          discountCategory: discountCategory,
        },
        transactionCodeId: transactionCodeId,
        payments: payments.length > 0 ? payments : undefined,
      });

      invoices.push(invoice);
    }

    return invoices;
  }
  async updateMetadata(invoiceId: string, metadata: any) {
    // First get the existing invoice to check for appliedDiscount
    const invoice = await this.subscriptionInvoiceModel.findById(invoiceId);
    if (!invoice) {
      throw new Error('Invoice not found');
    }

    // Preserve appliedDiscount if not explicitly provided in new metadata
    if (
      invoice.metadata?.appliedDiscount !== undefined &&
      metadata.appliedDiscount === undefined
    ) {
      metadata.appliedDiscount = invoice.metadata.appliedDiscount;
    }

    return this.subscriptionInvoiceModel.findByIdAndUpdate(
      invoiceId,
      { $set: { metadata } },
      { new: true },
    );
  }

  async fetchUpcomingInvoicesByStudentId(
    studentId: string,
    locationId: string,
  ): Promise<any[]> {
    const subscriptions =
      await this.subscriptionService.fetchSubscriptionsByStudentId(
        studentId,
        locationId,
      );

    const invoices = await this.subscriptionInvoiceModel.find({
      subscriptionId: { $in: subscriptions.map((sub) => sub._id) },
      status: {
        $in: [InvoiceStatus.UPCOMING, InvoiceStatus.SCHEDULED],
      },
    });

    // Format invoice data
    const invoiceData = invoices
      .map((invoice) => {
        const dueDate = dayjs(invoice.dueDate || invoice.startDate).format(
          'MMM DD, YYYY',
        );

        const subscription = subscriptions.find(
          (sub) => sub._id.toString() === invoice.subscriptionId.toString(),
        );
        const paymentFrequency = subscription?.billingCycle || 'One Time';

        return invoice.line_items
          .filter((item) => item.type !== 'Session Registration Fee')
          .map((item) => ({
            invoiceId: invoice._id.toString(),
            upcomingChargeDate: dueDate,
            productName: item.name,
            paymentFrequency: paymentFrequency,
            type: item.type,
            amount: `${invoice.finalAmount.toFixed(2)}`,
            tuitionFee: `${invoice.baseAmount.toFixed(2)}`,
            appliedDiscount: `${invoice.metadata.appliedDiscount.toFixed(2)}`,
          }));
      })
      .flat();

    return invoiceData.sort((a, b) =>
      dayjs(a.upcomingChargeDate, 'MMM DD, YYYY').diff(
        dayjs(b.upcomingChargeDate, 'MMM DD, YYYY'),
      ),
    );
  }

  async updateLineItemsByInvoiceId(
    invoiceId: string,
    updateLineItemsDto: UpdateLineItemsDto,
  ): Promise<SubscriptionInvoice> {
    try {
      const invoice = await this.subscriptionInvoiceModel.findById(invoiceId);
      if (!invoice) {
        throw new Error('Invoice not found');
      }

      // Update specific line items based on productName and type
      updateLineItemsDto.lineItems.forEach((updateItem) => {
        const existingItemIndex = invoice.line_items.findIndex(
          (item) =>
            item.name === updateItem.productName &&
            item.type.toString() === updateItem.type.toString(),
        );

        if (existingItemIndex >= 0) {
          invoice.line_items[existingItemIndex] = {
            ...invoice.line_items[existingItemIndex],
            name: updateItem.productName,
            amount: updateItem.amount,
            total:
              updateItem.amount *
              invoice.line_items[existingItemIndex].quantity,
          };
        }
      });

      // Calculate new baseAmount from line items
      const newBaseAmount = invoice.line_items.reduce(
        (sum, item) => sum + item.amount * item.quantity,
        0,
      );

      // If there's a coupon, recalculate the discount
      let appliedDiscount = 0;
      if (invoice.appliedCouponId) {
        // Get the tuition fee from the line items
        const tuitionFee =
          invoice.line_items.find((item) => item.type === 'Tuition Fee')
            ?.amount || 0;

        appliedDiscount =
          await this.discountService.recalculateDiscountForInvoice(
            invoice.appliedCouponId,
            newBaseAmount,
            tuitionFee,
          );
      } else {
        // If no coupon, use existing discount
        appliedDiscount = invoice.metadata?.appliedDiscount || 0;
      }

      //final amount cannot be less than 0
      if (newBaseAmount - appliedDiscount < 0) {
        throw new Error('Final amount cannot be less than 0');
      }

      // Ensure baseAmount and finalAmount maintain proper relationship
      const updatedData = {
        line_items: invoice.line_items,
        baseAmount: newBaseAmount,
        finalAmount: newBaseAmount - appliedDiscount,
        'metadata.appliedDiscount': appliedDiscount,
        updatedAt: new Date(),
      };

      return this.subscriptionInvoiceModel
        .findOneAndUpdate(
          { _id: invoiceId },
          { $set: updatedData },
          { new: true, runValidators: true },
        )
        .exec();
    } catch (error) {
      throw new Error(error.message);
    }
  }

  async getPaymentHistoryByDateRange(
    startDate: string,
    endDate: string,
    locationId: string,
  ): Promise<any> {
    const start = dayjs(startDate).startOf('day');
    const end = dayjs(endDate).endOf('day');

    const invoices = await this.subscriptionInvoiceModel
      .find({
        studioId: Types.ObjectId.createFromHexString(locationId),
        dueDate: {
          $gte: start.toDate(),
          $lte: end.toDate(),
        },
      })
      .exec();

    // Group by date and status
    const groupedByDate = invoices.reduce<Record<string, DailyPaymentStats>>(
      (acc, invoice) => {
        const date = dayjs(invoice.dueDate || invoice.startDate).format(
          'YYYY-MM-DD',
        );

        if (!acc[date]) {
          acc[date] = {
            date: dayjs(date).format('ddd, MMM DD, YYYY'),
            totalPayments: 0,
            successful: 0,
            failed: 0,
            totalAmount: 0,
          };
        }

        acc[date].totalPayments++;

        if (invoice.status === InvoiceStatus.PAID) {
          acc[date].successful++;
        } else if (invoice.status === InvoiceStatus.FAILED) {
          acc[date].failed++;
        }
        acc[date].totalAmount += invoice.finalAmount;

        return acc;
      },
      {},
    );

    // Convert to array and sort by date
    return Object.values(groupedByDate)
      .sort((a, b) => dayjs(b.date).diff(dayjs(a.date)))
      .map((day) => ({
        date: day.date,
        totalPayments: day.totalPayments,
        successful: day.successful,
        failed: day.failed,
        totalAmount: `${day.totalAmount.toFixed(2)}`,
      }));
  }

  async getPendingPaymentHistoryByDateRange(
    startDate: string,
    endDate: string,
    locationId: string,
  ): Promise<any> {
    const start = dayjs(startDate).startOf('day');
    const end = dayjs(endDate).endOf('day');

    const invoices = await this.subscriptionInvoiceModel
      .find({
        studioId: Types.ObjectId.createFromHexString(locationId),
        dueDate: {
          $gte: start.toDate(),
          $lte: end.toDate(),
        },
      })
      .exec();

    // Group by date and status
    const groupedByDate = invoices.reduce<
      Record<string, DailyPendingPaymentStats>
    >((acc, invoice) => {
      const date = dayjs(invoice.dueDate || invoice.startDate).format(
        'YYYY-MM-DD',
      );

      if (!acc[date]) {
        acc[date] = {
          date: dayjs(date).format('ddd, MMM DD, YYYY'),
          pending: 0,
        };
      }

      if (invoice.status === InvoiceStatus.PENDING) {
        acc[date].pending++;
      }

      return acc;
    }, {});

    // Convert to array and sort by date
    return Object.values(groupedByDate)
      .sort((a, b) => dayjs(b.date).diff(dayjs(a.date)))
      .map((day) => ({
        date: day.date,
        pending: day.pending,
      }));
  }

  async getPaymentDetailsByDate(
    date: string,
    locationId: string,
  ): Promise<DailySummary> {
    const startOfDay = dayjs(date).startOf('day');
    const endOfDay = dayjs(date).endOf('day');

    // Make a single database call to get all invoices for the day
    const allInvoicesForDate = await this.subscriptionInvoiceModel
      .find({
        studioId: Types.ObjectId.createFromHexString(locationId),
        dueDate: {
          $gte: startOfDay.toDate(),
          $lte: endOfDay.toDate(),
        },
        $or: [
          { 'metadata.isPaymentApproved': false },
          { 'metadata.isPaymentApproved': { $exists: false } },
        ],
      })
      .populate({
        path: 'studentId',
        model: 'Student',
        select: 'firstName lastName',
      })
      .exec();

    // Create a helper function to map invoices to details
    const mapToInvoiceDetails = (invoice): DailyInvoiceDetail => ({
      studentName: `${invoice.studentId['firstName']} ${invoice.studentId['lastName']}`,
      productName: invoice.line_items[0]?.name || 'N/A',
      amount: invoice.finalAmount,
      paymentProvider: invoice.paymentProvider || 'N/A',
      paymentMethod: invoice.paymentMethod || 'N/A',
      status: invoice.status,
      invoiceId: invoice._id.toString(),
    });

    // Initialize result object with status-specific arrays
    const result = {
      totalPayments: allInvoicesForDate.length,
      totalAmount: allInvoicesForDate
        .reduce((sum, inv) => sum + inv.finalAmount, 0)
        .toFixed(2),
      successful: [],
      failed: [],
      pending: [],
      upcoming: [],
      scheduled: [],
      invoices: [],
    };

    // Process all invoices in a single loop
    allInvoicesForDate.forEach((invoice) => {
      const invoiceDetail = mapToInvoiceDetails(invoice);

      // Add to the general invoices list
      result.invoices.push(invoiceDetail);

      // Add to the status-specific list
      switch (invoice.status) {
        case InvoiceStatus.PAID:
          result.successful.push(invoiceDetail);
          break;
        case InvoiceStatus.FAILED:
          result.failed.push(invoiceDetail);
          break;
        case InvoiceStatus.PENDING:
          result.pending.push(invoiceDetail);
          break;
        case InvoiceStatus.UPCOMING:
          result.upcoming.push(invoiceDetail);
          break;
        case InvoiceStatus.SCHEDULED:
          result.scheduled.push(invoiceDetail);
          break;
      }
    });

    return result;
  }

  async getPaymentDetailsPending(
    startDate: string,
    endDate: string,
    locationId: string,
  ) {
    const startOfDay = dayjs(startDate).startOf('day');
    const endOfDay = dayjs(endDate).endOf('day');

    // Make a single database call to get all invoices for the day
    const allInvoicesForDate = await this.subscriptionInvoiceModel
      .find({
        studioId: Types.ObjectId.createFromHexString(locationId),
        status: {
          $in: [InvoiceStatus.UPCOMING, InvoiceStatus.SCHEDULED],
        },
        dueDate: {
          $gte: startOfDay.toDate(),
          $lte: endOfDay.toDate(),
        },
        $or: [
          { 'metadata.isPaymentApproved': false },
          { 'metadata.isPaymentApproved': { $exists: false } },
        ],
      })
      .populate({
        path: 'studentId',
        model: 'Student',
        select: 'firstName lastName',
      })
      .exec();

    // Create a helper function to map invoices to details
    const mapToInvoiceDetails = (invoice): DailyInvoiceDetail => ({
      studentName: `${invoice.studentId['firstName']} ${invoice.studentId['lastName']}`,
      productName: invoice.line_items[0]?.name || 'N/A',
      amount: invoice.finalAmount,
      paymentProvider: invoice.paymentProvider || 'N/A',
      paymentMethod: invoice.paymentMethod || 'N/A',
      status: invoice.status,
      invoiceId: invoice._id.toString(),
    });

    // Create invoice details from filtered invoices
    const invoiceDetails: DailyInvoiceDetail[] =
      allInvoicesForDate.map(mapToInvoiceDetails);

    return {
      totalPayments: allInvoicesForDate.length,
      totalAmount: allInvoicesForDate
        .reduce((sum, inv) => sum + inv.finalAmount, 0)
        .toFixed(2),
      invoices: invoiceDetails,
    };
  }

  async approveManualPaymentsForInvoices(invoiceId: string[]) {
    const invoices = await this.subscriptionInvoiceModel.find({
      _id: {
        $in: invoiceId.map((id) => Types.ObjectId.createFromHexString(id)),
      },
      status: { $ne: InvoiceStatus.PENDING },
    });
    await Promise.all(
      invoices.map(async (invoice) => {
        const paymentIntentId =
          await this.paymentProcessorService.captureManualPayment(
            invoice.studioId.toString(),
            invoice._id.toString(),
          );
        invoice.metadata.paymentIntentId = paymentIntentId;
        invoice.metadata.isPaymentApproved = true;
        return invoice.save();
      }),
    );
  }

  async fetchLinesItemsByInvoiceId(invoiceId: string) {
    const invoice = await this.subscriptionInvoiceModel.findById(invoiceId);

    if (!invoice) {
      return [];
    }

    const dueDate = dayjs(invoice.dueDate || invoice.startDate).format(
      'MMM DD, YYYY',
    );

    return invoice.line_items.map((item) => ({
      invoiceId: invoice._id.toString(),
      upcomingChargeDate: dueDate,
      productName: item.name,
      type: item.type,
      amount: `${item.total.toFixed(2)}`,
    }));
  }

  async fetchBulkChargeHistoryByDateRange(
    locationId: string,
    startDate: string,
    endDate: string,
  ) {
    const startOfDay = dayjs(startDate).startOf('day');
    const endOfDay = dayjs(endDate).endOf('day');

    const invoices = await this.subscriptionInvoiceModel.find({
      studioId: Types.ObjectId.createFromHexString(locationId),
      createdAt: {
        $gte: startOfDay.toDate(),
        $lte: endOfDay.toDate(),
      },
      type: InvoiceType.MANUAL,
      'metadata.bulkPaymentId': { $exists: true },
    });

    // Group invoices by bulkPaymentId
    const groupedInvoices = invoices.reduce(
      (acc, invoice) => {
        const bulkId = invoice.metadata.bulkPaymentId;
        if (!acc[bulkId]) {
          acc[bulkId] = {
            date: dayjs(invoice.createdAt).format('MMM DD, YYYY h:mma'),
            totalAmount: 0,
            studentsCount: 0,
            chargeName: invoice.line_items[0]?.name || 'N/A',
            bulkPaymentId: bulkId,
          };
        }

        acc[bulkId].totalAmount += invoice.finalAmount;
        acc[bulkId].studentsCount += invoice.line_items.reduce(
          (sum, item) => sum + (item.quantity || 0),
          0,
        );

        return acc;
      },
      {} as Record<string, BulkChargeGroup>,
    );

    return Object.values(groupedInvoices).map((group) => ({
      date: group.date,
      totalAmount: group.totalAmount.toFixed(2),
      studentsCount: group.studentsCount,
      chargeName: group.chargeName,
      bulkPaymentId: group.bulkPaymentId,
    }));
  }

  async fetchBulkChargeHistoryByBulkPaymentId(
    locationId: string,
    bulkPaymentId: string,
  ) {
    const invoices = await this.subscriptionInvoiceModel
      .find({
        studioId: Types.ObjectId.createFromHexString(locationId),
        'metadata.bulkPaymentId': bulkPaymentId,
        type: InvoiceType.MANUAL,
      })
      .populate({
        path: 'studentId',
        model: 'Student',
        select: 'firstName lastName',
      });

    // Map invoices to required format
    return invoices
      .map((invoice) => ({
        name: `${invoice.studentId['firstName']} ${invoice.studentId['lastName']}`,
        amount: invoice.finalAmount.toFixed(2),
        status: invoice.status,
      }))
      .sort((a, b) => a.name.localeCompare(b.name));
  }

  async getPaymentHistoryByStudentId(
    studentId: string,
    startDate: string,
    endDate: string,
    locationId: string,
  ): Promise<any> {
    const start = dayjs(startDate).startOf('day');
    const end = dayjs(endDate).endOf('day');

    const invoices = await this.subscriptionInvoiceModel
      .find({
        studioId: Types.ObjectId.createFromHexString(locationId),
        studentId: Types.ObjectId.createFromHexString(studentId),
        status: { $nin: [InvoiceStatus.PAUSED, InvoiceStatus.UPCOMING] },
        paymentProvider: { $ne: PaymentProvider.CASH },
        createdAt: {
          $gte: start.toDate(),
          $lte: end.toDate(),
        },
      })
      .sort({ dueDate: -1 })
      .lean()
      .exec();

    try {
      const result = invoices.map((invoice) => {
        let baseAmount = 0;
        const refundTypes = ['partial refund', 'full refund'];
        if (invoice.line_items) {
          baseAmount = invoice.line_items
            .filter((item) => !refundTypes.includes(item.type))
            .reduce((acc, item) => acc + (item.amount || 0), 0);
        }

        let discount = 0;
        if (invoice.metadata.appliedDiscount) {
          discount = invoice.metadata.appliedDiscount;
        }

        const paymentSum =
          invoice.payments?.reduce((sum, payment) => sum + payment.amount, 0) ||
          0;

        try {
          return {
            invoiceDate: invoice.createdAt,
            paymentDate: invoice.paymentDate,
            _id: invoice._id.toString(),
            invoiceId: invoice.metadata.paymentIntentId || null,
            className: invoice.line_items
              .filter(
                (item) => item.type === 'Tuition Fee' || item.type === 'Manual',
              )
              .map((item) => item.name)
              .join(''),
            baseAmount: baseAmount.toFixed(2),
            finalAmount: invoice.finalAmount.toFixed(2),
            discount: discount.toFixed(2),
            paidAmount: paymentSum.toFixed(2),
            status: invoice.status,
            paymentProvider: invoice.paymentProvider,
            paymentMethod: invoice.paymentMethod,
            failureReason: invoice.metadata?.failureReason || null,
            refundReason: invoice.metadata?.refundReason || null,
            attemptCount: invoice.metadata?.attemptCount || 0,
            lastAttemptDate: invoice.metadata?.lastAttemptDate || null,
            internalTransactionId:
              invoice.metadata?.internalTransactionId || null,
            refundId: invoice.metadata?.refundId || null,
            appliedDiscount: invoice.metadata?.appliedDiscount || 0,
            payments: invoice.payments || [],
            metadata: invoice.metadata || {},
          };
        } catch (error) {
          console.log(error);
          return null;
        }
      });

      // Filter out null values and sort to move cancelled/void statuses to end
      const filteredResult = result.filter((item) => item !== null);

      filteredResult.sort((a, b) => {
        if (!a || !b) return 0;

        const statusesToEnd = [InvoiceStatus.CANCELLED, InvoiceStatus.VOID];
        const aIsCancelledOrVoid = statusesToEnd.includes(a.status);
        const bIsCancelledOrVoid = statusesToEnd.includes(b.status);

        if (aIsCancelledOrVoid !== bIsCancelledOrVoid) {
          return aIsCancelledOrVoid ? 1 : -1;
        }

        return b.invoiceDate.getTime() - a.invoiceDate.getTime();
      });

      return filteredResult;
    } catch (error) {
      console.log(error);
      return [];
    }
  }

  async getPaymentHistoryByParentId(
    parentId: string,
    startDate: string,
    endDate: string,
    locationId: string,
  ): Promise<any> {
    const start = dayjs(startDate).startOf('day');
    const end = dayjs(endDate).endOf('day');

    const invoices = await this.subscriptionInvoiceModel
      .find({
        studioId: Types.ObjectId.createFromHexString(locationId),
        parentId: Types.ObjectId.createFromHexString(parentId),
        status: {
          $nin: [
            InvoiceStatus.PAUSED,
            InvoiceStatus.UPCOMING,
            InvoiceStatus.VOID,
            InvoiceStatus.CANCELLED,
          ],
        },
        paymentProvider: { $ne: PaymentProvider.CASH },
        createdAt: {
          $gte: start.toDate(),
          $lte: end.toDate(),
        },
      })
      .populate({
        path: 'studentId',
        model: 'Student',
        select: '_id name',
      })
      .sort({ dueDate: -1 })
      .lean()
      .exec();

    return invoices.map((invoice) => {
      let baseAmount = 0;
      if (invoice.line_items) {
        baseAmount =
          invoice.line_items.reduce((acc, curr) => acc + curr.amount, 0) || 0;
      }

      let discount = 0;
      if (invoice?.metadata?.appliedDiscount) {
        discount = invoice.metadata.appliedDiscount;
      }

      const paymentSum =
        invoice.payments?.reduce((sum, payment) => sum + payment.amount, 0) ||
        0;

      return {
        invoiceDate: invoice?.createdAt || null,
        paymentDate: invoice?.paymentDate || null,
        _id: invoice?._id.toString() || null,
        invoiceId: invoice?.metadata?.paymentIntentId || null,
        subscriptionId: invoice?.subscriptionId?.toString() || null,
        parentId: invoice?.parentId || null,
        studentId: invoice?.studentId?._id?.toString() || null,
        studentName: invoice?.studentId?.['name'] || null,
        className: invoice?.line_items
          .filter(
            (item) => item.type === 'Tuition Fee' || item.type === 'Manual',
          )
          .map((item) => item?.name)
          .join('') || null,
        discount: discount.toFixed(2) || null,
        baseAmount: baseAmount.toFixed(2) || null,
        paidAmount: paymentSum.toFixed(2) || null,
        status: invoice?.status || null,
        paymentProvider: invoice?.paymentProvider || null,
        paymentMethod: invoice?.paymentMethod || null,
        failureReason: invoice?.metadata?.failureReason || null,
        refundReason: invoice?.metadata?.refundReason || null,
        attemptCount: invoice?.metadata?.attemptCount || 0,
        lastAttemptDate: invoice?.metadata?.lastAttemptDate || null,
        internalTransactionId: invoice?.metadata?.internalTransactionId || null,
        refundId: invoice?.metadata?.refundId || null,
        appliedDiscount: invoice?.metadata?.appliedDiscount || 0,
        payments: invoice?.payments || [],
        metadata: invoice?.metadata || {},
      };
    });
  }

  async fetchStudentInvoicesByStatus(studentId: string, status: string[]) {
    const invoices = await this.subscriptionInvoiceModel.find({
      studentId: Types.ObjectId.createFromHexString(studentId),
      status: { $in: status },
    });

    return invoices.map((invoice) => {
      // Calculate total payments made
      const totalPayments =
        invoice.payments?.reduce((sum, payment) => sum + payment.amount, 0) ||
        0;

      // Calculate outstanding amount by subtracting total payments from final amount
      const outstandingAmount = invoice.finalAmount - totalPayments;

      return {
        invoiceId: invoice._id.toString(),
        productName: invoice.line_items
          .filter((item) => item.type === 'Tuition Fee')
          .map((item) => item.name)
          .join(''),
        invoiceDate: invoice.createdAt,
        outstandingAmount: outstandingAmount,
      };
    });
  }
}
