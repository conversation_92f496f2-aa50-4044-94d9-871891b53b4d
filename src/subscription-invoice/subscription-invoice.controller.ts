import {
  Body,
  Controller,
  Get,
  Param,
  Put,
  Req,
  UseGuards,
  Query,
  Post,
  HttpException,
  HttpStatus,
  Res,
} from '@nestjs/common';
import { SubscriptionInvoiceService } from './subscription-invoice.service';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { UpdateLineItemsDto } from './dto/update.dto';
import { DateRangeDto, DateDto } from './dto/invoices.dto';
import { Response } from 'express';

@UseGuards(JwtAuthGuard)
@Controller('subscription-invoices')
export class SubscriptionInvoiceController {
  constructor(
    private readonly subscriptionInvoiceService: SubscriptionInvoiceService,
  ) {}

  @Get('student/:studentId/upcoming')
  async getUpcomingInvoicesByStudentId(
    @Param('studentId') studentId: string,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return await this.subscriptionInvoiceService.fetchUpcomingInvoicesByStudentId(
      studentId,
      locationId,
    );
  }

  @Put('update-line-items/:invoiceId')
  async updateLineItemsByInvoiceId(
    @Param('invoiceId') invoiceId: string,
    @Body() updateLineItemsDto: UpdateLineItemsDto,
    @Res() response: Response,
  ) {
    try {
      const result =
        await this.subscriptionInvoiceService.updateLineItemsByInvoiceId(
          invoiceId,
          updateLineItemsDto,
        );
      return response.json(result);
    } catch (error) {
      return response.status(400).json({ message: error.message });
    }
  }

  @Get('payment-history')
  async getPaymentHistory(
    @Query() dateRange: DateRangeDto,
    @Req() request: Request,
  ) {
    return await this.subscriptionInvoiceService.getPaymentHistoryByDateRange(
      dateRange.startDate,
      dateRange.endDate,
      request['locationId'],
    );
  }

  @Get('pending-payment-history')
  async getPendingPaymentHistory(
    @Query() dateRange: DateRangeDto,
    @Req() request: Request,
  ) {
    return await this.subscriptionInvoiceService.getPendingPaymentHistoryByDateRange(
      dateRange.startDate,
      dateRange.endDate,
      request['locationId'],
    );
  }

  @Get('payment-details')
  async getPaymentDetails(@Query() dateDto: DateDto, @Req() request: Request) {
    return await this.subscriptionInvoiceService.getPaymentDetailsByDate(
      dateDto.date,
      request['locationId'],
    );
  }
  @Get('payment-details-pending')
  async getPaymentDetailsPending(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Req() request: Request,
  ) {
    return await this.subscriptionInvoiceService.getPaymentDetailsPending(
      startDate,
      endDate,
      request['locationId'],
    );
  }

  @Post('approve-manual-payments')
  async approveManualPaymentsForInvoices(
    @Body() body: { invoiceId: string[] },
  ) {
    return await this.subscriptionInvoiceService.approveManualPaymentsForInvoices(
      body.invoiceId,
    );
  }

  @Get('lines-items/:invoiceId')
  async fetchLinesItemsByInvoiceId(@Param('invoiceId') invoiceId: string) {
    return await this.subscriptionInvoiceService.fetchLinesItemsByInvoiceId(
      invoiceId,
    );
  }

  @Get('bulk-charge-history')
  async fetchBulkChargeHistoryByDateRange(
    @Query() dateRange: DateRangeDto,
    @Req() request: Request,
  ) {
    return await this.subscriptionInvoiceService.fetchBulkChargeHistoryByDateRange(
      request['locationId'],
      dateRange.startDate,
      dateRange.endDate,
    );
  }

  @Get('bulk-charge-history/:bulkPaymentId')
  async fetchBulkChargeHistoryByBulkPaymentId(
    @Param('bulkPaymentId') bulkPaymentId: string,
    @Req() request: Request,
  ) {
    return await this.subscriptionInvoiceService.fetchBulkChargeHistoryByBulkPaymentId(
      request['locationId'],
      bulkPaymentId,
    );
  }

  @Get('payment-history/student/:studentId')
  async getPaymentHistoryByStudentId(
    @Param('studentId') studentId: string,
    @Query() dateRange: DateRangeDto,
    @Req() request: Request,
  ) {
    return await this.subscriptionInvoiceService.getPaymentHistoryByStudentId(
      studentId,
      dateRange.startDate,
      dateRange.endDate,
      request['locationId'],
    );
  }

  @Get('payment-history/parent/:parentId')
  async getPaymentHistoryByParentId(
    @Param('parentId') parentId: string,
    @Query() dateRange: DateRangeDto,
    @Req() request: Request,
  ) {
    return await this.subscriptionInvoiceService.getPaymentHistoryByParentId(
      parentId,
      dateRange.startDate,
      dateRange.endDate,
      request['locationId'],
    );
  }

  @Post('student/:studentId')
  async fetchStudentInvoicesByStatus(
    @Param('studentId') studentId: string,
    @Body() body: { status: string[] },
  ) {
    return await this.subscriptionInvoiceService.fetchStudentInvoicesByStatus(
      studentId,
      body.status,
    );
  }
}
