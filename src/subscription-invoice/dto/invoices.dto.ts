import { IsString } from 'class-validator';

export class DateRangeDto {
  @IsString()
  startDate: string;

  @IsString()
  endDate: string;
}

export interface DailyPaymentStats {
  date: string;
  totalPayments: number;
  successful: number;
  failed: number;
  totalAmount: number;
}

export interface DailyPendingPaymentStats {
  date: string;
  pending: number;
}

export interface DailyInvoiceDetail {
  studentName: string;
  productName: string;
  amount: number;
  paymentProvider: string;
  paymentMethod: string;
  status: string;
  invoiceId: string;
}

export interface DailySummary {
  totalPayments: number;
  totalAmount: string;
  successful: DailyInvoiceDetail[];
  failed: DailyInvoiceDetail[];
  pending: DailyInvoiceDetail[];
  upcoming: DailyInvoiceDetail[];
  scheduled: DailyInvoiceDetail[];
  invoices: DailyInvoiceDetail[];
}

export class DateDto {
  @IsString()
  date: string;
}
