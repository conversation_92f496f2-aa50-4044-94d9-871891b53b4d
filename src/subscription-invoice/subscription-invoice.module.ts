import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { SubscriptionInvoiceService } from './subscription-invoice.service';
import { SubscriptionInvoiceController } from './subscription-invoice.controller';
import {
  SubscriptionInvoice,
  SubscriptionInvoiceSchema,
} from '../database/schema/subscriptionInvoice';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { SubscriptionModule } from 'src/subscription/subscription.module';
import { TransactionService } from 'src/transaction/transaction.service';
import { JwtService } from '@nestjs/jwt';
import { SubscriptionSchema } from 'src/database/schema/subscription';
import { Subscription } from 'src/database/schema/subscription';
import { TransactionModule } from 'src/transaction/transaction.module';
import {
  Transaction,
  TransactionSchema,
} from 'src/database/schema/transaction';
import { Student, StudentSchema } from 'src/database/schema/student';
import { Invoice, InvoiceSchema } from 'src/database/schema/invoice';
import { PaymentTransactionModule } from 'src/payment-transaction/payment-transaction.module';
import { CurrencyModule } from 'src/currency/currency.module';
import { PaymentProcessorModule } from 'src/payment-processor/payment-processor.module';
import { PaymentTransaction } from 'src/database/schema/paymentTransaction';
import { PaymentTransactionSchema } from 'src/database/schema/paymentTransaction';
import { AuthModule } from 'src/auth/auth.module';
import { StudiosModule } from 'src/studios/studios.module';
import { DiscountModule } from 'src/discount/discount.module';
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: SubscriptionInvoice.name, schema: SubscriptionInvoiceSchema },
      { name: Subscription.name, schema: SubscriptionSchema },
      { name: Transaction.name, schema: TransactionSchema },
      { name: Student.name, schema: StudentSchema },
      { name: Invoice.name, schema: InvoiceSchema },
      { name: PaymentTransaction.name, schema: PaymentTransactionSchema },
    ]),
    AuthModule,
    forwardRef(() => SubscriptionModule),
    forwardRef(() => TransactionModule),
    forwardRef(() => PaymentTransactionModule),
    forwardRef(() => CurrencyModule),
    forwardRef(() => PaymentProcessorModule),
    forwardRef(() => StudiosModule),
    forwardRef(() => DiscountModule),
  ],
  controllers: [SubscriptionInvoiceController],
  providers: [
    SubscriptionInvoiceService,
    JwtService,
    SubscriptionService,
    TransactionService,
  ],
  exports: [SubscriptionInvoiceService],
})
export class SubscriptionInvoiceModule {}
