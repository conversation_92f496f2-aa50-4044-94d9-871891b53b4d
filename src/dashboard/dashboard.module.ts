import { Module } from '@nestjs/common';
import { DashboardService } from './dashboard.service';
import { DashboardController } from './dashboard.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Student, StudentSchema } from 'src/database/schema/student';
import { JwtModule } from '@nestjs/jwt';
import { Session, SessionSchema } from 'src/database/schema/session';
import { Enrollment, EnrollmentSchema } from 'src/database/schema/enrollment';
import {
  EnrollmentHistory,
  EnrollmentHistorySchema,
} from 'src/database/schema/enrollmentHistory';
import {
  ClassHistory,
  ClassHistorySchema,
} from 'src/database/schema/classHistory';
import { Studio, StudioSchema } from 'src/database/schema/studio';
import {
  SubscriptionInvoice,
  SubscriptionInvoiceSchema,
} from 'src/database/schema/subscriptionInvoice';
import { AuthModule } from 'src/auth/auth.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Enrollment.name, schema: EnrollmentSchema },
      { name: EnrollmentHistory.name, schema: EnrollmentHistorySchema },
      { name: ClassHistory.name, schema: ClassHistorySchema },
      { name: Student.name, schema: StudentSchema },
      { name: Session.name, schema: SessionSchema },
      { name: Studio.name, schema: StudioSchema },
      { name: SubscriptionInvoice.name, schema: SubscriptionInvoiceSchema },
    ]),
    JwtModule,
    AuthModule,
  ],
  controllers: [DashboardController],
  providers: [DashboardService],
})
export class DashboardModule {}
