import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, PipelineStage, Types } from 'mongoose';
import { Student, StudentDocument } from 'src/database/schema/student';
import { Session, SessionDocument } from 'src/database/schema/session';
import { ObjectId } from 'mongodb';
import { Enrollment, EnrollmentDocument } from 'src/database/schema/enrollment';
import { groupBy } from './dto/dashboard-services.dto';
import {
  EnrollmentHistory,
  EnrollmentHistoryDocument,
} from 'src/database/schema/enrollmentHistory';
import {
  ClassHistory,
  ClassHistoryDocument,
} from 'src/database/schema/classHistory';
import { Studio } from 'src/database/schema/studio';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import { InvoiceStatus } from 'src/stripe/type';

@Injectable()
export class DashboardService {
  private readonly logger = new Logger(DashboardService.name);

  constructor(
    @InjectModel(Student.name) private studentModel: Model<StudentDocument>,
    @InjectModel(Session.name) private sessionModel: Model<SessionDocument>,
    @InjectModel(Enrollment.name)
    private enrollmentModel: Model<EnrollmentDocument>,
    @InjectModel(EnrollmentHistory.name)
    private enrollmentHistoryModel: Model<EnrollmentHistoryDocument>,
    @InjectModel(ClassHistory.name)
    private classHistoryModel: Model<ClassHistoryDocument>,
    @InjectModel(Studio.name)
    private readonly studioModel: Model<Studio>,
    @InjectModel(SubscriptionInvoice.name)
    private readonly subscriptionInvoiceModel: Model<SubscriptionInvoice>,
  ) {}

  async getAbsentPercentStats(
    sessionId: string,
    groupBy: groupBy = 'className',
  ) {
    try {
      const session = await this.sessionModel.findById(sessionId);
      if (!session?.studioId) throw new Error('Session not found');
      const studioId = session.studioId;

      let pipeline: PipelineStage[] = [
        {
          $match: {
            studioId: studioId,
          },
        },
        {
          $unwind: '$attendance',
        },
        {
          $group: {
            _id: '$attendance.classId',
            total: {
              $sum: 1,
            },
            absentCount: {
              $sum: {
                $cond: [
                  {
                    $eq: ['$attendance.status', 'absent'],
                  },
                  1,
                  0,
                ],
              },
            },
          },
        },
        {
          $lookup: {
            from: 'enrollments',
            localField: '_id',
            foreignField: '_id',
            as: 'class',
          },
        },
        {
          $unwind: '$class',
        },
      ];

      if (groupBy === 'className') {
        pipeline = [
          ...pipeline,
          {
            $project: {
              _id: 0,
              name: '$class.title',
              studio: '$class.studio',
              total: '$total',
              absentCount: '$absentCount',
              classes: [
                {
                  className: '$class.title',
                  classId: '$class._id',
                  absentCount: '$absentCount',
                },
              ],
              absentPercent: {
                $round: [
                  {
                    $cond: [
                      {
                        $eq: ['$total', 0],
                      },
                      0,
                      {
                        $divide: ['$absentCount', '$total'],
                      },
                    ],
                  },
                  2,
                ],
              },
            },
          },
        ];
      } else if (groupBy === 'groupName') {
        pipeline = [
          ...pipeline,
          {
            $unwind: '$class.group',
          },
          {
            $group: {
              _id: '$class.group',
              classes: {
                $push: {
                  className: '$class.title',
                  classId: '$class._id',
                  absentCount: '$absentCount',
                },
              },
              total: {
                $sum: '$total',
              },
              absentCount: {
                $sum: '$absentCount',
              },
            },
          },
          {
            $lookup: {
              from: 'customforms',
              localField: '_id',
              foreignField: '_id',
              as: 'group',
            },
          },
          {
            $unwind: {
              path: '$group',
            },
          },
          {
            $project: {
              _id: 0,
              name: '$group.fieldName',
              studio: '$group.studio',
              total: '$total',
              classes: '$classes',
              absentCount: '$absentCount',
              absentPercent: {
                $round: [
                  {
                    $cond: [
                      {
                        $eq: ['$total', 0],
                      },
                      0,
                      {
                        $divide: ['$absentCount', '$total'],
                      },
                    ],
                  },
                  2,
                ],
              },
            },
          },
        ];
      }
      const absentPercentByClass = await this.studentModel.aggregate(pipeline);
      return absentPercentByClass;
    } catch (error) {
      this.logger.error(
        `Error fetching absent percent by student: ${error.message}`,
      );
      throw new Error('Failed to fetch absent percent by student');
    }
  }

  async getStudentsAgeDistribution(studioId: string) {
    const pipeline: PipelineStage[] = [
      {
        $match: {
          studioId: new ObjectId(studioId),
        },
      },
      {
        $project: {
          dob: 1,
          age: {
            $cond: [
              { $ifNull: ['$dob', false] },
              {
                $floor: {
                  $divide: [
                    { $subtract: [new Date(), '$dob'] },
                    31557600000, // milliseconds in a year
                  ],
                },
              },
              null,
            ],
          },
        },
      },
      {
        $addFields: {
          ageGroup: {
            $switch: {
              branches: [
                {
                  case: {
                    $and: [{ $ne: ['$age', null] }, { $lte: ['$age', 3] }],
                  },
                  then: '0-3',
                },
                {
                  case: {
                    $and: [
                      { $ne: ['$age', null] },
                      { $gte: ['$age', 4] },
                      { $lte: ['$age', 5] },
                    ],
                  },
                  then: '4-5',
                },
                {
                  case: {
                    $and: [
                      { $ne: ['$age', null] },
                      { $gte: ['$age', 6] },
                      { $lte: ['$age', 8] },
                    ],
                  },
                  then: '6-8',
                },
                {
                  case: {
                    $and: [
                      { $ne: ['$age', null] },
                      { $gte: ['$age', 9] },
                      { $lte: ['$age', 11] },
                    ],
                  },
                  then: '9-11',
                },
                {
                  case: {
                    $and: [
                      { $ne: ['$age', null] },
                      { $gte: ['$age', 12] },
                      { $lte: ['$age', 14] },
                    ],
                  },
                  then: '12-14',
                },
                {
                  case: {
                    $and: [
                      { $ne: ['$age', null] },
                      { $gte: ['$age', 15] },
                      { $lte: ['$age', 18] },
                    ],
                  },
                  then: '15-18',
                },
              ],
              default: '18+',
            },
          },
        },
      },
      {
        $group: {
          _id: '$ageGroup',
          count: { $sum: 1 },
        },
      },
      {
        $project: {
          _id: 0,
          ageGroup: '$_id',
          count: 1,
        },
      },
    ];
    const result = await this.studentModel.aggregate(pipeline).exec();
    const totalCount = result.reduce((acc, curr) => acc + curr.count, 0);
    return { totalCount, result };
  }

  async getClassCapacity(sessionId: string, groupBy: groupBy = 'className') {
    try {
      const pipeline: PipelineStage[] = [
        {
          $match: {
            session: new ObjectId(sessionId),
          },
        },
        // Stage 2: Lookup students enrolled in this class with active or scheduled status
        {
          $lookup: {
            from: 'students',
            // The name of the students collection
            let: {
              classId: '$_id',
            },
            // Variable for the class ID
            pipeline: [
              // Match students that have this enrollmentId in their enrollments array
              {
                $match: {
                  $expr: {
                    $in: ['$$classId', '$enrollments.enrollmentId'],
                  },
                },
              },
              // Unwind the enrollments array to process each enrollment individually
              {
                $unwind: '$enrollments',
              },
              // Match only the specific enrollment and active/scheduled status
              {
                $match: {
                  $expr: {
                    $and: [
                      {
                        $eq: ['$enrollments.enrollmentId', '$$classId'],
                      },
                      {
                        $in: [
                          '$enrollments.subscriptionStatus',
                          ['active', 'scheduled'],
                        ],
                      },
                    ],
                  },
                },
              },
              // Count the number of students matching the criteria
              {
                $count: 'registeredCount',
              },
            ],
            as: 'studentStats', // Output array field name
          },
        },
        // Stage 3: Add fields for registered count and available seats
        {
          $addFields: {
            // Extract registered count, default to 0 if no students found
            registered: {
              $ifNull: [
                {
                  $first: '$studentStats.registeredCount',
                },
                0,
              ],
            },
          },
        },
      ];

      if (groupBy === 'className') {
        pipeline.push(
          {
            $addFields: {
              // Calculate available seats
              seatsAvailable: {
                $subtract: ['$maxSize', '$registered'],
              },
              classes: [
                {
                  className: '$title',
                  classId: '$_id',
                  seatsAvailable: '$seatsAvailable',
                  registered: '$registered',
                  maxSize: '$maxSize',
                  studioId: '$studio',
                },
              ],
            },
          },
          // Stage 4: Project the final desired fields
          {
            $project: {
              _id: 0,
              id: '$_id',
              name: '$title',
              classes: '$classes',
              stuidioId: '$studio',
              capacity: '$maxSize',
              registered: 1,
              seatsAvailable: 1,
              seatAvailabilityPercent: {
                $round: [
                  {
                    $cond: [
                      {
                        $eq: ['$maxSize', 0],
                      },
                      0,
                      {
                        $divide: ['$seatsAvailable', '$maxSize'],
                      },
                    ],
                  },
                  2,
                ],
              },
            },
          },
        );
      } else if (groupBy === 'groupName') {
        pipeline.push(
          {
            $addFields: {
              // Calculate available seats
              seatsAvailable: {
                $subtract: ['$maxSize', '$registered'],
              },
            },
          },
          {
            $unwind: '$group',
          },
          {
            $group: {
              _id: '$group',
              classes: {
                $push: {
                  className: '$title',
                  classId: '$_id',
                  seatsAvailable: '$seatsAvailable',
                  registered: '$registered',
                  maxSize: '$maxSize',
                  studioId: '$studio',
                },
              },
              seatsAvailable: {
                $sum: '$seatsAvailable',
              },
              registered: {
                $sum: '$registered',
              },
              maxSize: {
                $sum: '$maxSize',
              },
            },
          },
          {
            $lookup: {
              from: 'customforms',
              localField: '_id',
              foreignField: '_id',
              as: 'group',
            },
          },
          {
            $unwind: '$group',
          },
          // Stage 4: Project the final desired fields
          {
            $project: {
              _id: 0,
              id: '$_id',
              name: '$group.fieldName',
              classes: '$classes',
              capacity: '$maxSize',
              studioId: '$group.studio',
              registered: 1,
              seatsAvailable: 1,
              seatAvailabilityPercent: {
                $round: [
                  {
                    $cond: [
                      {
                        $eq: ['$maxSize', 0],
                      },
                      0,
                      {
                        $divide: ['$seatsAvailable', '$maxSize'],
                      },
                    ],
                  },
                  2,
                ],
              },
            },
          },
        );
      }

      const result = await this.enrollmentModel.aggregate(pipeline).exec();
      return result;
    } catch (error) {
      this.logger.error(`Error fetching class capacity: ${error.message}`);
      throw new Error('Failed to fetch class capacity');
    }
  }

  async getStudentDistribution(
    sessionId: string,
    groupBy: groupBy = 'className',
  ) {
    try {
      const pipeline: PipelineStage[] = [
        // 1) filter to only the "classes" in this session
        {
          $match: {
            session: new ObjectId(sessionId),
          },
        },
        // 2) for each class, lookup students whose enrollments.enrollmentId == _id
        {
          $lookup: {
            from: 'students',
            let: {
              classId: '$_id',
            },
            pipeline: [
              {
                $unwind: '$enrollments',
              },
              {
                $match: {
                  'enrollments.subscriptionStatus': {
                    $in: ['active', 'scheduled'],
                  },
                },
              },
              {
                $match: {
                  $expr: {
                    $eq: ['$$classId', '$enrollments.enrollmentId'],
                  },
                },
              },
              {
                $count: 'studentCount',
              },
            ],
            as: 'students',
          },
        },
        {
          $unwind: '$students',
        },
      ];

      if (groupBy === 'className') {
        pipeline.push({
          $project: {
            _id: 0,
            id: '$_id',
            name: '$title',
            studentCount: '$students.studentCount',
            studioId: '$studio',
            classes: [
              {
                className: '$title',
                classId: '$_id',
                studentCount: '$students.studentCount',
              },
            ],
          },
        });
      } else if (groupBy === 'groupName') {
        pipeline.push(
          {
            $unwind: '$group',
          },
          {
            $group: {
              _id: '$group',
              classes: {
                $push: {
                  className: '$title',
                  classId: '$_id',
                  studentCount: '$students.studentCount',
                },
              },
              studioId: {
                $first: '$studio',
              },
              studentCount: {
                $sum: '$students.studentCount',
              },
            },
          },
          {
            $lookup: {
              from: 'customforms',
              localField: '_id',
              foreignField: '_id',
              as: 'group',
            },
          },
          {
            $unwind: '$group',
          },
          {
            $project: {
              _id: 0,
              id: '$_id',
              name: '$group.fieldName',
              studentCount: 1,
              studioId: 1,
              classes: 1,
            },
          },
        );
      }
      const result = await this.enrollmentModel.aggregate(pipeline).exec();
      const totalCount = result.reduce(
        (acc, curr) => acc + curr.studentCount,
        0,
      );
      result.forEach((item) => {
        item.studentPercent =
          totalCount === 0
            ? 0
            : Number((item.studentCount / totalCount).toFixed(2));
      });
      return { result, totalCount };
    } catch (error) {
      this.logger.error(
        `Error fetching student distribution by class: ${error.message}`,
      );
      throw new Error('Failed to fetch student distribution by class');
    }
  }

  async getDropRateAnalysis(sessionId: string, groupBy: groupBy = 'className') {
    try {
      const session = await this.sessionModel.findById(sessionId);
      if (!session) {
        throw new BadRequestException('Session not found');
      }
      const studioId = session.studioId;

      const pipeline: PipelineStage[] = [
        { $match: { studioId } },
        {
          $unwind: '$students',
        },
        {
          $group: {
            _id: '$classId',
            studioId: {
              $first: '$studioId',
            },
            droppedCount: {
              $sum: {
                $cond: [
                  {
                    $eq: ['$students.status', 'dropped'],
                  },
                  1,
                  0,
                ],
              },
            },
            enrolledCount: {
              $sum: {
                $cond: [
                  {
                    $in: ['$students.status', ['active', 'scheduled']],
                  },
                  1,
                  0,
                ],
              },
            },
          },
        },
        {
          $lookup: {
            from: 'enrollments',
            localField: '_id',
            foreignField: '_id',
            as: 'enrollment',
          },
        },
        {
          $unwind: '$enrollment',
        },
        {
          $project: {
            _id: 0,
            id: {
              $toString: '$_id',
            },
            name: '$enrollment.title',
            group: '$enrollment.group',
            droppedCount: 1,
            enrolledCount: 1,
            totalStudents: {
              $add: ['$droppedCount', '$enrolledCount'],
            },
            dropRatePercentage: {
              $round: [
                {
                  $cond: [
                    {
                      $eq: [
                        {
                          $add: ['$droppedCount', '$enrolledCount'],
                        },
                        0,
                      ],
                    },
                    0,
                    {
                      $multiply: [
                        {
                          $divide: [
                            '$droppedCount',
                            {
                              $add: ['$droppedCount', '$enrolledCount'],
                            },
                          ],
                        },
                        100,
                      ],
                    },
                  ],
                },
                2,
              ],
            },
          },
        },
      ];

      if (groupBy === 'className') {
        pipeline.push({
          $addFields: {
            classes: [
              {
                className: '$name',
                classId: '$id',
                droppedCount: '$droppedCount',
                enrolledCount: '$enrolledCount',
                totalStudents: '$totalStudents',
                dropRatePercentage: '$dropRatePercentage',
              },
            ],
          },
        });
      } else if (groupBy === 'groupName') {
        pipeline.push(
          {
            $unwind: '$group',
          },
          {
            $lookup: {
              from: 'customforms',
              localField: 'group',
              foreignField: '_id',
              as: 'group',
            },
          },
          {
            $unwind: '$group',
          },
          {
            $group: {
              _id: '$group._id',
              name: { $first: '$group.fieldName' },
              droppedCount: { $sum: '$droppedCount' },
              enrolledCount: { $sum: '$enrolledCount' },
              totalStudents: { $sum: '$totalStudents' },
              classes: {
                $push: {
                  className: '$name',
                  classId: '$id',
                  droppedCount: '$droppedCount',
                  enrolledCount: '$enrolledCount',
                  totalStudents: '$totalStudents',
                  dropRatePercentage: '$dropRatePercentage',
                },
              },
            },
          },
          {
            $project: {
              _id: 0,
              id: '$_id',
              name: '$name',
              droppedCount: 1,
              enrolledCount: 1,
              totalStudents: 1,
              classes: 1,
              dropRatePercentage: {
                $round: [
                  {
                    $cond: [
                      {
                        $eq: [{ $add: ['$droppedCount', '$enrolledCount'] }, 0],
                      },
                      0,
                      {
                        $multiply: [
                          {
                            $divide: [
                              '$droppedCount',
                              { $add: ['$droppedCount', '$enrolledCount'] },
                            ],
                          },
                          100,
                        ],
                      },
                    ],
                  },
                  2,
                ],
              },
            },
          },
        );
      }

      const dropRateAnalysis = await this.classHistoryModel
        .aggregate(pipeline)
        .exec();
      return dropRateAnalysis;
    } catch (error) {
      this.logger.error(`Error fetching drop rate analysis: ${error.message}`);
      throw new Error('Failed to fetch drop rate analysis');
    }
  }

  async getTotalClass(sessionId: string) {
    try {
      const session = await this.sessionModel.findById(sessionId);
      if (!session) {
        throw new BadRequestException('Session not found');
      }
      const studioId = session.studioId;

      const result = await this.enrollmentModel.countDocuments({
        studio: studioId,
      });
      return { totalClasses: result };
    } catch (error) {
      this.logger.error(`Error fetching total classes: ${error.message}`);
      throw new Error('Failed to fetch total classes');
    }
  }

  async getAverageAttendance(sessionId: string) {
    const absentStat = await this.getAbsentPercentStats(sessionId);
    if (!absentStat || absentStat.length === 0) {
      return { averageAttendance: 1, averageAbsentPercent: 0 };
    }
    const totalAbsentPercent = absentStat.reduce(
      (acc, curr) => acc + (curr.absentPercent ?? 0),
      0,
    );
    const averageAbsentPercent = totalAbsentPercent / absentStat.length;
    const averageAttendance = 1 - averageAbsentPercent;
    return {
      averageAttendance: Number(averageAttendance.toFixed(2)),
      averageAbsentPercent: Number(averageAbsentPercent.toFixed(2)),
    };
  }

  async getAverageDropRate(sessionId: string) {
    const dropRate = await this.getDropRateAnalysis(sessionId);
    if (!dropRate || dropRate.length === 0) {
      return { averageDropRate: 0 };
    }
    const totalDropRate = dropRate.reduce(
      (acc, curr) => acc + (curr.dropRatePercentage ?? 0),
      0,
    );
    const averageDropRate = totalDropRate / dropRate.length;
    return { averageDropRate: Number(averageDropRate.toFixed(2)) };
  }

  async getRevenueSnapshot(
    studioId: string,
    timeframe: 'daily' | 'weekly' | 'monthly',
  ) {
    const studio = await this.studioModel.findById(studioId).lean().exec();
    if (!studio) {
      throw new NotFoundException('Studio not found');
    }

    const studioIdObjectId = new Types.ObjectId(studioId);
    const now = new Date(); // Current moment

    const timeframes: ('daily' | 'weekly' | 'monthly')[] = [
      'daily',
      'weekly',
      'monthly',
    ];
    const revenueSnapshots = [];

    for (const tf of timeframes) {
      let startDateCurrent: Date;
      const endDateCurrentQuery = new Date(now);

      let startDatePrevious: Date;
      let endDatePrevious: Date;

      const todayUTCStart = new Date(
        Date.UTC(
          now.getUTCFullYear(),
          now.getUTCMonth(),
          now.getUTCDate(),
          0,
          0,
          0,
          0,
        ),
      );

      switch (tf) {
        case 'daily':
          startDateCurrent = new Date(todayUTCStart);
          endDatePrevious = new Date(startDateCurrent.getTime() - 1);
          startDatePrevious = new Date(
            Date.UTC(
              endDatePrevious.getUTCFullYear(),
              endDatePrevious.getUTCMonth(),
              endDatePrevious.getUTCDate(),
              0,
              0,
              0,
              0,
            ),
          );
          break;
        case 'weekly':
          const currentUTCDayOfWeek = now.getUTCDay();
          startDateCurrent = new Date(
            Date.UTC(
              now.getUTCFullYear(),
              now.getUTCMonth(),
              now.getUTCDate() - currentUTCDayOfWeek,
              0,
              0,
              0,
              0,
            ),
          );
          endDatePrevious = new Date(startDateCurrent.getTime() - 1);
          startDatePrevious = new Date(
            Date.UTC(
              endDatePrevious.getUTCFullYear(),
              endDatePrevious.getUTCMonth(),
              endDatePrevious.getUTCDate() - 6,
              0,
              0,
              0,
              0,
            ),
          );
          break;
        case 'monthly':
          startDateCurrent = new Date(
            Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), 1, 0, 0, 0, 0),
          );
          endDatePrevious = new Date(startDateCurrent.getTime() - 1);
          startDatePrevious = new Date(
            Date.UTC(
              endDatePrevious.getUTCFullYear(),
              endDatePrevious.getUTCMonth(),
              1,
              0,
              0,
              0,
              0,
            ),
          );
          break;
      }

      const currentInvoices = await this.subscriptionInvoiceModel
        .find({
          studioId: studioIdObjectId,
          status: InvoiceStatus.PAID,
          createdAt: { $gte: startDateCurrent, $lte: endDateCurrentQuery },
        })
        .lean()
        .exec();
      const totalRevenueCurrent = currentInvoices.reduce(
        (acc, invoice) => acc + invoice.finalAmount,
        0,
      );

      const previousInvoices = await this.subscriptionInvoiceModel
        .find({
          studioId: studioIdObjectId,
          status: InvoiceStatus.PAID,
          createdAt: { $gte: startDatePrevious, $lte: endDatePrevious },
        })
        .lean()
        .exec();
      const totalRevenuePrevious = previousInvoices.reduce(
        (acc, invoice) => acc + invoice.finalAmount,
        0,
      );

      let percentageChange = 0;
      if (totalRevenuePrevious > 0) {
        percentageChange =
          ((totalRevenueCurrent - totalRevenuePrevious) /
            totalRevenuePrevious) *
          100;
      } else if (totalRevenueCurrent > 0) {
        percentageChange = 100;
      }

      revenueSnapshots.push({
        timeframe: tf,
        totalRevenue: parseFloat(totalRevenueCurrent.toFixed(2)),
        percentageChange: parseFloat(percentageChange.toFixed(2)),
      });
    }

    // Prepare data for the chart (using full UTC days/weeks/months based on the input 'timeframe' parameter)
    const dataForChart = [];

    if (timeframe === 'daily') {
      // Show last 7 full UTC days
      const daysToShow = 7;
      for (let i = 0; i < daysToShow; i++) {
        const dayStart = new Date(
          Date.UTC(
            now.getUTCFullYear(),
            now.getUTCMonth(),
            now.getUTCDate() - (daysToShow - 1 - i),
            0,
            0,
            0,
            0,
          ),
        );
        const dayEnd = new Date(
          Date.UTC(
            now.getUTCFullYear(),
            now.getUTCMonth(),
            now.getUTCDate() - (daysToShow - 1 - i),
            23,
            59,
            59,
            999,
          ),
        );

        const dailyRevenue = (
          await this.subscriptionInvoiceModel
            .find({
              studioId: studioIdObjectId,
              status: InvoiceStatus.PAID,
              createdAt: { $gte: dayStart, $lte: dayEnd },
            })
            .lean()
            .exec()
        ).reduce((acc, inv) => acc + inv.finalAmount, 0);

        dataForChart.push({
          date: dayStart.toISOString().split('T')[0],
          revenue: parseFloat(dailyRevenue.toFixed(2)),
          label: dayStart.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            timeZone: 'UTC',
          }),
        });
      }
    } else if (timeframe === 'weekly') {
      // Show last 4 full UTC weeks (Sun-Sat)
      const weeksToShow = 4;
      const currentWeekChartStartDay = new Date(
        Date.UTC(
          now.getUTCFullYear(),
          now.getUTCMonth(),
          now.getUTCDate() - now.getUTCDay(),
          0,
          0,
          0,
          0,
        ),
      );

      for (let i = 0; i < weeksToShow; i++) {
        const weekStart = new Date(currentWeekChartStartDay);
        weekStart.setUTCDate(
          currentWeekChartStartDay.getUTCDate() - 7 * (weeksToShow - 1 - i),
        );

        const weekEnd = new Date(weekStart);
        weekEnd.setUTCDate(weekStart.getUTCDate() + 6);
        weekEnd.setUTCHours(23, 59, 59, 999);

        const weeklyRevenue = (
          await this.subscriptionInvoiceModel
            .find({
              studioId: studioIdObjectId,
              status: InvoiceStatus.PAID,
              createdAt: { $gte: weekStart, $lte: weekEnd },
            })
            .lean()
            .exec()
        ).reduce((acc, inv) => acc + inv.finalAmount, 0);

        dataForChart.push({
          date: weekStart.toISOString().split('T')[0],
          revenue: parseFloat(weeklyRevenue.toFixed(2)),
          label: `Week of ${weekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric', timeZone: 'UTC' })}`,
        });
      }
    } else if (timeframe === 'monthly') {
      // Show last 6 full UTC months
      const monthsToShow = 6;
      for (let i = 0; i < monthsToShow; i++) {
        const monthStart = new Date(
          Date.UTC(
            now.getUTCFullYear(),
            now.getUTCMonth() - (monthsToShow - 1 - i),
            1,
            0,
            0,
            0,
            0,
          ),
        );
        const monthEnd = new Date(
          Date.UTC(
            now.getUTCFullYear(),
            now.getUTCMonth() - (monthsToShow - 1 - i) + 1,
            0,
            23,
            59,
            59,
            999,
          ),
        ); // Day 0 of next month is last day of current month

        const monthlyRevenue = (
          await this.subscriptionInvoiceModel
            .find({
              studioId: studioIdObjectId,
              status: InvoiceStatus.PAID,
              createdAt: { $gte: monthStart, $lte: monthEnd },
            })
            .lean()
            .exec()
        ).reduce((acc, inv) => acc + inv.finalAmount, 0);

        dataForChart.push({
          date: monthStart.toISOString().split('T')[0],
          revenue: parseFloat(monthlyRevenue.toFixed(2)),
          label: monthStart.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            timeZone: 'UTC',
          }),
        });
      }
    }

    return {
      revenueSnapshots,
      dataForChart,
    };
  }

  async getOutstandingOverview(studioId: string) {
    const studioObjectId = new Types.ObjectId(studioId);
    const now = new Date();

    // Fetch all pending, failed, and partially paid invoices
    const overdueInvoices = await this.subscriptionInvoiceModel
      .find({
        studioId: studioObjectId,
        status: {
          $in: [
            InvoiceStatus.PENDING,
            InvoiceStatus.FAILED,
            InvoiceStatus.PARTIALLY_PAID,
          ],
        },
        dueDate: { $lt: now }, // Consider invoices whose dueDate is in the past
      })
      .lean()
      .exec();

    let totalOutstandingBalance = 0;
    const agingBuckets = {
      '0-15 days': { amount: 0, invoices: [] },
      '16-30 days': { amount: 0, invoices: [] },
      '30+ days': { amount: 0, invoices: [] },
    };
    const outstandingPayments = [];

    for (const invoice of overdueInvoices) {
      let currentInvoiceOutstandingAmount = 0;
      if (invoice.status === InvoiceStatus.PARTIALLY_PAID) {
        const totalPaymentsMade =
          invoice.payments?.reduce((sum, payment) => sum + payment.amount, 0) ||
          0;
        currentInvoiceOutstandingAmount =
          invoice.finalAmount - totalPaymentsMade;
      } else {
        currentInvoiceOutstandingAmount = invoice.finalAmount;
      }

      // Only process if there's an actual outstanding amount
      if (currentInvoiceOutstandingAmount <= 0) {
        continue;
      }

      totalOutstandingBalance += currentInvoiceOutstandingAmount;

      const dueDate = new Date(invoice.dueDate);
      const daysOverdue = Math.max(
        0,
        Math.floor((now.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24)),
      ); // Ensure daysOverdue is not negative

      let bucketLabel = '';
      if (daysOverdue <= 15) {
        bucketLabel = '0-15 days';
      } else if (daysOverdue <= 30) {
        bucketLabel = '16-30 days';
      } else {
        bucketLabel = '30+ days';
      }

      if (agingBuckets[bucketLabel]) {
        agingBuckets[bucketLabel].amount += currentInvoiceOutstandingAmount;
        // Optionally, add more invoice details to agingBuckets[bucketLabel].invoices if needed for the frontend
      }

      // Populate outstandingPayments array
      const parent = await this.studioModel.db
        .collection('parents')
        .findOne({ _id: invoice.parentId });
      const student = await this.studioModel.db
        .collection('students')
        .findOne({ _id: invoice.studentId });

      outstandingPayments.push({
        studentOrParentName: parent
          ? `${parent.firstName} ${parent.lastName}`
          : student
            ? `${student.firstName} ${student.lastName}`
            : 'N/A',
        amountDue: parseFloat(currentInvoiceOutstandingAmount.toFixed(2)),
        daysOverdue: daysOverdue,
        status: invoice.status,
        // Potentially add invoiceId or other identifiers if needed for actions on the frontend
      });
    }

    // Calculate percentages for aging buckets
    const formattedAgingBuckets = Object.keys(agingBuckets).map((label) => ({
      label,
      amount: parseFloat(agingBuckets[label].amount.toFixed(2)),
      percentage:
        totalOutstandingBalance > 0
          ? parseFloat(
              (
                (agingBuckets[label].amount / totalOutstandingBalance) *
                100
              ).toFixed(2),
            )
          : 0,
    }));

    return {
      totalOutstandingBalance: parseFloat(totalOutstandingBalance.toFixed(2)),
      agingBuckets: formattedAgingBuckets,
      outstandingPayments: outstandingPayments.sort(
        (a, b) => b.daysOverdue - a.daysOverdue,
      ), // Sort by most overdue
    };
  }

  async getFailedPaymentsOverview(studioId: string) {
    const studioObjectId = new Types.ObjectId(studioId);
    const now = new Date();
    const currentMonth = now.getUTCMonth(); // 0-indexed
    const currentYear = now.getUTCFullYear();

    // --- Total Failed This Month ---
    const startOfCurrentMonth = new Date(
      Date.UTC(currentYear, currentMonth, 1, 0, 0, 0, 0),
    );
    const endOfCurrentMonth = new Date(
      Date.UTC(currentYear, currentMonth + 1, 0, 23, 59, 59, 999),
    ); // Day 0 of next month is last day of current

    const failedInvoicesThisMonth = await this.subscriptionInvoiceModel
      .find({
        studioId: studioObjectId,
        status: InvoiceStatus.FAILED,
        updatedAt: { $gte: startOfCurrentMonth, $lte: endOfCurrentMonth }, // Assuming updatedAt reflects the failure date
      })
      .lean()
      .exec();

    const totalFailedCountThisMonth = failedInvoicesThisMonth.length;
    const totalFailedAmountThisMonth = failedInvoicesThisMonth.reduce(
      (sum, invoice) => sum + invoice.finalAmount,
      0,
    );

    // --- Monthly Payment Failures (Last 6 Months including current) ---
    const monthlyFailures = [];
    for (let i = 5; i >= 0; i--) {
      // Iterate from 5 months ago up to current month
      const monthDate = new Date(Date.UTC(currentYear, currentMonth - i, 1));
      const monthStart = new Date(
        Date.UTC(
          monthDate.getUTCFullYear(),
          monthDate.getUTCMonth(),
          1,
          0,
          0,
          0,
          0,
        ),
      );
      const monthEnd = new Date(
        Date.UTC(
          monthDate.getUTCFullYear(),
          monthDate.getUTCMonth() + 1,
          0,
          23,
          59,
          59,
          999,
        ),
      );

      const failedInvoicesInMonth = await this.subscriptionInvoiceModel
        .find({
          studioId: studioObjectId,
          status: InvoiceStatus.FAILED,
          updatedAt: { $gte: monthStart, $lte: monthEnd },
        })
        .lean()
        .exec();

      const count = failedInvoicesInMonth.length;
      const amount = failedInvoicesInMonth.reduce(
        (sum, inv) => sum + inv.finalAmount,
        0,
      );

      monthlyFailures.push({
        month: monthStart.toLocaleDateString('en-US', {
          month: 'short',
          year: 'numeric',
          timeZone: 'UTC',
        }), // e.g., "May 2024"
        monthShort: monthStart.toLocaleDateString('en-US', {
          month: 'short',
          timeZone: 'UTC',
        }), // e.g., "May"
        failedPayments: count,
        totalAmount: parseFloat(amount.toFixed(2)),
      });
    }

    // --- Attention Required (using the count from this month for now) ---
    // This could be defined differently, e.g., based on how long they have been failed.
    // For now, it matches the "Total Failed This Month" count.
    const attentionRequiredCount = totalFailedCountThisMonth;

    return {
      attentionRequiredCount,
      totalFailedCountThisMonth,
      totalFailedAmountThisMonth: parseFloat(
        totalFailedAmountThisMonth.toFixed(2),
      ),
      monthlyFailures, // This will be an array of the last 6 months of data
    };
  }

  async getScheduledUpcomingOverview(
    studioId: string,
    timelineDaysParam?: number,
  ) {
    const studioObjectId = new Types.ObjectId(studioId);
    const now = new Date();
    const todayUTCStart = new Date(
      Date.UTC(
        now.getUTCFullYear(),
        now.getUTCMonth(),
        now.getUTCDate(),
        0,
        0,
        0,
        0,
      ),
    );

    // --- Expected Revenue (Next 7 Days) ---
    const sevenDaysFromNow = new Date(todayUTCStart);
    sevenDaysFromNow.setUTCDate(todayUTCStart.getUTCDate() + 7);

    const upcomingInvoicesNext7Days = await this.subscriptionInvoiceModel
      .find({
        studioId: studioObjectId,
        status: { $in: [InvoiceStatus.SCHEDULED, InvoiceStatus.UPCOMING] }, // Consider both scheduled and upcoming
        // Assuming 'dueDate' is the field that indicates when the payment is expected
        dueDate: { $gte: todayUTCStart, $lt: sevenDaysFromNow },
      })
      .lean()
      .exec();
    const expectedRevenueNext7Days = upcomingInvoicesNext7Days.reduce(
      (sum, invoice) => sum + invoice.finalAmount,
      0,
    );

    // --- Expected Revenue (Next 30 Days) ---
    const thirtyDaysFromNow = new Date(todayUTCStart);
    thirtyDaysFromNow.setUTCDate(todayUTCStart.getUTCDate() + 30);

    const upcomingInvoicesNext30Days = await this.subscriptionInvoiceModel
      .find({
        studioId: studioObjectId,
        status: { $in: [InvoiceStatus.SCHEDULED, InvoiceStatus.UPCOMING] },
        dueDate: { $gte: todayUTCStart, $lt: thirtyDaysFromNow },
      })
      .lean()
      .exec();
    const expectedRevenueNext30Days = upcomingInvoicesNext30Days.reduce(
      (sum, invoice) => sum + invoice.finalAmount,
      0,
    );

    // --- Payment Timeline (e.g., next 14 days for a typical view) ---
    const timelineDays = timelineDaysParam || 14; // Number of days to show in the timeline
    const paymentTimeline = [];
    const dailyAggregates = {};

    // Aggregate payments by due date
    const upcomingInvoicesForTimeline = await this.subscriptionInvoiceModel
      .find({
        studioId: studioObjectId,
        status: { $in: [InvoiceStatus.SCHEDULED, InvoiceStatus.UPCOMING] },
        dueDate: {
          $gte: todayUTCStart,
          $lt: new Date(
            new Date(todayUTCStart).setUTCDate(
              todayUTCStart.getUTCDate() + timelineDays,
            ),
          ),
        },
      })
      .lean()
      .exec();

    for (const invoice of upcomingInvoicesForTimeline) {
      const dueDateStr = new Date(invoice.dueDate).toISOString().split('T')[0]; // YYYY-MM-DD
      if (!dailyAggregates[dueDateStr]) {
        dailyAggregates[dueDateStr] = { amount: 0, count: 0 };
      }
      dailyAggregates[dueDateStr].amount += invoice.finalAmount;
      dailyAggregates[dueDateStr].count += 1;
    }

    for (let i = 0; i < timelineDays; i++) {
      const day = new Date(todayUTCStart);
      day.setUTCDate(todayUTCStart.getUTCDate() + i);
      const dayStr = day.toISOString().split('T')[0];

      paymentTimeline.push({
        date: dayStr,
        dayOfWeek: day.toLocaleDateString('en-US', {
          weekday: 'short',
          timeZone: 'UTC',
        }), // e.g., "Fri"
        dayOfMonth: day.getUTCDate(), // e.g., 23
        totalAmount: parseFloat(
          (dailyAggregates[dayStr]?.amount || 0).toFixed(2),
        ),
        paymentCount: dailyAggregates[dayStr]?.count || 0,
      });
    }

    return {
      expectedRevenueNext7Days: parseFloat(expectedRevenueNext7Days.toFixed(2)),
      expectedRevenueNext30Days: parseFloat(
        expectedRevenueNext30Days.toFixed(2),
      ),
      paymentTimeline,
    };
  }
}
