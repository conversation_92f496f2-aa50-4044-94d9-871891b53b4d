import { Controller, Get, Param, Query, UseGuards, Req } from '@nestjs/common';
import { DashboardService } from './dashboard.service';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { groupBy } from './dto/dashboard-services.dto';

@Controller('dashboard')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get('absent-stats/:sessionId')
  async getAbsentPercentStats(
    @Param('sessionId') sessionId: string,
    @Query('groupBy') groupBy: groupBy = 'className',
  ) {
    return this.dashboardService.getAbsentPercentStats(sessionId, groupBy);
  }

  @Get('students-distribution/age/:studioId')
  async getStudentsAgeDistribution(@Param('studioId') studioId: string) {
    return this.dashboardService.getStudentsAgeDistribution(studioId);
  }

  @Get('class-capacity/:sessionId')
  async getClassCapacity(
    @Param('sessionId') sessionId: string,
    @Query('groupBy') groupBy: groupBy = 'className',
  ) {
    return this.dashboardService.getClassCapacity(sessionId, groupBy);
  }

  @Get('student-distribution/class/:sessionId')
  async getStudentDistribution(
    @Param('sessionId') sessionId: string,
    @Query('groupBy') groupBy: groupBy = 'className',
  ) {
    return this.dashboardService.getStudentDistribution(sessionId, groupBy);
  }

  @Get('drop-rate-analysis/:sessionId')
  async getDropRateAnalysis(
    @Param('sessionId') sessionId: string,
    @Query('groupBy') groupBy: groupBy = 'className',
  ) {
    const a = await this.dashboardService.getDropRateAnalysis(
      sessionId,
      groupBy,
    );
    return a;
  }

  @Get('total-classes/:sessionId')
  async getTotalClasses(@Param('sessionId') sessionId: string) {
    return this.dashboardService.getTotalClass(sessionId);
  }

  @Get('average-attendance/:sessionId')
  async getAverageAttendance(@Param('sessionId') sessionId: string) {
    return this.dashboardService.getAverageAttendance(sessionId);
  }

  @Get('average-drop-rate/:sessionId')
  async getAverageDropRate(@Param('sessionId') sessionId: string) {
    return this.dashboardService.getAverageDropRate(sessionId);
  }

  @Get('revenue-snapshot')
  @UseGuards(JwtAuthGuard)
  async getRevenueSnapshot(
    @Req() request: Request,
    @Query('timeframe') timeframe: 'daily' | 'weekly' | 'monthly',
  ) {
    const studioId = request['locationId'];
    return this.dashboardService.getRevenueSnapshot(studioId, timeframe);
  }

  @Get('outstanding-overview')
  @UseGuards(JwtAuthGuard)
  async getOutstandingOverview(@Req() request: Request) {
    const studioId = request['locationId'];
    return this.dashboardService.getOutstandingOverview(studioId);
  }

  @Get('failed-payments-overview')
  @UseGuards(JwtAuthGuard)
  async getFailedPaymentsOverview(@Req() request: Request) {
    const studioId = request['locationId'];
    return this.dashboardService.getFailedPaymentsOverview(studioId);
  }

  @Get('scheduled-upcoming-overview')
  @UseGuards(JwtAuthGuard)
  async getScheduledUpcomingOverview(
    @Req() request: Request,
    @Query('timelineDays') timelineDays?: string,
  ) {
    const studioId = request['locationId'];
    const timelineDaysNum = timelineDays
      ? parseInt(timelineDays, 10)
      : undefined;
    return this.dashboardService.getScheduledUpcomingOverview(
      studioId,
      timelineDaysNum,
    );
  }
}
