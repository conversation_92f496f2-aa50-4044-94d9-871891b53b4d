import { Module } from '@nestjs/common';
import { CustomFormService } from './custom-form.service';
import { CustomFormController } from './custom-form.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { CustomForm, CustomFormSchema } from 'src/database/schema/customForm';
import { JwtModule } from '@nestjs/jwt';
import { GohighlevelModule } from 'src/gohighlevel/gohighlevel.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: CustomForm.name, schema: CustomFormSchema },
    ]),
    GohighlevelModule,
    JwtModule,
  ],
  controllers: [CustomFormController],
  providers: [CustomFormService],
})
export class CustomFormModule {}
