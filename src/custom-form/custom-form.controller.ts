import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Put,
  UseGuards,
  Req,
  Query,
  BadRequestException,
} from '@nestjs/common';
import { CustomFormService } from './custom-form.service';
import { CreateCustomFormDto } from './dto/create-custom-form.dto';
import { UpdateCustomFormDto } from './dto/update-custom-form.dto';
import { CustomForm } from 'src/database/schema/customForm';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { Types } from 'mongoose';

@Controller('custom-form')
@UseGuards(JwtAuthGuard)
export class CustomFormController {
  constructor(private readonly customFormService: CustomFormService) {}

  @Post('/room')
  createRoom(
    @Body() createCustomField: CreateCustomFormDto,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    const locationId_objectId = Types.ObjectId.createFromHexString(locationId);
    return this.customFormService.create(
      createCustomField,
      locationId_objectId,
      'room',
    );
  }

  @Get('/room')
  getRooms(@Req() request: Request) {
    const locationId = request['locationId'];
    const locationId_objectId = Types.ObjectId.createFromHexString(locationId);
    return this.customFormService.findByLocationIdAndType(
      locationId_objectId,
      'room',
    );
  }

  @Post('/instructor')
  createInstructor(
    @Body() createCustomField: CreateCustomFormDto,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    const locationId_objectId = Types.ObjectId.createFromHexString(locationId);
    return this.customFormService.create(
      createCustomField,
      locationId_objectId,
      'instructor',
    );
  }

  @Get('/instructor')
  getInstructors(@Req() request: Request) {
    const locationId = request['locationId'];
    const locationId_objectId = Types.ObjectId.createFromHexString(locationId);
    return this.customFormService.findByLocationIdAndType(
      locationId_objectId,
      'instructor',
    );
  }

  // @Post('/tags')
  // createTags(@Body() createCustomField: CreateCustomFormDto, @Req() request: Request) {
  //   const locationId = request['locationId'];
  //   const locationId_objectId = Types.ObjectId.createFromHexString(locationId);
  //   return this.customFormService.create(createCustomField, locationId_objectId, 'tags');
  // }

  // @Get('/tags')
  // getTags(@Req() request: Request) {
  //   const locationId = request['locationId'];
  //   const locationId_objectId = Types.ObjectId.createFromHexString(locationId);
  //   return this.customFormService.findByLocationIdAndType(locationId_objectId, 'tags');
  // }

  @Post('/group')
  createGroup(
    @Body() createCustomField: CreateCustomFormDto,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    const locationId_objectId = Types.ObjectId.createFromHexString(locationId);
    return this.customFormService.create(
      createCustomField,
      locationId_objectId,
      'group',
    );
  }

  @Get('/group')
  getGroups(@Req() request: Request) {
    const locationId = request['locationId'];
    const locationId_objectId = Types.ObjectId.createFromHexString(locationId);
    return this.customFormService.findByLocationIdAndType(
      locationId_objectId,
      'group',
    );
  }

  @Post('/session')
  createSession(
    @Body() createCustomField: CreateCustomFormDto,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    const locationId_objectId = Types.ObjectId.createFromHexString(locationId);
    return this.customFormService.create(
      createCustomField,
      locationId_objectId,
      'session',
    );
  }

  @Get('/session')
  getSession(@Req() request: Request) {
    const locationId = request['locationId'];
    const locationId_objectId = Types.ObjectId.createFromHexString(locationId);
    return this.customFormService.findByLocationIdAndType(
      locationId_objectId,
      'session',
    );
  }

  @Post('/location')
  createLocation(
    @Body() createCustomField: CreateCustomFormDto,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    const locationId_objectId = Types.ObjectId.createFromHexString(locationId);
    return this.customFormService.create(
      createCustomField,
      locationId_objectId,
      'location',
    );
  }

  @Get('/location')
  getLocation(@Req() request: Request) {
    const locationId = request['locationId'];
    const locationId_objectId = Types.ObjectId.createFromHexString(locationId);
    return this.customFormService.findByLocationIdAndType(
      locationId_objectId,
      'location',
    );
  }

  @Put(':studioId/fields')
  async updateFields(
    @Param('studioId') studioId: string,
    @Body() newField: UpdateCustomFormDto,
  ) {
    // Call the service method to update the fields array
    const updatedCustomForm = await this.customFormService.updateFields(
      studioId,
      newField,
    );
    return updatedCustomForm;
  }

  @Get()
  findAll(@Req() request: Request) {
    const locationId = request['locationId'];
    const locationId_objectId = Types.ObjectId.createFromHexString(locationId);
    return this.customFormService.getCustomFormByStudio(locationId_objectId);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.customFormService.findOne(+id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateCustomFormDto: UpdateCustomFormDto,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return this.customFormService.update(id, updateCustomFormDto, locationId);
  }

  @Delete(':id')
  remove(@Param('id') id: string, @Req() request: Request) {
    const locationId = request['locationId'];
    return this.customFormService.remove(id, locationId);
  }

  @Get('all/studio')
  async getCustomFormByStudio(@Req() request: Request): Promise<CustomForm[]> {
    const locationId = request['locationId'];
    const locationId_objectId = Types.ObjectId.createFromHexString(locationId);
    return this.customFormService.getCustomFormByStudio(locationId_objectId);
  }

  // @Get('studio/fields')
  // async getFieldsByType(
  //   @Req() request: Request,
  //   @Query('type') type: 'room' | 'instructor' | 'tags' | 'group' | 'location',
  //   @Query('page') page: number = 1,
  //   @Query('limit') limit: number = 10,
  // ) {
  //   // Validate field type
  //   const locationId = request['locationId'];
  //   const validTypes = ['room', 'instructor', 'tags', 'group', 'location'];
  //   if (type && !validTypes.includes(type)) {
  //     throw new BadRequestException('Invalid field type');
  //   }

  //   // Convert studioId to ObjectId
  //   const studioObjectId = Types.ObjectId.createFromHexString(locationId);

  //   return this.customFormService.findByStudioIdAndTypeWithPagination(
  //     studioObjectId,
  //     type,
  //     page,
  //     limit
  //   );
  // }

  @Get('studio/fields')
  async searchCustomForms(
    @Req() request: Request,
    @Query('search') searchTerm: string,
    @Query('type') type?: 'room' | 'instructor' | 'tags' | 'group' | 'location',
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    const locationId = request['locationId'];

    // Validate type if provided
    if (type) {
      const validTypes = ['room', 'instructor', 'tags', 'group', 'location'];
      if (!validTypes.includes(type)) {
        throw new BadRequestException('Invalid field type');
      }
    }

    // Only validate search term if it's provided
    if (searchTerm !== undefined && searchTerm.trim().length === 0) {
      throw new BadRequestException('Search term cannot be empty');
    }

    return this.customFormService.searchCustomForms(
      locationId,
      searchTerm?.trim(),
      type,
      page,
      limit,
    );
  }
}
