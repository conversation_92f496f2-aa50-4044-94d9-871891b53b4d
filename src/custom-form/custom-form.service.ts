import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateCustomFormDto } from './dto/create-custom-form.dto';
import { UpdateCustomFormDto } from './dto/update-custom-form.dto';
import { InjectModel } from '@nestjs/mongoose';
import { CustomForm, CustomFormDocument } from 'src/database/schema/customForm';
import { Model, Types } from 'mongoose';
import { GohighlevelService } from 'src/gohighlevel/gohighlevel.service';
// import { tryCatch } from 'bullmq';

@Injectable()
export class CustomFormService {
  constructor(
    @InjectModel(CustomForm.name)
    private customFormModel: Model<CustomFormDocument>,
    private readonly gohighlevelService: GohighlevelService,
  ) {}

  async create(
    createCustomFormDto: CreateCustomFormDto,
    studioId,
    type: string,
  ): Promise<CustomForm> {
    try {
      const createdCustomForm = new this.customFormModel({
        studio: studioId,
        // classEvent: Types.ObjectId.createFromHexString(classEvent),
        fieldName: createCustomFormDto.fieldName,
        fieldType: type,
      });

      try {
        if (type === 'tags') {
          await this.gohighlevelService.addTagToLocation(
            studioId,
            createCustomFormDto.fieldName,
          );
        }
      } catch (error) {
        console.log('error creating tag in location ghl.');
      }
      // Save the document to the database
      return await createdCustomForm.save();
    } catch (error) {
      console.error('error creating custom form fields', error);
    }
  }

  async updateFields(
    studioId: string,
    updateFieldDto: UpdateCustomFormDto,
  ): Promise<CustomForm> {
    try {
      const studioObjectId = Types.ObjectId.createFromHexString(studioId);
      const updatedCustomForm = await this.customFormModel
        .findOneAndUpdate(
          { studio: studioObjectId },
          {
            $push: {
              fields: updateFieldDto,
            },
          },
          { new: true },
        )
        .exec();

      return updatedCustomForm;
    } catch (error) {
      console.error('error updating fields', error);
    }
  }

  findAll() {
    return `This action returns all customForm`;
  }

  findOne(id: number) {
    return `This action returns a #${id} customForm`;
  }

  async update(
    id: string,
    updateCustomFormDto: UpdateCustomFormDto,
    locationId: string,
  ) {
    const locationId_objectId = Types.ObjectId.createFromHexString(locationId);
    const customFormId_objectId = Types.ObjectId.createFromHexString(id);
    const updatedForm = await this.customFormModel.findOneAndUpdate(
      {
        _id: customFormId_objectId,
        studio: locationId_objectId,
      },
      {
        $set: updateCustomFormDto,
      },
      { new: true },
    );

    if (!updatedForm) {
      throw new NotFoundException(`Custom form with ID ${id} not found`);
    }

    return updatedForm;
  }

  async remove(id: string, locationId: string): Promise<CustomForm> {
    const deletedForm = await this.customFormModel.findByIdAndDelete(id).exec();

    if (!deletedForm) {
      throw new NotFoundException(`Custom form with ID ${id} not found`);
    }

    return deletedForm;
  }

  async getCustomFormByStudio(studioId): Promise<CustomForm[]> {
    return this.customFormModel.find({ studio: studioId }).exec();
  }

  async findByLocationIdAndType(
    locationId: Types.ObjectId,
    type: string,
  ): Promise<CustomForm[]> {
    return this.customFormModel
      .find({ studio: locationId, fieldType: type })
      .exec();
  }

  async findByStudioIdAndTypeWithPagination(
    studioId: Types.ObjectId,
    type: string,
    page: number,
    limit: number,
  ) {
    const skip = (page - 1) * limit;

    const query: any = { studio: studioId };
    if (type) {
      query.fieldType = type;
    }

    const [items, total] = await Promise.all([
      this.customFormModel.find(query).skip(skip).limit(limit).exec(),
      this.customFormModel.countDocuments(query),
    ]);

    return {
      items,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async searchCustomForms(
    studioIdString: string,
    searchTerm?: string,
    type?: string,
    page: number = 1,
    limit: number = 10,
  ) {
    const skip = (page - 1) * limit;
    const studioId = Types.ObjectId.createFromHexString(studioIdString);
    // Build search query
    const query: any = { studio: studioId };

    // Only add search condition if searchTerm exists and is not empty
    if (searchTerm?.trim()) {
      query.fieldName = { $regex: searchTerm.trim(), $options: 'i' };
    }

    if (type) {
      query.fieldType = type;
    }

    const [items, total] = await Promise.all([
      this.customFormModel
        .find(query)
        .sort({ _id: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.customFormModel.countDocuments(query),
    ]);

    return {
      items,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    };
  }
}
