import { IsOptional, IsBoolean, IsString, IsDateString } from 'class-validator';
import { Types } from 'mongoose';

export class UpdateAttendanceDto {
  @IsOptional()
  classEvent?: Types.ObjectId;

  @IsOptional()
  student?: Types.ObjectId;

  @IsOptional()
  @IsDateString()
  date?: Date;

  @IsOptional()
  @IsBoolean()
  isPresent?: boolean;

  @IsOptional()
  @IsString()
  statusDetail?: string;
}
