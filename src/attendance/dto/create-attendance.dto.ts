import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsBoolean,
  IsDateString,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

export class CreateAttendanceDto {
  @IsNotEmpty()
  @IsString()
  classId: string;

  @IsNotEmpty()
  @IsString()
  student: string;

  @IsNotEmpty()
  @IsDateString()
  date: Date;

  @IsNotEmpty()
  @ValidateNested()
  @IsString()
  attendanceId: string;

  @IsOptional()
  markByParent: boolean = false;

  @IsString()
  @IsNotEmpty()
  status: 'present' | 'absent';
}

export class CreateAttendanceDTO {
  @IsBoolean()
  @IsNotEmpty()
  isPresent: boolean;

  @IsString()
  @IsNotEmpty()
  statusDetail?: string;

  @IsBoolean()
  @IsNotEmpty()
  markByParent?: boolean;
}

export class GenerateAttendancePdfDto {
  @IsNotEmpty()
  @IsString()
  classId: string;

  @IsOptional()
  @IsBoolean()
  showBalance?: boolean = false; // Default to false to maintain existing behavior
}
