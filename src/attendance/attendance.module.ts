import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AttendanceService } from './attendance.service';
import { AttendanceController } from './attendance.controller';
import { Attendance, AttendanceSchema } from 'src/database/schema/attendance'; // Adjust the import according to your file structure
import { Student, StudentSchema } from 'src/database/schema/student';
import { Parent, ParentSchema } from 'src/database/schema/parent';
import { Enrollment, EnrollmentSchema } from 'src/database/schema/enrollment';
import { Event, EventSchema } from 'src/database/schema/event';
import {
  SubscriptionInvoice,
  SubscriptionInvoiceSchema,
} from 'src/database/schema/subscriptionInvoice';
import { AuthModule } from 'src/auth/auth.module';
import { StudentsModule } from 'src/students/students.module';
import { StudiosModule } from 'src/studios/studios.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Attendance.name, schema: AttendanceSchema },
      { name: Student.name, schema: StudentSchema },
      { name: Parent.name, schema: ParentSchema },
      { name: Enrollment.name, schema: EnrollmentSchema },
      { name: Event.name, schema: EventSchema },
      { name: SubscriptionInvoice.name, schema: SubscriptionInvoiceSchema },
    ]),
    AuthModule,
    StudentsModule,
    StudiosModule,
  ],
  controllers: [AttendanceController],
  providers: [AttendanceService],
})
export class AttendanceModule {}
