import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  Res,
} from '@nestjs/common';
import { Response } from 'express';
import { AttendanceService } from './attendance.service';
import {
  CreateAttendanceDTO,
  CreateAttendanceDto,
  GenerateAttendancePdfDto,
} from './dto/create-attendance.dto';
import { MarkAllPresentAbsentDto } from './dto/mark-all.dto';
import { UpdateAttendanceDto } from './dto/update-attendance.dto';
import { Attendance } from 'src/database/schema/attendance';
import { JwtAuthGuard } from 'src/auth/auth.guard';

@UseGuards(JwtAuthGuard)
@Controller('attendance')
export class AttendanceController {
  constructor(private readonly attendanceService: AttendanceService) {}

  @Post()
  create(
    @Body() createAttendanceDto: CreateAttendanceDto,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return this.attendanceService.createOrUpdate(
      locationId,
      createAttendanceDto,
    );
  }

  @Get()
  findAll() {
    return this.attendanceService.findAll();
  }

  @Post('mark-all-present')
  markAllPresent(
    @Body() createAttendanceDto: MarkAllPresentAbsentDto,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return this.attendanceService.markAllPresent(
      locationId,
      createAttendanceDto,
    );
  }

  @Post('mark-all-absent')
  markAllAbsent(
    @Body() createAttendanceDto: MarkAllPresentAbsentDto,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return this.attendanceService.markAllAbsent(
      locationId,
      createAttendanceDto,
    );
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.attendanceService.findOne(id);
  }

  // @Patch(':id')
  // update(@Param('id') id: string, @Body() updateAttendanceDto: Partial<Attendance>) {
  //   return this.attendanceService.update(id, updateAttendanceDto);
  // }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.attendanceService.remove(+id);
  // }

  @Post('/create')
  createAttendence(
    @Body() createAttendanceDto: CreateAttendanceDTO,
    @Req() request: Request,
  ) {
    // const locationId = request['locationId'];
    return this.attendanceService.createAttendance(createAttendanceDto);
  }

  @Post('generate-pdf')
  async generateAttendancePdf(
    @Body() generatePdfDto: GenerateAttendancePdfDto,
    @Req() request: Request,
    @Res() response: Response,
  ) {
    try {
      const locationId = request['locationId'];
      const pdfBuffer = await this.attendanceService.generateAttendancePdf(
        locationId,
        generatePdfDto,
      );

      const today = new Date().toISOString().split('T')[0];
      const filename = `attendance-${generatePdfDto.classId}-${today}-next6sessions.pdf`;

      response.set({
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': pdfBuffer.length.toString(),
      });

      response.end(pdfBuffer);
    } catch (error) {
      response.status(400).json({
        error: error.message || 'Failed to generate attendance PDF',
      });
    }
  }
}
