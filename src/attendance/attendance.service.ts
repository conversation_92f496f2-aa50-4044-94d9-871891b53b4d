import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  Attendance,
  AttendanceSchema,
  AttendanceDocument,
} from 'src/database/schema/attendance'; // Adjust the import according to your file structure
import {
  CreateAttendanceDto,
  CreateAttendanceDTO,
  GenerateAttendancePdfDto,
} from './dto/create-attendance.dto';
import { StudentsService } from 'src/students/students.service';
import { StudiosService } from 'src/studios/studios.service';
import { MarkAllPresentAbsentDto } from './dto/mark-all.dto';
import { Student, StudentDocument } from 'src/database/schema/student';
import { Parent, ParentDocument } from 'src/database/schema/parent';
import { Enrollment, EnrollmentDocument } from 'src/database/schema/enrollment';
import { Event } from 'src/database/schema/event';
import {
  SubscriptionInvoice,
  SubscriptionInvoiceDocument,
} from 'src/database/schema/subscriptionInvoice';
import { InvoiceStatus } from 'src/stripe/type';
import { format, addDays } from 'date-fns';
import * as https from 'https';
import * as http from 'http';
import { formatTime } from 'src/utils/datetime';
const PDFDocument = require('pdfkit');

@Injectable()
export class AttendanceService {
  constructor(
    @InjectModel(Attendance.name) private attendanceModel: Model<Attendance>,
    @InjectModel(Student.name) private studentModel: Model<StudentDocument>,
    @InjectModel(Parent.name) private parentModel: Model<ParentDocument>,
    @InjectModel(Enrollment.name)
    private enrollmentModel: Model<EnrollmentDocument>,
    @InjectModel(Event.name) private eventModel: Model<Event>,
    @InjectModel(SubscriptionInvoice.name)
    private subscriptionInvoiceModel: Model<SubscriptionInvoiceDocument>,
    private readonly studentService: StudentsService,
    private readonly studiosService: StudiosService,
  ) {}

  async normalizeDate(date: string | Date): Promise<Date> {
    const d = new Date(date);
    d.setUTCHours(0, 0, 0, 0); // Set time to 00:00:00.000 UTC
    return d;
  }

  async createOrUpdate(
    locationId: string,
    createAttendanceDto: CreateAttendanceDto,
  ): Promise<any> {
    const { classId, student, date, attendanceId, status } =
      createAttendanceDto;

    // Normalize the date to ensure consistency
    const normalizedDate = await this.normalizeDate(date);
    const classObjectId = Types.ObjectId.createFromHexString(classId);
    const studioId = Types.ObjectId.createFromHexString(locationId);
    const attendanceId_object =
      Types.ObjectId.createFromHexString(attendanceId);
    const studentId = Types.ObjectId.createFromHexString(student);

    await this.studentService.createStudentAttendance(
      studentId,
      attendanceId_object,
      normalizedDate,
      classObjectId,
      status,
    );

    const students = await this.studentService.getStudentsByFilters(
      locationId,
      classId,
      date,
    );

    return {
      students,
    };
  }

  async markAllPresent(
    locationId: string,
    markAllPresentDto: MarkAllPresentAbsentDto,
  ): Promise<any> {
    const { date, classId } = markAllPresentDto;
    const normalizedDate = await this.normalizeDate(date);
    const classObjectId = Types.ObjectId.createFromHexString(classId);
    const locationObjectId = Types.ObjectId.createFromHexString(locationId);
    const attendanceObjectId = Types.ObjectId.createFromHexString(
      '6755cbcd13d44e9caf27520c',
    ); // Hardcoded attendanceId for present

    // Fetch all students enrolled in the class
    const enrolledStudents =
      await this.studentService.getStudentsByEnrollmentId(locationId, classId);

    if (!enrolledStudents.length) {
      throw new Error('No students enrolled in the specified class.');
    }

    // Prepare bulk operations to mark all as present
    const bulkPullOperations = enrolledStudents.map((student) => ({
      updateOne: {
        filter: { _id: student._id },
        update: {
          $pull: {
            attendance: {
              classId: classObjectId,
              date: normalizedDate,
            },
          },
        },
      },
    }));

    const bulkPushOperations = enrolledStudents.map((student) => ({
      updateOne: {
        filter: { _id: student._id },
        update: {
          $push: {
            attendance: {
              attendanceId: attendanceObjectId,
              classId: classObjectId,
              date: normalizedDate,
            },
          },
        },
        upsert: true, // Create the document if it doesn't exist
      },
    }));

    // Execute bulk operations
    await this.studentService.bulkUpdateAttendance(
      bulkPullOperations,
      bulkPushOperations,
    );

    // Fetch updated students to return to the client
    const updatedStudents = await this.studentService.getStudentsByFilters(
      locationId,
      classId,
      normalizedDate.toISOString().split('T')[0],
    );

    return {
      students: updatedStudents,
    };
  }

  async markAllAbsent(
    locationId: string,
    markAllPresentDto: MarkAllPresentAbsentDto,
  ): Promise<any> {
    const { date, classId } = markAllPresentDto;
    const normalizedDate = await this.normalizeDate(date);
    const classObjectId = Types.ObjectId.createFromHexString(classId);
    const locationObjectId = Types.ObjectId.createFromHexString(locationId);
    const attendanceObjectId = Types.ObjectId.createFromHexString(
      '6755cbda13d44e9caf275212',
    ); // Hardcoded attendanceId for absent

    // Fetch all students enrolled in the class
    const enrolledStudents =
      await this.studentService.getStudentsByEnrollmentId(locationId, classId);

    if (!enrolledStudents.length) {
      throw new Error('No students enrolled in the specified class.');
    }

    // Prepare bulk operations to mark all as absent
    const bulkPullOperations = enrolledStudents.map((student) => ({
      updateOne: {
        filter: { _id: student._id },
        update: {
          $pull: {
            attendance: {
              classId: classObjectId,
              date: normalizedDate,
            },
          },
        },
      },
    }));

    const bulkPushOperations = enrolledStudents.map((student) => ({
      updateOne: {
        filter: { _id: student._id },
        update: {
          $push: {
            attendance: {
              attendanceId: attendanceObjectId,
              classId: classObjectId,
              date: normalizedDate,
            },
          },
        },
        upsert: true, // Create the document if it doesn't exist
      },
    }));

    // Execute bulk operations
    await this.studentService.bulkUpdateAttendance(
      bulkPullOperations,
      bulkPushOperations,
    );

    // Fetch updated students to return to the client
    const updatedStudents = await this.studentService.getStudentsByFilters(
      locationId,
      classId,
      normalizedDate.toISOString().split('T')[0],
    );

    return {
      students: updatedStudents,
    };
  }

  async findAll(): Promise<Attendance[]> {
    return this.attendanceModel.find().exec();
  }

  async findOne(id: string): Promise<Attendance> {
    const attendance = await this.attendanceModel.findById(id).exec();
    if (!attendance) {
      throw new NotFoundException(`Attendance record not found for ID: ${id}`);
    }
    return attendance;
  }

  // async update(id: string, data: Partial<Attendance>): Promise<Attendance | null> {
  //   const updateAttendanceData = {
  //     ...data,
  //     classEvent: new Types.ObjectId(data.classEvent),
  //     student: new Types.ObjectId(data.student)
  //   }
  //   return this.attendanceModel.findByIdAndUpdate(id, updateAttendanceData, { new: true }).exec();
  // }

  // async remove(id: string): Promise<Attendance> {
  //   const attendance = await this.attendanceModel.findByIdAndRemove(id).exec();
  //   if (!attendance) {
  //     throw new NotFoundException(`Attendance record not found for ID: ${id}`);
  //   }
  //   return attendance;
  // }

  async createAttendance(data: CreateAttendanceDTO): Promise<any> {
    const saveAttendance = new this.attendanceModel(data);
    return saveAttendance.save();
  }

  async generateAttendancePdf(
    locationId: string,
    generatePdfDto: GenerateAttendancePdfDto,
  ): Promise<Buffer> {
    const { classId, showBalance = true } = generatePdfDto;

    // Validate classId format
    if (!Types.ObjectId.isValid(classId)) {
      throw new BadRequestException('Invalid class ID format');
    }

    try {
      // Get class details - first try enrollment, then events
      let classDetails: any = await this.enrollmentModel
        .findById(classId)
        .lean()
        .exec();

      let isEvent = false;

      // If not found in enrollments, search in events
      if (!classDetails) {
        classDetails = await this.eventModel.findById(classId).lean().exec();

        if (classDetails) {
          isEvent = true;
        } else {
          throw new NotFoundException('Class or Event not found');
        }
      }

      // Get studio details with logo
      const studioData =
        await this.studiosService.findByLocationIdWithImage(locationId);

      // Parse class schedule dates
      const classStartDate = new Date(classDetails.startDate);
      const classEndDate = new Date(classDetails.endDate);
      const classDays = classDetails.days || []; // Array of day names like ["Mon", "Tue", "Wed", "Thu", "Fri"]

      // Handle case where no class days are specified
      if (!classDays.length) {
        console.log(
          'No class days specified for this enrollment. Generating PDF with message.',
        );
      }

      // Generate the next 10 class sessions instead of next 10 days
      const searchStartDate = new Date();
      searchStartDate.setHours(0, 0, 0, 0); // Start of today

      // Get all students enrolled in the class or event
      const enrolledStudents = isEvent
        ? await this.studentService.getStudentsByEventId(locationId, classId)
        : await this.studentService.getStudentsByEnrollmentId(
            locationId,
            classId,
          );

      if (!enrolledStudents.length) {
        throw new Error(
          `No students enrolled in the specified ${isEvent ? 'event' : 'class'}.`,
        );
      }

      // Get attendance data for the next 10 class sessions
      const attendanceData = await this.getAttendanceDataForPdf(
        enrolledStudents,
        classId,
        searchStartDate,
        classStartDate,
        classEndDate,
        classDays,
      );

      // Generate PDF
      return this.createAttendancePdf(
        classDetails,
        attendanceData,
        studioData,
        showBalance,
      );
    } catch (error) {
      console.error('Error generating attendance PDF:', error);
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new Error('Failed to generate attendance PDF. Please try again.');
    }
  }

  /**
   * Generate the next 10 class sessions based on class schedule
   */
  private async fetchImage(url: string): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      const client = url.startsWith('https:') ? https : http;
      client
        .get(url, (response: any) => {
          if (response.statusCode === 200) {
            const chunks: Buffer[] = [];
            response.on('data', (chunk: Buffer) => chunks.push(chunk));
            response.on('end', () => resolve(Buffer.concat(chunks)));
          } else {
            reject(new Error(`HTTP ${response.statusCode}`));
          }
        })
        .on('error', reject);
    });
  }

  private getNext6ClassSessions(
    startDate: Date,
    classStartDate: Date,
    classEndDate: Date,
    classDays: string[],
  ): Date[] {
    const classSessions: Date[] = [];
    let currentDate = new Date(
      Math.max(startDate.getTime(), classStartDate.getTime()),
    );
    currentDate.setHours(0, 0, 0, 0);

    const maxEndDate = new Date(classEndDate);
    maxEndDate.setHours(23, 59, 59, 999);

    // If no class days specified, return empty array
    if (!classDays.length) {
      return classSessions;
    }

    // Generate up to 6 class sessions or until class end date
    while (classSessions.length < 6 && currentDate <= maxEndDate) {
      const dayName = format(currentDate, 'EEE'); // Get day abbreviation (Mon, Tue, etc.)

      if (classDays.includes(dayName)) {
        classSessions.push(new Date(currentDate));
      }

      // Move to next day
      currentDate = addDays(currentDate, 1);
    }

    return classSessions;
  }

  private async getAttendanceDataForPdf(
    students: any[],
    classId: string,
    searchStartDate: Date,
    classStartDate: Date,
    classEndDate: Date,
    classDays: string[],
  ) {
    // Get the next 6 class sessions instead of a fixed date range
    const days = this.getNext6ClassSessions(
      searchStartDate,
      classStartDate,
      classEndDate,
      classDays,
    );

    // If no valid class days found, create empty PDF with message
    if (!days.length) {
      console.log(
        'No valid class days found in the specified date range. Generating empty PDF.',
      );
    }

    // Optimize database queries using aggregation to avoid N+1 problem
    const parentIds = students.map((s) => s.parentId);

    // Get all parent details in one query
    const parents = await this.parentModel
      .find({ _id: { $in: parentIds } })
      .lean()
      .exec();

    const parentMap = new Map(parents.map((p) => [p._id.toString(), p]));

    // Get all outstanding balances in one aggregation query
    const outstandingBalances = await this.subscriptionInvoiceModel.aggregate([
      {
        $match: {
          parentId: { $in: parentIds },
          status: {
            $in: [
              InvoiceStatus.PENDING,
              InvoiceStatus.FAILED,
              InvoiceStatus.SCHEDULED,
            ],
          },
        },
      },
      {
        $group: {
          _id: '$parentId',
          totalOutstanding: { $sum: '$finalAmount' },
        },
      },
    ]);

    const balanceMap = new Map(
      outstandingBalances.map((b) => [b._id.toString(), b.totalOutstanding]),
    );

    // Sort students by firstName before processing
    students.sort((a, b) => {
      return a.firstName.localeCompare(b.firstName);
    });

    const studentsWithDetails = students.map((student) => {
      const parent = parentMap.get(student.parentId.toString());
      const totalOutstanding = balanceMap.get(student.parentId.toString()) || 0;

      // Get attendance records for this student for the specific class session dates
      const attendanceRecords =
        student.attendance?.filter((att: any) => {
          const attDate = new Date(att.date);
          attDate.setHours(0, 0, 0, 0); // Normalize to start of day for comparison

          return (
            att.classId.toString() === classId &&
            days.some((classDate) => {
              const normalizedClassDate = new Date(classDate);
              normalizedClassDate.setHours(0, 0, 0, 0);
              return attDate.getTime() === normalizedClassDate.getTime();
            })
          );
        }) || [];

      // Calculate age
      const age = student.dob
        ? Math.floor(
            (Date.now() - new Date(student.dob).getTime()) /
              (365.25 * 24 * 60 * 60 * 1000),
          )
        : null;

      return {
        studentId: student._id,
        firstName: student.firstName,
        lastName: student.lastName,
        fullName: `${student.firstName} ${student.lastName}`,
        phone: parent?.primaryPhone || 'N/A',
        dob: student.dob,
        age,
        gender: student.gender,
        balance: totalOutstanding.toFixed(2),
        parentContact: {
          name: parent ? `${parent.firstName} ${parent.lastName}` : 'N/A',
          email: parent?.email || 'N/A',
          phone: parent?.primaryPhone || 'N/A',
        },
        attendanceRecords,
        dailyAttendance: days.map((day) => {
          const dayAttendance = attendanceRecords.find((att: any) => {
            const attDate = new Date(att.date);
            return format(attDate, 'yyyy-MM-dd') === format(day, 'yyyy-MM-dd');
          });
          return {
            date: format(day, 'MM/dd'),
            fullDate: format(day, 'EEE MM/dd'), // e.g., "Mon 01/15" with space
            status: dayAttendance?.status || 'not-marked',
            isToday:
              format(day, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd'),
          };
        }),
      };
    });

    return {
      students: studentsWithDetails,
      days: days.map((day) => ({
        date: format(day, 'MM/dd'),
        fullDate: format(day, 'EEE MM/dd'), // e.g., "Mon 01/15" with space
        dayName: format(day, 'EEE'), // e.g., "Mon"
        isToday: format(day, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd'),
      })),
    };
  }

  private async createAttendancePdf(
    classDetails: any,
    attendanceData: any,
    studioData: any,
    showBalance: boolean = true,
  ): Promise<Buffer> {
    try {
      const doc = new PDFDocument({
        margin: 30, // Reduced margin for more space
        size: 'A4',
        layout: 'landscape',
        autoFirstPage: true, // Enable automatic page creation
        info: {
          Title: `${classDetails.title || 'Class'} - Attendance Sheet`,
          Author: 'Enrollio',
          Subject: 'Class Attendance',
          Keywords: 'attendance, class, students',
        },
      });

      // Store original addPage method for multi-page support
      const originalAddPage = doc.addPage.bind(doc);
      // Create a promise that resolves when the PDF stream is complete
      const pdfBufferPromise = new Promise<Buffer>((resolve, reject) => {
        const chunks: Buffer[] = [];
        doc.on('data', (chunk: Buffer) => chunks.push(chunk));
        doc.on('end', () => resolve(Buffer.concat(chunks)));
        doc.on('error', reject);
      });

      // Compact Header with logo - optimized for single page
      let currentY = 40;

      // Add studio logo if available (smaller) - fetch remote image first
      if (studioData.imageUrl) {
        try {
          const imageBuffer = await this.fetchImage(studioData.imageUrl);
          // Set max logo width to 80% of page width
          const maxLogoWidth = doc.page.width * 0.8;
          const maxLogoHeight = 50; // Reduced maximum height for the image

          // Calculate center position for the image
          const logoX = (doc.page.width - maxLogoWidth) / 2;
          doc.image(imageBuffer, logoX, currentY, {
            fit: [maxLogoWidth, maxLogoHeight],
            align: 'center',
          });
          currentY += maxLogoHeight + 25; // Increased spacing after logo
        } catch (error) {
          console.warn('Failed to add logo to PDF:', error);
          // Continue without logo
        }
      } else if (studioData.studio?.subaccountName) {
        // Show subaccountName as fallback when no logo is available
        doc.fontSize(16).fillColor('#2c3e50').font('Helvetica-Bold');
        doc.text(studioData.studio.subaccountName, 30, currentY, {
          align: 'center',
        });
        currentY += 35; // Increased spacing after studio name
      }

      // Studio name (smaller)
      if (studioData.studio?.name) {
        doc.fontSize(12).fillColor('#7f8c8d').font('Helvetica');
        doc.text(studioData.studio.name, 30, currentY, { align: 'center' });
        currentY += 15; // Reduced spacing
      }

      // Class title (with better spacing)
      doc.fontSize(18).fillColor('#2c3e50').font('Helvetica-Bold');
      doc.text(`${classDetails.title || 'Class Attendance'}`, 30, currentY, {
        align: 'center',
      });
      currentY += 25; // Increased spacing after class title

      doc.fontSize(14).fillColor('#34495e').font('Helvetica');
      doc.text('Attendance Sheet', 30, currentY, { align: 'center' });
      currentY += 30; // Increased spacing before class details

      // Class details (more compact)
      doc.fontSize(10).fillColor('#7f8c8d').font('Helvetica');
      if (classDetails.mainTeacher) {
        doc.text(`Instructor: ${classDetails.mainTeacher}`, 30, currentY, {
          align: 'center',
        });
        currentY += 12; // Reduced spacing
      }
      if (classDetails.startTime && classDetails.endTime) {
        const formattedStartTime = formatTime(classDetails.startTime);
        const formattedEndTime = formatTime(classDetails.endTime);
        doc.text(
          `Time: ${formattedStartTime} - ${formattedEndTime}`,
          30,
          currentY,
          { align: 'center' },
        );
        currentY += 12; // Reduced spacing
      }

      // Add a subtle line separator with better spacing
      currentY += 15; // Increased spacing before separator
      doc.strokeColor('#bdc3c7').lineWidth(1);
      doc
        .moveTo(80, currentY)
        .lineTo(doc.page.width - 80, currentY)
        .stroke();
      currentY += 25; // Increased spacing after separator before table

      // Set the Y position for the table
      doc.y = currentY;

      // Calculate available space for multi-page layout
      const tableTop = doc.y;
      const availableHeight = doc.page.height - tableTop - 80; // Reserve space for footer
      const studentCount = attendanceData.students.length;
      const headerHeight = 27; // Standard header height
      const rowHeight = 22; // Fixed row height for consistency across pages

      // Calculate students per page
      const studentsPerPage = Math.floor(
        (availableHeight - headerHeight) / rowHeight,
      );
      const totalPages =
        studentCount > 0 ? Math.ceil(studentCount / studentsPerPage) : 1;

      // Calculate dynamic column widths with better proportions
      const hasDates = attendanceData.days && attendanceData.days.length > 0;
      const pageWidth = doc.page.width - 80; // Account for margins

      // Calculate fixed columns width based on showBalance
      const baseFixedWidth = 410; // name(125) + phone(115) + age(55) + gender(40) + dob(75) = 410
      const fixedColumnsWidth = showBalance
        ? baseFixedWidth + 60
        : baseFixedWidth;
      const availableForDates = pageWidth - fixedColumnsWidth;

      let dayColumnWidth = 45; // default
      if (hasDates) {
        const minDayWidth = 40; // Reduced minimum to fit more columns
        const maxDayWidth = 70; // Reduced maximum to prevent overflow
        const calculatedWidth = availableForDates / attendanceData.days.length;
        dayColumnWidth = Math.max(
          minDayWidth,
          Math.min(maxDayWidth, calculatedWidth),
        );
      } else {
        // When no dates, calculate width for 6 blank columns
        const minDayWidth = 35; // Reduced minimum for blank columns
        const maxDayWidth = 65; // Reduced maximum to prevent overflow
        const calculatedWidth = availableForDates / 6; // 6 blank columns
        dayColumnWidth = Math.max(
          minDayWidth,
          Math.min(maxDayWidth, calculatedWidth),
        );
      }

      // Safety check: ensure total table width doesn't exceed page width
      const dateColumnsCount = hasDates ? attendanceData.days.length : 6;
      const totalTableWidth =
        fixedColumnsWidth + dateColumnsCount * dayColumnWidth;
      const maxAllowedWidth = pageWidth - 20; // Leave some buffer

      if (totalTableWidth > maxAllowedWidth) {
        // Recalculate day column width to fit within page
        const availableForDays = maxAllowedWidth - fixedColumnsWidth;
        dayColumnWidth = Math.max(30, availableForDays / dateColumnsCount); // Minimum 30px per day column
      }

      // Redistribute balance column width when hidden
      const balanceWidth = showBalance ? 60 : 0;
      const extraWidthPerColumn = showBalance ? 0 : 15; // Distribute 60px across 4 columns (15px each)

      const colWidths = {
        name: 110 + extraWidthPerColumn,
        phone: 100 + extraWidthPerColumn,
        balance: balanceWidth,
        age: 40 + extraWidthPerColumn,
        gender: 40,
        dob: 60 + extraWidthPerColumn,
        day: dayColumnWidth,
      };

      // Add table header for first page
      this.addTableHeader(doc, attendanceData, colWidths, showBalance);
      let tableCurrentY = doc.y;

      // Enhanced student rows with alternating colors - multi-page support
      let currentPage = 1;
      let studentsProcessed = 0;

      // Process students in pages
      for (let page = 0; page < totalPages; page++) {
        const startIndex = page * studentsPerPage;
        const endIndex = Math.min(startIndex + studentsPerPage, studentCount);
        const studentsOnThisPage = attendanceData.students.slice(
          startIndex,
          endIndex,
        );

        // Skip empty pages
        if (studentsOnThisPage.length === 0) {
          continue;
        }

        if (page > 0) {
          // Add new page for subsequent pages
          originalAddPage();

          // Re-add header for new page (with logo support)
          await this.addPageHeaderAsync(
            doc,
            classDetails,
            attendanceData,
            studioData,
            page + 1,
            totalPages,
          );

          // Reset table position for new page
          tableCurrentY = doc.y;
        }

        let rowIndex = 0;
        studentsOnThisPage.forEach((student: any) => {
          let currentX = 80; // Move table more to the right

          // Alternating row background
          const isEvenRow = rowIndex % 2 === 0;
          const rowBgColor = isEvenRow ? '#ffffff' : '#f8f9fa';

          // Helper function to draw data cell
          const drawDataCell = (
            text: string,
            width: number,
            align: 'left' | 'center' = 'left',
            customPadding?: number,
          ) => {
            // Cell background
            doc.fillColor(rowBgColor);
            doc.rect(currentX, tableCurrentY, width, rowHeight).fill();

            // Cell border
            doc.strokeColor('#dee2e6').lineWidth(0.5);
            doc.rect(currentX, tableCurrentY, width, rowHeight).stroke();

            // Cell text (smaller font)
            doc.fillColor('#2c3e50').font('Helvetica').fontSize(8);
            const textY = tableCurrentY + (rowHeight - 8) / 2;
            const padding = customPadding || 4; // Use custom padding or default
            doc.text(text, currentX + padding, textY, {
              width: width - padding * 2,
              align: align,
            });
            currentX += width;
          };

          // Student name (adjusted truncation for smaller column)
          const truncatedName =
            student.fullName.length > 18
              ? student.fullName.substring(0, 15) + '...'
              : student.fullName;
          drawDataCell(truncatedName, colWidths.name, 'left', 4);

          // Phone number
          drawDataCell(student.phone, colWidths.phone, 'center');

          // Balance (conditional)
          if (showBalance) {
            drawDataCell(`$${student.balance}`, colWidths.balance, 'center');
          }

          // Age
          drawDataCell(
            student.age?.toString() || 'N/A',
            colWidths.age,
            'center',
          );

          // Gender
          drawDataCell(student.gender || 'N/A', colWidths.gender, 'center');

          // DOB
          const dobText = student.dob
            ? format(new Date(student.dob), 'MM/dd/yy')
            : 'N/A';
          drawDataCell(dobText, colWidths.dob, 'center');

          // Enhanced daily attendance
          if (student.dailyAttendance && student.dailyAttendance.length > 0) {
            student.dailyAttendance.forEach((day: any) => {
              // Cell background - highlight today or use alternating colors
              let cellBgColor = rowBgColor;
              if (day.isToday) {
                cellBgColor = '#e3f2fd'; // Light blue for today
              }

              doc.fillColor(cellBgColor);
              doc
                .rect(currentX, tableCurrentY, colWidths.day, rowHeight)
                .fill();

              // Cell border
              doc.strokeColor('#dee2e6').lineWidth(0.5);
              doc
                .rect(currentX, tableCurrentY, colWidths.day, rowHeight)
                .stroke();

              // Attendance status with better styling
              let statusText = '';
              let statusColor = '#2c3e50';

              if (day.status === 'present') {
                statusText = '✓';
                statusColor = '#27ae60'; // Green
              } else if (day.status === 'absent') {
                statusText = '✗';
                statusColor = '#e74c3c'; // Red
              } else {
                statusText = ''; // Empty for manual marking
                statusColor = '#95a5a6'; // Gray
              }

              doc.fillColor(statusColor).font('Helvetica-Bold').fontSize(10); // Smaller symbols
              const textY = tableCurrentY + (rowHeight - 10) / 2;
              doc.text(statusText, currentX, textY, {
                width: colWidths.day,
                align: 'center',
              });

              currentX += colWidths.day;
            });
          } else {
            // Show 6 blank columns with better styling
            for (let i = 0; i < 6; i++) {
              // Cell background
              doc.fillColor(rowBgColor);
              doc
                .rect(currentX, tableCurrentY, colWidths.day, rowHeight)
                .fill();

              // Cell border
              doc.strokeColor('#dee2e6').lineWidth(0.5);
              doc
                .rect(currentX, tableCurrentY, colWidths.day, rowHeight)
                .stroke();

              currentX += colWidths.day;
            }
          }

          tableCurrentY += rowHeight;
          rowIndex++;
          studentsProcessed++;
        });
      }

      // End the document
      doc.end();

      // Return the promise that resolves when PDF is complete
      return pdfBufferPromise;
    } catch (error) {
      throw error;
    }
  }

  private addTableHeader(
    doc: any,
    attendanceData: any,
    colWidths: any,
    showBalance: boolean = true,
  ): void {
    const hasDates = attendanceData.days && attendanceData.days.length > 0;

    let currentX = 80; // Move table more to the right
    const headerHeight = 27; // Standard header height
    let tableCurrentY = doc.y;

    // Calculate actual table width based on column widths
    const fixedColumnsWidth =
      colWidths.name +
      colWidths.phone +
      (showBalance ? colWidths.balance : 0) +
      colWidths.age +
      colWidths.gender +
      colWidths.dob;
    const dateColumnsCount = hasDates ? attendanceData.days.length : 6;
    const actualTableWidth =
      fixedColumnsWidth + dateColumnsCount * colWidths.day;

    // Header background
    doc.fillColor('#34495e');
    doc.rect(currentX, tableCurrentY, actualTableWidth, headerHeight).fill();

    // Header text styling
    doc.fillColor('#ffffff').font('Helvetica-Bold').fontSize(9);

    // Helper function for header cells
    const drawHeaderCell = (text: string, width: number) => {
      const textY = tableCurrentY + (headerHeight - 9) / 2;
      doc.text(text, currentX + 4, textY, {
        width: width - 8,
        align: 'center',
        ellipsis: true,
      });
      currentX += width;
    };

    // Draw header cells
    drawHeaderCell('Student Name', colWidths.name);
    drawHeaderCell('Phone', colWidths.phone);
    if (showBalance) {
      drawHeaderCell('Balance', colWidths.balance);
    }
    drawHeaderCell('Age', colWidths.age);
    drawHeaderCell('Gender', colWidths.gender);
    drawHeaderCell('DOB', colWidths.dob);

    // Date headers
    if (hasDates) {
      attendanceData.days.forEach((day: any) => {
        drawHeaderCell(day.fullDate, colWidths.day);
      });
    } else {
      // Show 6 blank date columns
      for (let i = 0; i < 6; i++) {
        drawHeaderCell(`Day ${i + 1}`, colWidths.day);
      }
    }

    // Set the Y position for the table
    doc.y = tableCurrentY + headerHeight;
  }

  private async addPageHeaderAsync(
    doc: any,
    classDetails: any,
    attendanceData: any,
    studioData: any,
    currentPage: number,
    totalPages: number,
  ): Promise<void> {
    let currentY = 40;

    // Add studio logo if available (same as first page)
    if (studioData.imageUrl) {
      try {
        const imageBuffer = await this.fetchImage(studioData.imageUrl);
        // Set max logo width to 80% of page width
        const maxLogoWidth = doc.page.width * 0.8;
        const maxLogoHeight = 50; // Reduced maximum height for the image

        // Calculate center position for the image
        const logoX = (doc.page.width - maxLogoWidth) / 2;
        doc.image(imageBuffer, logoX, currentY, {
          fit: [maxLogoWidth, maxLogoHeight],
          align: 'center',
        });
        currentY += maxLogoHeight + 25; // Increased spacing after logo (consistent with first page)
      } catch (error) {
        console.warn('Failed to add logo to PDF:', error);
        // Continue without logo
      }
    } else if (studioData.studio?.subaccountName) {
      // Show subaccountName as fallback when no logo is available
      doc.fontSize(16).fillColor('#2c3e50').font('Helvetica-Bold');
      doc.text(studioData.studio.subaccountName, 30, currentY, {
        align: 'center',
      });
      currentY += 35; // Increased spacing after studio name (consistent with first page)
    }

    // Studio name (smaller) - consistent with first page
    if (studioData.studio?.name) {
      doc.fontSize(12).fillColor('#7f8c8d').font('Helvetica');
      doc.text(studioData.studio.name, 30, currentY, { align: 'center' });
      currentY += 15; // Reduced spacing
    }

    // Class title (smaller) - simplified for subsequent pages
    doc.fontSize(18).fillColor('#2c3e50').font('Helvetica-Bold');
    doc.text(
      `${classDetails.title || 'Class'} - Attendance Sheet`,
      30,
      currentY,
      {
        align: 'center',
      },
    );
    currentY += 30; // Increased spacing after class title

    // Only show instructor on subsequent pages (no period, days, or page numbers)
    if (classDetails.instructor?.fieldName) {
      doc.fontSize(10).fillColor('#7f8c8d').font('Helvetica');
      doc.text(`Instructor: ${classDetails.instructor.fieldName}`, {
        align: 'center',
      });
      currentY += 25; // Increased spacing before table
    } else {
      currentY += 15; // Add some spacing even if no instructor
    }

    // Set the Y position for the table
    doc.y = currentY;
  }

  private addPageHeaderSync(
    doc: any,
    classDetails: any,
    attendanceData: any,
    studioData: any,
    currentPage: number,
    totalPages: number,
  ): void {
    let currentY = 40;

    // Studio name without logo (fallback for sync method)
    doc
      .fontSize(16)
      .fillColor('#2c3e50')
      .font('Helvetica-Bold')
      .text(studioData.subaccountName || 'Studio', { align: 'center' });
    currentY += 25;

    // Class title and details - consistent with first page
    doc
      .fontSize(18)
      .fillColor('#2c3e50')
      .font('Helvetica-Bold')
      .text(`${classDetails.title || 'Class'} - Attendance Sheet`, {
        align: 'center',
      });
    currentY += 25;

    // Class details in a more compact format - consistent with first page
    const classInfo = [];
    if (classDetails.instructor?.fieldName) {
      classInfo.push(`Instructor: ${classDetails.instructor.fieldName}`);
    }
    if (classDetails.startDate && classDetails.endDate) {
      classInfo.push(
        `Period: ${format(new Date(classDetails.startDate), 'MMM dd, yyyy')} - ${format(new Date(classDetails.endDate), 'MMM dd, yyyy')}`,
      );
    }
    if (classDetails.days && classDetails.days.length > 0) {
      classInfo.push(`Days: ${classDetails.days.join(', ')}`);
    }

    if (classInfo.length > 0) {
      doc.fontSize(10).fillColor('#7f8c8d').font('Helvetica');
      classInfo.forEach((info) => {
        doc.text(info, { align: 'center' });
        currentY += 12;
      });
    }

    // Page indicator for multi-page documents
    if (totalPages > 1) {
      doc.fontSize(10).fillColor('#7f8c8d').font('Helvetica');
      doc.text(`Page ${currentPage} of ${totalPages}`, { align: 'center' });
      currentY += 15;
    }

    // Set the Y position for the table
    doc.y = currentY;

    // Calculate column widths for this page
    const hasDates = attendanceData.days && attendanceData.days.length > 0;
    const pageWidth = doc.page.width - 80;
    const fixedColumnsWidth = 470; // Updated for phone column (110+100+60+40+40+60+60)
    const availableForDates = pageWidth - fixedColumnsWidth;

    let dayColumnWidth = 45;
    if (hasDates) {
      const minDayWidth = 40; // Reduced minimum to fit more columns
      const maxDayWidth = 70; // Reduced maximum to prevent overflow
      const calculatedWidth = availableForDates / attendanceData.days.length;
      dayColumnWidth = Math.max(
        minDayWidth,
        Math.min(maxDayWidth, calculatedWidth),
      );
    } else {
      const minDayWidth = 35; // Reduced minimum for blank columns
      const maxDayWidth = 65; // Reduced maximum to prevent overflow
      const calculatedWidth = availableForDates / 6;
      dayColumnWidth = Math.max(
        minDayWidth,
        Math.min(maxDayWidth, calculatedWidth),
      );
    }

    // Safety check: ensure total table width doesn't exceed page width
    const dateColumnsCount = hasDates ? attendanceData.days.length : 6;
    const totalTableWidth =
      fixedColumnsWidth + dateColumnsCount * dayColumnWidth;
    const maxAllowedWidth = pageWidth - 20; // Leave some buffer

    if (totalTableWidth > maxAllowedWidth) {
      // Recalculate day column width to fit within page
      const availableForDays = maxAllowedWidth - fixedColumnsWidth;
      dayColumnWidth = Math.max(30, availableForDays / dateColumnsCount); // Minimum 30px per day column
    }

    const colWidths = {
      name: 110, // Slightly reduced to make room for larger phone column
      phone: 100, // Increased to accommodate full phone numbers
      balance: 60,
      age: 40,
      gender: 40,
      dob: 60,
      day: dayColumnWidth,
    };

    // Add table header
    this.addTableHeader(doc, attendanceData, colWidths, true); // Default showBalance to true
  }
}
