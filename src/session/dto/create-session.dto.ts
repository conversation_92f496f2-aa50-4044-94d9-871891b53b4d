import {
  IsString,
  IsDate,
  IsBoolean,
  IsN<PERSON>ber,
  Min,
  IsOptional,
} from 'class-validator';
import { Type } from 'class-transformer';

export class CreateSessionDto {
  @IsString()
  name: string;

  @Type(() => Date)
  @IsDate()
  startDate: Date;

  @Type(() => Date)
  @IsDate()
  endDate: Date;

  @IsBoolean()
  isRegistrationFee: boolean;

  @IsNumber()
  @IsOptional()
  registrationFeeAmount: number;

  @IsBoolean()
  @IsOptional()
  isArchive: boolean;

  @IsBoolean()
  @IsOptional()
  isDefault: boolean;

  @IsBoolean()
  @IsOptional()
  isClassDefault: boolean;

  @IsBoolean()
  @IsOptional()
  isEventDefault: boolean;
}
