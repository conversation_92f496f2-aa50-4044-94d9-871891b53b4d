import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { CreateSessionDto } from './dto/create-session.dto';
import { UpdateSessionDto } from './dto/update-session.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Session } from 'src/database/schema/session';
import { Enrollment } from 'src/database/schema/enrollment';
import { Parent } from 'src/database/schema/parent';
import { Event } from 'src/database/schema/event';

@Injectable()
export class SessionService {
  constructor(
    @InjectModel(Session.name)
    private readonly sessionModel: Model<Session>,
    @InjectModel(Enrollment.name)
    private readonly enrollmentModel: Model<Enrollment>,
    @InjectModel(Event.name)
    private readonly eventModel: Model<Event>,
    @InjectModel(Parent.name)
    private readonly parentModel: Model<Parent>,
  ) {}

  async create(createSessionDto: CreateSessionDto, locationId: string) {
    const studioIdObj = Types.ObjectId.createFromHexString(locationId);

    // Handle class default
    if (createSessionDto.isClassDefault) {
      await this.sessionModel.updateMany(
        {
          studioId: studioIdObj,
          isClassDefault: true,
        },
        { $set: { isClassDefault: false } },
      );
    }

    // Handle event default
    if (createSessionDto.isEventDefault) {
      await this.sessionModel.updateMany(
        {
          studioId: studioIdObj,
          isEventDefault: true,
        },
        { $set: { isEventDefault: false } },
      );
    }

    const session = new this.sessionModel({
      ...createSessionDto,
      studioId: studioIdObj,
    });
    return await session.save();
  }

  async findAll(
    locationId: string,
    {
      page,
      limit,
      isDefault,
      isArchive,
    }: {
      page: number;
      limit: number;
      isDefault?: boolean;
      isArchive?: boolean;
    },
  ) {
    const skip = (page - 1) * limit;

    // Build query based on parameters
    const query: any = {
      studioId: Types.ObjectId.createFromHexString(locationId),
    };

    // Add isDefault to query if it's provided
    if (typeof isDefault === 'boolean') {
      query.isDefault = isDefault;
    }

    // Add isArchive to query if it's provided
    if (typeof isArchive === 'boolean') {
      query.isArchive = isArchive;
    } else {
      // Default behavior: show non-archived sessions
      query.isArchive = false;
    }

    const [sessions, total] = await Promise.all([
      this.sessionModel
        .find(query)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 }),
      this.sessionModel.countDocuments(query),
    ]);

    return {
      sessions,
      pagination: {
        total,
        page: Number(page),
        limit: Number(limit),
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string, locationId: string) {
    const session = await this.sessionModel.findOne({
      _id: id,
      studioId: Types.ObjectId.createFromHexString(locationId),
      isArchive: false,
    });

    if (!session) {
      throw new NotFoundException(`Session with ID ${id} not found`);
    }

    return session;
  }

  async update(
    id: string,
    updateSessionDto: UpdateSessionDto,
    locationId: string,
  ) {
    const studioIdObj = Types.ObjectId.createFromHexString(locationId);

    // Check if trying to archive a default session
    if (updateSessionDto.isArchive) {
      const existingSession = await this.sessionModel.findOne({
        _id: id,
        studioId: studioIdObj,
      });

      if (existingSession) {
        if (
          existingSession.isDefault ||
          existingSession.isClassDefault ||
          existingSession.isEventDefault
        ) {
          throw new BadRequestException(
            'Cannot archive a default session. Please remove default status first.',
          );
        }
      }
    }

    // Handle class default
    if (updateSessionDto.isClassDefault) {
      await this.sessionModel.updateMany(
        {
          studioId: studioIdObj,
          _id: { $ne: id },
          isClassDefault: true,
        },
        { $set: { isClassDefault: false } },
      );
    }

    // Handle event default
    if (updateSessionDto.isEventDefault) {
      await this.sessionModel.updateMany(
        {
          studioId: studioIdObj,
          _id: { $ne: id },
          isEventDefault: true,
        },
        { $set: { isEventDefault: false } },
      );
    }

    const session = await this.sessionModel.findOneAndUpdate(
      { _id: id, studioId: studioIdObj },
      { $set: updateSessionDto },
      { new: true },
    );

    if (!session) {
      throw new NotFoundException(`Session with ID ${id} not found`);
    }

    return session;
  }

  async remove(id: string, locationId: string) {
    const session = await this.sessionModel.findOneAndDelete({
      _id: id,
      studioId: Types.ObjectId.createFromHexString(locationId),
    });

    if (!session) {
      throw new NotFoundException(`Session with ID ${id} not found`);
    }

    return session;
  }

  async searchByName(
    locationId: string,
    name?: string,
    {
      page = 1,
      limit = 10,
      isArchive,
      isClassDefault,
      isEventDefault,
    }: {
      page?: number;
      limit?: number;
      isArchive?: boolean;
      isClassDefault?: boolean;
      isEventDefault?: boolean;
    } = {},
  ) {
    const skip = (page - 1) * limit;

    const query: any = {
      studioId: Types.ObjectId.createFromHexString(locationId),
    };

    if (name && name.trim()) {
      query.name = { $regex: name, $options: 'i' };
    }

    // Add isArchive filter
    if (typeof isArchive === 'boolean') {
      query.isArchive = isArchive;
    } else {
      query.isArchive = false;
    }

    // Add isClassDefault filter
    if (typeof isClassDefault === 'boolean') {
      query.isClassDefault = isClassDefault;
    }

    // Add isEventDefault filter
    if (typeof isEventDefault === 'boolean') {
      query.isEventDefault = isEventDefault;
    }

    const [sessions, total] = await Promise.all([
      this.sessionModel
        .find(query)
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 }),
      this.sessionModel.countDocuments(query),
    ]);

    return {
      sessions,
      pagination: {
        total,
        page: Number(page),
        limit: Number(limit),
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async updateAllisArchive() {
    // First unset all defaults
    await this.sessionModel.updateMany(
      {},
      {
        $set: {
          isArchive: false,
          isDefault: false,
          isClassDefault: false,
        },
      },
    );
  }

  async findCountById(sessionId: string) {
    const enrollments = await this.enrollmentModel.find({
      session: Types.ObjectId.createFromHexString(sessionId),
    });
    const events = await this.eventModel.find({
      session: Types.ObjectId.createFromHexString(sessionId),
    });
    return {
      classes: enrollments.length,
      events: events.length,
    };
  }

  async isSessionFeePaid(sessionId: string, parentId: string) {
    const session = await this.sessionModel.findById(sessionId);
    if (!session) {
      throw new NotFoundException('Session not found');
    }

    const parent = await this.parentModel.findById(parentId);
    if (!parent) {
      throw new NotFoundException('Parent not found');
    }

    const isSessionFeePaid = parent.isSessionFeePaid?.[sessionId] ?? false;

    let sessionFee = 0;
    //check if the session is registration fee
    if (session.isRegistrationFee) {
      sessionFee = session.registrationFeeAmount;
    }

    return {
      isSessionFeePaid,
      sessionFee,
    };
  }
}
