import { Module } from '@nestjs/common';
import { SessionService } from './session.service';
import { SessionController } from './session.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Session, SessionSchema } from 'src/database/schema/session';
import { JwtModule } from '@nestjs/jwt';
import { Enrollment, EnrollmentSchema } from 'src/database/schema/enrollment';
import { Event, EventSchema } from 'src/database/schema/event';
import { Parent, ParentSchema } from 'src/database/schema/parent';
@Module({
  imports: [
    MongooseModule.forFeature([{ name: Session.name, schema: SessionSchema }]),
    MongooseModule.forFeature([
      { name: Enrollment.name, schema: EnrollmentSchema },
    ]),
    MongooseModule.forFeature([{ name: Event.name, schema: EventSchema }]),
    MongooseModule.forFeature([{ name: Parent.name, schema: ParentSchema }]),
    JwtModule,
  ],
  controllers: [SessionController],
  providers: [SessionService],
  exports: [SessionService],
})
export class SessionModule {}

//isclassDedault or isEventDefault
