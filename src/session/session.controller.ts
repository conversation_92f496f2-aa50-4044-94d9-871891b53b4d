import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
  ParseBoolPipe,
  Put,
} from '@nestjs/common';
import { SessionService } from './session.service';
import { CreateSessionDto } from './dto/create-session.dto';
import { UpdateSessionDto } from './dto/update-session.dto';
import { JwtAuthGuard } from 'src/auth/auth.guard';
// import { Request } from 'express';

export class QuerySessionDto {
  page?: number;
  limit?: number;
  name?: string;
}

@UseGuards(JwtAuthGuard)
@Controller('session')
export class SessionController {
  constructor(private readonly sessionService: SessionService) {}

  @Post()
  create(@Body() createSessionDto: CreateSessionDto, @Req() request: Request) {
    const locationId = request['locationId'];
    return this.sessionService.create(createSessionDto, locationId);
  }

  @Get()
  findAll(
    @Req() request: Request,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('isDefault', new ParseBoolPipe({ optional: true }))
    isDefault?: boolean,
    @Query('isArchive', new ParseBoolPipe({ optional: true }))
    isArchive?: boolean,
  ) {
    const locationId = request['locationId'];
    return this.sessionService.findAll(locationId, {
      page,
      limit,
      isDefault,
      isArchive,
    });
  }

  @Get('search')
  searchByName(
    @Req() request: Request,
    @Query('name') name?: string,
    @Query('isArchive', new ParseBoolPipe({ optional: true }))
    isArchive?: boolean,
    @Query('isClassDefault', new ParseBoolPipe({ optional: true }))
    isClassDefault?: boolean,
    @Query('isEventDefault', new ParseBoolPipe({ optional: true }))
    isEventDefault?: boolean,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    const locationId = request['locationId'];
    return this.sessionService.searchByName(locationId, name, {
      page,
      limit,
      isArchive,
      isClassDefault,
      isEventDefault,
    });
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Req() request: Request) {
    const locationId = request['locationId'];
    return this.sessionService.findOne(id, locationId);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateSessionDto: UpdateSessionDto,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return this.sessionService.update(id, updateSessionDto, locationId);
  }

  @Delete(':id')
  remove(@Param('id') id: string, @Req() request: Request) {
    const locationId = request['locationId'];
    return this.sessionService.remove(id, locationId);
  }

  @Put('updateAllisArchive')
  updateAllisArchive() {
    return this.sessionService.updateAllisArchive();
  }

  @Get('count/:id')
  findCountById(@Param('id') id: string) {
    return this.sessionService.findCountById(id);
  }

  @Get('/:sessionId/session-fee-status')
  async isSessionFeePaid(
    @Param('sessionId') sessionId: string,
    @Query('parentId') parentId: string,
  ) {
    return this.sessionService.isSessionFeePaid(sessionId, parentId);
  }
}
