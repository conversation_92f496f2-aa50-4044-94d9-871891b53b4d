import {
  IsBoolean,
  IsNotEmpty,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';

export class CreateTriggerDto {
  @IsString()
  @IsNotEmpty()
  key: string;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  targetUrl: string;

  @IsString()
  @IsOptional()
  locationId?: string;

  @IsString()
  @IsOptional()
  workflowId?: string;

  @IsObject()
  @IsOptional()
  filters?: Record<string, any>;

  @IsString()
  @IsOptional()
  companyId?: string;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}

export class GhlTriggerWebhookDto {
  @IsObject()
  @IsNotEmpty()
  triggerData: {
    id: string;
    key: string;
    filters?: any[];
    eventType: 'CREATED' | 'UPDATED' | 'DELETED';
    targetUrl: string;
  };

  @IsObject()
  @IsNotEmpty()
  meta: {
    key: string;
    version: string;
  };

  @IsObject()
  @IsNotEmpty()
  extras: {
    locationId: string;
    workflowId: string;
    companyId: string;
  };
}
