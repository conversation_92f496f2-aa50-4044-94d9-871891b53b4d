import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema({ timestamps: true })
export class Trigger extends Document {
  @Prop({ required: true })
  key: string;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  targetUrl: string;

  @Prop()
  locationId: string;

  @Prop()
  workflowId: string;

  @Prop({ type: Object })
  filters: Record<string, any>;

  @Prop()
  companyId: string;

  @Prop({ default: true })
  isActive: boolean;
}

export const TriggerSchema = SchemaFactory.createForClass(Trigger);
