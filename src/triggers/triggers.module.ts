import { Modu<PERSON> } from '@nestjs/common';
import { TriggersService } from './triggers.service';
import { TriggersController } from './triggers.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Trigger, TriggerSchema } from './entities/trigger.entity';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { Studio, StudioSchema } from '../database/schema/studio';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Trigger.name, schema: TriggerSchema },
      { name: Studio.name, schema: StudioSchema },
    ]),
    HttpModule,
    ConfigModule,
  ],
  controllers: [TriggersController],
  providers: [TriggersService],
  exports: [TriggersService],
})
export class TriggersModule {}
