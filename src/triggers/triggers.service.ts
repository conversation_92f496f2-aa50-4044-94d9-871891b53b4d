import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import {
  CreateTriggerDto,
  GhlTriggerWebhookDto,
} from './dto/create-trigger.dto';
import { UpdateTriggerDto } from './dto/update-trigger.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Trigger } from './entities/trigger.entity';
import { SendToGhlDto } from './dto/send-to-ghl.dto';
import axios from 'axios';
import { Studio } from '../database/schema/studio';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';

@Injectable()
export class TriggersService {
  private readonly logger = new Logger(TriggersService.name);

  constructor(
    @InjectModel(Trigger.name)
    private triggerModel: Model<Trigger>,
    @InjectModel(Studio.name)
    private studioModel: Model<Studio>,
  ) {}

  async create(createTriggerDto: CreateTriggerDto) {
    try {
      const newTrigger = new this.triggerModel(createTriggerDto);
      return await newTrigger.save();
    } catch (error) {
      this.logger.error(
        `Failed to create trigger: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to create trigger',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async handleGhlWebhook(webhookData: GhlTriggerWebhookDto) {
    try {
      const { triggerData, extras } = webhookData;
      const { eventType, key, targetUrl, id, filters } = triggerData;
      const { locationId, workflowId, companyId } = extras;

      // Handle different event types
      switch (eventType) {
        case 'CREATED':
        case 'UPDATED':
          // Create or update trigger subscription
          const existingTrigger = await this.triggerModel
            .findOne({
              key,
              workflowId,
            })
            .exec();

          if (existingTrigger) {
            // Update existing trigger
            await this.triggerModel.updateOne(
              { _id: existingTrigger._id },
              {
                targetUrl,
                filters,
                locationId,
                workflowId,
                companyId,
                isActive: true,
              },
            );
            return { success: true, message: 'Trigger updated successfully' };
          } else {
            // Create new trigger
            const newTrigger = new this.triggerModel({
              key,
              name: key, // Using key as name for now
              targetUrl,
              filters,
              locationId,
              workflowId,
              companyId,
              isActive: true,
            });
            await newTrigger.save();
            return { success: true, message: 'Trigger created successfully' };
          }

        case 'DELETED':
          // Deactivate trigger rather than deleting it
          const trigger = await this.triggerModel
            .findOne({
              key,
              workflowId,
            })
            .exec();

          if (trigger) {
            await this.triggerModel.updateOne(
              { _id: trigger._id },
              { isActive: false },
            );
            return {
              success: true,
              message: 'Trigger deactivated successfully',
            };
          }
          return { success: true, message: 'Trigger not found' };

        default:
          throw new HttpException(
            `Unknown event type: ${eventType}`,
            HttpStatus.BAD_REQUEST,
          );
      }
    } catch (error) {
      this.logger.error(
        `Failed to handle GHL webhook: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to handle GHL webhook',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async sendToGhl(sendToGhlDto: SendToGhlDto) {
    try {
      const { triggerKey, data, locationId, companyId } = sendToGhlDto;

      this.logger.log('Received trigger request with data:', data);

      // Try to find the studio if locationId is provided
      let studio = null;
      if (locationId) {
        studio = await this.studioModel
          .findOne({ locationId: locationId })
          .lean()
          .exec();
      }

      // Find all active triggers with matching key
      // If locationId or companyId is provided, filter by those too
      const filter: any = { key: triggerKey, isActive: true };

      if (locationId) {
        filter.locationId = locationId;
      }

      if (companyId) {
        filter.companyId = companyId;
      }

      const triggers = await this.triggerModel.find(filter).lean().exec();

      if (!triggers || triggers.length === 0) {
        throw new HttpException(
          'No active triggers found with the given key',
          HttpStatus.NOT_FOUND,
        );
      }

      // Create an array of promises for each trigger
      const triggerPromises = triggers.map(async (trigger) => {
        return axios
          .post(
            trigger.targetUrl,
            {
              customData: data,
              filters: trigger.filters,
            },
            {
              headers: {
                Authorization: `Bearer ${studio?.access_token || ''}`,
                Version: '2021-07-28',
                Source: 'WORKFLOW',
                'source-id': 'WORKFLOW',
                'Content-Type': 'application/x-www-form-urlencoded',
                Accept: 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                locationId: locationId || '',
              },
            },
          )
          .then((response) => ({
            triggerId: trigger._id,
            success: true,
            status: response.status,
            data: response.data,
          }))
          .catch((error) => {
            this.logger.error(
              `Failed to send data to GHL for trigger ${trigger._id}: ${error.response?.data?.message}`,
              error.stack,
            );
            return {
              triggerId: trigger._id,
              success: false,
              error: error.response?.data?.message,
              status: error.response?.status || 500,
            };
          });
      });

      // Wait for all promises to settle (either fulfill or reject)
      const results = await Promise.allSettled(triggerPromises);

      // Process the results
      const responses = results.map((result, index) => {
        if (result.status === 'fulfilled') {
          return result.value;
        } else {
          // This is a safeguard in case the promise itself throws
          return {
            triggerId: triggers[index]._id,
            success: false,
            error: result.reason?.message || 'Unknown error',
            status: 500,
          };
        }
      });

      const successfulTriggers = responses.filter((r) => r.success).length;

      return {
        success: successfulTriggers > 0,
        totalTriggers: triggers.length,
        successfulTriggers,
        failedTriggers: triggers.length - successfulTriggers,
        responses,
      };
    } catch (error) {
      this.logger.error(`Failed to send data to GHL: ${error.message}`);
      throw new HttpException(
        'Failed to send data to GHL',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async findAll() {
    return this.triggerModel.find().exec();
  }

  async findOne(id: string) {
    const trigger = await this.triggerModel.findById(id).exec();
    if (!trigger) {
      throw new HttpException('Trigger not found', HttpStatus.NOT_FOUND);
    }
    return trigger;
  }

  async update(id: string, updateTriggerDto: UpdateTriggerDto) {
    const trigger = await this.findOne(id);
    await this.triggerModel.updateOne({ _id: id }, updateTriggerDto);
    return { ...trigger.toJSON(), ...updateTriggerDto };
  }

  async remove(id: string) {
    const trigger = await this.findOne(id);
    await this.triggerModel.deleteOne({ _id: id });
    return { success: true, message: 'Trigger deleted successfully' };
  }
}
