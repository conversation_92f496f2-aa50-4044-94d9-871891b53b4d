import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { TriggersService } from './triggers.service';
import {
  CreateTriggerDto,
  GhlTriggerWebhookDto,
} from './dto/create-trigger.dto';
import { UpdateTriggerDto } from './dto/update-trigger.dto';
import { SendToGhlDto } from './dto/send-to-ghl.dto';

@Controller('triggers')
export class TriggersController {
  constructor(private readonly triggersService: TriggersService) {}

  @Post()
  create(@Body() createTriggerDto: CreateTriggerDto) {
    return this.triggersService.create(createTriggerDto);
  }

  @Post('ghl-webhook')
  @HttpCode(HttpStatus.OK)
  handleGhlWebhook(@Body() webhookData: GhlTriggerWebhookDto) {
    return this.triggersService.handleGhlWebhook(webhookData);
  }

  @Post('send-to-ghl')
  @HttpCode(HttpStatus.OK)
  sendToGhl(@Body() sendToGhlDto: SendToGhlDto) {
    return this.triggersService.sendToGhl(sendToGhlDto);
  }

  @Get()
  findAll() {
    return this.triggersService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.triggersService.findOne(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateTriggerDto: UpdateTriggerDto) {
    return this.triggersService.update(id, updateTriggerDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.triggersService.remove(id);
  }
}
