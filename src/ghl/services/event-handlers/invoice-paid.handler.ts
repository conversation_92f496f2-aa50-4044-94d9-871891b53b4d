import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { GhlEventHandler } from 'src/webhooks/interfaces/ghl-event-handler.interface';
import { GhlInvoiceWebhookDto } from 'src/webhooks/dto/ghl-webhook.dto';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import { Studio } from 'src/database/schema/studio';
import { Parent } from 'src/database/schema/parent';
import { Student } from 'src/database/schema/student';
import {
  InvoiceStatus,
  InvoiceType,
  PaymentMethod,
  PaymentProvider,
} from 'src/stripe/type';

@Injectable()
export class InvoicePaidHandler implements GhlEventHandler<GhlInvoiceWebhookDto> {
  private readonly logger = new Logger(InvoicePaidHandler.name);

  constructor(
    @InjectModel(SubscriptionInvoice.name)
    private subscriptionInvoiceModel: Model<SubscriptionInvoice>,
    @InjectModel(Studio.name)
    private studioModel: Model<Studio>,
    @InjectModel(Parent.name)
    private parentModel: Model<Parent>,
    @InjectModel(Student.name)
    private studentModel: Model<Student>,
  ) {}

  async handleEvent(eventData: GhlInvoiceWebhookDto): Promise<void> {
    this.logger.log(`Processing GHL invoicePaid event for invoice: ${eventData._id}`);

    try {
      // Find the studio by altId (locationId)
      const studio = await this.studioModel.findOne({
        locationId: eventData.altId
      }).exec();

      if (!studio) {
        this.logger.error(`Studio not found for locationId: ${eventData.altId}`);
        return;
      }

      // Find existing invoice for this GHL invoice
      const existingInvoice = await this.subscriptionInvoiceModel.findOne({
        ghlInvoiceId: eventData._id,
      }).exec();

      if (existingInvoice) {
        this.logger.log(`Updating existing invoice for GHL invoice: ${eventData._id}`);
        // Update existing invoice to paid status
        existingInvoice.status = InvoiceStatus.PAID;
        existingInvoice.paymentDate = eventData.updatedAt ? new Date(eventData.updatedAt) : new Date();

        // Update payment information
        if (!existingInvoice.payments || existingInvoice.payments.length === 0) {
          existingInvoice.payments = [{
            method: PaymentMethod.CARD,
            amount: eventData.amountPaid,
            paymentIntentId: eventData._id,
            date: eventData.updatedAt ? new Date(eventData.updatedAt) : new Date(),
          }];
        }

        // Update metadata
        existingInvoice.metadata.isPaymentApproved = true;
        existingInvoice.metadata.ghlInvoiceData = eventData;

        await existingInvoice.save();
        this.logger.log(`Successfully updated invoice to paid status: ${eventData._id}`);
        return;
      }

      // Create new paid invoice if it doesn't exist
      this.logger.log(`Creating new paid invoice for GHL invoice: ${eventData._id}`);

      const invoiceData = {
        studioId: studio._id,
        ghlInvoiceId: eventData._id,
        status: InvoiceStatus.PAID,
        paymentProvider: PaymentProvider.GHL,
        paymentMethod: PaymentMethod.CARD,
        type: InvoiceType.ONE_TIME,
        transactionId: eventData._id,
        baseAmount: eventData.total,
        finalAmount: eventData.total,
        paymentDate: eventData.updatedAt ? new Date(eventData.updatedAt) : new Date(),
        dueDate: eventData.dueDate ? new Date(eventData.dueDate) : new Date(),
        line_items: this.mapInvoiceItems(eventData.invoiceItems || []),
        payments: [{
          method: PaymentMethod.CARD,
          amount: eventData.amountPaid,
          paymentIntentId: eventData._id,
          date: eventData.updatedAt ? new Date(eventData.updatedAt) : new Date(),
        }],
        metadata: {
          isPaymentApproved: true,
          ghlInvoiceData: eventData,
        },
      };

      // Try to find parent and student based on GHL contact data
      if (eventData.contactDetails?.id) {
        const parent = await this.parentModel.findOne({
          ghlContactId: eventData.contactDetails.id,
          studioId: studio._id,
        }).exec();

        if (parent) {
          invoiceData['parentId'] = parent._id;

          const student = await this.studentModel.findOne({
            parentId: parent._id,
            studioId: studio._id,
          }).exec();

          if (student) {
            invoiceData['studentId'] = student._id;
          }
        }
      }

      const newInvoice = new this.subscriptionInvoiceModel(invoiceData);
      await newInvoice.save();

      this.logger.log(`Successfully created paid subscription invoice for GHL invoice: ${eventData._id}`);

    } catch (error) {
      this.logger.error(`Error processing GHL invoicePaid event: ${error.message}`, error.stack);
      throw error;
    }
  }

  private mapInvoiceItems(ghlItems: any[]): any[] {
    return ghlItems.map(item => ({
      name: item.name || 'GHL Item',
      amount: item.amount || 0,
      type: 'item',
      quantity: item.qty || 1,
      total: (item.amount || 0) * (item.qty || 1),
    }));
  }
}
