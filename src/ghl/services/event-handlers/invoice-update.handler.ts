import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { GhlEventHandler } from 'src/webhooks/interfaces/ghl-event-handler.interface';
import { GhlInvoiceWebhookDto, GhlInvoiceStatus } from 'src/webhooks/dto/ghl-webhook.dto';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import { Studio } from 'src/database/schema/studio';
import { Parent } from 'src/database/schema/parent';
import { Student } from 'src/database/schema/student';
import {
  InvoiceStatus,
  InvoiceType,
  PaymentMethod,
  PaymentProvider,
} from 'src/stripe/type';

@Injectable()
export class InvoiceUpdateHandler implements GhlEventHandler<GhlInvoiceWebhookDto> {
  private readonly logger = new Logger(InvoiceUpdateHandler.name);

  constructor(
    @InjectModel(SubscriptionInvoice.name)
    private subscriptionInvoiceModel: Model<SubscriptionInvoice>,
    @InjectModel(Studio.name)
    private studioModel: Model<Studio>,
    @InjectModel(Parent.name)
    private parentModel: Model<Parent>,
    @InjectModel(Student.name)
    private studentModel: Model<Student>,
  ) {}

  async handleEvent(eventData: GhlInvoiceWebhookDto): Promise<void> {
    this.logger.log(`Processing GHL invoice update event for invoice: ${eventData._id}`);

    try {
      // Find the studio by altId (locationId)
      const studio = await this.studioModel.findOne({
        locationId: eventData.altId
      }).exec();

      if (!studio) {
        this.logger.error(`Studio not found for locationId: ${eventData.altId}`);
        return;
      }

      // Find existing invoice for this GHL invoice
      const existingInvoice = await this.subscriptionInvoiceModel.findOne({
        ghlInvoiceId: eventData._id,
      }).exec();

      if (existingInvoice) {
        this.logger.log(`Updating existing invoice for GHL invoice: ${eventData._id}`);

        // Update invoice with new data
        let updated = false;

        // Update status if changed
        const newStatus = this.mapGhlStatusToInvoiceStatus(eventData.status);
        if (existingInvoice.status !== newStatus) {
          existingInvoice.status = newStatus;
          updated = true;
        }

        // Update amounts if changed
        if (existingInvoice.finalAmount !== eventData.total) {
          existingInvoice.finalAmount = eventData.total;
          existingInvoice.baseAmount = eventData.total;
          updated = true;
        }

        // Update due date if changed
        if (eventData.dueDate) {
          const newDueDate = new Date(eventData.dueDate);
          if (existingInvoice.dueDate?.getTime() !== newDueDate.getTime()) {
            existingInvoice.dueDate = newDueDate;
            updated = true;
          }
        }

        // Update line items if changed
        if (eventData.invoiceItems && eventData.invoiceItems.length > 0) {
          existingInvoice.line_items = this.mapInvoiceItems(eventData.invoiceItems);
          updated = true;
        }

        // Update payment information if invoice is now paid
        if (eventData.status === GhlInvoiceStatus.PAID && eventData.amountPaid > 0) {
          if (!existingInvoice.paymentDate) {
            existingInvoice.paymentDate = eventData.updatedAt ? new Date(eventData.updatedAt) : new Date();
            updated = true;
          }

          // Add payment record if not exists
          if (!existingInvoice.payments || existingInvoice.payments.length === 0) {
            existingInvoice.payments = [{
              method: PaymentMethod.CARD,
              amount: eventData.amountPaid,
              paymentIntentId: eventData._id,
              date: eventData.updatedAt ? new Date(eventData.updatedAt) : new Date(),
            }];
            updated = true;
          }
        }

        // Always update metadata
        existingInvoice.metadata.ghlInvoiceData = eventData;
        updated = true;

        if (updated) {
          await existingInvoice.save();
          this.logger.log(`Successfully updated invoice: ${eventData._id}`);
        }
      } else {
        // Create a new invoice if it doesn't exist (this handles cases where we receive update events before create events)
        this.logger.log(`Creating new invoice from update event for GHL invoice: ${eventData._id}`);

        const invoiceData = {
          studioId: studio._id,
          ghlInvoiceId: eventData._id,
          status: this.mapGhlStatusToInvoiceStatus(eventData.status),
          paymentProvider: PaymentProvider.GHL,
          paymentMethod: PaymentMethod.CARD,
          type: InvoiceType.ONE_TIME,
          transactionId: eventData._id,
          baseAmount: eventData.total,
          finalAmount: eventData.total,
          dueDate: eventData.dueDate ? new Date(eventData.dueDate) : new Date(),
          line_items: this.mapInvoiceItems(eventData.invoiceItems || []),
          payments: eventData.status === GhlInvoiceStatus.PAID && eventData.amountPaid > 0 ? [{
            method: PaymentMethod.CARD,
            amount: eventData.amountPaid,
            paymentIntentId: eventData._id,
            date: eventData.updatedAt ? new Date(eventData.updatedAt) : new Date(),
          }] : [],
          metadata: {
            isPaymentApproved: eventData.status === GhlInvoiceStatus.PAID,
            ghlInvoiceData: eventData,
          },
        };

        // Try to find parent and student based on GHL contact data
        if (eventData.contactDetails?.id) {
          const parent = await this.parentModel.findOne({
            ghlContactId: eventData.contactDetails.id,
            studioId: studio._id,
          }).exec();

          if (parent) {
            invoiceData['parentId'] = parent._id;

            const student = await this.studentModel.findOne({
              parentId: parent._id,
              studioId: studio._id,
            }).exec();

            if (student) {
              invoiceData['studentId'] = student._id;
            }
          }
        }

        const newInvoice = new this.subscriptionInvoiceModel(invoiceData);
        await newInvoice.save();

        this.logger.log(`Created new invoice from update event: ${eventData._id}`);
      }

    } catch (error) {
      this.logger.error(`Error processing GHL invoice update event: ${error.message}`, error.stack);
      throw error;
    }
  }

  private mapGhlStatusToInvoiceStatus(ghlStatus: GhlInvoiceStatus): InvoiceStatus {
    switch (ghlStatus) {
      case GhlInvoiceStatus.PAID:
        return InvoiceStatus.PAID;
      case GhlInvoiceStatus.PARTIALLY_PAID:
        return InvoiceStatus.PARTIALLY_PAID;
      case GhlInvoiceStatus.SENT:
        return InvoiceStatus.PENDING;
      case GhlInvoiceStatus.DRAFT:
        return InvoiceStatus.UPCOMING;
      case GhlInvoiceStatus.VOID:
        return InvoiceStatus.VOID;
      case GhlInvoiceStatus.DELETE:
        return InvoiceStatus.CANCELLED;
      default:
        return InvoiceStatus.PENDING;
    }
  }

  private mapInvoiceItems(ghlItems: any[]): any[] {
    return ghlItems.map(item => ({
      name: item.name || 'GHL Item',
      amount: item.amount || 0,
      type: 'item',
      quantity: item.qty || 1,
      total: (item.amount || 0) * (item.qty || 1),
    }));
  }
}
