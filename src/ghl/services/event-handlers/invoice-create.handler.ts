import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { GhlEventHandler } from 'src/webhooks/interfaces/ghl-event-handler.interface';
import { GhlInvoiceWebhookDto } from 'src/webhooks/dto/ghl-webhook.dto';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import { Studio } from 'src/database/schema/studio';
import { Parent } from 'src/database/schema/parent';
import { Student } from 'src/database/schema/student';
import {
  InvoiceStatus,
  InvoiceType,
  PaymentMethod,
  PaymentProvider,
} from 'src/stripe/type';

@Injectable()
export class InvoiceCreateHandler implements GhlEventHandler<GhlInvoiceWebhookDto> {
  private readonly logger = new Logger(InvoiceCreateHandler.name);

  constructor(
    @InjectModel(SubscriptionInvoice.name)
    private subscriptionInvoiceModel: Model<SubscriptionInvoice>,
    @InjectModel(Studio.name)
    private studioModel: Model<Studio>,
    @InjectModel(Parent.name)
    private parentModel: Model<Parent>,
    @InjectModel(Student.name)
    private studentModel: Model<Student>,
  ) {}

  async handleEvent(eventData: GhlInvoiceWebhookDto): Promise<void> {
    this.logger.log(`Processing GHL invoiceCreate event for invoice: ${eventData._id}`);

    try {
      // Find the studio by altId (locationId)
      const studio = await this.studioModel.findOne({
        locationId: eventData.altId
      }).exec();

      if (!studio) {
        this.logger.error(`Studio not found for locationId: ${eventData.altId}`);
        return;
      }

      // Check if invoice already exists for this GHL invoice
      const existingInvoice = await this.subscriptionInvoiceModel.findOne({
        ghlInvoiceId: eventData._id,
      }).exec();

      if (existingInvoice) {
        this.logger.log(`Invoice already exists for GHL invoice: ${eventData._id}`);
        // Update existing invoice if needed
        await this.updateExistingInvoice(existingInvoice, eventData);
        return;
      }

      // Create new subscription invoice from GHL invoice
      const invoiceData = {
        studioId: studio._id,
        ghlInvoiceId: eventData._id,
        status: this.mapGhlStatusToInvoiceStatus(eventData.status),
        paymentProvider: PaymentProvider.GHL,
        paymentMethod: PaymentMethod.CARD, // Default to card
        type: InvoiceType.ONE_TIME, // Default to one-time
        transactionId: eventData._id,
        baseAmount: eventData.total,
        finalAmount: eventData.total,
        dueDate: eventData.dueDate ? new Date(eventData.dueDate) : new Date(),
        line_items: this.mapInvoiceItems(eventData.invoiceItems || []),
        payments: eventData.status === 'paid' ? [{
          method: PaymentMethod.CARD,
          amount: eventData.amountPaid,
          paymentIntentId: eventData._id,
          date: eventData.updatedAt ? new Date(eventData.updatedAt) : new Date(),
        }] : [],
        metadata: {
          isPaymentApproved: eventData.status === 'paid',
          ghlInvoiceData: eventData,
        },
      };

      // Try to find parent and student based on GHL contact data
      if (eventData.contactDetails?.id) {
        const parent = await this.parentModel.findOne({
          ghlContactId: eventData.contactDetails.id,
          studioId: studio._id,
        }).exec();

        if (parent) {
          invoiceData['parentId'] = parent._id;

          // Find a student for this parent
          const student = await this.studentModel.findOne({
            parentId: parent._id,
            studioId: studio._id,
          }).exec();

          if (student) {
            invoiceData['studentId'] = student._id;
          }
        }
      }

      const newInvoice = new this.subscriptionInvoiceModel(invoiceData);
      await newInvoice.save();

      this.logger.log(`Successfully created subscription invoice for GHL invoice: ${eventData._id}`);

    } catch (error) {
      this.logger.error(`Error processing GHL invoiceCreate event: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async updateExistingInvoice(existingInvoice: any, eventData: GhlInvoiceWebhookDto): Promise<void> {
    let updated = false;

    // Update status if changed
    const newStatus = this.mapGhlStatusToInvoiceStatus(eventData.status);
    if (existingInvoice.status !== newStatus) {
      existingInvoice.status = newStatus;
      updated = true;
    }

    // Update amounts if changed
    if (existingInvoice.finalAmount !== eventData.total) {
      existingInvoice.finalAmount = eventData.total;
      existingInvoice.baseAmount = eventData.total;
      updated = true;
    }

    // Update metadata
    existingInvoice.metadata.ghlInvoiceData = eventData;
    updated = true;

    if (updated) {
      await existingInvoice.save();
      this.logger.log(`Updated subscription invoice for GHL invoice: ${eventData._id}`);
    }
  }

  private mapGhlStatusToInvoiceStatus(ghlStatus: string): InvoiceStatus {
    switch (ghlStatus) {
      case 'paid':
        return InvoiceStatus.PAID;
      case 'partially_paid':
        return InvoiceStatus.PARTIALLY_PAID;
      case 'sent':
        return InvoiceStatus.PENDING;
      case 'draft':
        return InvoiceStatus.UPCOMING;
      case 'void':
        return InvoiceStatus.VOID;
      case 'delete':
        return InvoiceStatus.CANCELLED;
      default:
        return InvoiceStatus.PENDING;
    }
  }

  private mapInvoiceItems(ghlItems: any[]): any[] {
    return ghlItems.map(item => ({
      name: item.name || 'GHL Item',
      amount: item.amount || 0,
      type: 'item',
      quantity: item.qty || 1,
      total: (item.amount || 0) * (item.qty || 1),
    }));
  }
}
