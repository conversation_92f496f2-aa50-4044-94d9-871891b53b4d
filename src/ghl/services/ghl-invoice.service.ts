import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { GohighlevelService } from 'src/gohighlevel/gohighlevel.service';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import { Studio } from 'src/database/schema/studio';
import { Parent } from 'src/database/schema/parent';
import { Student } from 'src/database/schema/student';
import {
  InvoiceStatus,
  InvoiceType,
  PaymentMethod,
  PaymentProvider,
} from 'src/stripe/type';

@Injectable()
export class GhlInvoiceService {
  private readonly logger = new Logger(GhlInvoiceService.name);

  constructor(
    @InjectModel(SubscriptionInvoice.name)
    private subscriptionInvoiceModel: Model<SubscriptionInvoice>,
    @InjectModel(Studio.name)
    private studioModel: Model<Studio>,
    @InjectModel(Parent.name)
    private parentModel: Model<Parent>,
    @InjectModel(Student.name)
    private studentModel: Model<Student>,
    private readonly gohighlevelService: GohighlevelService,
  ) {}

  /**
   * Sync invoices from GHL API for a specific location
   */
  async syncInvoicesFromGhl(locationId: string): Promise<void> {
    this.logger.log(`Starting invoice sync for location: ${locationId}`);

    try {
      // Find the studio
      const studio = await this.studioModel.findOne({ locationId }).exec();
      if (!studio) {
        this.logger.error(`Studio not found for locationId: ${locationId}`);
        return;
      }

      // Get invoices from GHL API
      const ghlInvoices = await this.getGhlInvoices(locationId);
      
      for (const ghlInvoice of ghlInvoices) {
        await this.processGhlInvoice(ghlInvoice, studio);
      }

      this.logger.log(`Completed invoice sync for location: ${locationId}`);
    } catch (error) {
      this.logger.error(`Error syncing invoices for location ${locationId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get invoices from GHL API
   */
  private async getGhlInvoices(locationId: string): Promise<any[]> {
    try {
      // Use the GHL service to make API call
      const response = await this.gohighlevelService.makeHttpRequestWithRetry(
        'get',
        'https://services.leadconnectorhq.com/invoices/',
        locationId,
        null,
        { limit: 100 } // Add query parameters as needed
      );

      return response.invoices || [];
    } catch (error) {
      this.logger.error(`Error fetching invoices from GHL: ${error.message}`);
      throw error;
    }
  }

  /**
   * Process a single GHL invoice
   */
  private async processGhlInvoice(ghlInvoice: any, studio: any): Promise<void> {
    try {
      // Check if invoice already exists
      const existingInvoice = await this.subscriptionInvoiceModel.findOne({
        ghlInvoiceId: ghlInvoice.id,
      }).exec();

      if (existingInvoice) {
        // Update existing invoice if needed
        await this.updateExistingInvoice(existingInvoice, ghlInvoice);
        return;
      }

      // Create new subscription invoice from GHL invoice
      await this.createInvoiceFromGhl(ghlInvoice, studio);

    } catch (error) {
      this.logger.error(`Error processing GHL invoice ${ghlInvoice.id}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create a new subscription invoice from GHL invoice data
   */
  private async createInvoiceFromGhl(ghlInvoice: any, studio: any): Promise<void> {
    const invoiceData = {
      studioId: studio._id,
      ghlInvoiceId: ghlInvoice.id,
      status: this.mapGhlStatusToInvoiceStatus(ghlInvoice.status),
      paymentProvider: PaymentProvider.GHL,
      paymentMethod: PaymentMethod.CARD, // Default, can be updated based on GHL data
      type: InvoiceType.ONE_TIME, // Default, can be updated based on invoice type
      transactionId: ghlInvoice.id,
      baseAmount: ghlInvoice.total || 0,
      finalAmount: ghlInvoice.total || 0,
      dueDate: ghlInvoice.dueDate ? new Date(ghlInvoice.dueDate) : new Date(),
      line_items: this.mapGhlLineItems(ghlInvoice.items || []),
      payments: [],
      metadata: {
        ghlInvoiceData: ghlInvoice,
      },
    };

    // Try to find parent and student based on GHL contact data
    if (ghlInvoice.contactId) {
      const parent = await this.parentModel.findOne({
        ghlContactId: ghlInvoice.contactId,
        studioId: studio._id,
      }).exec();

      if (parent) {
        invoiceData['parentId'] = parent._id;
        
        // Find a student for this parent (you might need to adjust this logic)
        const student = await this.studentModel.findOne({
          parentId: parent._id,
          studioId: studio._id,
        }).exec();

        if (student) {
          invoiceData['studentId'] = student._id;
        }
      }
    }

    const newInvoice = new this.subscriptionInvoiceModel(invoiceData);
    await newInvoice.save();

    this.logger.log(`Created subscription invoice for GHL invoice: ${ghlInvoice.id}`);
  }

  /**
   * Update existing invoice with GHL data
   */
  private async updateExistingInvoice(existingInvoice: any, ghlInvoice: any): Promise<void> {
    let updated = false;

    // Update status if changed
    const newStatus = this.mapGhlStatusToInvoiceStatus(ghlInvoice.status);
    if (existingInvoice.status !== newStatus) {
      existingInvoice.status = newStatus;
      updated = true;
    }

    // Update amount if changed
    if (existingInvoice.finalAmount !== ghlInvoice.total) {
      existingInvoice.finalAmount = ghlInvoice.total;
      existingInvoice.baseAmount = ghlInvoice.total;
      updated = true;
    }

    // Update metadata
    existingInvoice.metadata.ghlInvoiceData = ghlInvoice;
    updated = true;

    if (updated) {
      await existingInvoice.save();
      this.logger.log(`Updated subscription invoice for GHL invoice: ${ghlInvoice.id}`);
    }
  }

  /**
   * Map GHL invoice status to our invoice status
   */
  private mapGhlStatusToInvoiceStatus(ghlStatus: string): InvoiceStatus {
    switch (ghlStatus?.toLowerCase()) {
      case 'paid':
        return InvoiceStatus.PAID;
      case 'pending':
        return InvoiceStatus.PENDING;
      case 'draft':
        return InvoiceStatus.UPCOMING;
      case 'void':
        return InvoiceStatus.VOID;
      case 'overdue':
        return InvoiceStatus.FAILED;
      default:
        return InvoiceStatus.PENDING;
    }
  }

  /**
   * Map GHL line items to our line items format
   */
  private mapGhlLineItems(ghlItems: any[]): any[] {
    return ghlItems.map(item => ({
      name: item.name || item.description || 'GHL Item',
      amount: item.price || item.amount || 0,
      type: 'item',
      quantity: item.quantity || 1,
      total: (item.price || item.amount || 0) * (item.quantity || 1),
    }));
  }

  /**
   * Handle invoice webhook event (for future use when GHL adds invoice webhooks)
   */
  async handleInvoiceWebhook(webhookData: any): Promise<void> {
    this.logger.log(`Processing GHL invoice webhook: ${JSON.stringify(webhookData)}`);

    try {
      const { locationId, data } = webhookData;
      
      // Find the studio
      const studio = await this.studioModel.findOne({ locationId }).exec();
      if (!studio) {
        this.logger.error(`Studio not found for locationId: ${locationId}`);
        return;
      }

      // Process the invoice data
      await this.processGhlInvoice(data, studio);

    } catch (error) {
      this.logger.error(`Error processing GHL invoice webhook: ${error.message}`, error.stack);
      throw error;
    }
  }
}
