import { Controller, Post, Get, Query, Body, UseGuards, Req, Res } from '@nestjs/common';
import { Request, Response } from 'express';
import { GhlInvoiceService } from './services/ghl-invoice.service';
import { JwtAuthGuard } from 'src/auth/auth.guard';

@Controller('ghl/invoices')
export class GhlInvoiceController {
  constructor(
    private readonly ghlInvoiceService: GhlInvoiceService,
  ) {}

  /**
   * Manual sync trigger endpoint
   */
  @Post('sync')
  @UseGuards(JwtAuthGuard)
  async syncInvoices(
    @Req() request: Request,
    @Query('locationId') locationId?: string,
  ) {
    // If no locationId provided, use the one from the authenticated request
    const targetLocationId = locationId || request['locationId'];

    await this.ghlInvoiceService.syncInvoicesFromGhl(targetLocationId);

    return {
      success: true,
      message: `Invoice sync triggered${targetLocationId ? ` for location: ${targetLocationId}` : ' for all locations'}`,
    };
  }

  /**
   * Webhook endpoint for GHL invoice events (for future use)
   */
  @Post('webhook')
  async handleInvoiceWebhook(
    @Body() webhookData: any,
    @Query('locationId') locationId: string,
    @Res() res: Response,
  ) {
    try {
      // Add locationId to webhook data if not present
      if (!webhookData.locationId && locationId) {
        webhookData.locationId = locationId;
      }

      await this.ghlInvoiceService.handleInvoiceWebhook(webhookData);

      return res.status(200).json({
        success: true,
        message: 'Invoice webhook processed successfully',
      });
    } catch (error) {
      return res.status(500).json({
        success: false,
        error: error.message,
      });
    }
  }

  /**
   * Get sync status for a location
   */
  @Get('sync-status')
  @UseGuards(JwtAuthGuard)
  async getSyncStatus(@Req() request: Request) {
    const locationId = request['locationId'];
    
    // You can implement logic to track sync status here
    // For now, return a simple response
    return {
      locationId,
      lastSyncTime: new Date().toISOString(),
      status: 'active',
      message: 'Invoice sync is running every 15 minutes',
    };
  }
}
