import { Module, forwardRef } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ScheduleModule } from '@nestjs/schedule';
import { GhlInvoiceService } from './services/ghl-invoice.service';
import { GhlInvoiceController } from './ghl-invoice.controller';
import { InvoiceCreateHandler } from './services/event-handlers/invoice-create.handler';
import { InvoicePaidHandler } from './services/event-handlers/invoice-paid.handler';
import { InvoiceUpdateHandler } from './services/event-handlers/invoice-update.handler';
import { SubscriptionInvoice, SubscriptionInvoiceSchema } from 'src/database/schema/subscriptionInvoice';
import { Studio, StudioSchema } from 'src/database/schema/studio';
import { Parent, ParentSchema } from 'src/database/schema/parent';
import { Student, StudentSchema } from 'src/database/schema/student';
import { GohighlevelModule } from 'src/gohighlevel/gohighlevel.module';
import { AuthModule } from 'src/auth/auth.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: SubscriptionInvoice.name, schema: SubscriptionInvoiceSchema },
      { name: Studio.name, schema: StudioSchema },
      { name: Parent.name, schema: ParentSchema },
      { name: Student.name, schema: StudentSchema },
    ]),
    ScheduleModule.forRoot(),
    forwardRef(() => GohighlevelModule),
    AuthModule,
  ],
  controllers: [GhlInvoiceController],
  providers: [
    GhlInvoiceService,
    InvoiceCreateHandler,
    InvoicePaidHandler,
    InvoiceUpdateHandler,
  ],
  exports: [
    GhlInvoiceService,
    InvoiceCreateHandler,
    InvoicePaidHandler,
    InvoiceUpdateHandler,
  ],
})
export class GhlModule {}
