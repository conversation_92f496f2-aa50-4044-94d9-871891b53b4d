import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { PaymentTransactionService } from './payment-transaction.service';
import {
  PaymentTransaction,
  PaymentTransactionSchema,
} from '../database/schema/paymentTransaction';
import {
  SubscriptionInvoice,
  SubscriptionInvoiceSchema,
} from 'src/database/schema/subscriptionInvoice';
import { AuthModule } from 'src/auth/auth.module';
import { Parent } from 'src/database/schema/parent';
import { Student } from 'src/database/schema/student';
import { StudentSchema } from 'src/database/schema/student';
import { ParentSchema } from 'src/database/schema/parent';
import { PaymentTransactionController } from './payment-transaction.controller';
import { TransactionCode } from 'src/transaction-code/entities/transaction-code.entity';
import { TransactionCodeSchema } from 'src/transaction-code/entities/transaction-code.entity';
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: PaymentTransaction.name, schema: PaymentTransactionSchema },
      { name: SubscriptionInvoice.name, schema: SubscriptionInvoiceSchema },
      { name: Parent.name, schema: ParentSchema },
      { name: Student.name, schema: StudentSchema },
      { name: TransactionCode.name, schema: TransactionCodeSchema },
    ]),
    AuthModule,
  ],
  controllers: [PaymentTransactionController],
  providers: [PaymentTransactionService],
  exports: [PaymentTransactionService],
})
export class PaymentTransactionModule {}
