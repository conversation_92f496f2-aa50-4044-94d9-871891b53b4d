export class MainTransactionTableRowDto {
  invoiceDate: Date;
  paidDate: Date | null;
  invoiceId: string;
  paymentId: string | null;
  parentName: string;
  studentName: string;
  classChargeName: string;
  transactionCode: string | null;
  status: string;
  invoiceValue: number;
  discountAdjustment: number;
  paidAmount: number;
  refundId: string | null;
  refundAmount: number;
}

export class PaginatedMainTransactionTableResultDto {
  data: MainTransactionTableRowDto[];
  total: number;
  page: number;
  limit: number;
}
