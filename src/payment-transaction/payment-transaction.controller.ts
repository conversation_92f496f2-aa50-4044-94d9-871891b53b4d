import { Controller, Get, Query, Req, UseGuards } from '@nestjs/common';
import { PaymentTransactionService } from './payment-transaction.service';
import { JwtAuthGuard } from 'src/auth/auth.guard';

@Controller('payment-transaction')
export class PaymentTransactionController {
  constructor(
    private readonly paymentTransactionService: PaymentTransactionService,
  ) {}

  @Get('/list')
  @UseGuards(JwtAuthGuard)
  async list(
    @Req() request: Request,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('parentName') parentName?: string,
    @Query('studentName') studentName?: string,
    @Query('classChargeName') classChargeName?: string,
    @Query('transactionCode') transactionCode?: string,
    @Query('status') status?: string,
  ) {
    const studioId = request['locationId'];
    return this.paymentTransactionService.findAllForMainTransactionTable(
      studioId,
      page,
      limit,
      parentName,
      studentName,
      classChargeName,
      transactionCode,
      status,
    );
  }
}
