import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  PaymentTransaction,
  PaymentTransactionDocument,
} from '../database/schema/paymentTransaction';
import { InvoiceStatus, PaymentTransactionStatus } from 'src/stripe/type';
import {
  SubscriptionInvoice,
  SubscriptionInvoiceDocument,
} from 'src/database/schema/subscriptionInvoice';
import { Parent } from 'src/database/schema/parent';
import { Student } from 'src/database/schema/student';
import {
  MainTransactionTableRowDto,
  PaginatedMainTransactionTableResultDto,
} from './dto/main-transaction-table-row.dto';
import { TransactionCode } from 'src/transaction-code/entities/transaction-code.entity';

@Injectable()
export class PaymentTransactionService {
  constructor(
    @InjectModel(PaymentTransaction.name)
    private readonly paymentTransactionModel: Model<PaymentTransaction>,
    @InjectModel(SubscriptionInvoice.name)
    private readonly subscriptionInvoiceModel: Model<SubscriptionInvoiceDocument>,
    @InjectModel(Parent.name)
    private readonly parentModel: Model<Parent>,
    @InjectModel(Student.name)
    private readonly studentModel: Model<Student>,
    @InjectModel(TransactionCode.name)
    private readonly transactionCodeModel: Model<TransactionCode>,
  ) {}

  async create(
    createDto: Partial<PaymentTransaction>,
  ): Promise<PaymentTransaction> {
    const created = new this.paymentTransactionModel(createDto);
    return created.save();
  }

  async findAll(studioId: string): Promise<PaymentTransaction[]> {
    return this.paymentTransactionModel.find({ studioId }).exec();
  }

  async findOne(id: string): Promise<PaymentTransaction> {
    return this.paymentTransactionModel.findById(id).exec();
  }

  async findChildTransactionByStripeSessionId(
    stripeSessionId: string,
  ): Promise<PaymentTransaction[]> {
    return this.paymentTransactionModel
      .find({
        'metadata.stripeSessionId': stripeSessionId,
      })
      .exec();
  }

  async findChildTransactionByPaymentIntentId(
    paymentIntentId: string,
  ): Promise<PaymentTransaction> {
    return this.paymentTransactionModel
      .findOne({
        'metadata.paymentIntentId': paymentIntentId,
      })
      .exec();
  }

  async findScheduledChildTransactionByStudentId(
    studentId: string,
  ): Promise<PaymentTransaction> {
    return this.paymentTransactionModel
      .findOne({
        studentId,
        status: PaymentTransactionStatus.SCHEDULED,
      })
      .exec();
  }

  async findChildTransactions(groupId: string): Promise<PaymentTransaction[]> {
    return this.paymentTransactionModel
      .find({
        groupId: groupId,
      })
      .exec();
  }

  async findOldestInvoiceByStatus(
    transactionId: string,
    status: PaymentTransactionStatus,
  ): Promise<SubscriptionInvoiceDocument> {
    return this.subscriptionInvoiceModel
      .findOne({
        'metadata.internalTransactionId': transactionId,
        status: status,
      })
      .sort({ createdAt: 1 })
      .exec();
  }

  async update(
    id: string,
    updateDto: Partial<PaymentTransactionDocument>,
  ): Promise<PaymentTransaction> {
    return this.paymentTransactionModel
      .findByIdAndUpdate(id, updateDto, { new: true })
      .exec();
  }

  async remove(id: string): Promise<PaymentTransaction> {
    return this.paymentTransactionModel.findByIdAndDelete(id).exec();
  }

  async findInvoiceByPaymentIntent(paymentIntentId: string) {
    return this.subscriptionInvoiceModel
      .find({
        'metadata.paymentIntentId': paymentIntentId,
      })
      .exec();
  }

  async findTransactionsByStatus(
    status: PaymentTransactionStatus,
  ): Promise<PaymentTransaction[]> {
    return this.paymentTransactionModel
      .find({
        status,
        // Find root transactions, not child transactions
        type: { $ne: 'enrollment' },
        // Only retry transactions with an amount greater than 0
        amount: { $gt: 0 },
      })
      .exec();
  }

  async findAllForMainTransactionTable(
    studioId: string,
    page: number,
    limit: number,
    parentName?: string,
    studentName?: string,
    classChargeName?: string,
    transactionCode?: string,
    status?: string,
  ): Promise<PaginatedMainTransactionTableResultDto> {
    const studioObjectId = new Types.ObjectId(studioId);
    const skip = (page - 1) * limit;

    const query: any = { studioId: studioObjectId };

    if (status) {
      query.status = status;
    }

    // Always exclude cancelled and void unless explicitly requested
    if (
      !status ||
      (status !== InvoiceStatus.CANCELLED && status !== InvoiceStatus.VOID)
    ) {
      query.status = {
        $nin: [InvoiceStatus.CANCELLED, InvoiceStatus.VOID],
      };
    }

    // We need to fetch parent and student documents first to filter by their names
    // This is not the most efficient way, especially for large datasets.
    // Consider denormalizing parent/student names into the invoice schema or using a more advanced search solution (e.g., Elasticsearch) for better performance.

    let parentIds: Types.ObjectId[] | undefined;
    if (parentName) {
      const parents = await this.parentModel
        .find({
          studioId: studioObjectId, // Assuming parents are also scoped by studioId
          $or: [
            { name: { $regex: parentName, $options: 'i' } },
            { firstName: { $regex: parentName, $options: 'i' } },
            { lastName: { $regex: parentName, $options: 'i' } },
          ],
        })
        .select('_id')
        .exec();
      parentIds = parents.map((p) => p._id as Types.ObjectId);
      if (parentIds.length === 0) {
        // If no parents match, no invoices will match either
        return {
          data: [],
          total: 0,
          page,
          limit,
        };
      }
      query.parentId = { $in: parentIds };
    }

    let studentIds: Types.ObjectId[] | undefined;
    if (studentName) {
      const students = await this.studentModel
        .find({
          studioId: studioObjectId, // Assuming students are also scoped by studioId
          $or: [
            { name: { $regex: studentName, $options: 'i' } },
            { firstName: { $regex: studentName, $options: 'i' } },
            { lastName: { $regex: studentName, $options: 'i' } },
          ],
        })
        .select('_id')
        .exec();
      studentIds = students.map((s) => s._id as Types.ObjectId);
      if (studentIds.length === 0) {
        // If no students match, no invoices will match either
        return {
          data: [],
          total: 0,
          page,
          limit,
        };
      }
      query.studentId = { $in: studentIds };
    }

    if (classChargeName) {
      query['line_items.name'] = { $regex: classChargeName, $options: 'i' };
    }

    let transactionCodeIds: Types.ObjectId[] | undefined;
    if (transactionCode) {
      const codes = await this.transactionCodeModel
        .find({
          code: { $regex: transactionCode, $options: 'i' },
          // Assuming transaction codes are not studio-specific, otherwise add studioId filter
        })
        .select('_id')
        .exec();
      transactionCodeIds = codes.map((tc) => tc._id as Types.ObjectId);
      if (transactionCodeIds.length === 0) {
        return {
          data: [],
          total: 0,
          page,
          limit,
        };
      }
      query.transactionCodeId = { $in: transactionCodeIds };
    }

    const invoices = await this.subscriptionInvoiceModel
      .find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .exec();

    const total = await this.subscriptionInvoiceModel.countDocuments(query);

    const tableData: MainTransactionTableRowDto[] = await Promise.all(
      invoices.map(async (invoice) => {
        let paymentTransaction: PaymentTransaction | null = null;
        if (invoice.metadata?.internalTransactionId) {
          try {
            paymentTransaction = await this.paymentTransactionModel
              .findById(invoice.metadata.internalTransactionId)
              .exec();
          } catch (error) {
            console.error(
              `Error fetching payment transaction for invoice ${invoice._id}:`,
              error,
            );
            // Decide if you want to throw, or continue with null paymentTransaction
          }
        }

        let parent: Parent | null = null;
        if (invoice.parentId) {
          try {
            parent = await this.parentModel.findById(invoice.parentId).exec();
          } catch (error) {
            console.error(
              `Error fetching parent for invoice ${invoice._id}:`,
              error,
            );
          }
        }

        let student: Student | null = null;
        if (invoice.studentId) {
          try {
            student = await this.studentModel
              .findById(invoice.studentId)
              .exec();
          } catch (error) {
            console.error(
              `Error fetching student for invoice ${invoice._id}:`,
              error,
            );
          }
        }

        const classChargeNames = invoice.line_items?.[0]?.name || 'N/A';
        const discountAdjustment = invoice.metadata.appliedDiscount || 0;

        const refundAmount =
          invoice.line_items?.find((li) => li.name === 'Refund')?.amount || 0;

        let transactionCode: TransactionCode | null = null;
        if (invoice.transactionCodeId) {
          try {
            transactionCode = await this.transactionCodeModel
              .findById(invoice.transactionCodeId)
              .exec();
          } catch (error) {
            console.error(
              `Error fetching transaction code for invoice ${invoice._id}:`,
              error,
            );
          }
        }

        let paidAmount = 0;
        if (invoice.payments) {
          paidAmount =
            invoice.payments.reduce((acc, curr) => acc + curr.amount, 0) || 0;
        }

        let invoiceValue = 0;
        if (invoice.line_items) {
          invoiceValue =
            invoice.line_items.reduce((acc, curr) => acc + curr.amount, 0) || 0;
        }

        return {
          invoiceId: invoice._id.toString(),
          invoiceDate: invoice?.createdAt || null,
          paidDate: invoice?.paymentDate || null,
          paymentId: invoice.metadata?.paymentIntentId || null,
          parentId: invoice?.parentId || null,
          parentName:
            parent?.name ||
            (parent ? `${parent.firstName} ${parent.lastName}` : 'N/A'),
          studentId: invoice?.studentId || null,
          studentName:
            student?.name ||
            (student ? `${student.firstName} ${student.lastName}` : 'N/A'),
          classChargeName: classChargeNames,
          transactionCode: transactionCode?.code || null,
          status: invoice?.status || 'N/A',
          invoiceValue: invoiceValue,
          discountAdjustment: discountAdjustment || 0,
          paidAmount: paidAmount,
          refundId: invoice?.metadata?.refundId || null,
          refundAmount: refundAmount || 0,
          lineItems: invoice?.line_items || [],
          paymentProvider: invoice.paymentProvider,
          paymentMethod: invoice.paymentMethod,
          failureReason: invoice.metadata?.failureReason || null,
          refundReason: invoice.metadata?.refundReason || null,
          attemptCount: invoice.metadata?.attemptCount || 0,
          lastAttemptDate: invoice.metadata?.lastAttemptDate || null,
          internalTransactionId:
            invoice.metadata?.internalTransactionId || null,
          appliedDiscount: invoice.metadata?.appliedDiscount || 0,
          payments: invoice.payments || [],
        };
      }),
    );

    return {
      data: tableData,
      total,
      page,
      limit,
    };
  }

  async findTransactionsByQuery(query: any): Promise<PaymentTransaction[]> {
    return this.paymentTransactionModel
      .find(query)
      .sort({ createdAt: 1 })
      .exec();
  }

  // Find the oldest scheduled transaction matching a query
  async findOldestScheduledTransaction(
    query: any,
  ): Promise<PaymentTransaction | null> {
    return this.paymentTransactionModel
      .findOne(query)
      .sort({ createdAt: 1 })
      .exec();
  }

  // Find one transaction by query
  async findOneByQuery(query: any): Promise<PaymentTransaction | null> {
    return this.paymentTransactionModel.findOne(query).exec();
  }

  // Remove a line item by description from a transaction
  async pullLineItem(
    transactionId: string,
    description: string,
  ): Promise<void> {
    await this.paymentTransactionModel.updateOne(
      { _id: transactionId },
      {
        $pull: {
          'metadata.line_items': {
            'price_data.product_data.description': description,
          },
        },
      },
    );
  }

  // Add a line item to a transaction
  async pushLineItem(transactionId: string, lineItem: any): Promise<void> {
    await this.paymentTransactionModel.updateOne(
      { _id: transactionId },
      { $push: { 'metadata.line_items': lineItem } },
    );
  }
}
