import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Policy } from 'src/database/schema/policy';
import { CreatePolicyDto } from './dto/create-policy.dto';
import { StudiosService } from 'src/studios/studios.service';
import { Enrollment } from 'src/database/schema/enrollment';
import { Event } from 'src/database/schema/event';

@Injectable()
export class PoliciesService {
  constructor(
    @InjectModel(Policy.name) private readonly policyModel: Model<Policy>,
    @InjectModel(Enrollment.name)
    private readonly enrollmentModel: Model<Enrollment>,
    @Inject(forwardRef(() => StudiosService))
    private readonly studioService: StudiosService,
    @InjectModel(Event.name)
    private readonly eventModel: Model<Event>,
  ) {}

  async create(locationId, createPolicyDto: CreatePolicyDto): Promise<Policy> {
    try {
      const policyData = {
        ...createPolicyDto,
        name: createPolicyDto.name.toLocaleUpperCase(),
        studio: Types.ObjectId.createFromHexString(locationId),
        class: createPolicyDto.class
          .filter((id) => id)
          .map((id) => new Types.ObjectId(id)),
        event: createPolicyDto.event
          .filter((id) => id)
          .map((id) => new Types.ObjectId(id)),
      };

      const createdPolicy = new this.policyModel(policyData);

      // Save the policy first to get its ID
      await createdPolicy.save();

      // Update enrollments (classes) with the new policy ID
      if (policyData.class.length > 0) {
        await this.enrollmentModel.updateMany(
          {
            _id: { $in: policyData.class },
          },
          { $addToSet: { policyGroup: createdPolicy._id } },
        );
      }

      // Update events with the new policy ID
      if (policyData.event.length > 0) {
        await this.eventModel.updateMany(
          {
            _id: { $in: policyData.event },
          },
          { $addToSet: { policyGroup: createdPolicy._id } },
        );
      }

      return createdPolicy;
    } catch (error) {
      console.error('Error creating policy:', error);
      throw error;
    }
  }

  async findAll(locationId): Promise<Policy[]> {
    try {
      // const studioId = await this.studioService.findByLocationIdString(locationId);
      // const classEventObjectId = new Types.ObjectId(classEventId);
      // const studioObjectId = Types.ObjectId.createFromHexString(locationId);
      return await this.policyModel
        .find({
          studio: locationId,
        })
        // .populate('studio') // Populate studio details if needed
        .exec();
    } catch (error) {
      throw new Error(`Error fetching policies: ${error}`);
    }
  }

  async findOne(id: string): Promise<Policy | null> {
    return this.policyModel
      .findById(id)
      .populate('class')
      .populate('event')
      .exec();
  }

  async update(id, data: Partial<Policy>): Promise<Policy | null> {
    try {
      // Get the original policy to find removed classes/events
      const originalPolicy = await this.policyModel.findById(id).lean();
      if (!originalPolicy) {
        return null;
      }

      const policyData = {
        ...data,
        class: data.class
          .filter((id) => id)
          .map((id) => new Types.ObjectId(id)),
        event: data.event
          .filter((id) => id)
          .map((id) => new Types.ObjectId(id)),
      };

      // Update the policy document
      const updatedPolicy = await this.policyModel
        .findByIdAndUpdate(id, policyData, { new: true })
        .exec();

      if (!updatedPolicy) {
        return null;
      }

      // Find classes that were removed
      const removedClasses = originalPolicy.class.filter(
        (originalId) =>
          !policyData.class.some(
            (newId) => newId.toString() === originalId.toString(),
          ),
      );

      // Find events that were removed
      const removedEvents = originalPolicy.event.filter(
        (originalId) =>
          !policyData.event.some(
            (newId) => newId.toString() === originalId.toString(),
          ),
      );

      // Remove policy from classes that were removed
      if (removedClasses.length > 0) {
        await this.enrollmentModel.updateMany(
          { _id: { $in: removedClasses } },
          {
            $pull: { policyGroup: Types.ObjectId.createFromHexString(id) },
          },
        );
      }

      // Remove policy from events that were removed
      if (removedEvents.length > 0) {
        await this.eventModel.updateMany(
          { _id: { $in: removedEvents } },
          {
            $pull: { policyGroup: Types.ObjectId.createFromHexString(id) },
          },
        );
      }

      // Add policy to new classes
      if (policyData.class.length > 0) {
        await this.enrollmentModel.updateMany(
          { _id: { $in: policyData.class } },
          {
            $addToSet: { policyGroup: Types.ObjectId.createFromHexString(id) },
          },
        );
      }

      // Add policy to new events
      if (policyData.event.length > 0) {
        await this.eventModel.updateMany(
          { _id: { $in: policyData.event } },
          {
            $addToSet: { policyGroup: Types.ObjectId.createFromHexString(id) },
          },
        );
      }

      return updatedPolicy;
    } catch (error) {
      console.error('Error updating policy:', error);
      throw error;
    }
  }

  async delete(id: string): Promise<Policy | null> {
    return this.policyModel.findByIdAndDelete(id).exec();
  }

  async getPoliciesByStudioId(locationId): Promise<Policy[]> {
    try {
      // const studioId = await this.studioService.findByLocationIdString(locationId);
      // const classEventObjectId = new Types.ObjectId(classEventId);
      const studioObjectId = Types.ObjectId.createFromHexString(locationId);
      return await this.policyModel
        .find({
          studio: studioObjectId,
        })
        // .populate('studio') // Populate studio details if needed
        .exec();
    } catch (error) {
      throw new Error(`Error fetching policies: ${error}`);
    }
  }

  async findPoliciesByClassId(locationId, classId: string): Promise<Policy[]> {
    try {
      const studioId =
        await this.studioService.findByLocationIdString(locationId);
      const classEventObjectId = Types.ObjectId.createFromHexString(classId);
      const studioObjectId = Types.ObjectId.createFromHexString(
        studioId._id.toString(),
      );
      return await this.policyModel
        .find({
          class: { $elemMatch: { $eq: classEventObjectId } },
          studio: studioObjectId,
        })
        // .populate('studio') // Populate studio details if needed
        .exec();
    } catch (error) {
      throw new Error(`Error fetching policies: ${error}`);
    }
  }

  async findPoliciesByEventId(locationId, eventId: string): Promise<Policy[]> {
    try {
      const studioId =
        await this.studioService.findByLocationIdString(locationId);
      const classEventObjectId = Types.ObjectId.createFromHexString(eventId);
      const studioObjectId = Types.ObjectId.createFromHexString(
        studioId._id.toString(),
      );
      return await this.policyModel
        .find({
          event: { $elemMatch: { $eq: classEventObjectId } },
          studio: studioObjectId,
        })
        // .populate('studio') // Populate studio details if needed
        .exec();
    } catch (error) {
      throw new Error(`Error fetching policies: ${error}`);
    }
  }

  async findPoliciesByClassWithStudioId(
    studioId,
    classId: string,
  ): Promise<Policy[]> {
    try {
      const classObjectId = Types.ObjectId.createFromHexString(classId);
      const studioObjectId = Types.ObjectId.createFromHexString(
        studioId.toString(),
      );
      return await this.policyModel
        .find({
          class: { $elemMatch: { $eq: classObjectId } },
          studio: studioObjectId,
        })
        // .populate('studio') // Populate studio details if needed
        .exec();
    } catch (error) {
      throw new Error(`Error fetching policies: ${error}`);
    }
  }

  async findPoliciesByEventWithStudioId(
    studioId,
    eventId: string,
  ): Promise<Policy[]> {
    try {
      const eventObjectId = Types.ObjectId.createFromHexString(eventId);
      const studioObjectId = Types.ObjectId.createFromHexString(
        studioId.toString(),
      );
      return await this.policyModel
        .find({
          event: { $elemMatch: { $eq: eventObjectId } },
          studio: studioObjectId,
        })
        // .populate('studio') // Populate studio details if needed
        .exec();
    } catch (error) {
      throw new Error(`Error fetching policies: ${error}`);
    }
  }

  async searchByName(query: string, locationId_string) {
    const locationId = Types.ObjectId.createFromHexString(locationId_string);
    return this.policyModel.find({
      studio: locationId,
      name: { $regex: new RegExp(query, 'i') },
    });
  }

  async searchPoliciesByClassName(
    className: string,
    locationId: string,
    pagination?: {
      page?: number;
      limit?: number;
    },
  ) {
    // Set default pagination
    const page = pagination?.page || 1;
    const limit = pagination?.limit || 10;
    const skip = (page - 1) * limit;

    // Convert locationId to ObjectId
    const locationObjectId = Types.ObjectId.createFromHexString(locationId);

    // Use an aggregation pipeline to perform the search
    const pipeline: any[] = [
      // Match policies for the specific location
      {
        $match: {
          studio: locationObjectId,
        },
      },

      // Lookup the associated class/enrollment
      {
        $lookup: {
          from: 'enrollments', // Assuming the collection name for enrollments
          localField: 'classEvent', // Assuming this is the field linking policies to classes
          foreignField: '_id',
          as: 'classDetails',
        },
      },

      // Unwind the class details
      {
        $unwind: {
          path: '$classDetails',
          preserveNullAndEmptyArrays: true,
        },
      },

      // Filter by class name (case-insensitive partial match)
      {
        $match: {
          'classDetails.title': {
            $regex: new RegExp(className, 'i'),
          },
        },
      },

      // Facet for pagination and data
      {
        $facet: {
          metadata: [{ $count: 'totalCount' }],
          data: [
            // Apply pagination
            { $skip: skip },
            { $limit: limit },

            // Optional: Project to shape the output
            {
              $project: {
                _id: 1,
                name: 1,
                description: 1,
                className: '$classDetails.title',
                studio: 1,
              },
            },
          ],
        },
      },
    ];

    // Execute the aggregation
    const results = await this.policyModel.aggregate(pipeline);

    // Get all possible classes
    const getAllPossibleClasses = async () => {
      const classes = await this.enrollmentModel
        .find({
          studio: locationObjectId,
        })
        .select('_id title');

      return {
        classes: classes.map((classItem) => ({
          _id: classItem._id,
          name: classItem.title,
        })),
      };
    };

    // Extract total count, data, and get possible classes in parallel
    const [possibleValues, totalCount, policyData] = await Promise.all([
      getAllPossibleClasses(),
      results[0].metadata[0]?.totalCount || 0,
      results[0].data,
    ]);

    return {
      data: {
        policies: policyData,
        ...possibleValues,
      },
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    };
  }

  async getPoliciesByStudioIdCsv(locationId: string) {
    const locationId_object = Types.ObjectId.createFromHexString(locationId);
    const policies = await this.policyModel
      .find({ studio: locationId_object })
      .populate({
        path: 'class', // Assuming 'classes' is the field to populate
        model: this.enrollmentModel, // Assuming 'Class' is the model name for classes
        select: 'title', // Selecting fields to include in the population
      })
      .populate({
        path: 'event', // Assuming 'classes' is the field to populate
        model: this.eventModel, // Assuming 'Class' is the model name for classes
        select: 'title', // Selecting fields to include in the population
      })
      .exec();

    return policies.map((policy) => {
      const plainPolicy = policy.toObject();
      return {
        name: plainPolicy.name,
        description: plainPolicy.description,
        classes: plainPolicy.class
          ? plainPolicy.class.map((cls) =>
              typeof cls === 'object' && 'title' in cls
                ? cls.title
                : cls.toString(),
            )
          : '',
        events: plainPolicy.event
          ? plainPolicy.event.map((cls) =>
              typeof cls === 'object' && 'title' in cls
                ? cls.title
                : cls.toString(),
            )
          : '',
      };
    });
  }

  async findPolicyByName(name: string): Promise<Policy | null> {
    return this.policyModel.findOne({ name }).exec();
  }

  async remove(id: string, studioId: string): Promise<boolean> {
    try {
      const studioObjectId = Types.ObjectId.createFromHexString(studioId);
      const policyId = Types.ObjectId.createFromHexString(id);

      // Delete policy
      const result = await this.policyModel.deleteOne({
        _id: policyId,
        studio: studioObjectId,
      });

      // Remove policy from enrollments
      await this.enrollmentModel.updateMany(
        { studio: studioObjectId },
        { $pull: { policyGroup: policyId } },
      );

      // Remove policy from events
      await this.eventModel.updateMany(
        { studio: studioObjectId },
        { $pull: { policyGroup: policyId } },
      );

      return result.deletedCount > 0;
    } catch (error) {
      console.error('Error removing policy:', error);
      throw error;
    }
  }

  async removeBatch(
    ids: string[],
    studioId: string,
  ): Promise<{ deletedCount: number }> {
    const studioObjectId = Types.ObjectId.createFromHexString(studioId);
    const policyIds = ids.map((id) => Types.ObjectId.createFromHexString(id));
    const result = await this.policyModel.deleteMany({
      _id: { $in: policyIds },
      studio: studioObjectId,
    });
    return { deletedCount: result.deletedCount };
  }
}
