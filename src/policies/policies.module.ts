import { forwardRef, Module } from '@nestjs/common';
import { PoliciesService } from './policies.service';
import { PoliciesController } from './policies.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Policy, PolicySchema } from 'src/database/schema/policy';
import { JwtModule } from '@nestjs/jwt';
import { StudiosModule } from 'src/studios/studios.module';
import { Enrollment } from 'src/database/schema/enrollment';
import { EnrollmentSchema } from 'src/database/schema/enrollment';
import { EventSchema } from 'src/database/schema/event';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Policy.name, schema: PolicySchema },
      { name: Enrollment.name, schema: EnrollmentSchema },
      { name: Event.name, schema: EventSchema },
    ]),
    JwtModule,
    forwardRef(() => StudiosModule),
  ],
  controllers: [PoliciesController],
  providers: [PoliciesService],
  exports: [PoliciesService],
})
export class PoliciesModule {}
