import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
} from '@nestjs/common';
import { PoliciesService } from './policies.service';
import { CreatePolicyDto } from './dto/create-policy.dto';
import { UpdatePolicyDto } from './dto/update-policy.dto';
import { Policy } from 'src/database/schema/policy';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { Types } from 'mongoose';

@Controller('policies')
export class PoliciesController {
  constructor(private readonly policiesService: PoliciesService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  create(@Body() createPolicyDto: CreatePolicyDto, @Req() request: Request) {
    const locationId = request['locationId'];
    return this.policiesService.create(locationId, createPolicyDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  findAll(@Req() request: Request) {
    const locationId = request['locationId'];
    const locationId_objectaId = Types.ObjectId.createFromHexString(locationId);
    return this.policiesService.findAll(locationId_objectaId);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.policiesService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  async update(
    @Param('id') id: string,
    @Body() data: Partial<Policy>,
    @Req() request: Request,
  ): Promise<Policy | null> {
    const locationId = request['locationId'];
    const locationId_objectaId = Types.ObjectId.createFromHexString(locationId);
    return this.policiesService.update(id, data);
  }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.policiesService.delete(id);
  // }

  @Get('studio/all')
  @UseGuards(JwtAuthGuard)
  async getPoliciesByStudioId(@Req() request: Request): Promise<Policy[]> {
    const locationId = request['locationId'];
    return await this.policiesService.getPoliciesByStudioId(locationId);
  }

  @Get('studio/all/csv')
  @UseGuards(JwtAuthGuard)
  async getPoliciesByStudioIdCsv(
    @Req() request: Request,
  ): Promise<{ name: string; description: string; classes: unknown }[]> {
    const locationId = request['locationId'];
    return await this.policiesService.getPoliciesByStudioIdCsv(locationId);
  }

  @Get('class/:id/:locationId')
  async getPoliciesByClassId(
    @Param('id') id: string,
    @Param('locationId') locationId: string,
  ): Promise<Policy[]> {
    // const locationId = request['locationId'];
    return await this.policiesService.findPoliciesByClassId(locationId, id);
  }

  @Get('event/:id/:locationId')
  async getPoliciesByEventId(
    @Param('id') id: string,
    @Param('locationId') locationId: string,
  ): Promise<Policy[]> {
    // const locationId = request['locationId'];
    return await this.policiesService.findPoliciesByEventId(locationId, id);
  }

  @Get('class/:id')
  @UseGuards(JwtAuthGuard)
  async getPoliciesByClass(
    @Param('id') id: string,
    @Req() request: Request,
  ): Promise<Policy[]> {
    const studioId = request['locationId'];
    return await this.policiesService.findPoliciesByClassWithStudioId(
      studioId,
      id,
    );
  }

  @Get('event/:id')
  @UseGuards(JwtAuthGuard)
  async getPoliciesByEvent(
    @Param('id') id: string,
    @Req() request: Request,
  ): Promise<Policy[]> {
    const studioId = request['locationId'];
    return await this.policiesService.findPoliciesByEventWithStudioId(
      studioId,
      id,
    );
  }

  @Get('search/policy')
  @UseGuards(JwtAuthGuard)
  async searchPolicies(
    @Query('className') className: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return this.policiesService.searchPoliciesByClassName(
      className,
      locationId,
      {
        page: Number(page),
        limit: Number(limit),
      },
    );
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  remove(@Param('id') id: string, @Req() request: Request) {
    const studioId = request['locationId'];
    return this.policiesService.remove(id, studioId);
  }

  @Delete('batch/all')
  @UseGuards(JwtAuthGuard)
  removeBatch(@Body() ids: string[], @Req() request: Request) {
    const studioId = request['locationId'];
    return this.policiesService.removeBatch(ids, studioId);
  }
}
