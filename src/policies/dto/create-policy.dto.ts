import { Type } from 'class-transformer';
import { IsString, IsNotEmpty, IsArray, IsMongoId } from 'class-validator';
import { Types } from 'mongoose';

export class CreatePolicyDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  description: string;

  // @IsMongoId()
  // @IsNotEmpty()
  // studio: Types.ObjectId;

  @IsArray()
  @IsMongoId({ each: true })
  class: Types.ObjectId[];

  @IsArray()
  @IsMongoId({ each: true })
  event: Types.ObjectId[];
}
