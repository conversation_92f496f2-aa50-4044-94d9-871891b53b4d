import { Test, TestingModule } from '@nestjs/testing';
import { TrialstudentsController } from './trialstudents.controller';
import { TrialstudentsService } from './trialstudents.service';

describe('TrialstudentsController', () => {
  let controller: TrialstudentsController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [TrialstudentsController],
      providers: [TrialstudentsService],
    }).compile();

    controller = module.get<TrialstudentsController>(TrialstudentsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
