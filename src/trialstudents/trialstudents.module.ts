import { Modu<PERSON> } from '@nestjs/common';
import { TrialstudentsService } from './trialstudents.service';
import { TrialstudentsController } from './trialstudents.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { TrialStudentSchema } from 'src/database/schema/trialStudent';
import { StudioSchema } from 'src/database/schema/studio';
import { GohighlevelModule } from 'src/gohighlevel/gohighlevel.module';
import { EnrollmentSchema } from 'src/database/schema/enrollment';
import { JwtModule } from '@nestjs/jwt';
import { ParentsModule } from 'src/parents/parents.module';
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: 'TrialStudent', schema: TrialStudentSchema },
    ]),
    MongooseModule.forFeature([{ name: 'Studio', schema: StudioSchema }]),
    MongooseModule.forFeature([
      { name: 'Enrollment', schema: EnrollmentSchema },
    ]),
    GohighlevelModule,
    JwtModule,
    ParentsModule,
  ],
  controllers: [TrialstudentsController],
  providers: [TrialstudentsService],
})
export class TrialstudentsModule {}
