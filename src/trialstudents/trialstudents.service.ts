import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { CreateTrialStudentDto } from './dto/create-trialstudent.dto';
import { UpdateTrialstudentDto } from './dto/update-trialstudent.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import {
  AppointmentStatus,
  TrialStudent,
} from 'src/database/schema/trialStudent';
import { Studio } from 'src/database/schema/studio';
import { GohighlevelService } from 'src/gohighlevel/gohighlevel.service';
import { Enrollment } from 'src/database/schema/enrollment';
import { ParentsService } from 'src/parents/parents.service';
import { formatInTimeZone, toZonedTime } from 'date-fns-tz';

@Injectable()
export class TrialstudentsService {
  private readonly logger = new Logger(TrialstudentsService.name);
  constructor(
    @InjectModel('TrialStudent') private trialStudentModel: Model<TrialStudent>,
    @InjectModel('Studio') private studioModel: Model<Studio>,
    private readonly gohighlevelService: GohighlevelService,
    @InjectModel('Enrollment') private enrollmentModel: Model<Enrollment>,
    private readonly parentsService: ParentsService,
  ) {}

  async create(createTrialStudentDto: CreateTrialStudentDto) {
    try {
      const locationId = createTrialStudentDto.locationId;
      if (createTrialStudentDto.type == 'AppointmentCreate') {
        //check the calendarId is valid and exists
        const calendarDetails =
          await this.gohighlevelService.fetchCalendarDetails(
            createTrialStudentDto.locationId,
            createTrialStudentDto['appointment'].calendarId,
          );
        const calendarName = calendarDetails?.calendar?.name || null;

        const classDetails = await this.enrollmentModel
          .findOne({
            calendarId_ghl: createTrialStudentDto['appointment'].calendarId,
          })
          .lean()
          .exec();

        const classId = classDetails?._id;

        if (!classId) {
          this.logger.error('Class not found');
          throw new Error('Class not found');
        }

        const studio = await this.studioModel
          .findOne({ locationId: locationId })
          .lean()
          .exec();
        if (!studio) {
          this.logger.error('Studio not found');
          throw new Error('Studio not found');
        }

        const contactDetails =
          await this.gohighlevelService.fetchContactDetails(
            locationId,
            createTrialStudentDto['appointment'].contactId,
          );

        const customFields =
          await this.gohighlevelService.fetchCustomFields(locationId);

        // Find the first available student name
        let studentName: string | null = null;

        // Try child_first_name first
        const firstStudentField = customFields.customFields.find(
          (field) => field.fieldKey === 'contact.child_first_name',
        );
        if (firstStudentField) {
          studentName =
            contactDetails['contact'].customFields.find(
              (field) => field.id === firstStudentField.id,
            )?.value || null;
        }

        const email = contactDetails?.contact?.email || null;
        const firstName = contactDetails?.contact?.firstName || null;
        const lastName = contactDetails?.contact?.lastName || null;
        const contactNo = contactDetails?.contact?.phone || null;

        const parentDetails = await this.parentsService.findOneByEmail(
          email,
          studio._id.toString(),
        );

        const parentCreationData = {
          firstName: firstName,
          lastName: lastName,
          email: email,
          relation: '',
          primaryPhone: contactNo,
          address: {
            apartment: '',
            street: '',
            city: '',
            state: '',
            zip: '',
            country: '',
          },
          //TODO: This is wrong
          //the function createWithLocationId is expecting locationId, we are passing locationId
          //the key is studioId, so we need to change it to locationId, and make corresponding changes in the function createWithLocationId
          studioId: createTrialStudentDto.locationId, //locationId
          familyName: lastName,
          registration: true,
          workPhone: '',
          homePhone: '',
          birthDate: null,
          emergencyContacts: [],
          secondParent: null,
          policies: [],
          dateOfRegistration: new Date(),
          resetPasswordToken: '',
          resetPasswordExpires: null,
          students: [],
          isCreatedFromStudio: true,
          isLead: false,
          isSessionFeePaid: {},
          paymentMethod: '',
        };

        if (!parentDetails) {
          try {
            const parent =
              await this.parentsService.createWithLocationId(
                parentCreationData,
              );
            this.logger.log('Parent created', parent);
          } catch (error) {
            this.logger.error('Error creating parent', error);
          }
        }

        const startTime =
          createTrialStudentDto['appointment'].startTime || null;
        const endTime = createTrialStudentDto['appointment'].endTime || null;

        const className = classDetails?.title;

        const appointmentStatus = AppointmentStatus.SCHEDULED;

        // Always create only one trial student
        const data = {
          ...createTrialStudentDto['appointment'],
          eventId: createTrialStudentDto['appointment'].id,
          studioId: studio._id,
          studentName: studentName || lastName, // fallback to lastName if no student name found
          email,
          firstName,
          lastName,
          contactNo,
          calendarName,
          classId,
          className,
          startTime,
          endTime,
          locationId,
          isPresent: null,
          appointmentStatus,
        };

        // Check for existing student
        const existingStudent = await this.trialStudentModel
          .findOne({
            email,
            eventId: createTrialStudentDto['appointment'].id,
          })
          .lean()
          .exec();

        if (existingStudent) {
          this.logger.warn(
            `Trial student with email ${email} and eventId ${createTrialStudentDto['appointment'].id} already exists. Skipping creation.`,
          );
          return existingStudent;
        }

        const newTrialStudent = new this.trialStudentModel(data);
        const savedStudent = await newTrialStudent.save();
        return savedStudent;
      }
    } catch (error) {
      this.logger.error('Error creating trial student', error.message);
      throw error;
    }
  }

  async findAll(
    studioId: string,
    options: {
      classId?: string;
      date?: string;
      page?: number;
      limit?: number;
      search?: string;
    },
  ) {
    const studio = await this.studioModel.findById(studioId).lean().exec();

    if (!studio) {
      this.logger.error('Studio not found');
      throw new Error('Studio not found');
    }

    const locationId = studio.locationId;

    //fectch location details from gohighlevel
    const locationDetails =
      await this.gohighlevelService.getLocationDetails(locationId);

    const timezone = locationDetails.location.timezone;

    const query: any = { studioId: studio._id };
    if (options.classId) {
      query.classId = Types.ObjectId.createFromHexString(options.classId);
    }

    if (options.date) {
      const dateOnly = options.date.split('T')[0];
      query.startTime = {
        $regex: dateOnly,
        $options: 'i',
      };
    }

    if (options.search) {
      query.$or = [
        { firstName: { $regex: options.search, $options: 'i' } },
        { lastName: { $regex: options.search, $options: 'i' } },
        { email: { $regex: options.search, $options: 'i' } },
        { studentName: { $regex: options.search, $options: 'i' } },
      ];
    }

    const skip = ((options.page || 1) - 1) * (options.limit || 10);

    const [data, total] = await Promise.all([
      this.trialStudentModel
        .find(query)
        .populate({
          path: 'classId',
          model: 'Enrollment',
          select: 'title',
        })
        .lean()
        .skip(skip)
        .limit(options.limit)
        .sort({ createdAt: -1 })
        .exec(),
      this.trialStudentModel.countDocuments(query),
    ]);

    //format startTime and endTime
    const formattedData = data.map((item) => {
      if (item.startTime) {
        item.startTime = formatInTimeZone(
          new Date(item.startTime),
          timezone,
          "yyyy-MM-dd'T'HH:mm:ss.SSS",
        );
      }
      if (item.endTime) {
        item.endTime = formatInTimeZone(
          new Date(item.endTime),
          timezone,
          "yyyy-MM-dd'T'HH:mm:ss.SSS",
        );
      }
      return item;
    });

    return {
      data: formattedData,
      pagination: {
        totalCount: total,
        page: options.page || 1,
        limit: options.limit || 10,
        totalPages: Math.ceil(total / (options.limit || 10)),
      },
    };
  }

  async findOne(id: string) {
    return await this.trialStudentModel.findById(id).exec();
  }

  async update(id: string, updateTrialstudentDto: UpdateTrialstudentDto) {
    return await this.trialStudentModel
      .findByIdAndUpdate(id, updateTrialstudentDto, { new: true })
      .exec();
  }

  async remove(id: string, locationId: string) {
    return await this.trialStudentModel.findByIdAndDelete(id).exec();
  }

  async updateAppointmentStatus(
    id: string,
    status: AppointmentStatus,
    locationId: string,
  ): Promise<TrialStudent> {
    const trialStudent = await this.trialStudentModel
      .findByIdAndUpdate(id, { appointmentStatus: status }, { new: true })
      .populate({
        path: 'classId',
        model: 'Enrollment',
        select: 'title',
      })
      .exec();

    if (!trialStudent) {
      throw new NotFoundException(`Trial student with ID ${id} not found`);
    }

    return trialStudent;
  }

  async updateAttendance(
    id: string,
    isPresent: boolean,
    studioId: string,
  ): Promise<TrialStudent> {
    let appointmentStatus = AppointmentStatus.SCHEDULED;
    const trialStudentDetails = await this.findOne(id);
    const eventId = trialStudentDetails.eventId;
    const updateData = {
      appointmentStatus: isPresent ? 'showed' : 'noshow',
    };

    //find locationId
    const studio = await this.studioModel.findById(studioId);
    const locationId = studio.locationId;

    if (isPresent) {
      //for your portal
      appointmentStatus = AppointmentStatus.COMPLETED;
      //for GHL
      const ghlResponse = await this.gohighlevelService.updateAppointment(
        locationId,
        eventId,
        updateData,
      );
      this.logger.log('Appointment status updated for GHL', ghlResponse);
    } else {
      //for your portal
      appointmentStatus = AppointmentStatus.MISSED;
      //for GHL
      const ghlResponse = await this.gohighlevelService.updateAppointment(
        locationId,
        eventId,
        updateData,
      );
      this.logger.log('Appointment status updated for GHL', ghlResponse);
    }

    const trialStudent = await this.trialStudentModel
      .findByIdAndUpdate(id, { isPresent, appointmentStatus }, { new: true })
      .populate({
        path: 'classId',
        model: 'Enrollment',
        select: 'title',
      })
      .exec();

    if (!trialStudent) {
      throw new NotFoundException(`Trial student with ID ${id} not found`);
    }

    return trialStudent;
  }
}
