import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
} from '@nestjs/common';
import { TrialstudentsService } from './trialstudents.service';
import { CreateTrialStudentDto } from './dto/create-trialstudent.dto';
import { UpdateTrialstudentDto } from './dto/update-trialstudent.dto';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { UpdateAppointmentStatusDto } from './dto/updateappointmentstatusdto';
import { UpdateAttendanceDto } from './dto/update-attendancedto';

@Controller('trialstudents')
export class TrialstudentsController {
  constructor(private readonly trialstudentsService: TrialstudentsService) {}

  @Post()
  create(@Body() createTrialStudentDto: CreateTrialStudentDto) {
    return this.trialstudentsService.create(createTrialStudentDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  findAll(
    @Req() request: Request,
    @Query('classId') classId?: string,
    @Query('date') date?: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('search') search?: string,
  ) {
    const locationId = request['locationId'];
    return this.trialstudentsService.findAll(locationId, {
      classId,
      date,
      page,
      limit,
      search,
    });
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.trialstudentsService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateTrialstudentDto: UpdateTrialstudentDto,
  ) {
    return this.trialstudentsService.update(id, updateTrialstudentDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  remove(@Param('id') id: string, @Req() request: Request) {
    const locationId = request['locationId'];
    return this.trialstudentsService.remove(id, locationId);
  }

  @Patch(':id/status')
  @UseGuards(JwtAuthGuard)
  updateAppointmentStatus(
    @Param('id') id: string,
    @Body() updateStatusDto: UpdateAppointmentStatusDto,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return this.trialstudentsService.updateAppointmentStatus(
      id,
      updateStatusDto.status,
      locationId,
    );
  }

  @Patch(':id/attendance')
  @UseGuards(JwtAuthGuard)
  updateAttendance(
    @Param('id') id: string,
    @Body() updateAttendanceDto: UpdateAttendanceDto,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return this.trialstudentsService.updateAttendance(
      id,
      updateAttendanceDto.isPresent,
      locationId,
    );
  }
}
