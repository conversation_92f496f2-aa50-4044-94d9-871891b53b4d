import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Notes, NotesDocument } from 'src/database/schema/notes';
import { GohighlevelService } from 'src/gohighlevel/gohighlevel.service';
import {
  createdNotePayloadDto,
  createNoteDto,
  updateNotePayloadDto,
} from './dto/notes-payload.dto';
import { Parent, ParentDocument } from 'src/database/schema/parent';
import { Student, StudentDocument } from 'src/database/schema/student';
import { ObjectId } from 'mongodb';

@Injectable()
export class NotesService {
  private readonly logger = new Logger(NotesService.name);

  constructor(
    private readonly gohighlevelService: GohighlevelService,
    @InjectModel(Notes.name) private notesModel: Model<NotesDocument>,
    @InjectModel(Parent.name) private parentModel: Model<ParentDocument>,
    @InjectModel(Student.name) private studentModel: Model<StudentDocument>,
  ) {}

  async getAllNotes(
    entityId: string,
    entityType: 'Parent' | 'Student',
    page: number = 1,
    limit: number = 10,
  ) {
    this.logger.log(
      `Fetching notes for entityId: ${entityId}, entityType: ${entityType}, page: ${page}, limit: ${limit}`,
    );
    const skip = (page - 1) * limit;
    const [notes, total] = await Promise.all([
      this.notesModel
        .find({ entityId, entityType })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.notesModel.countDocuments({ entityId, entityType }),
    ]);
    return {
      notes,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async createNote(createNoteDto: createdNotePayloadDto) {
    this.logger.log(
      `Creating note for entityId: ${createNoteDto.entityId}, studioId: ${createNoteDto.studioId}`,
    );
    let note = createNoteDto.note || '';
    let parent: ParentDocument | null = null;

    if (createNoteDto.entityType === 'Parent') {
      parent = await this.parentModel.findById(createNoteDto.entityId).exec();
    } else if (createNoteDto.entityType === 'Student') {
      const student = await this.studentModel
        .findById(createNoteDto.entityId)
        .populate('parentId')
        .exec();
      parent = student?.parentId as any;
      note = `Student: ${student?.firstName} ${student?.lastName}\n${note}`;
    }

    if (!parent) {
      throw new Error('Parent not found');
    }

    // Search for contact by email in GoHighLevel
    let contactId = await this.gohighlevelService.searchContactByEmail(
      createNoteDto.studioId,
      parent.email,
    );

    let ghlNoteId = null;
    let ghlContactId = null;

    // If contact found in GHL, create note in GHL
    if (contactId) {
      try {
        const newNote = await this.gohighlevelService.addNotesToContact(
          createNoteDto.studioId,
          contactId,
          note,
        );
        ghlNoteId = newNote?.note?.id;
        ghlContactId = contactId;
        this.logger.log(`Note created in GHL with ID: ${ghlNoteId}`);
      } catch (error) {
        this.logger.error(`Failed to create note in GHL: ${error.message}`);
        // Continue to create note in portal even if GHL fails
      }
    }

    // Always create note in portal database
    const createdNote = await this.notesModel.create({
      ...createNoteDto,
      ghlNoteId: ghlNoteId ? ghlNoteId : 'Contact Not Found',
      ghlContactId: ghlContactId ? ghlContactId : 'Contact Not Found',
    });

    this.logger.log(
      `Note created in portal with ID: ${createdNote?._id?.toString()}`,
    );

    // If no contact found in GHL, throw error after creating note in portal
    if (!contactId) {
      this.logger.warn(`Contact not found in GHL for email: ${parent.email}`);
      throw new Error('Failed to Create Note in CRM, Contact not Found');
    }

    return createdNote;
  }

  async updateNote(noteId: string, updateNoteDto: updateNotePayloadDto) {
    this.logger.log(`Updating note with ID: ${noteId}`);
    const updatedNote = await this.notesModel
      .findByIdAndUpdate(
        noteId,
        {
          $set: {
            note: updateNoteDto.note,
          },
        },
        { new: true },
      )
      .exec();

    if (!updatedNote) {
      throw new Error('Note not found');
    }

    let note = updateNoteDto.note || '';
    let parent: ParentDocument | null = null;

    if (updatedNote.entityType === 'Parent') {
      parent = await this.parentModel.findById(updatedNote.entityId).exec();
    } else if (updatedNote.entityType === 'Student') {
      const student = await this.studentModel
        .findById(updatedNote.entityId)
        .populate('parentId')
        .exec();
      parent = student?.parentId as any;
      note = `Student: ${student?.firstName} ${student?.lastName}\n${note}`;
    }

    if (!parent) {
      throw new Error('Parent not found');
    }

    // Search for contact by email in GoHighLevel
    let contactId = await this.gohighlevelService.searchContactByEmail(
      updatedNote.studioId,
      parent.email,
    );

    // Only update in GHL if contact is found
    if (contactId && updatedNote.ghlNoteId) {
      try {
        await this.gohighlevelService.updateNoteForContact(
          updatedNote.studioId,
          contactId,
          updatedNote.ghlNoteId,
          note,
        );
        this.logger.log(
          `Note updated in GHL with ID: ${updatedNote.ghlNoteId}`,
        );
      } catch (error) {
        this.logger.error(`Failed to update note in GHL: ${error.message}`);
        // Continue even if GHL update fails
      }
    } else if (!contactId) {
      this.logger.warn(
        `Contact not found in GHL for email: ${parent.email}, note updated only in portal`,
      );
    }

    this.logger.log(`Note with ID: ${noteId} updated successfully`);
    return { message: 'Note updated successfully' };
  }

  async deleteNote(noteId: string) {
    this.logger.log(`Deleting note with ID: ${noteId}`);
    const deleteNote = await this.notesModel.findByIdAndDelete(noteId).exec();

    if (!deleteNote) {
      throw new Error('Note not found');
    }

    let parent: ParentDocument | null = null;

    if (deleteNote.entityType === 'Parent') {
      parent = await this.parentModel.findById(deleteNote.entityId).exec();
    } else if (deleteNote.entityType === 'Student') {
      const student = await this.studentModel
        .findById(deleteNote.entityId)
        .populate('parentId')
        .exec();
      parent = student?.parentId as any;
    }

    if (!parent) {
      this.logger.warn(
        'Parent not found for deleted note, skipping GHL deletion',
      );
      return { message: 'Note deleted successfully' };
    }

    // Search for contact by email in GoHighLevel
    let contactId = await this.gohighlevelService.searchContactByEmail(
      deleteNote.studioId,
      parent.email,
    );

    // Only delete from GHL if contact is found and note has GHL ID
    if (contactId && deleteNote.ghlNoteId) {
      try {
        await this.gohighlevelService.deleteNoteForContact(
          deleteNote.studioId,
          contactId,
          deleteNote.ghlNoteId,
        );
        this.logger.log(
          `Note deleted from GHL with ID: ${deleteNote.ghlNoteId}`,
        );
      } catch (error) {
        this.logger.error(`Failed to delete note from GHL: ${error.message}`);
        // Continue even if GHL deletion fails
      }
    } else if (!contactId) {
      this.logger.warn(
        `Contact not found in GHL for email: ${parent.email}, note deleted only from portal`,
      );
    }

    return { message: 'Note deleted successfully' };
  }

  async getNoteById(noteId: string) {
    this.logger.log(`Fetching note with ID: ${noteId}`);
    try {
      const note = await this.notesModel.findById(noteId).exec();
      return note;
    } catch (error) {
      this.logger.error(
        `Error fetching note with ID: ${noteId}`,
        error.stack || error,
      );
      throw error;
    }
  }
}
