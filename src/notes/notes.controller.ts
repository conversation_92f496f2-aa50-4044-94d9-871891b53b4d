import { NotesService } from './notes.service';
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import {
  createdNotePayloadDto,
  createNoteDto,
  updateNotePayloadDto,
} from './dto/notes-payload.dto';

@Controller('notes')
export class NotesController {
  private readonly logger = new Logger(NotesController.name);

  constructor(private readonly notesService: NotesService) {
    // Constructor logic can be added here if needed
  }

  @Get('all')
  async getAllNotes(
    @Query('entityId') entityId: string,
    @Query('entityType') entityType: 'Parent' | 'Student',
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    try {
      return await this.notesService.getAllNotes(
        entityId,
        entityType,
        page,
        limit,
      );
    } catch (error) {
      this.logger.error(
        `Error getting all notes: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        error.message || 'Failed to retrieve notes',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('add')
  async createNote(@Body() createNoteDto: createdNotePayloadDto) {
    try {
      return await this.notesService.createNote(createNoteDto);
    } catch (error) {
      this.logger.error(`Error creating note: ${error.message}`, error.stack);
      throw new HttpException(
        error.message || 'Failed to create note',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Patch(':noteId')
  async updateNote(
    @Body() updateNoteDto: updateNotePayloadDto,
    @Param('noteId') noteId: string,
  ) {
    try {
      return await this.notesService.updateNote(noteId, updateNoteDto);
    } catch (error) {
      this.logger.error(
        `Error updating note ${noteId}: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        error.message || 'Failed to update note',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':noteId')
  async deleteNote(@Param('noteId') noteId: string) {
    try {
      return await this.notesService.deleteNote(noteId);
    } catch (error) {
      this.logger.error(
        `Error deleting note ${noteId}: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        error.message || 'Failed to delete note',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':noteId')
  async getNoteById(@Param('noteId') noteId: string) {
    try {
      return await this.notesService.getNoteById(noteId);
    } catch (error) {
      this.logger.error(
        `Error getting note ${noteId}: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        error.message || 'Failed to retrieve note',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
