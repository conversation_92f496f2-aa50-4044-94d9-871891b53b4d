import { Modu<PERSON> } from '@nestjs/common';
import { NotesController } from './notes.controller';
import { NotesService } from './notes.service';
import { MongooseModule } from '@nestjs/mongoose';
import { Notes, NotesSchema } from 'src/database/schema/notes';
import { JwtModule } from '@nestjs/jwt';
import { GohighlevelModule } from 'src/gohighlevel/gohighlevel.module';
import { Parent, ParentSchema } from 'src/database/schema/parent';
import { Student, StudentSchema } from 'src/database/schema/student';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Notes.name, schema: NotesSchema },
      { name: Parent.name, schema: ParentSchema },
      { name: Student.name, schema: StudentSchema },
    ]),
    JwtModule,
    GohighlevelModule,
  ],
  controllers: [NotesController],
  providers: [NotesService],
  exports: [NotesService],
})
export class NotesModule {}
