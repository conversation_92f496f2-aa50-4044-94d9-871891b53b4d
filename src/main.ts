import * as express from 'express';
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Logger } from '@nestjs/common';
import { Storage } from '@google-cloud/storage';
import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';
const logger = new Logger('Bootstrap');

/**
 * Downloads the environment file from GCS and loads it.
 */
async function loadEnvFromGCS() {
  const bucketName = process.env.GCS_ENV_BUCKET;
  const fileName = process.env.GCS_ENV_FILE;

  if (!bucketName || !fileName) {
    logger.warn(
      'GCS_ENV_BUCKET or GCS_ENV_FILE not set. Skipping GCS env loading.',
    );
    // Attempt to load a local .env file as a fallback for local development
    const localEnvPath = path.resolve(__dirname, '..', '.env'); // Adjust path if needed
    if (fs.existsSync(localEnvPath)) {
      logger.log(`Loading environment variables from local ${localEnvPath}`);
      dotenv.config({ path: localEnvPath });
    } else {
      logger.warn(`Local .env file not found at ${localEnvPath}`);
    }
    return;
  }

  // Only attempt GCS download if running in production (or explicitly configured)
  if (process.env.NODE_ENV === 'production') {
    logger.log(
      `Attempting to download ${fileName} from GCS bucket ${bucketName}...`,
    );
    const storage = new Storage();
    const tempFilePath = path.join('/tmp', fileName); // Use /tmp directory in App Engine

    try {
      await storage.bucket(bucketName).file(fileName).download({
        destination: tempFilePath,
      });

      logger.log(`Successfully downloaded ${fileName} to ${tempFilePath}`);
      dotenv.config({ path: tempFilePath }); // Load the downloaded file
      logger.log(`Environment variables loaded from ${tempFilePath}`);
      fs.unlinkSync(tempFilePath); // Clean up the temporary file
    } catch (error) {
      logger.error(
        `Failed to download or load env file from GCS: ${error.message}`,
        error.stack,
      );
      // Decide if you want to exit or continue with potentially missing env vars
      // For critical env vars, exiting might be safer:
      // process.exit(1);
      throw new Error('Failed to load environment configuration from GCS.');
    }
  } else {
    logger.log(
      `NODE_ENV is not 'production' (${process.env.NODE_ENV}). Skipping GCS download.`,
    );
    // Optional: Load local .env for non-production environments
    const localEnvPath = path.resolve(__dirname, '..', '.env'); // Or .env.development etc.
    if (fs.existsSync(localEnvPath)) {
      logger.log(`Loading environment variables from local ${localEnvPath}`);
      dotenv.config({ path: localEnvPath });
    } else {
      logger.warn(`Local .env file not found at ${localEnvPath}`);
    }
  }
}

async function bootstrap() {
  if (process.env.NODE_ENV === 'production') {
    await loadEnvFromGCS();
  }

  const app = await NestFactory.create(AppModule, {
    // Optionally pass the logger instance to NestFactory
    logger:
      process.env.NODE_ENV === 'production'
        ? ['error', 'warn', 'log']
        : ['log', 'error', 'warn', 'debug', 'verbose'],
    rawBody: true,
  });

  // Log CORS origins for debugging
  const allowedOrigins = [
    'https://enrollio-fe-dot-enrollio-portal.uc.r.appspot.com',
    'https://enrollio-portal.uc.r.appspot.com',
    'https://portal.enrollio.ai',
    'http://portal.enrollio.ai',
    'http://localhost:5173',
    'http://localhost:5174',
    'https://enrollio-fe-dot-enrollio-portal-staging.uc.r.appspot.com',
    'https://enrollio-portal-staging.uc.r.appspot.com',
  ];

  app.enableCors({
    origin: (origin, callback) => {
      // Allow requests with no origin (like mobile apps or curl requests)
      if (!origin) return callback(null, true);
      if (allowedOrigins.indexOf(origin) !== -1) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS'));
      }
    },
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH', 'HEAD'],
    allowedHeaders: [
      'Accept',
      'Accept-Language',
      'Content-Language',
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'Origin',
      'Access-Control-Request-Method',
      'Access-Control-Request-Headers',
    ],
    exposedHeaders: [
      'Access-Control-Allow-Origin',
      'Access-Control-Allow-Headers',
      'Access-Control-Allow-Methods',
    ],
    maxAge: 3600,
    credentials: true,
    preflightContinue: false,
    optionsSuccessStatus: 200,
  });

  //log the url that is being called and the method
  app.use((req, res, next) => {
    logger.log(`${req.method} ${req.url}`);
    next();
  });

  const port = process.env.PORT || 3000;

  await app.listen(port);
  logger.log(`Application listening on port ${port}`);
}
bootstrap();
