import { PartialType } from '@nestjs/mapped-types';
import { CreateStudioDto } from './create-studio.dto';
import { IsBoolean, IsNumber, IsOptional, IsString } from 'class-validator';
import { PaymentProcessingMethod, ProrationMode } from 'src/stripe/type';

export class UpdateStudioDto extends PartialType(CreateStudioDto) {}

export class UpdatePaymentSettingsDto {
  @IsOptional()
  @IsString()
  paymentProvider: string;

  @IsOptional()
  @IsString()
  paymentProcessingMethod: PaymentProcessingMethod;

  @IsOptional()
  @IsString()
  prorationMode: ProrationMode;

  @IsOptional()
  @IsBoolean()
  isTransactionFeeEnabled: boolean;

  @IsOptional()
  @IsNumber()
  transactionFeePercentage: number;

  @IsOptional()
  @IsNumber()
  failedPaymentRetryCount: number;
}
