import { IsNotEmpty, <PERSON><PERSON><PERSON>al, IsString, IsNumber } from 'class-validator';

export class CreateStudioDto {
  @IsNotEmpty()
  @IsString()
  access_token: string;

  @IsNotEmpty()
  @IsString()
  token_type: string;

  @IsNotEmpty()
  @IsNumber()
  expires_in: number;

  @IsNotEmpty()
  @IsString()
  refresh_token: string;

  @IsNotEmpty()
  @IsString()
  scope: string;

  @IsNotEmpty()
  @IsString()
  userType: string;

  @IsNotEmpty()
  @IsString()
  companyId: string;

  @IsNotEmpty()
  @IsString()
  locationId: string;

  @IsNotEmpty()
  @IsString()
  userId: string;

  @IsString()
  subaccountName: string;

  @IsNotEmpty()
  logo: Buffer;

  @IsNotEmpty()
  @IsString()
  imageName: string;

  @IsNotEmpty()
  @IsString()
  imageUrl: string;

  @IsNotEmpty()
  image: Buffer;

  @IsNotEmpty()
  @IsN<PERSON>ber()
  failedPaymentRetryCount: number;
}

// export class UpdateStudioLogoDto {
//   @IsNotEmpty()
//   logo: Buffer;

//   @IsNotEmpty()
//   @IsString()
//   imageName: string;

//   @IsNotEmpty()
//   @IsString()
//   imageUrl: string;
// }
