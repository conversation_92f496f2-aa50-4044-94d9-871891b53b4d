import {
  forwardRef,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import {
  UpdatePaymentSettingsDto,
  UpdateStudioDto,
} from './dto/update-studio.dto';
import { GohighlevelService } from 'src/gohighlevel/gohighlevel.service';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CreateStudioDto } from './dto/create-studio.dto';
import {
  StudioSchema,
  Studio,
  StudioDocument,
} from 'src/database/schema/studio';
import { StudentsService } from 'src/students/students.service';
import { ParentsService } from 'src/parents/parents.service';
import { JwtService } from '@nestjs/jwt';
import { Enrollment } from 'src/database/schema/enrollment';
import { Student } from 'src/database/schema/student';
import { Request } from 'express';
import { Country } from 'src/database/schema/countries';
import { EmailTemplateService } from 'src/email-template/email-template.service';
import { Parent } from 'src/database/schema/parent';
import { GcpStorageService } from 'src/gcp-storage/gcp-storage.service';
import { Logger } from '@nestjs/common';

@Injectable()
export class StudiosService {
  private readonly logger = new Logger(StudiosService.name);
  constructor(
    private readonly ghlService: GohighlevelService,
    @InjectModel(Studio.name) private studioModel: Model<StudioDocument>,
    private readonly studentService: StudentsService,
    @Inject(forwardRef(() => ParentsService))
    private readonly parentService: ParentsService,
    private readonly jwtService: JwtService,
    @InjectModel('Enrollment')
    private readonly enrollmentModel: Model<Enrollment>,
    @InjectModel('Event') private readonly eventModel: Model<Event>,
    @InjectModel('Student') private readonly studentModel: Model<Student>,
    @InjectModel('Country') private readonly countryModel: Model<Country>,
    private readonly emailTemplateService: EmailTemplateService,
    @InjectModel('Parent') private readonly parentModel: Model<Parent>,
    private readonly gcpStorageService: GcpStorageService,
  ) {}
  async create(code, req: Request) {
    try {
      // Exchange code for token using GHL service
      let studioStructure: CreateStudioDto;
      try {
        studioStructure = await this.ghlService.exchangeCodeForToken(code);
      } catch (error) {
        console.log(error.message);
        throw new Error(error.message);
      }

      const existingStudio = await this.studioModel
        .findOne({ locationId: studioStructure.locationId })
        .exec();

      let studioId; // Variable to hold the studio ID for token generation

      if (existingStudio) {
        // Update existing studio with new tokens
        existingStudio.access_token = studioStructure.access_token;
        existingStudio.refresh_token = studioStructure.refresh_token;
        await existingStudio.save();
        studioId = existingStudio._id.toString();
        const emailTemplate = await this.emailTemplateService.findByStudioId(
          existingStudio._id as Types.ObjectId,
        );
        if (!emailTemplate) {
          await this.emailTemplateService.create(
            {
              studioId: existingStudio._id as Types.ObjectId,
              subject: 'Getting Onboarded',
              paragraph1:
                "<p>We're excited to have you on board 🎉.</p><p>To get started, please use the temporary password below to log in to your account.</p>",
              paragraph2:
                "For security reasons, we recommend changing your password after your first login. If you didn't request this, please contact support immediately.",
            },
            existingStudio._id as Types.ObjectId,
          );
        }
      } else {
        // Create a new studio
        const createdStudio = new this.studioModel(studioStructure);
        await createdStudio.save();
        studioId = createdStudio._id.toString(); // Use new studio ID
        await this.emailTemplateService.create(
          {
            studioId: createdStudio._id as Types.ObjectId,
            subject: 'Getting Onboarded',
            paragraph1:
              "<p>We're excited to have you on board 🎉.</p><p>To get started, please use the temporary password below to log in to your account.</p>",
            paragraph2:
              "For security reasons, we recommend changing your password after your first login. If you didn't request this, please contact support immediately.",
          },
          createdStudio._id as Types.ObjectId,
        );
      }

      // Generate JWT token using the studio ID
      const token = this.jwtService.sign(
        {
          locationId: studioId,
          userAgent: req.headers['user-agent'],
          ip: req.ip,
        },
        { secret: 'yourSecretKey**huy67541!1@#543%&^8', expiresIn: '10h' }, // set an appropriate expiration time
      );

      return { studio: studioStructure, token };
    } catch (error) {
      throw new InternalServerErrorException(
        `An error occurred while creating the studio. Please try again later. Error: ${error.message}`,
        error,
      );
    }
  }

  async createByLocationId(locationId, req: Request) {
    try {
      // Exchange code for token using GHL service
      const studioStructure: CreateStudioDto =
        await this.ghlService.refreshToken_ghl(locationId);

      const existingStudio = await this.studioModel
        .findOne({ locationId: studioStructure.locationId })
        .exec();

      let studioId; // Variable to hold the studio ID for token generation

      if (existingStudio) {
        // Update existing studio with new tokens
        existingStudio.access_token = studioStructure.access_token;
        existingStudio.refresh_token = studioStructure.refresh_token;
        await existingStudio.save();
        studioId = existingStudio._id.toString(); // Use existing studio ID
      } else {
        // Create a new studio
        const createdStudio = new this.studioModel(studioStructure);
        await createdStudio.save();
        studioId = createdStudio._id.toString(); // Use new studio ID
      }

      // Generate JWT token using the studio ID
      const token = this.jwtService.sign(
        {
          locationId: studioId,
          userAgent: req.headers['user-agent'],
          ip: req.ip,
        },
        {
          secret: 'yourSecretKey**huy67541!1@#543%&^8',
          expiresIn: '10h',
        },
      );

      let imageUrl = '';
      try {
        imageUrl = await this.gcpStorageService.getPublicImage(
          existingStudio._id.toString(),
          'studio-logo',
        );
      } catch (error) {
        this.logger.warn('Failed to get studio logo URL', {
          studioId: locationId,
          error: error.message,
        });
      }

      return { studio: studioStructure, token, imageUrl };
    } catch (error) {
      throw new InternalServerErrorException(
        'An error occurred while getting the studio.',
        error,
      );
    }
  }

  async findByLocationIdString(locationId) {
    const studio = await this.studioModel.findOne({ locationId }).lean().exec();
    if (!studio) {
      throw new NotFoundException(`Location with ID ${locationId} not found`);
    }
    return studio;
  }

  async findByLocationIdObjectId(id) {
    let studio;
    if (Types.ObjectId.isValid(id)) {
      studio = await this.studioModel
        .findOne({ _id: Types.ObjectId.createFromHexString(id) })
        .exec();
    } else {
      studio = await this.studioModel.findOne({ locationId: id }).exec();
    }
    if (!studio) {
      throw new NotFoundException(`Studio with ID ${id} not found`);
    }
    return studio;
  }

  async findByLocationId(locationId) {
    const studio = await this.studioModel.findOne({ _id: locationId }).exec();
    if (!studio) {
      throw new NotFoundException(`Location with ID ${locationId} not found`);
    }
    return studio;
  }

  async findByLocationIdWithImage(locationId) {
    const studio = await this.studioModel.findOne({ _id: locationId }).exec();
    if (!studio) {
      throw new NotFoundException(`Location with ID ${locationId} not found`);
    }
    let imageUrl = '';
    try {
      imageUrl = await this.gcpStorageService.getPublicImage(
        studio._id.toString(),
        'studio-logo',
      );
    } catch (error) {
      this.logger.warn('Failed to get studio logo URL', {
        studioId: studio._id.toString(),
        error: error.message,
      });
    }
    return { studio, imageUrl };
  }

  async findOne(id: string): Promise<StudioDocument> {
    try {
      const studio = await this.studioModel.findById(id).exec();
      if (!studio) {
        throw new NotFoundException(`Studio with ID ${id} not found`);
      }
      return studio;
    } catch (error) {
      throw new InternalServerErrorException(
        'An error occurred while retrieving the studio.',
        error,
      );
    }
  }

  async update(id: string, updateStudioDto: UpdateStudioDto) {
    // Create an update object to collect all changes
    const updateData: any = {};

    // Add failedPaymentRetryCount to the update if provided
    if (updateStudioDto.failedPaymentRetryCount !== undefined) {
      updateData.failedPaymentRetryCount =
        updateStudioDto.failedPaymentRetryCount;
    }

    // Apply any other fields from updateStudioDto that should be directly updated
    // Add other direct database updates here as needed

    // Update the studio with all collected changes
    let studio;
    if (Object.keys(updateData).length > 0) {
      studio = await this.studioModel.findByIdAndUpdate(id, updateData, {
        new: true,
      });
    } else {
      studio = await this.studioModel.findById(id);
    }

    if (!studio) {
      throw new NotFoundException(`Studio with ID ${id} not found`);
    }

    // Handle image upload if present
    let imageUrl = '';
    if (updateStudioDto.image) {
      imageUrl = await this.gcpStorageService.uploadFileToGCP(
        undefined,
        updateStudioDto.image,
        updateStudioDto.imageName,
        id,
        'studio-logo',
      );

      // Return both the updated studio and the image URL
      return { studio, imageUrl };
    }

    // If no image was uploaded, just return the updated studio
    return studio;
  }

  remove(id: number) {
    return `This action removes a #${id} studio`;
  }

  async updateStudentBasicMedicalInfo(id, data) {
    return await this.studentService.updateBasicInfo(id, data);
  }

  async updateParentContact(id, data) {
    const studentDetails = await this.studentService.findOne(id);
    const parentId = studentDetails.parentId.toString();
    return await this.parentService.updateContactDetails(parentId, data);
  }

  async updateEmergencyContact(id, data) {
    const studentDetails = await this.studentService.findOne(id);
    const parentId = studentDetails.parentId.toString();
    return await this.parentService.updateEmergencyContact(parentId, data);
  }

  async updateParentAllDetails(id, data, studioId) {
    const studentDetails = await this.studentService.findOne(id);
    const parentId = studentDetails.parentId.toString();
    return await this.parentService.update(parentId, data, studioId);
  }

  async getParentFromStudio(id) {
    const studentDetails = await this.studentService.findOne(id);
    const parentId = studentDetails.parentId.toString();
    return await this.parentService.findOne(parentId);
  }

  async getStudioData(studioId_string: string) {
    try {
      const studioId = Types.ObjectId.createFromHexString(studioId_string);
      // Use Promise.all to run the queries in parallel for performance
      const [classCount, eventCount, studentCount] = await Promise.all([
        this.enrollmentModel.countDocuments({
          studio: studioId,
          isDeleted: false,
        }),
        this.eventModel.countDocuments({ studio: studioId, isDeleted: false }),
        this.studentModel.countDocuments({ studioId }),
      ]);

      // Return the aggregated counts
      return {
        totalClasses: classCount,
        totalEvents: eventCount,
        totalStudents: studentCount,
      };
    } catch (error) {
      throw new InternalServerErrorException(
        'Error fetching studio data',
        error,
      );
    }
  }

  async getCountries() {
    const countries = await this.countryModel
      .find()
      .select('_id name iso2')
      .exec();
    return countries.map((country) => ({
      _id: country._id,
      name: country.name,
      iso2: country.iso2,
    }));
  }

  async getStatesByCountry(country) {
    const countryDoc = await this.countryModel
      .findOne({ name: country })
      .exec();
    if (!countryDoc || !countryDoc.states) {
      throw new NotFoundException(`States not found for country: ${country}`);
    }
    return countryDoc.states.map((state) => ({
      id: state.id,
      name: state.name,
    }));
  }

  async findAllBasic() {
    const studios = await this.studioModel
      .find()
      .select('locationId subaccountName')
      .exec();

    const analyticsPromises = studios.map(async (studio) => {
      const [enrollmentsCount, eventsCount, studentsCount, parentsCount] =
        await Promise.all([
          this.enrollmentModel.countDocuments({ studio: studio._id }),
          this.eventModel.countDocuments({ studio: studio._id }),
          this.studentModel.countDocuments({ studioId: studio._id }),
          this.parentModel.countDocuments({ studioId: studio._id }),
        ]);

      return {
        locationId: studio.locationId,
        subaccountName: studio.subaccountName,
        classes: enrollmentsCount,
        events: eventsCount,
        students: studentsCount,
        parents: parentsCount,
      };
    });

    return Promise.all(analyticsPromises);
  }

  async updatePaymentSettings(
    studioId,
    updatePaymentSettingsDto: UpdatePaymentSettingsDto,
  ) {
    const studio = await this.studioModel.findByIdAndUpdate(
      studioId,
      updatePaymentSettingsDto,
      { new: true },
    );
    return studio;
  }

  async fetchDefaultPaymentProvider(studioId) {
    const studio = await this.studioModel.findById(studioId);
    return {
      paymentProvider: studio.paymentProvider,
    };
  }
}
