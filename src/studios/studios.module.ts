import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { StudiosService } from './studios.service';
import { StudiosController } from './studios.controller';
import { GohighlevelModule } from 'src/gohighlevel/gohighlevel.module';
import { StudioSchema, Studio } from 'src/database/schema/studio';
import { StudentsModule } from 'src/students/students.module';
import { ParentsModule } from 'src/parents/parents.module';
import { JwtModule } from '@nestjs/jwt';
import { Enrollment, EnrollmentSchema } from 'src/database/schema/enrollment';
import { Event, EventSchema } from 'src/database/schema/event';
import { Student, StudentSchema } from 'src/database/schema/student';
import { Country } from 'src/database/schema/countries';
import { CountrySchema } from 'src/database/schema/countries';
import { EmailTemplateModule } from 'src/email-template/email-template.module';
import { Parent, ParentSchema } from 'src/database/schema/parent';
import { GcpStorageModule } from 'src/gcp-storage/gcp-storage.module';

@Module({
  imports: [
    GohighlevelModule,
    MongooseModule.forFeature([{ name: Studio.name, schema: StudioSchema }]),
    MongooseModule.forFeature([
      { name: Enrollment.name, schema: EnrollmentSchema },
    ]),
    MongooseModule.forFeature([{ name: Event.name, schema: EventSchema }]),
    MongooseModule.forFeature([{ name: Student.name, schema: StudentSchema }]),
    MongooseModule.forFeature([{ name: Country.name, schema: CountrySchema }]),
    StudentsModule,
    forwardRef(() => ParentsModule),
    JwtModule,
    EmailTemplateModule,
    MongooseModule.forFeature([{ name: Parent.name, schema: ParentSchema }]),
    GcpStorageModule,
  ],
  controllers: [StudiosController],
  providers: [StudiosService],
  exports: [StudiosService],
})
export class StudiosModule {}
