import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  BadRequestException,
  NotFoundException,
  Res,
  UseGuards,
  Req,
  UseInterceptors,
  UploadedFile,
  InternalServerErrorException,
  Put,
} from '@nestjs/common';
import { StudiosService } from './studios.service';
import { CreateStudioDto } from './dto/create-studio.dto';
import {
  UpdatePaymentSettingsDto,
  UpdateStudioDto,
} from './dto/update-studio.dto';
import { request, response, Response } from 'express';
import { UpdateStudentBasicInfoDto } from 'src/students/dto/studentBasicInfoUpdateDto';
import { UpdateParentContactDto } from 'src/parents/dto/udpate-parent-contactdto';
import {
  EmergencyContactDto,
  UpdateParentDto,
} from 'src/parents/dto/update-parent.dto';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { Request } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { Logger } from '@nestjs/common';

@Controller('studios')
export class StudiosController {
  private readonly logger = new Logger(StudiosController.name);

  constructor(private readonly studiosService: StudiosService) {}

  @Post('/auth')
  async create(
    @Body('code') code: string,
    @Res() response: Response,
    @Req() request: Request,
  ) {
    const { studio, token } = await this.studiosService.create(code, request);
    return response.status(201).json({ studio, token });
  }

  @Get('location')
  @UseGuards(JwtAuthGuard)
  async findAll(@Req() request: Request, @Res() response: Response) {
    const locationId = request['locationId'];
    const locationExists =
      await this.studiosService.findByLocationId(locationId);
    return response.status(200).json(locationExists);
  }

  @Get('location/with-image')
  @UseGuards(JwtAuthGuard)
  async findAllWithImage(@Req() request: Request, @Res() response: Response) {
    try {
      const locationId = request['locationId'];
      const { studio, imageUrl } =
        await this.studiosService.findByLocationIdWithImage(locationId);

      return response.status(200).json({
        ...studio.toObject(),
        imageUrl,
      });
    } catch (error) {
      this.logger.error('Failed to fetch studio with image', {
        error: error.message,
        locationId: request['locationId'],
      });
      return response.status(500).json({
        message: 'Failed to fetch studio information',
      });
    }
  }
  @Get(':id')
  @UseGuards(JwtAuthGuard)
  findOne(@Param('id') id: string, @Req() request: Request) {
    const locationId = request['locationId'];
    return this.studiosService.findOne(locationId);
  }

  @Patch('update/studio')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(FileInterceptor('file'))
  async update(
    @Req() request: Request,
    @Res() response: Response,
    @UploadedFile() file: Express.Multer.File,
    @Body('payload') payload: string,
    @Body('fileType') fileType: 'file' | 'url',
    @Body('fileUrl') fileUrl: string,
  ) {
    const studioId = request['locationId'];
    try {
      this.logger.log('Processing studio update request', {
        studioId,
        hasFile: !!file,
        fileType,
        hasFileUrl: !!fileUrl,
      });

      // const updateStudioDto: UpdateStudioDto = JSON.parse(payload);
      const updateStudioDto: UpdateStudioDto = {
        image: file ? file.buffer : undefined,
        imageName: file ? file.originalname : undefined,
        imageUrl: fileUrl,
      };

      if (file && fileType === 'file') {
        this.logger.debug('Processing file upload for studio update', {
          studioId,
          fileName: file.originalname,
          fileSize: file.size,
        });

        updateStudioDto.image = file.buffer;
        updateStudioDto.imageName = file.originalname;
      } else if (fileType === 'url') {
        this.logger.debug('Processing URL image for studio update', {
          studioId,
          hasUrl: !!fileUrl,
        });

        updateStudioDto.imageUrl = fileUrl;
      }

      const imageUrl = await this.studiosService.update(
        studioId,
        updateStudioDto,
      );
      return response.status(200).json({ imageUrl });
    } catch (error) {
      this.logger.error('Failed to process studio update request', {
        error: error.message,
        stack: error.stack,
        studioId,
        fileType,
        hasFile: !!file,
      });

      if (error instanceof SyntaxError) {
        throw new BadRequestException('Invalid payload format');
      }
      throw new InternalServerErrorException('Failed to update studio');
    }
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.studiosService.remove(+id);
  }

  @Post('update/student/:id')
  async updateStudentBasicMedicalInfo(
    @Param('id') id: string,
    @Body() updateStudentDto: UpdateStudentBasicInfoDto,
  ) {
    return await this.studiosService.updateStudentBasicMedicalInfo(
      id,
      updateStudentDto,
    );
  }

  @Post('update/parent/contact/:id')
  async updateParentContact(
    @Param('id') id: string,
    @Body() updateParentContactDto: UpdateParentDto,
  ) {
    return await this.studiosService.updateParentContact(
      id,
      updateParentContactDto,
    );
  }

  @Post('update/parent/emergencyContact/:id')
  async updateEmergencyContact(
    @Param('id') id: string,
    @Body() updateEmergencyContactDto: EmergencyContactDto,
  ) {
    if (!Array.isArray(updateEmergencyContactDto)) {
      throw new BadRequestException(
        'Payload must be an array of emergency contact objects.',
      );
    }
    return await this.studiosService.updateEmergencyContact(
      id,
      updateEmergencyContactDto,
    );
  }

  @Get('get/parent/:id')
  async getParentFromStudio(@Param('id') id: string) {
    return await this.studiosService.getParentFromStudio(id);
  }

  @Get('data/all')
  @UseGuards(JwtAuthGuard)
  async getStudioData(@Req() request: Request, @Res() response: Response) {
    try {
      const studioId = request['locationId'];
      const studioData = await this.studiosService.getStudioData(studioId);
      return response.status(200).json(studioData);
    } catch (error) {
      return response.status(500).json({
        message: 'Error fetching studio data',
        error: error.message,
      });
    }
  }

  @Get('dashboard/location')
  async ghlLoginStudio(
    @Query('locationId') locationId: string,
    @Res() response: Response,
    @Req() request: Request,
  ) {
    const { studio, token, imageUrl } =
      await this.studiosService.createByLocationId(locationId, request);
    return response.status(201).json({ studio, token, imageUrl });
  }

  @Get('countries/all')
  async getCountries(@Req() request: Request, @Res() response: Response) {
    return response.status(201).json(await this.studiosService.getCountries());
  }

  @Get('states/all/:country')
  async getStatesByCountry(
    @Param('country') country: string,
    @Res() response: Response,
  ) {
    return response
      .status(201)
      .json(await this.studiosService.getStatesByCountry(country));
  }

  @Patch('update/parent/all-details/:id')
  @UseGuards(JwtAuthGuard)
  async updateParentDetails(
    @Param('id') id: string,
    @Body() updateParent: UpdateParentDto,
    @Req() request: Request,
  ) {
    // if (!Array.isArray(updateEmergencyContactDto)) {
    //   throw new BadRequestException(
    //     'Payload must be an array of emergency contact objects.'
    //   );
    // }
    const studioId = request['locationId'];
    return await this.studiosService.updateParentAllDetails(
      id,
      updateParent,
      studioId,
    );
  }

  @Get('all/basic')
  async getAllStudiosBasic(@Res() response: Response) {
    const studios = await this.studiosService.findAllBasic();
    return response.status(200).json(studios);
  }

  @Put('payment-settings')
  @UseGuards(JwtAuthGuard)
  async updatePaymentSettings(
    @Body() updatePaymentSettingsDto: UpdatePaymentSettingsDto,
    @Req() request: Request,
  ) {
    const studioId = request['locationId'];
    return await this.studiosService.updatePaymentSettings(
      studioId,
      updatePaymentSettingsDto,
    );
  }

  @Get('default-payment-provider')
  async fetchDefaultPaymentProvider(
    @Req() request: Request,
    @Res() response: Response,
  ) {
    const studioId = request['locationId'];
    const defaultPaymentProvider =
      await this.studiosService.fetchDefaultPaymentProvider(studioId);
    return response.status(200).json(defaultPaymentProvider);
  }
}
