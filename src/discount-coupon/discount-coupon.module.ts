import { forwardRef, Module } from '@nestjs/common';
import { DiscountCouponService } from './discount-coupon.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  DiscountCoupon,
  DiscountCouponSchema,
} from 'src/database/schema/discountCoupon';
import { Discount, DiscountSchema } from 'src/database/schema/discount';
import { DiscountModule } from 'src/discount/discount.module';
import { AuthModule } from 'src/auth/auth.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: DiscountCoupon.name, schema: DiscountCouponSchema },
      { name: Discount.name, schema: DiscountSchema },
    ]),
    AuthModule,
    forwardRef(() => DiscountModule),
  ],
  controllers: [],
  providers: [DiscountCouponService],
  exports: [DiscountCouponService],
})
export class DiscountCouponModule {}
