import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { DiscountCoupon } from '../database/schema/discountCoupon';
import { Discount } from 'src/database/schema/discount';
import { DiscountService } from 'src/discount/discount.service';

@Injectable()
export class DiscountCouponService {
  constructor(
    @InjectModel(DiscountCoupon.name)
    private readonly discountCouponModel: Model<DiscountCoupon>,
    @Inject()
    private readonly discountService: DiscountService,
  ) {}

  async create(createDto: Partial<DiscountCoupon>): Promise<DiscountCoupon> {
    const created = new this.discountCouponModel(createDto);
    return created.save();
  }

  async findAll(studioId: string): Promise<DiscountCoupon[]> {
    return this.discountCouponModel.find({ studioId }).exec();
  }

  async findOne(id: string): Promise<DiscountCoupon> {
    return this.discountCouponModel.findById(id).exec();
  }

  async update(
    id: string,
    updateDto: Partial<DiscountCoupon>,
  ): Promise<DiscountCoupon> {
    return this.discountCouponModel
      .findByIdAndUpdate(id, updateDto, { new: true })
      .exec();
  }

  async remove(id: string): Promise<DiscountCoupon> {
    return this.discountCouponModel.findByIdAndDelete(id).exec();
  }

  async createCoupon(params: {
    type: 'percentage' | 'fixed';
    value: number;
    name: string;
    studioId: Types.ObjectId;
    category: 'enrollment' | 'scholarship';
  }) {
    const discount = await this.discountService.findOne(
      params.studioId.toString(),
    );

    const existingCoupon = await this.discountCouponModel.findOne({
      studioId: params.studioId,
      value: params.value,
      type: params.type,
      duration: discount.discountRules === 'first-month' ? 'once' : 'forever',
      category: params.category,
    });

    if (existingCoupon) {
      return existingCoupon;
    }

    return this.create({
      ...params,
      isActive: true,
      duration: discount.discountRules === 'first-month' ? 'once' : 'forever',
      category: params.category,
    });
  }
}
