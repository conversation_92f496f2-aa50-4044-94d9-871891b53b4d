import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { EnrollmentService } from './enrollment.service';
import { EnrollmentController } from './enrollment.controller';
import { Enrollment, EnrollmentSchema } from 'src/database/schema/enrollment';
import { CustomForm, CustomFormSchema } from 'src/database/schema/customForm';
import { GohighlevelModule } from 'src/gohighlevel/gohighlevel.module';
import { AuthModule } from 'src/auth/auth.module';
import { StudiosModule } from 'src/studios/studios.module';
import { PoliciesModule } from 'src/policies/policies.module';
import { StripeModule } from 'src/stripe/stripe.module';
import { BullModule } from '@nestjs/bullmq';
import { Proration } from 'src/database/schema/prorations';
import { ProrationSchema } from 'src/database/schema/prorations';
import { CurrencyModule } from 'src/currency/currency.module';
import { Session, SessionSchema } from 'src/database/schema/session';
import { StudentsModule } from 'src/students/students.module';
import { Tag, TagSchema } from 'src/database/schema/tags';
import { Event, EventSchema } from 'src/database/schema/event';
import { CreateProductProcessor } from './processors/create_product';
import { CreateProductPriceProcessor } from './processors/create_price_of_product';
import { CreateCalendarProcessor } from './processors/create_calendar';
import { BlockCalendarSlotsProcessor } from './processors/block_calendar';
import { GcpStorageModule } from 'src/gcp-storage/gcp-storage.module';
import { PaymentTransaction } from 'src/database/schema/paymentTransaction';
import { PaymentTransactionSchema } from 'src/database/schema/paymentTransaction';
import {
  Subscription,
  SubscriptionSchema,
} from 'src/database/schema/subscription';
import {
  SubscriptionInvoice,
  SubscriptionInvoiceSchema,
} from 'src/database/schema/subscriptionInvoice';
import {
  DiscountCoupon,
  DiscountCouponSchema,
} from 'src/database/schema/discountCoupon';
import { SubscriptionModule } from 'src/subscription/subscription.module';
import { SubscriptionInvoiceModule } from 'src/subscription-invoice/subscription-invoice.module';
import { DiscountCouponModule } from 'src/discount-coupon/discount-coupon.module';
import { ParentsModule } from 'src/parents/parents.module';
import { RedisModule } from 'src/redis/redis.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Enrollment.name, schema: EnrollmentSchema },
      { name: CustomForm.name, schema: CustomFormSchema },
      { name: Proration.name, schema: ProrationSchema },
      { name: Session.name, schema: SessionSchema },
      { name: Tag.name, schema: TagSchema },
      { name: Event.name, schema: EventSchema },
      { name: PaymentTransaction.name, schema: PaymentTransactionSchema },
      { name: Subscription.name, schema: SubscriptionSchema },
      { name: SubscriptionInvoice.name, schema: SubscriptionInvoiceSchema },
      { name: DiscountCoupon.name, schema: DiscountCouponSchema },
    ]),
    RedisModule,
    GohighlevelModule,
    AuthModule,
    forwardRef(() => SubscriptionModule),
    forwardRef(() => SubscriptionInvoiceModule),
    forwardRef(() => DiscountCouponModule),
    forwardRef(() => StudiosModule),
    forwardRef(() => PoliciesModule),
    forwardRef(() => StripeModule),
    forwardRef(() => StudentsModule),
    forwardRef(() => ParentsModule),
    BullModule.registerQueue(
      {
        name: 'enrollment-create-product',
        defaultJobOptions: {
          attempts: 3,
          backoff: { type: 'exponential', delay: 2000 },
          removeOnComplete: 100,
          removeOnFail: 50,
          delay: 1000,
        },
      },
      {
        name: 'enrollment-create-product-price',
        defaultJobOptions: {
          attempts: 3,
          backoff: { type: 'exponential', delay: 2000 },
          removeOnComplete: 100,
          removeOnFail: 50,
          delay: 1000,
        },
      },
      {
        name: 'enrollment-create-calendar',
        defaultJobOptions: {
          attempts: 3,
          backoff: { type: 'exponential', delay: 2000 },
          removeOnComplete: 100,
          removeOnFail: 50,
          delay: 1000,
        },
      },
      {
        name: 'enrollment-block-calendar-slots',
        defaultJobOptions: {
          attempts: 3,
          backoff: { type: 'exponential', delay: 2000 },
          removeOnComplete: 100,
          removeOnFail: 50,
          delay: 1000,
        },
      },
    ),
    forwardRef(() => CurrencyModule),
    CurrencyModule,
    forwardRef(() => GcpStorageModule),
  ],
  controllers: [EnrollmentController],
  providers: [
    EnrollmentService,
    CreateProductProcessor,
    CreateProductPriceProcessor,
    CreateCalendarProcessor,
    BlockCalendarSlotsProcessor,
  ],
  exports: [EnrollmentService],
})
export class EnrollmentModule {}
