import {
  calculateSubscriptionStartDate,
  convertTo24Hour,
} from './../utils/helperFunction';
import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { InjectQueue } from '@nestjs/bullmq';
import { Model, Types } from 'mongoose';
import { CreateEnrollmentDto } from './dto/create-enrollment.dto';
import { Enrollment } from 'src/database/schema/enrollment';
import { GohighlevelService } from 'src/gohighlevel/gohighlevel.service';
import { StudiosService } from 'src/studios/studios.service';
import {
  CalendarDto,
  CalendarType,
  MeetingLocationType,
  RecurrenceFrequency,
  RecurrenceBookingOption,
  CalendarDayArrayIndexMap,
} from 'src/gohighlevel/dto/createCalendarDto';
import {
  convertTo24HourTime,
  createBodyToCreatePriceForAProductInGHL,
  calculateBlockDateRanges,
  calculateSlotDuration,
  formatToISODateTime,
  formatDateTimeAvailability,
  JobNames,
} from 'src/utils/helperFunction';
import { PoliciesService } from 'src/policies/policies.service';
import { UpdateEnrollmentDto } from './dto/update-enrollment.dto';
import { CustomForm } from 'src/database/schema/customForm';
import { Proration } from 'src/database/schema/prorations';
import { formatInTimeZone } from 'date-fns-tz';
import { parseISO } from 'date-fns';
import { formatToISODate } from '../utils/helperFunction';
import { Queue, QueueEvents, Job, delay } from 'bullmq';
import { CurrencyService } from 'src/currency/currency.service';
import { Session } from 'src/database/schema/session';
import { StudentsService } from 'src/students/students.service';
import { Tag } from 'src/database/schema/tags';
import { formatTimeToDate, formatDateTime } from 'src/utils/datetime';
import { GcpStorageService } from 'src/gcp-storage/gcp-storage.service';
import { PaymentTransaction } from 'src/database/schema/paymentTransaction';
import {
  PaymentTransactionType,
  PaymentTransactionStatus,
  InvoiceStatus,
  PaymentMethod,
  PaymentProcessingMethod,
  SubscriptionStatus,
} from 'src/stripe/type';
import { Event } from 'src/database/schema/event';
import type { Redis } from 'ioredis';
import { generateInvoiceDates } from 'src/utils/invoiceDates';
import { Subscription } from 'src/database/schema/subscription';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import { SubscriptionInvoiceService } from 'src/subscription-invoice/subscription-invoice.service';
import { DiscountCoupon } from 'src/database/schema/discountCoupon';
import { DiscountCouponService } from 'src/discount-coupon/discount-coupon.service';
import { StripeCommonService } from 'src/stripe/services/stripe-common.service';
import { getGcpIpAddress } from 'src/utils/helperFunction';
import { ParentsService } from 'src/parents/parents.service';
import { supportsUSBankAccount } from 'src/utils/helperFunction';

@Injectable()
export class EnrollmentService {
  private readonly logger = new Logger(EnrollmentService.name);
  private productQueueEvents: QueueEvents;
  private productPriceQueueEvents: QueueEvents;
  private calendarQueueEvents: QueueEvents;
  private blockCalendarQueueEvents: QueueEvents;
  constructor(
    @InjectModel(Enrollment.name) private enrollmentModel: Model<Enrollment>,
    @InjectModel(Proration.name) private prorationModel: Model<Proration>,
    @InjectModel(Event.name) private eventModel: Model<Event>,
    @InjectModel(Subscription.name)
    private subscriptionModel: Model<Subscription>,
    @InjectModel(SubscriptionInvoice.name)
    private subscriptionInvoiceModel: Model<SubscriptionInvoice>,
    @InjectModel(DiscountCoupon.name)
    private discountCouponModel: Model<DiscountCoupon>,
    private readonly ghlService: GohighlevelService,
    @Inject(forwardRef(() => StudiosService))
    private readonly studioService: StudiosService,
    @Inject(forwardRef(() => PoliciesService))
    private readonly policyService: PoliciesService,
    @Inject(forwardRef(() => StudentsService))
    private readonly studentService: StudentsService,
    @InjectModel(CustomForm.name) private customFormModel: Model<CustomForm>,
    @Inject(forwardRef(() => CurrencyService))
    private readonly currencyService: CurrencyService,
    @InjectQueue('enrollment-create-product')
    private productQueue: Queue,
    @InjectQueue('enrollment-create-product-price')
    private productPriceQueue: Queue,
    @InjectQueue('enrollment-create-calendar')
    private calendarQueue: Queue,
    @InjectQueue('enrollment-block-calendar-slots')
    private blockCalendarQueue: Queue,
    @InjectModel(Session.name) private sessionModel: Model<Session>,
    @InjectModel(Tag.name) private tagModel: Model<Tag>,
    @InjectModel(PaymentTransaction.name)
    private paymentTransactionModel: Model<PaymentTransaction>,
    @Inject(forwardRef(() => GcpStorageService))
    private readonly gcpStorageService: GcpStorageService,
    @Inject(forwardRef(() => SubscriptionService))
    private readonly subscriptionService: SubscriptionService,
    @Inject(forwardRef(() => SubscriptionInvoiceService))
    private readonly subscriptionInvoiceService: SubscriptionInvoiceService,
    @Inject(forwardRef(() => DiscountCouponService))
    private readonly discountCouponService: DiscountCouponService,
    @Inject('REDIS_CLIENT') private readonly redis: Redis,
    @Inject(forwardRef(() => StripeCommonService))
    private readonly stripeCommonService: StripeCommonService,
    @Inject(forwardRef(() => ParentsService))
    private readonly parentsService: ParentsService,
  ) {
    // Create QueueEvents instances using the global Redis client
    this.productQueueEvents = new QueueEvents('enrollment-create-product', {
      connection: this.redis,
    });
    this.productPriceQueueEvents = new QueueEvents(
      'enrollment-create-product-price',
      { connection: this.redis },
    );
    this.calendarQueueEvents = new QueueEvents('enrollment-create-calendar', {
      connection: this.redis,
    });
    // this.blockCalendarQueueEvents = new QueueEvents('enrollment-block-calendar-slots', { connection: this.redis });
  }

  async createProduct(
    newEnrollmentData,
    createEnrollmentDto,
    locationId_ghl,
    studio,
  ) {
    let ghlProductId_ghl = '';
    try {
      console.log('Input Data:', {
        title: newEnrollmentData.title,
        locationId: studio.locationId,
        description: createEnrollmentDto.description,
      });

      const ghl_product_details = await this.ghlService.createProduct(
        newEnrollmentData.title,
        studio.locationId,
        createEnrollmentDto.description,
      );
      console.log('GHL Product Response:', ghl_product_details);

      ghlProductId_ghl = ghl_product_details._id;
      console.log('Studio ID for currency:', studio._id);

      //TODO: move this to studio collection, unwanted call and unnecessary collection call
      const currency = await this.currencyService.findByStudioId(studio._id);
      console.log('Currency Data:', currency);

      const price_body = await createBodyToCreatePriceForAProductInGHL(
        createEnrollmentDto,
        studio.locationId,
        'class',
        ghlProductId_ghl,
        this.prorationModel,
        currency.name,
      );
      console.log('Price Body:', price_body);

      const priceResponse = await this.ghlService.createPriceForAProduct(
        studio.locationId,
        ghlProductId_ghl,
        price_body,
      );
      console.log('Price Creation Response:', priceResponse);
    } catch (error) {
      console.error('Error Details:', {
        message: error.message,
        stack: error.stack,
        data: {
          newEnrollmentData,
          studioId: studio._id,
          locationId: studio.locationId,
        },
      });
    }
    return ghlProductId_ghl;
  }

  async createCalendar(
    newEnrollmentData,
    createEnrollmentDto,
    locationId_ghl,
    studio,
  ) {
    try {
      const slotDuration = calculateSlotDuration(
        newEnrollmentData.availability[0].startTime,
        newEnrollmentData.availability[0].endTime,
      );
      const dayIndexes = newEnrollmentData.days.map(
        (day) =>
          CalendarDayArrayIndexMap[
            day as keyof typeof CalendarDayArrayIndexMap
          ],
      );

      const openHours = newEnrollmentData.availability.map((day) => {
        const { hour: openHour, minute: openMinute } = convertTo24HourTime(
          day.startTime,
        );
        const { hour: closeHour, minute: closeMinute } = convertTo24HourTime(
          day.endTime,
        );
        return {
          daysOfTheWeek: [
            CalendarDayArrayIndexMap[
              day.day as keyof typeof CalendarDayArrayIndexMap
            ],
          ], // Map day to index
          hours: [
            {
              openHour,
              openMinute,
              closeHour,
              closeMinute,
            },
          ],
        };
      });

      // Define CalendarDto object with mapped and hardcoded values
      const createCalendarDetails: Partial<CalendarDto> = {
        description: newEnrollmentData.description,
        calendarType: CalendarType.EVENT,
        appoinmentPerSlot: newEnrollmentData.maxSize,
        allowReschedule: false,
        enableRecurring: false,
        teamMembers: [
          {
            userId: studio.userId,
            meetingLocation: newEnrollmentData.location
              ? newEnrollmentData.location.toString()
              : null,
            meetingLocationType: MeetingLocationType.CUSTOM,
          },
        ],
        openHours: openHours,
        slotDuration: slotDuration,
        slotDurationUnit: 'mins',
        widgetSlug: `${Math.floor(100000 + Math.random() * 900000)}-${newEnrollmentData.title.replace(/[^a-zA-Z0-9\-]/g, '-')}`,
      };

      const ghl_calendar_id = await this.ghlService.createCalendar(
        newEnrollmentData.title,
        studio.locationId,
        createCalendarDetails,
      );
      return ghl_calendar_id;
    } catch (error) {
      console.error('error creating calendar in ghl:', error);
    }
  }

  async create(
    createEnrollmentDto: CreateEnrollmentDto,
    locationId: string,
  ): Promise<Partial<Enrollment> & { imageUrl?: string }> {
    try {
      const studio = await this.studioService.findByLocationId(
        createEnrollmentDto.studio,
      );

      const locationId_ghl = studio.locationId;

      //fetch timezone from GHL
      const locationDetails =
        await this.ghlService.getLocationDetails(locationId_ghl);
      const timezone = locationDetails.location.timezone;

      try {
        (createEnrollmentDto.policyGroup as any) =
          createEnrollmentDto.policyGroup.map((policyId: string) =>
            Types.ObjectId.createFromHexString(policyId),
          );
        if (createEnrollmentDto.tags && createEnrollmentDto.tags.length > 0) {
          (createEnrollmentDto.tags as any) = createEnrollmentDto.tags.map(
            (tagId: string) => Types.ObjectId.createFromHexString(tagId),
          );
        } else {
          (createEnrollmentDto.tags as any) = [];
        }
        if (
          createEnrollmentDto.instructor &&
          createEnrollmentDto.instructor.length > 0
        ) {
          (createEnrollmentDto.instructor as any) =
            createEnrollmentDto.instructor.map((instructorId: string) =>
              Types.ObjectId.createFromHexString(instructorId),
            );
        } else {
          (createEnrollmentDto.instructor as any) = [];
        }
        if (createEnrollmentDto.group && createEnrollmentDto.group.length > 0) {
          (createEnrollmentDto.group as any) = createEnrollmentDto.group.map(
            (groupId: string) => Types.ObjectId.createFromHexString(groupId),
          );
        } else {
          (createEnrollmentDto.group as any) = [];
        }
        if (createEnrollmentDto.room == '') {
          (createEnrollmentDto.room as any) = null;
        } else if (
          createEnrollmentDto.room &&
          createEnrollmentDto.room !== ''
        ) {
          (createEnrollmentDto.room as any) =
            Types.ObjectId.createFromHexString(createEnrollmentDto.room);
        }
        if (createEnrollmentDto.session == '') {
          (createEnrollmentDto.session as any) = null;
        } else if (
          createEnrollmentDto.session &&
          createEnrollmentDto.session !== ''
        ) {
          (createEnrollmentDto.session as any) =
            Types.ObjectId.createFromHexString(createEnrollmentDto.session);
        }
        if (
          createEnrollmentDto.location &&
          createEnrollmentDto.location !== ''
        ) {
          (createEnrollmentDto.location as any) =
            Types.ObjectId.createFromHexString(createEnrollmentDto.location);
        } else if (
          createEnrollmentDto.location &&
          createEnrollmentDto.location == ''
        ) {
          createEnrollmentDto.location = null;
        }
      } catch (error) {
        console.error('error converting string ids to object ids', error);
      }
      const newEnrollment = new this.enrollmentModel(createEnrollmentDto);
      const newEnrollmentData = await newEnrollment.save();
      try {
        const newEnrolledDataId = Types.ObjectId.createFromHexString(
          newEnrollmentData._id.toString(),
        );
        await Promise.all(
          createEnrollmentDto.policyGroup.map(async (policyId) => {
            const policy = await this.policyService.findOne(policyId);
            if (!policy.class.includes(newEnrolledDataId)) {
              policy.class.push(newEnrolledDataId);
              await policy.save();
            }
          }),
        );
      } catch (error) {
        console.error('error updating policies with enrollment id:', error);
        this.logger.error({
          error: error,
          message: 'error updating policies with enrollment id',
          studioId: locationId,
          enrollmentId: newEnrollmentData._id.toString(),
        });
      }

      let ghl_product_id = '';
      let ghl_calendar_id = '';
      // Try to create GHL product
      try {
        ghl_product_id = await this.createProduct(
          newEnrollmentData,
          createEnrollmentDto,
          locationId_ghl,
          studio,
        );
        newEnrollmentData.productId_ghl = ghl_product_id;
      } catch (error) {
        console.error('Failed to create GHL product:', error);
        this.logger.error({
          error: error,
          message: 'Failed to create GHL product',
          studioId: locationId,
          enrollmentId: newEnrollmentData._id.toString(),
        });
        // Continue with enrollment creation, just without GHL product
      }

      // Try to create GHL calendar
      try {
        ghl_calendar_id = await this.createCalendar(
          newEnrollmentData,
          createEnrollmentDto,
          locationId_ghl,
          studio,
        );
        newEnrollmentData.calendarId_ghl = ghl_calendar_id;

        try {
          const blockDateRanges = await calculateBlockDateRanges(
            createEnrollmentDto.startDate,
            createEnrollmentDto.endDate,
            timezone,
          );

          await Promise.all([
            this.ghlService.blockCalendarSlots(
              locationId_ghl,
              ghl_calendar_id,
              createEnrollmentDto,
              blockDateRanges.firstRange.start,
              blockDateRanges.firstRange.end,
            ),
            this.ghlService.blockCalendarSlots(
              locationId_ghl,
              ghl_calendar_id,
              createEnrollmentDto,
              blockDateRanges.secondRange.start,
              blockDateRanges.secondRange.end,
            ),
          ]);
        } catch (error) {
          console.error('Failed to block calendar slots:', error);
          this.logger.error({
            error: error,
            message: 'Failed to block calendar slots',
            studioId: locationId,
            enrollmentId: newEnrollmentData._id.toString(),
          });
        }
      } catch (error) {
        console.error('Failed to create GHL calendar:', error);
        this.logger.error({
          message: 'Failed to create GHL calendar',
          context: locationId,
        });
      }

      await this.enrollmentModel.findByIdAndUpdate(newEnrollmentData._id, {
        $set: {
          calendarId_ghl: ghl_calendar_id,
          productId_ghl: ghl_product_id,
        },
      });
      let imageUrl: string | undefined;

      if (createEnrollmentDto.image) {
        try {
          this.logger.log(
            `Attempting to upload class preview image for enrollment ${newEnrollmentData._id}`,
          );

          // Upload image in WebP and original formats
          const uploadResult =
            await this.gcpStorageService.uploadImageVariantsToGCP(
              undefined,
              createEnrollmentDto.image,
              createEnrollmentDto.imageName,
              createEnrollmentDto.studio.toString(),
              'class-preview-image',
              newEnrollmentData._id.toString(),
            );

          // Use the WebP version as the main image URL (fallback to original)
          imageUrl = uploadResult.webpUrl || uploadResult.originalUrl;

          // Store both WebP and original URLs
          await this.enrollmentModel.findByIdAndUpdate(newEnrollmentData._id, {
            $set: {
              webpImageUrl: uploadResult.webpUrl,
              originalImageUrl: uploadResult.originalUrl,
              defaultImageUrl: '', // Clear default since we have processed versions
            },
          });

          this.logger.log(
            `Successfully uploaded class preview image for enrollment ${newEnrollmentData._id}`,
            {
              hasWebp: !!uploadResult.webpUrl,
              hasOriginal: !!uploadResult.originalUrl,
            },
          );
        } catch (error) {
          this.logger.error(
            `Failed to upload class preview image for enrollment ${newEnrollmentData._id}`,
            {
              error: error.message,
              stack: error.stack,
              enrollmentId: newEnrollmentData._id.toString(),
              studioId: locationId,
            },
          );
        }
      } else if (createEnrollmentDto.imageUrl) {
        imageUrl = createEnrollmentDto.imageUrl;
        createEnrollmentDto.defaultImageUrl = createEnrollmentDto.imageUrl;
        await this.enrollmentModel.findByIdAndUpdate(newEnrollmentData._id, {
          $set: { defaultImageUrl: createEnrollmentDto.imageUrl },
        });
      }
      return { ...newEnrollmentData.toObject(), imageUrl };
    } catch (error) {
      console.error('error creating enrollment: ', error);
    }
  }

  formatDateTime(isoString: string) {
    return formatInTimeZone(
      parseISO(isoString),
      'UTC',
      'MMM d, yyyy, h:mm:ss a',
    );
  }

  private async waitForJobWithTimeout(
    job: Job,
    queueEvents: QueueEvents,
    timeoutMs: number = 30000,
    enrollmentTitle?: string,
  ): Promise<any> {
    try {
      // Add a timeout wrapper around waitUntilFinished
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => {
          reject(
            new Error(
              `Job timeout after ${timeoutMs}ms for enrollment: ${enrollmentTitle}`,
            ),
          );
        }, timeoutMs);
      });

      const jobPromise = job.waitUntilFinished(queueEvents);

      const result = await Promise.race([jobPromise, timeoutPromise]);
      return result;
    } catch (error) {
      this.logger.error(`Job failed for enrollment: ${enrollmentTitle}`, {
        error: error.message,
        jobId: job.id,
        jobName: job.name,
      });

      // Check if the job still exists and try to get its state
      try {
        const jobState = await job.getState();
        this.logger.log(`Job state for ${enrollmentTitle}: ${jobState}`);

        if (jobState === 'completed') {
          // Try to get the result directly
          return job.returnvalue;
        }
      } catch (stateError) {
        this.logger.error(`Could not get job state for ${enrollmentTitle}`, {
          error: stateError.message,
        });
      }

      // Re-throw the original error
      throw error;
    }
  }

  async bulkCreateEnrollments(enrollments: any[], locationId): Promise<any> {
    this.logger.log(
      `Starting bulk enrollment process for location: ${locationId}`,
    );
    const locationId_object = Types.ObjectId.createFromHexString(locationId);
    const studio = await this.studioService.findOne(locationId);

    try {
      if (!Array.isArray(enrollments) || enrollments.length === 0) {
        this.logger.warn(
          'Invalid input: Enrollments data must be a non-empty array.',
        );
        throw new Error(
          'Invalid input: Enrollments data must be a non-empty array.',
        );
      }

      // const bulkOperations = [];
      const customFormCache = new Map<string, Types.ObjectId>();
      const policyGroupCache = new Map<string, Types.ObjectId>();
      const failedEnrollments = [];
      const successfulEnrollments = [];
      const maxBatchSize = 500;
      // Handle CustomForm references
      const resolveCustomFormIds = async (
        names: string[],
        type: string,
      ): Promise<Types.ObjectId[]> => {
        // Handle single string or array of strings
        const nameArray = Array.isArray(names) ? names : [names];

        // Clean and validate names
        const uniqueNames = [...new Set(nameArray)]
          .map((name) => String(name).trim()) // Ensure it's a string and trim
          .filter((name) => name.length > 0); // Remove empty strings

        const ids: Types.ObjectId[] = [];
        const seenIds = new Set();

        for (const name of uniqueNames) {
          const lowercaseName = name.toLowerCase();
          const cacheKey = `${lowercaseName}_${type}`;
          if (customFormCache.has(cacheKey)) {
            const id = customFormCache.get(cacheKey)!;
            if (!seenIds.has(id.toString())) {
              ids.push(id);
              seenIds.add(id.toString());
            }
          } else {
            let customForm = await this.customFormModel.findOne({
              fieldName: lowercaseName,
              fieldType: type,
              studio: locationId_object,
            });
            if (!customForm) {
              customForm = await this.customFormModel.create({
                fieldName: lowercaseName,
                fieldType: type,
                studio: locationId_object,
              });
            }
            const id = customForm._id;
            if (!seenIds.has(id.toString())) {
              customFormCache.set(lowercaseName, id);
              ids.push(id);
              seenIds.add(id.toString());
            }
          }
        }
        return ids;
      };

      const resolvePolicyGroupIds = async (
        names: string[],
      ): Promise<Types.ObjectId[]> => {
        const ids: Types.ObjectId[] = [];
        for (const name of names || []) {
          if (policyGroupCache.has(name)) {
            ids.push(policyGroupCache.get(name)!);
          } else {
            const policyGroup = await this.policyService.findPolicyByName(name);
            if (!policyGroup) {
              throw new Error(
                `PolicyGroup with name "${name}" does not exist.`,
              );
            }
            const id = Types.ObjectId.createFromHexString(
              policyGroup._id.toString(),
            );
            policyGroupCache.set(name, id);
            ids.push(id);
          }
        }
        return ids;
      };

      const resolveSessionId = async (
        sessionName: string,
      ): Promise<any | null> => {
        if (!sessionName) return null;

        const cleanSessionName = sessionName.trim();
        if (cleanSessionName === '') return null;

        try {
          // Try to find existing session
          let session = await this.sessionModel.findOne({
            studioId: locationId_object,
            name: cleanSessionName,
          });

          // If session doesn't exist, create it
          if (!session) {
            session = await this.sessionModel.create({
              name: cleanSessionName,
              studioId: locationId_object,
            });
          }

          return session._id;
        } catch (error) {
          console.error('Error resolving session:', error);
          return null;
        }
      };

      for (const enrollment of enrollments) {
        if (!enrollment.title) {
          this.logger.warn(
            `Skipping enrollment due to missing title: ${JSON.stringify(enrollment)}`,
          );
          failedEnrollments.push({ enrollment, reason: 'Missing title' });
          continue;
        }

        try {
          this.logger.log(`Processing enrollment: ${enrollment.title}`);
          const tags = await resolveCustomFormIds(
            enrollment.tags || [],
            'tags',
          );
          const instructor = await resolveCustomFormIds(
            enrollment.instructor || [],
            'instructor',
          );
          const group = await resolveCustomFormIds(
            enrollment.group || [],
            'group',
          );
          const room =
            (enrollment.room &&
              (await resolveCustomFormIds([enrollment.room], 'room'))[0]) ||
            null;
          const location =
            (enrollment.location &&
              (
                await resolveCustomFormIds([enrollment.location], 'location')
              )[0]) ||
            null;
          const session = enrollment.session
            ? await resolveSessionId(enrollment.session)
            : null;
          const policyGroup = await resolvePolicyGroupIds(
            enrollment.policyGroup || [],
          );

          const dayArray = enrollment.days.split(',').map((day) => day.trim());
          const availability = dayArray.map((day) => ({
            day,
            startTime: formatDateTime(
              formatTimeToDate(enrollment['start time'].replace('.', ':')),
            ),
            endTime: formatDateTime(
              formatTimeToDate(enrollment['end time'].replace('.', ':')),
            ),
          }));

          // this.logger.log(
          //   `Queueing GHL product and calendar creation for: ${enrollment.title}`,
          // );
          // const job = await this.enrollmentQueue.add(
          //   'create-product-and-calendar',
          //   {
          //     enrollment,
          //     locationId_ghl: studio.locationId,
          //     createEnrollmentDto: enrollment,
          //     studio,
          //     availability,
          //   },
          //   {
          //     attempts: 3,
          //     backoff: {
          //       type: 'exponential',
          //       delay: 1000,
          //     },
          //   },
          // );
          // const result = await job.waitUntilFinished(this.enrollmentQueueEvents);
          // const { mappedEnrollmentData, success } = result;
          // const productId_ghl = mappedEnrollmentData.productId_ghl;
          // const calendarId_ghl = mappedEnrollmentData.calendarId_ghl;

          // const waitForJob = async (job: Job) => {
          //   return new Promise((resolve, reject) => {
          //     const timeout = setTimeout(() => {
          //       reject(new Error('Job timed out'));
          //     }, 30000);

          //     this.enrollmentQueueEvents.on('completed', (args) => {
          //       if (args.jobId === job.id) {
          //         clearTimeout(timeout);
          //         resolve(args.returnvalue);
          //       }
          //     });

          //     this.enrollmentQueueEvents.on('failed', (args) => {
          //       if (args.jobId === job.id) {
          //         clearTimeout(timeout);
          //         reject(new Error(`Job failed: ${args.failedReason}`));
          //       }
          //     });
          //   });
          // };
          // In your enrollment orchestration code:
          let productId_ghl = '';
          let calendarId_ghl = '';

          try {
            this.logger.log(`Creating product for: ${enrollment.title}`);
            const productJob = await this.productQueue.add(
              'create-product',
              {
                enrollment,
                locationId_ghl: studio.locationId,
              },
              {
                attempts: 3,
                backoff: { type: 'exponential', delay: 2000 },
                removeOnComplete: 50,
                removeOnFail: 50,
              },
            );

            const productResult = await this.waitForJobWithTimeout(
              productJob,
              this.productQueueEvents,
              30000, // 30 second timeout
              enrollment.title,
            );
            productId_ghl = productResult?.productId_ghl || '';

            if (productId_ghl) {
              this.logger.log(
                `Creating product price for: ${enrollment.title}`,
              );
              const priceJob = await this.productPriceQueue.add(
                'create-product-price',
                {
                  locationId_ghl: studio.locationId,
                  productId_ghl,
                  createEnrollmentDto: enrollment,
                  studio,
                },
                {
                  attempts: 3,
                  backoff: { type: 'exponential', delay: 2000 },
                  removeOnComplete: 50,
                  removeOnFail: 50,
                },
              );
              await this.waitForJobWithTimeout(
                priceJob,
                this.productPriceQueueEvents,
                30000,
                enrollment.title,
              );
            }

            this.logger.log(`Creating calendar for: ${enrollment.title}`);
            const calendarJob = await this.calendarQueue.add(
              'create-calendar',
              {
                enrollment,
                locationId_ghl: studio.locationId,
                createEnrollmentDto: enrollment,
                studio,
                availability,
              },
              {
                attempts: 3,
                backoff: { type: 'exponential', delay: 2000 },
                removeOnComplete: 50,
                removeOnFail: 50,
              },
            );
            const calendarResult = await this.waitForJobWithTimeout(
              calendarJob,
              this.calendarQueueEvents,
              30000,
              enrollment.title,
            );
            calendarId_ghl = calendarResult?.calendarId_ghl || '';
          } catch (queueError) {
            this.logger.error(
              `Queue operation failed for enrollment: ${enrollment.title}. Error: ${queueError.message}`,
            );
            // Don't fail the entire enrollment, just log the error and continue
            productId_ghl = '';
            calendarId_ghl = '';
          }

          // if (calendarId_ghl) {
          //   this.logger.log(`Blocking calendar slots for: ${enrollment.title}`);
          //   const blockJob = await this.blockCalendarQueue.add('block-calendar-slots', {
          //     locationId_ghl: studio.locationId,
          //     calendarId_ghl,
          //     createEnrollmentDto: enrollment,
          //   });
          //   await blockJob.waitUntilFinished(this.blockCalendarQueueEvents);
          // }

          successfulEnrollments.push({
            title: enrollment.title,
            tags,
            instructor,
            group,
            room,
            location,
            session,
            policyGroup,
            productId_ghl,
            calendarId_ghl,
            enrollment,
            availability,
          });
        } catch (error) {
          this.logger.error(
            `Error processing enrollment: ${enrollment.title}. Reason: ${error.message}`,
          );
          failedEnrollments.push({ enrollment, reason: error.message });
        }
      }

      this.logger.log(
        `Bulk writing ${successfulEnrollments.length} enrollments.`,
      );
      for (let i = 0; i < successfulEnrollments.length; i += maxBatchSize) {
        const batch = successfulEnrollments
          .slice(i, i + maxBatchSize)
          .map(
            ({
              enrollment,
              tags,
              instructor,
              group,
              room,
              location,
              session,
              policyGroup,
              productId_ghl,
              calendarId_ghl,
              availability,
            }) => ({
              insertOne: {
                document: {
                  title: enrollment.title,
                  studio: enrollment.studio,
                  days: enrollment.days
                    ? enrollment.days.split(',').map((day) => day.trim())
                    : [],
                  location,
                  tags,
                  instructor,
                  group,
                  room,
                  session,
                  policyGroup,
                  productId_ghl,
                  calendarId_ghl,
                  registrationStartDate: formatToISODate(
                    enrollment['registration start date'],
                  ),
                  startTime: convertTo24Hour(
                    enrollment['start time'].replace('.', ':'),
                  ),
                  endTime: convertTo24Hour(
                    enrollment['end time'].replace('.', ':'),
                  ),
                  startDate: formatToISODate(enrollment['start date']),
                  endDate: formatToISODate(enrollment['end date']),
                  duration: enrollment.duration,
                  tuitionFee: enrollment['tuition fee'],
                  maxSize: enrollment['class size'],
                  startYear: enrollment['start age year'],
                  endYear: enrollment['end age year'],
                  startMonth: enrollment['start age month'],
                  endMonth: enrollment['end age month'],
                  tuitionBillingCycle:
                    enrollment['tuition billing cycle'].toLowerCase() ===
                    'one time'
                      ? 'one-time'
                      : enrollment['tuition billing cycle'].toLowerCase(),
                  billingDay: enrollment['billing day'] || 1,
                  registrationFeeAmount: enrollment['registration fee'],
                  registrationFee: true,
                  availability: availability,
                  hide: enrollment['hide'],
                  color: enrollment['color'],
                  description: enrollment['description (under 200 characters)'],
                  costumeFee: enrollment['costume fee'],
                },
              },
            }),
          );

        await this.enrollmentModel.bulkWrite(batch);
      }

      this.logger.log(
        `Bulk enrollment process completed. Successful: ${successfulEnrollments.length}, Failed: ${failedEnrollments.length}`,
      );
      return {
        message: 'Enrollments processing initiated.',
        totalProcessed: enrollments.length,
        successfulEnrollments: successfulEnrollments.length,
        failedEnrollments,
      };
    } catch (error) {
      this.logger.error(
        `Critical error in bulkCreateEnrollments: ${error.message}`,
      );
      throw new Error(`Failed to process enrollments: ${error.message}`);
    }
  }

  async update(
    id: string,
    updateEnrollmentDto: UpdateEnrollmentDto,
    locationId,
  ): Promise<Enrollment> {
    // Get the existing enrollment to compare fee changes
    const existingEnrollment = await this.enrollmentModel.findById(id);
    if (!existingEnrollment) {
      throw new NotFoundException(`Enrollment with ID ${id} not found`);
    }

    updateEnrollmentDto.studio = Types.ObjectId.createFromHexString(locationId);
    if (updateEnrollmentDto.policyGroup) {
      updateEnrollmentDto.policyGroup = updateEnrollmentDto.policyGroup.map(
        (policyId: string) => Types.ObjectId.createFromHexString(policyId),
      ) as any;
    }

    if (updateEnrollmentDto.tags) {
      updateEnrollmentDto.tags = updateEnrollmentDto.tags.map((tagId: string) =>
        Types.ObjectId.createFromHexString(tagId),
      ) as any;
    }

    if (updateEnrollmentDto.instructor) {
      updateEnrollmentDto.instructor = updateEnrollmentDto.instructor.map(
        (instructorId: string) =>
          Types.ObjectId.createFromHexString(instructorId),
      ) as any;
    }

    if (updateEnrollmentDto.group) {
      updateEnrollmentDto.group = updateEnrollmentDto.group.map(
        (groupId: string) => Types.ObjectId.createFromHexString(groupId),
      ) as any;
    }
    if (updateEnrollmentDto.room) {
      updateEnrollmentDto.room = Types.ObjectId.createFromHexString(
        updateEnrollmentDto.room,
      ) as any;
    }
    if (updateEnrollmentDto.location) {
      updateEnrollmentDto.location = Types.ObjectId.createFromHexString(
        updateEnrollmentDto.location,
      ) as any;
    }
    if (updateEnrollmentDto.session) {
      updateEnrollmentDto.session = Types.ObjectId.createFromHexString(
        updateEnrollmentDto.session,
      ) as any;
    }

    let imageUrl: string | undefined;
    if (updateEnrollmentDto.image) {
      try {
        this.logger.log(
          `Attempting to update class preview image for enrollment ${id}`,
          {
            enrollmentId: id,
            studioId: locationId,
          },
        );

        // Upload image in WebP and original formats
        const uploadResult =
          await this.gcpStorageService.uploadImageVariantsToGCP(
            undefined,
            updateEnrollmentDto.image,
            updateEnrollmentDto.imageName,
            locationId,
            'class-preview-image',
            id,
          );

        // Use the WebP version as the main image URL (fallback to original)
        imageUrl = uploadResult.webpUrl || uploadResult.originalUrl;

        // Update the enrollment with WebP and original URLs
        await this.enrollmentModel.findByIdAndUpdate(id, {
          $set: {
            webpImageUrl: uploadResult.webpUrl,
            originalImageUrl: uploadResult.originalUrl,
            defaultImageUrl: '', // Clear default image URL since we have processed versions
          },
        });

        this.logger.log(
          `Successfully updated class preview image for enrollment ${id}`,
          {
            enrollmentId: id,
            studioId: locationId,
            hasWebp: !!uploadResult.webpUrl,
            hasOriginal: !!uploadResult.originalUrl,
          },
        );
      } catch (uploadError) {
        this.logger.error(
          `Failed to update class preview image for enrollment ${id}`,
          {
            error: uploadError.message,
            stack: uploadError.stack,
            enrollmentId: id,
            studioId: locationId,
          },
        );
      }
    } else if (updateEnrollmentDto.imageUrl) {
      try {
        // Remove existing file from GCP bucket if exists
        await this.gcpStorageService.deleteFileFromGCP(
          locationId,
          'class-preview-image',
          undefined,
          id,
        );

        this.logger.log(
          `Successfully removed custom image for enrollment ${id}`,
          {
            enrollmentId: id,
            studioId: locationId,
          },
        );
      } catch (deleteError) {
        this.logger.error(
          `Failed to remove custom image for enrollment ${id}`,
          {
            error: deleteError.message,
            stack: deleteError.stack,
            enrollmentId: id,
            studioId: locationId,
          },
        );
        // Continue execution even if delete fails
      }
      imageUrl = updateEnrollmentDto.imageUrl;
      updateEnrollmentDto.defaultImageUrl = updateEnrollmentDto.imageUrl;
    }

    // Check for fee changes and update related records
    await this.updateRelatedFeesAndTransactions(
      existingEnrollment,
      updateEnrollmentDto,
      id,
    );

    // Update the enrollment in the database
    const updatedEnrollment = await this.enrollmentModel
      .findByIdAndUpdate(id, updateEnrollmentDto, { new: true })
      .populate({
        path: 'room',
        model: 'CustomForm', // Specify the model to populate from
        match: { fieldType: 'room' },
        select: 'fieldName',
      })
      .populate({
        path: 'instructor',
        model: 'CustomForm',
        match: { fieldType: 'instructor' },
        select: 'fieldName',
      })
      .populate({ path: 'tags', model: 'Tag', select: 'fieldName' })
      .populate({
        path: 'group',
        model: 'CustomForm',
        match: { fieldType: 'group' },
        select: 'fieldName',
      })
      .populate({ path: 'session', model: 'Session', select: 'name' })
      .populate({
        path: 'location',
        model: 'CustomForm',
        match: { fieldType: 'location' },
        select: 'fieldName',
      })
      .populate({
        path: 'policyGroup',
        model: 'Policy',
        // match: { fieldType: 'group' },
        select: 'name',
      })
      .exec();

    // Handle case where the document is not found
    if (!updatedEnrollment) {
      throw new NotFoundException(`Enrollment with ID ${id} not found`);
    }

    try {
      const newEnrolledDataId = Types.ObjectId.createFromHexString(
        updatedEnrollment._id.toString(),
      );
      await Promise.all(
        updateEnrollmentDto.policyGroup.map(async (policyId) => {
          const policy = await this.policyService.findOne(policyId);
          if (!policy.class.includes(newEnrolledDataId)) {
            policy.class.push(newEnrolledDataId);
            await policy.save();
          }
        }),
      );
    } catch (error) {
      console.error('error updating policies with enrollment id:', error);
    }

    if (updatedEnrollment.calendarId_ghl) {
      await this.ghlService.updateCalendar(
        updatedEnrollment.calendarId_ghl,
        locationId,
        updateEnrollmentDto,
      );
    }

    // If startDate or endDate changed, update invoices for all subscriptions
    const endDateChanged =
      updateEnrollmentDto.endDate &&
      existingEnrollment.endDate.getTime() !==
        new Date(updateEnrollmentDto.endDate).getTime();
    if (endDateChanged) {
      const subscriptions = await this.subscriptionModel.find({
        entityId: Types.ObjectId.createFromHexString(id),
        entityType: 'class',
      });
      for (const subscription of subscriptions) {
        await this.handleSubscriptionDateChange(
          subscription,
          updatedEnrollment.startDate,
          updatedEnrollment.endDate,
          updatedEnrollment,
        );
      }
    }

    return updatedEnrollment;
  }

  private isFeeChanged(
    newValue: number | undefined,
    existingValue: number | null,
  ): boolean {
    // If new value is undefined, no change requested
    if (newValue === undefined) {
      return false;
    }

    // Normalize values: treat null as 0
    const normalizedNew = newValue ?? 0;
    const normalizedExisting = existingValue ?? 0;

    // Return true only if the normalized values are actually different
    return normalizedNew !== normalizedExisting;
  }

  private async updateRelatedFeesAndTransactions(
    existingEnrollment: Enrollment,
    updateEnrollmentDto: UpdateEnrollmentDto,
    enrollmentId: string,
  ): Promise<void> {
    try {
      // Check if any fees have changed using the helper function
      const tuitionFeeChanged = this.isFeeChanged(
        updateEnrollmentDto.tuitionFee,
        existingEnrollment.tuitionFee,
      );
      const costumeFeeChanged = this.isFeeChanged(
        updateEnrollmentDto.costumeFee,
        existingEnrollment.costumeFee,
      );
      const registrationFeeChanged = this.isFeeChanged(
        updateEnrollmentDto.registrationFeeAmount,
        existingEnrollment.registrationFeeAmount,
      );

      if (!tuitionFeeChanged && !costumeFeeChanged && !registrationFeeChanged) {
        return; // No fee changes, nothing to update
      }

      this.logger.log(`Fee changes detected for enrollment ${enrollmentId}`, {
        tuitionFeeChanged,
        costumeFeeChanged,
        registrationFeeChanged,
        enrollmentId,
      });

      // Find all subscriptions for this enrollment
      const subscriptions = await this.subscriptionModel.find({
        entityId: Types.ObjectId.createFromHexString(enrollmentId),
        entityType: 'class',
        status: {
          $in: [SubscriptionStatus.ACTIVE, SubscriptionStatus.SCHEDULED],
        },
      });

      // Update each subscription and its related records
      for (const subscription of subscriptions) {
        await this.updateSubscriptionAndRelatedRecords(
          subscription,
          existingEnrollment,
          updateEnrollmentDto,
          {
            tuitionFeeChanged,
            costumeFeeChanged,
            registrationFeeChanged,
          },
        );
      }

      this.logger.log(
        `Successfully updated fees for enrollment ${enrollmentId}`,
      );
    } catch (error) {
      this.logger.error(`Error updating fees for enrollment ${enrollmentId}`, {
        error: error.message,
        stack: error.stack,
        enrollmentId,
      });
      throw error;
    }
  }

  private async updateSubscriptionAndRelatedRecords(
    subscription: Subscription,
    existingEnrollment: Enrollment,
    updateEnrollmentDto: UpdateEnrollmentDto,
    feeChanges: {
      tuitionFeeChanged: boolean;
      costumeFeeChanged: boolean;
      registrationFeeChanged: boolean;
    },
  ): Promise<void> {
    try {
      // Update subscription if tuition fee changed
      if (feeChanges.tuitionFeeChanged) {
        const oldBaseAmount = subscription.baseAmount;
        const feeChange =
          updateEnrollmentDto.tuitionFee - existingEnrollment.tuitionFee;
        const newBaseAmount = oldBaseAmount + feeChange;

        // Special handling for tuition fee becoming zero
        if (updateEnrollmentDto.tuitionFee === 0) {
          await this.subscriptionModel.findByIdAndUpdate(subscription._id, {
            baseAmount: 0,
            finalAmount: 0,
            'metadata.appliedDiscount': 0,
          });
        } else {
          // Calculate new final amount maintaining the same discount
          const appliedDiscount =
            subscription.metadata?.appliedDiscount ||
            subscription.baseAmount - subscription.finalAmount;
          const newFinalAmount = newBaseAmount - appliedDiscount;

          await this.subscriptionModel.findByIdAndUpdate(subscription._id, {
            baseAmount: newBaseAmount,
            finalAmount: newFinalAmount,
          });
        }

        this.logger.log(
          `Updated subscription ${subscription._id} for tuition fee change`,
        );
      }

      if (subscription.finalAmount === 0 && subscription.status === 'active') {
        const student = await this.studentService.findOne(
          subscription.studentId.toString(),
        );
        if (!student) {
          throw new NotFoundException('Student not found');
        }
        // Calculate discount
        const { totalDiscount, discountSplit } =
          await this.stripeCommonService.calculateDiscounts({
            students: [
              {
                studentId: student._id.toString(),
                firstName: student.firstName,
                lastName: student.lastName,
                tuitionFee: updateEnrollmentDto.tuitionFee,
                enrollmentId: existingEnrollment._id.toString(),
                existingEnrollments: student.enrollments
                  .filter(
                    (e) =>
                      e.subscriptionStatus === 'active' ||
                      e.subscriptionStatus === 'scheduled',
                  )
                  .map((e) => ({
                    enrollmentId: e.enrollmentId.toString(),
                    subscriptionStatus: e.subscriptionStatus,
                  })),
              },
            ],
            studioId: subscription.studioId.toString(),
          });

        // Initialize Stripe
        const { stripe, credential } =
          await this.stripeCommonService.initializeStripe(
            subscription.studioId.toString(),
          );

        const parent = await this.parentsService.findOne(
          subscription.parentId.toString(),
        );

        const paymentMethod =
          await this.stripeCommonService.getCustomerDefaultPaymentMethod(
            parent.stripeCustomerId,
            stripe,
          );

        const studio = await this.studioService.findOne(
          subscription.studioId.toString(),
        );

        let paymentIntent = null;
        if (studio.paymentProcessingMethod === PaymentProcessingMethod.AUTO) {
          if (paymentMethod.type === PaymentMethod.US_BANK_ACCOUNT) {
            // Only proceed if the studio supports US bank account payments
            const currency = credential.currency || 'usd';
            if (!supportsUSBankAccount(currency.toUpperCase())) {
              throw new Error(
                'US Bank Account payments are not supported for non-USD studios',
              );
            }

            paymentIntent = await stripe.paymentIntents.create({
              amount: Math.floor(updateEnrollmentDto.tuitionFee * 100),
              currency: currency,
              customer: parent.stripeCustomerId,
              payment_method: paymentMethod.id,
              off_session: false,
              payment_method_types: ['us_bank_account'],
              confirm: false,
              description: `Tuition Payment for ${existingEnrollment._id}`,
              metadata: {
                enrollmentId: existingEnrollment._id.toString(),
              },
            });
            // Confirm with mandate data
            const serverIp = await getGcpIpAddress();
            paymentIntent = await stripe.paymentIntents.confirm(
              paymentIntent.id,
              {
                payment_method: paymentMethod.id,
                mandate_data: {
                  customer_acceptance: {
                    type: 'online',
                    online: {
                      ip_address: serverIp || '127.0.0.1',
                      user_agent: 'Enrollio Server Process',
                    },
                  },
                },
              },
            );
          } else if (
            paymentMethod.type === PaymentMethod.CARD ||
            paymentMethod.type === PaymentMethod.LINK
          ) {
            const paymentMethodTypes = [paymentMethod.type];
            if (paymentMethod.type === PaymentMethod.LINK) {
              paymentMethodTypes.push(PaymentMethod.CARD);
            }
            paymentIntent = await stripe.paymentIntents.create({
              amount: Math.floor(updateEnrollmentDto.tuitionFee * 100),
              currency: credential.currency || 'usd',
              customer: parent.stripeCustomerId,
              payment_method: paymentMethod.id,
              off_session: true,
              payment_method_types: paymentMethodTypes,
              confirm: true,
              description: `Tuition Payment for ${existingEnrollment._id}`,
              metadata: {
                enrollmentId: existingEnrollment._id.toString(),
              },
            });
          }
        }
        // Use the calculated discount and paymentIntent in invoice generation
        const subStartDate = calculateSubscriptionStartDate({
          ...existingEnrollment,
          tuitionFee: updateEnrollmentDto.tuitionFee,
        });
        subscription.startDate = subStartDate;
        const transaction = await this.paymentTransactionModel.findOne({
          _id: Types.ObjectId.createFromHexString(
            subscription.metadata.transactionId,
          ),
        });

        await this.subscriptionInvoiceService.generateSubscriptionInvoices({
          subscription,
          childTransaction: transaction, // or a transaction object if needed
          discountAmount: totalDiscount || 0,
          enrollment: existingEnrollment,
          transactionId: transaction._id.toString(),
          transactionCodeId: existingEnrollment.transactionCodeId,
          applyDiscountTo: 'all',
          noPaymentMethod: paymentMethod ? false : true,
          paymentIntentSent: paymentIntent ? true : false,
          discountCategory: 'discount',
          paymentProcessingMethod: studio.paymentProcessingMethod,
        });
      } else {
        // Find and update subscription invoices
        const invoiceFilter: any = {
          subscriptionId: subscription._id,
          status: {
            $in: [InvoiceStatus.UPCOMING, InvoiceStatus.SCHEDULED],
          },
        };

        const invoices =
          await this.subscriptionInvoiceModel.find(invoiceFilter);

        for (const invoice of invoices) {
          if (updateEnrollmentDto.tuitionFee === 0) {
            // For zero tuition fee, set invoice to FREE status
            await this.subscriptionInvoiceModel.findByIdAndUpdate(invoice._id, {
              status: InvoiceStatus.FREE,
              baseAmount: 0,
              finalAmount: 0,
              'metadata.appliedDiscount': 0,
            });
          } else {
            await this.updateInvoiceForFeeChanges(
              invoice,
              existingEnrollment,
              updateEnrollmentDto,
              feeChanges,
            );
          }
        }
      }

      // Find and update payment transactions
      const transactionFilter: any = {
        typeId: Types.ObjectId.createFromHexString(
          subscription.entityId.toString(),
        ),
        type: PaymentTransactionType.ENROLLMENT,
        status: {
          $in: [PaymentTransactionStatus.SCHEDULED],
        },
      };

      const transactions =
        await this.paymentTransactionModel.find(transactionFilter);

      for (const transaction of transactions) {
        if (updateEnrollmentDto.tuitionFee === 0) {
          // For zero tuition fee, set transaction to FREE status
          await this.paymentTransactionModel.findByIdAndUpdate(
            transaction._id,
            {
              status: PaymentTransactionStatus.FREE,
              amount: 0,
              'metadata.totalAmount': 0,
              'metadata.originalAmount': 0,
              'metadata.remainingAmount': 0,
            },
          );
        } else {
          await this.updateTransactionForFeeChanges(
            transaction,
            existingEnrollment,
            updateEnrollmentDto,
            feeChanges,
          );
        }
      }
    } catch (error) {
      this.logger.error(`Error updating subscription ${subscription._id}`, {
        error: error.message,
        subscriptionId: subscription._id.toString(),
      });
      throw error;
    }
  }

  private async updateInvoiceForFeeChanges(
    invoice: SubscriptionInvoice,
    existingEnrollment: Enrollment,
    updateEnrollmentDto: UpdateEnrollmentDto,
    feeChanges: {
      tuitionFeeChanged: boolean;
      costumeFeeChanged: boolean;
      registrationFeeChanged: boolean;
    },
  ): Promise<void> {
    try {
      let lineItemsUpdated = false;
      const updatedLineItems = [...invoice.line_items];

      // Check if this is the first invoice (contains registration fees)
      const isFirstInvoice = invoice.line_items.some(
        (item) =>
          item.type === 'Registration Fee' ||
          item.type === 'Session Registration Fee' ||
          item.type === 'Costume Fee',
      );

      // Update line items based on fee changes
      for (let i = 0; i < updatedLineItems.length; i++) {
        const item = updatedLineItems[i];

        // Update tuition fee items (affects all invoices)
        if (feeChanges.tuitionFeeChanged && item.type === 'Tuition Fee') {
          updatedLineItems[i] = {
            ...item,
            amount: updateEnrollmentDto.tuitionFee,
            total: updateEnrollmentDto.tuitionFee * item.quantity,
          };
          lineItemsUpdated = true;
        }

        // Update costume fee items (only for first invoice)
        if (
          feeChanges.costumeFeeChanged &&
          isFirstInvoice &&
          item.type === 'Costume Fee'
        ) {
          updatedLineItems[i] = {
            ...item,
            amount: updateEnrollmentDto.costumeFee,
            total: updateEnrollmentDto.costumeFee * item.quantity,
          };
          lineItemsUpdated = true;
        }

        // Update registration fee items (only for first invoice)
        if (
          feeChanges.registrationFeeChanged &&
          isFirstInvoice &&
          (item.type === 'Registration Fee' ||
            item.type === 'Session Registration Fee')
        ) {
          updatedLineItems[i] = {
            ...item,
            amount: updateEnrollmentDto.registrationFeeAmount,
            total: updateEnrollmentDto.registrationFeeAmount * item.quantity,
          };
          lineItemsUpdated = true;
        }
      }

      if (lineItemsUpdated) {
        // Calculate new base amount from updated line items
        const newBaseAmount = updatedLineItems.reduce(
          (sum, item) => sum + item.total,
          0,
        );

        // Get applied discount and calculate new final amount
        let appliedDiscount = invoice.metadata?.appliedDiscount || 0;

        // If tuition fee changed and there's a coupon, recalculate discount
        if (feeChanges.tuitionFeeChanged && invoice.appliedCouponId) {
          appliedDiscount = await this.recalculateDiscountForInvoice(
            invoice.appliedCouponId,
            newBaseAmount,
            updateEnrollmentDto.tuitionFee,
          );
        }

        const newFinalAmount = newBaseAmount - appliedDiscount;

        // Update the invoice
        await this.subscriptionInvoiceModel.findByIdAndUpdate(
          (invoice as any)._id,
          {
            line_items: updatedLineItems,
            baseAmount: newBaseAmount,
            finalAmount: newFinalAmount,
            'metadata.appliedDiscount': appliedDiscount,
          },
        );

        this.logger.log(
          `Updated invoice ${(invoice as any)._id} for fee changes`,
          {
            invoiceId: (invoice as any)._id.toString(),
            oldBaseAmount: invoice.baseAmount,
            newBaseAmount,
            oldFinalAmount: invoice.finalAmount,
            newFinalAmount,
          },
        );
      }
    } catch (error) {
      this.logger.error(`Error updating invoice ${(invoice as any)._id}`, {
        error: error.message,
        invoiceId: (invoice as any)._id.toString(),
      });
      throw error;
    }
  }

  private async updateTransactionForFeeChanges(
    transaction: PaymentTransaction,
    existingEnrollment: Enrollment,
    updateEnrollmentDto: UpdateEnrollmentDto,
    feeChanges: {
      tuitionFeeChanged: boolean;
      costumeFeeChanged: boolean;
      registrationFeeChanged: boolean;
    },
  ): Promise<void> {
    try {
      let lineItemsUpdated = false;
      const updatedLineItems = [...transaction.metadata.line_items];

      // Check if this transaction contains registration fees (first payment)
      const hasRegistrationFees = transaction.metadata.line_items.some(
        (item) =>
          item.price_data.product_data.description === 'Registration Fee' ||
          item.price_data.product_data.description ===
            'Session Registration Fee' ||
          item.price_data.product_data.description === 'Costume Fee',
      );

      // Update line items based on fee changes
      for (let i = 0; i < updatedLineItems.length; i++) {
        const item = updatedLineItems[i];
        const description = item.price_data.product_data.description;

        // Update tuition fee items (affects all transactions)
        if (feeChanges.tuitionFeeChanged && description === 'Tuition Fee') {
          updatedLineItems[i] = {
            ...item,
            price_data: {
              ...item.price_data,
              unit_amount: updateEnrollmentDto.tuitionFee * 100, // Convert to cents
            },
          };
          lineItemsUpdated = true;
        }

        // Update costume fee items (only for transactions with registration fees)
        if (
          feeChanges.costumeFeeChanged &&
          hasRegistrationFees &&
          description === 'Costume Fee'
        ) {
          updatedLineItems[i] = {
            ...item,
            price_data: {
              ...item.price_data,
              unit_amount: updateEnrollmentDto.costumeFee * 100, // Convert to cents
            },
          };
          lineItemsUpdated = true;
        }

        // Update registration fee items (only for transactions with registration fees)
        if (
          feeChanges.registrationFeeChanged &&
          hasRegistrationFees &&
          (description === 'Registration Fee' ||
            description === 'Session Registration Fee')
        ) {
          updatedLineItems[i] = {
            ...item,
            price_data: {
              ...item.price_data,
              unit_amount: updateEnrollmentDto.registrationFeeAmount * 100, // Convert to cents
            },
          };
          lineItemsUpdated = true;
        }
      }

      if (lineItemsUpdated) {
        // Calculate new total amount from updated line items
        const newTotalAmount =
          updatedLineItems.reduce(
            (sum, item) => sum + item.price_data.unit_amount * item.quantity,
            0,
          ) / 100; // Convert back from cents

        // Update the transaction
        await this.paymentTransactionModel.findByIdAndUpdate(transaction._id, {
          amount: newTotalAmount,
          'metadata.line_items': updatedLineItems,
          'metadata.totalAmount': newTotalAmount,
          'metadata.originalAmount': newTotalAmount,
          status:
            transaction.status === PaymentTransactionStatus.FREE
              ? newTotalAmount > 0
                ? PaymentTransactionStatus.UPCOMING
                : PaymentTransactionStatus.FREE
              : transaction.status,
          // Keep remainingAmount the same as totalAmount if not partially paid
          'metadata.remainingAmount':
            (transaction.metadata as any).remainingAmount === transaction.amount
              ? newTotalAmount
              : (transaction.metadata as any).remainingAmount,
        });

        this.logger.log(
          `Updated transaction ${transaction._id} for fee changes`,
          {
            transactionId: transaction._id.toString(),
            oldAmount: transaction.amount,
            newAmount: newTotalAmount,
          },
        );
      }
    } catch (error) {
      this.logger.error(`Error updating transaction ${transaction._id}`, {
        error: error.message,
        transactionId: transaction._id.toString(),
      });
      throw error;
    }
  }

  private async recalculateDiscountForInvoice(
    appliedCouponId: Types.ObjectId,
    newBaseAmount: number,
    newTuitionFee: number,
  ): Promise<number> {
    try {
      const discountCoupon =
        await this.discountCouponModel.findById(appliedCouponId);
      if (!discountCoupon || !discountCoupon.isActive) {
        this.logger.warn(
          `Discount coupon ${appliedCouponId} not found or inactive`,
        );
        return 0;
      }

      let discountAmount = 0;

      // DiscountCoupon schema supports 'percentage' and 'fixed' types
      if (discountCoupon.type === 'percentage') {
        // For percentage discounts, apply to the new tuition fee
        discountAmount = (newTuitionFee * discountCoupon.value) / 100;
        this.logger.log(
          `Calculated percentage discount: ${discountAmount} (${discountCoupon.value}% of ${newTuitionFee})`,
        );
      } else if (discountCoupon.type === 'fixed') {
        // For fixed discounts, use the fixed amount (but don't exceed the base amount)
        discountAmount = Math.min(discountCoupon.value, newBaseAmount);
        this.logger.log(
          `Calculated fixed discount: ${discountAmount} (min of ${discountCoupon.value} and ${newBaseAmount})`,
        );
      } else {
        this.logger.warn(
          `Unknown discount type: ${discountCoupon.type} for coupon ${appliedCouponId}`,
        );
        return 0;
      }

      // Ensure discount is not negative and doesn't exceed the base amount
      discountAmount = Math.max(0, Math.min(discountAmount, newBaseAmount));

      this.logger.log(`Recalculated discount for coupon ${appliedCouponId}`, {
        couponId: appliedCouponId.toString(),
        couponType: discountCoupon.type,
        couponValue: discountCoupon.value,
        newBaseAmount,
        newTuitionFee,
        calculatedDiscount: discountAmount,
      });

      return discountAmount;
    } catch (error) {
      this.logger.error(
        `Error recalculating discount for coupon ${appliedCouponId}`,
        {
          error: error.message,
          couponId: appliedCouponId.toString(),
        },
      );
      return 0;
    }
  }

  async findOne(id: string): Promise<any> {
    const enrollmentData = await this.enrollmentModel
      .findOne({ _id: id, isDeleted: false })
      .populate({
        path: 'room',
        model: 'CustomForm',
        match: { fieldType: 'room' },
        select: 'fieldName',
      })
      .populate({
        path: 'instructor',
        model: 'CustomForm',
        match: { fieldType: 'instructor' },
        select: 'fieldName',
      })
      .populate({ path: 'tags', model: 'Tag', select: 'fieldName' })
      .populate({
        path: 'group',
        model: 'CustomForm',
        match: { fieldType: 'group' },
        select: 'fieldName',
      })
      .populate({
        path: 'location',
        model: 'CustomForm',
        match: { fieldType: 'location' },
        select: 'fieldName',
      })
      .populate({
        path: 'policyGroup',
        model: 'Policy',
        select: 'name description',
      })
      .populate({
        path: 'session',
        model: 'Session',
        select: 'name registrationFeeAmount isRegistrationFee billingDate',
      })
      .populate('students')
      .lean()
      .exec();

    if (!enrollmentData) {
      throw new NotFoundException(`Enrollment with ID ${id} not found`);
    }

    const studentsWithEnrollment =
      await this.studentService.findAllByEnrollmentId(id);
    const activeStudentCount = studentsWithEnrollment.filter((student) =>
      student.enrollments.some(
        (e) =>
          e.enrollmentId.toString() === id &&
          (e.subscriptionStatus === 'active' ||
            e.subscriptionStatus === 'scheduled'),
      ),
    ).length;

    let imageUrl: string | undefined;

    try {
      this.logger.log(
        `Attempting to retrieve class preview image for enrollment ${id}`,
        {
          enrollmentId: id,
          studioId: enrollmentData.studio,
        },
      );

      // Check if enrollment has processed WebP and original versions
      if (enrollmentData.webpImageUrl || enrollmentData.originalImageUrl) {
        this.logger.debug('Using stored image versions', {
          enrollmentId: id,
          hasWebp: !!enrollmentData.webpImageUrl,
          hasOriginal: !!enrollmentData.originalImageUrl,
        });

        // Prefer WebP version, fallback to original
        imageUrl =
          enrollmentData.webpImageUrl || enrollmentData.originalImageUrl;
      } else {
        // Fallback to old image retrieval method for backward compatibility
        try {
          imageUrl = await this.gcpStorageService.getPublicImage(
            enrollmentData.studio.toString(),
            'class-preview-image',
            id,
          );
          if (imageUrl === null) {
            imageUrl = enrollmentData.defaultImageUrl;
          }
        } catch (error) {
          this.logger.error(
            `Failed to retrieve class preview image for enrollment ${id}`,
            {
              error: error.message,
              stack: error.stack,
              enrollmentId: id,
              studioId: enrollmentData.studio,
            },
          );
        }

        if (!imageUrl) {
          imageUrl = enrollmentData.defaultImageUrl;
        }
      }

      this.logger.log(
        `Successfully retrieved class preview image URL for enrollment ${id}`,
        {
          enrollmentId: id,
          studioId: enrollmentData.studio,
          hasImageUrl: !!imageUrl,
          hasWebp: !!enrollmentData.webpImageUrl,
          hasOriginal: !!enrollmentData.originalImageUrl,
        },
      );
    } catch (error) {
      this.logger.error(
        `Failed to retrieve class preview image for enrollment ${id}`,
        {
          error: error.message,
          stack: error.stack,
          enrollmentId: id,
          studioId: enrollmentData.studio,
        },
      );
      // Fallback to default image URL
      imageUrl = enrollmentData.defaultImageUrl;
    }

    return {
      ...enrollmentData,
      remainingSeats: enrollmentData.maxSize - activeStudentCount,
      studentsWithEnrollment,
      imageUrl,
      webpImageUrl: enrollmentData.webpImageUrl,
      originalImageUrl: enrollmentData.originalImageUrl,
    };
  }

  async findOneActive(id: string): Promise<any> {
    const enrollmentData = await this.enrollmentModel
      .findOne({ _id: id, isDeleted: false })
      .populate({
        path: 'room',
        model: 'CustomForm',
        match: { fieldType: 'room' },
        select: 'fieldName',
      })
      .populate({
        path: 'instructor',
        model: 'CustomForm',
        match: { fieldType: 'instructor' },
        select: 'fieldName',
      })
      .populate({ path: 'tags', model: 'Tag', select: 'fieldName' })
      .populate({
        path: 'group',
        model: 'CustomForm',
        match: { fieldType: 'group' },
        select: 'fieldName',
      })
      .populate({
        path: 'location',
        model: 'CustomForm',
        match: { fieldType: 'location' },
        select: 'fieldName',
      })
      .populate({
        path: 'policyGroup',
        model: 'Policy',
        select: 'name',
      })
      .populate({
        path: 'session',
        model: 'Session',
        select: 'name registrationFeeAmount isRegistrationFee',
      })
      .populate('students')
      .lean()
      .exec();

    if (!enrollmentData) {
      throw new NotFoundException(`Enrollment with ID ${id} not found`);
    }

    const studentsWithEnrollment =
      await this.studentService.findAllByEnrollmentIdActive(id);
    const activeStudentCount = studentsWithEnrollment.filter((student) =>
      student.enrollments.some(
        (e) =>
          e.enrollmentId.toString() === id &&
          (e.subscriptionStatus === 'active' ||
            e.subscriptionStatus === 'scheduled'),
      ),
    ).length;

    return {
      ...enrollmentData,
      remainingSeats: enrollmentData.maxSize - activeStudentCount,
      studentsWithEnrollment,
    };
  }

  async findOneDetails(id: string): Promise<Enrollment> {
    const enrollmentData = await this.enrollmentModel
      .findOne({ _id: id, isDeleted: false })
      .lean()
      .select('location title status')
      .exec();
    if (!enrollmentData) {
      throw new NotFoundException(`Enrollment with ID ${id} not found`);
    }
    return enrollmentData as Enrollment;
  }

  async findAllByStudio(studioId: string): Promise<Enrollment[]> {
    return this.enrollmentModel
      .find({ studio: studioId, isDeleted: false })
      .exec();
  }

  async findAllByStudioId(studioId: string) {
    const studioId_object = Types.ObjectId.createFromHexString(studioId);
    return this.enrollmentModel
      .find({ studio: studioId_object, isDeleted: false })
      .populate({
        path: 'room',
        model: 'CustomForm',
        match: { fieldType: 'room' },
        select: 'fieldName',
      })
      .populate({
        path: 'instructor',
        model: 'CustomForm',
        match: { fieldType: 'instructor' },
        select: 'fieldName',
      })
      .populate({ path: 'tags', model: 'Tag', select: 'fieldName' })
      .populate({
        path: 'group',
        model: 'CustomForm',
        match: { fieldType: 'group' },
        select: 'fieldName',
      })
      .populate({
        path: 'session',
        model: 'Session',
        select: 'name',
      })
      .populate({
        path: 'location',
        model: 'CustomForm',
        match: { fieldType: 'location' },
        select: 'fieldName',
      })
      .exec();
  }

  async findAllByLocationId_Ghl(locationId: string): Promise<Enrollment[]> {
    const studioDetails =
      await this.studioService.findByLocationIdString(locationId);
    const enrollments = await this.enrollmentModel
      .find({
        studio: studioDetails._id,
        hide: false,
        isDeleted: false,
      })
      .populate({
        path: 'room',
        model: 'CustomForm',
        match: { fieldType: 'room' },
        select: 'fieldName',
      })
      .populate({
        path: 'instructor',
        model: 'CustomForm',
        match: { fieldType: 'instructor' },
        select: 'fieldName',
      })
      .populate({ path: 'tags', model: 'Tag', select: 'fieldName' })
      .populate({
        path: 'group',
        model: 'CustomForm',
        match: { fieldType: 'group' },
        select: 'fieldName',
      })
      .populate({ path: 'session', model: 'Session', select: 'name' })
      .populate({
        path: 'location',
        model: 'CustomForm',
        match: { fieldType: 'location' },
        select: 'fieldName',
      })
      .populate({
        path: 'policyGroup',
        model: 'Policy',
        // match: { fieldType: 'group' },
        select: 'name',
      })
      .lean()
      .exec();

    if (!enrollments || enrollments.length === 0) {
      throw new NotFoundException(
        `No enrollments found for studio with ID ${locationId}`,
      );
    }

    return enrollments as Enrollment[];
  }

  async searchByTitle(query: string, locationId_string) {
    const locationId = Types.ObjectId.createFromHexString(locationId_string);
    return this.enrollmentModel
      .find({
        studio: locationId,
        title: { $regex: new RegExp(query, 'i') },
        isDeleted: false,
      })
      .populate({
        path: 'room',
        model: 'CustomForm',
        match: { fieldType: 'room' },
        select: 'fieldName',
      })
      .populate({
        path: 'instructor',
        model: 'CustomForm',
        match: { fieldType: 'instructor' },
        select: 'fieldName',
      })
      .populate({ path: 'tags', model: 'Tag', select: 'fieldName' })
      .populate({
        path: 'group',
        model: 'CustomForm',
        match: { fieldType: 'group' },
        select: 'fieldName',
      })
      .populate({ path: 'session', model: 'Session', select: 'name' })
      .populate({
        path: 'location',
        model: 'CustomForm',
        match: { fieldType: 'location' },
        select: 'fieldName',
      })
      .exec();
  }

  async searchByProperties(
    locationId_string: string,
    filters: {
      name?: string;
      room?: string;
      location?: string | string[];
      instructor?: string | string[];
      days?: string | string[];
      tags?: string;
      session?: string;
      sessionId?: string;
      studio?: string;
      minAge?: number;
      maxAge?: number;
      pagination?: {
        page: number;
        limit: number;
      };
    },
  ) {
    const locationId = Types.ObjectId.createFromHexString(locationId_string);
    // Set default pagination
    const page = filters.pagination?.page || 1;
    const limit = filters.pagination?.limit || 10;
    const skip = (page - 1) * limit;

    const searchConditions: any = { studio: locationId, isDeleted: false };
    // check if the api call is not from studio, in that case show only the unhidden ones
    // studio portal will show all
    if (filters.studio === 'false') {
      searchConditions.hide = false;

      // Also exclude classes with archived sessions
      const archivedSessions = await this.sessionModel
        .find({
          studioId: locationId,
          isArchive: true,
        })
        .select('_id');

      if (archivedSessions.length > 0) {
        const archivedSessionIds = archivedSessions.map(
          (session) => session._id,
        );
        searchConditions.session = { $nin: archivedSessionIds };
      }
    }

    // If room is provided, first find the room IDs
    if (filters.room) {
      const roomDocs = await this.customFormModel.find({
        studio: locationId,
        fieldType: 'room',
        fieldName: { $regex: new RegExp(filters.room, 'i') },
      });

      if (roomDocs.length > 0) {
        // Use the found room IDs in search conditions
        searchConditions.room = { $in: roomDocs.map((room) => room._id) };
      } else {
        // If no rooms found, return empty result
        return {
          data: [],
          pagination: {
            page,
            limit,
            totalCount: 0,
            totalPages: 0,
          },
        };
      }
    }

    // Add location filter - support multi-select
    if (filters.location) {
      const locationArray = Array.isArray(filters.location)
        ? filters.location
        : [filters.location];

      // Build OR conditions for multiple location names
      const locationOrConditions = locationArray.map((loc) => ({
        fieldName: { $regex: new RegExp(loc, 'i') },
      }));

      const locationDocs = await this.customFormModel.find({
        studio: locationId,
        fieldType: 'location',
        $or: locationOrConditions,
      });

      if (locationDocs.length > 0) {
        searchConditions.location = {
          $in: locationDocs.map((location) => location._id),
        };
      } else {
        // If no matching locations found, return empty result
        return {
          data: {
            classes: [],
            rooms: [],
            instructors: [],
            tags: [],
            locations: [],
          },
          pagination: {
            page,
            limit,
            totalCount: 0,
            totalPages: 0,
          },
        };
      }
    }

    // Add name (title) filter
    if (filters.name) {
      searchConditions.title = { $regex: new RegExp(filters.name, 'i') };
    }

    // Add days filter - support multi-select
    if (filters.days) {
      const daysArray = Array.isArray(filters.days)
        ? filters.days
        : [filters.days];

      // For array fields, we need to match any element that matches any of the day patterns
      const dayOrConditions = daysArray.map((day) => ({
        days: { $elemMatch: { $regex: new RegExp(day, 'i') } },
      }));

      if (dayOrConditions.length === 1) {
        searchConditions.days = {
          $elemMatch: { $regex: new RegExp(daysArray[0], 'i') },
        };
      } else {
        searchConditions.$or = searchConditions.$or
          ? [...searchConditions.$or, ...dayOrConditions]
          : dayOrConditions;
      }
    }

    // Add instructor filter - support multi-select
    if (filters.instructor) {
      const instructorArray = Array.isArray(filters.instructor)
        ? filters.instructor
        : [filters.instructor];

      // Build OR conditions for multiple instructor names
      const instructorOrConditions = instructorArray.map((inst) => ({
        fieldName: { $regex: new RegExp(inst, 'i') },
      }));

      const instructorDocs = await this.customFormModel.find({
        studio: locationId,
        fieldType: 'instructor',
        $or: instructorOrConditions,
      });

      if (instructorDocs.length > 0) {
        searchConditions.instructor = {
          $in: instructorDocs.map((instructor) => instructor._id),
        };
      } else {
        // If no matching instructors found, return empty result
        return {
          data: {
            classes: [],
            rooms: [],
            instructors: [],
            tags: [],
          },
          pagination: {
            page,
            limit,
            totalCount: 0,
            totalPages: 0,
          },
        };
      }
    }

    // Add tags filter
    if (filters.tags) {
      const tagDocs = await this.tagModel.find({
        studioId: locationId,
        fieldName: { $regex: new RegExp(filters.tags, 'i') },
      });

      if (tagDocs.length > 0) {
        searchConditions.tags = {
          $in: tagDocs.map((tag) => tag._id),
        };
      } else {
        return {
          data: {
            classes: [],
            rooms: [],
            instructors: [],
            tags: [],
          },
          pagination: {
            page,
            limit,
            totalCount: 0,
            totalPages: 0,
          },
        };
      }
    }

    // Add age range filter
    if (filters.minAge !== undefined || filters.maxAge !== undefined) {
      const enrollmentQuery: any = {
        studio: locationId,
        isDeleted: false,
      };

      if (filters.maxAge !== undefined) {
        enrollmentQuery.startYear = { $lte: filters.maxAge }; // class can start at or before student's max age
      }
      if (filters.minAge !== undefined) {
        enrollmentQuery.endYear = { $gte: filters.minAge }; // class can end at or after student's min age
      }

      const enrollmentsWithAge = await this.enrollmentModel
        .find(enrollmentQuery)
        .select('_id');

      if (enrollmentsWithAge.length > 0) {
        searchConditions._id = {
          $in: enrollmentsWithAge.map((enrollment) => enrollment._id),
        };
      } else {
        // If no enrollments found for this age range, return empty result
        return {
          data: {
            classes: [],
            rooms: [],
            instructors: [],
            tags: [],
            ages: {},
          },
          pagination: {
            page,
            limit,
            totalCount: 0,
            totalPages: 0,
          },
        };
      }
    }
    let sessionDocs;
    // Add sessions filter
    if (filters.session || filters.sessionId) {
      const sessionQuery: any = {
        studioId: locationId,
        isArchive: false,
      };

      // If sessionId is provided, use it directly
      if (filters.sessionId) {
        sessionQuery._id = Types.ObjectId.createFromHexString(
          filters.sessionId,
        );
      }
      // Otherwise, use session name search if provided
      else if (filters.session) {
        sessionQuery.name = { $regex: new RegExp(filters.session, 'i') };
      }

      sessionDocs = await this.sessionModel.find(sessionQuery);

      if (sessionDocs.length > 0) {
        const sessionIds = sessionDocs.map((session) => session._id);

        // Merge with existing session conditions if they exist
        if (searchConditions.session && searchConditions.session.$nin) {
          searchConditions.session = {
            $in: sessionIds,
            $nin: searchConditions.session.$nin,
          };
        } else {
          searchConditions.session = {
            $in: sessionIds,
          };
        }
      } else {
        return {
          data: {
            classes: [],
            rooms: [],
            instructors: [],
            tags: [],
            sessions: [],
          },
          pagination: {
            page,
            limit,
            totalCount: 0,
            totalPages: 0,
          },
        };
      }
    }

    // Prepare the aggregation pipeline
    const pipeline: any[] = [
      // Match base conditions
      { $match: searchConditions },
      { $sort: { createdAt: -1 } },
      // Lookup room details
      {
        $lookup: {
          from: 'customforms',
          localField: 'room',
          foreignField: '_id',
          as: 'roomDocs',
        },
      },
      {
        $unwind: {
          path: '$roomDocs',
          preserveNullAndEmptyArrays: true,
        },
      },

      // Lookup location details
      {
        $lookup: {
          from: 'customforms',
          localField: 'location',
          foreignField: '_id',
          as: 'locationDocs',
        },
      },
      {
        $unwind: {
          path: '$locationDocs',
          preserveNullAndEmptyArrays: true,
        },
      },

      // Lookup instructors (multiple)
      {
        $lookup: {
          from: 'customforms',
          localField: 'instructor',
          foreignField: '_id',
          as: 'instructorDetails',
        },
      },

      // Lookup tags (multiple)
      {
        $lookup: {
          from: 'tags',
          localField: 'tags',
          foreignField: '_id',
          as: 'tagDetails',
        },
      },

      {
        $lookup: {
          from: 'sessions',
          localField: 'session',
          foreignField: '_id',
          as: 'sessionDetails',
        },
      },

      // Lookup groups (multiple)
      {
        $lookup: {
          from: 'customforms',
          localField: 'group',
          foreignField: '_id',
          as: 'groupDetails',
        },
      },

      // Lookup policy groups (multiple)
      {
        $lookup: {
          from: 'policies',
          localField: 'policyGroup',
          foreignField: '_id',
          as: 'policyGroupDetails',
        },
      },

      // Facet for pagination and data
      {
        $facet: {
          metadata: [{ $count: 'totalCount' }],
          data: [
            // Apply pagination
            { $skip: skip },
            { $limit: limit },

            // Project to reshape the output
            {
              $project: {
                // Include all original fields first
                ...Object.fromEntries(
                  Object.keys(this.enrollmentModel.schema.obj).map((key) => [
                    key,
                    1,
                  ]),
                ),

                // Overwrite location with populated details
                location: {
                  $cond: {
                    if: { $ne: ['$locationDocs', null] },
                    then: {
                      _id: '$locationDocs._id',
                      name: '$locationDocs.fieldName',
                      fieldType: '$locationDocs.fieldType',
                    },
                    else: null,
                  },
                },

                // Overwrite room with populated details
                room: {
                  $cond: {
                    if: { $ne: ['$roomDocs', null] },
                    then: {
                      _id: '$roomDocs._id',
                      name: '$roomDocs.fieldName',
                      fieldType: '$roomDocs.fieldType',
                    },
                    else: null,
                  },
                },

                // Overwrite instructor with populated details
                instructor: {
                  $map: {
                    input: '$instructorDetails',
                    as: 'instructor',
                    in: {
                      _id: '$$instructor._id',
                      name: '$$instructor.fieldName',
                      fieldType: '$$instructor.fieldType',
                    },
                  },
                },

                // Overwrite tags with populated details
                tags: {
                  $map: {
                    input: '$tagDetails',
                    as: 'tag',
                    in: {
                      _id: '$$tag._id',
                      name: '$$tag.fieldName',
                    },
                  },
                },

                session: {
                  $map: {
                    input: '$sessionDetails',
                    as: 'session',
                    in: {
                      _id: '$$session._id',
                      name: '$$session.name',
                      // fieldType: '$$session.fieldType',
                    },
                  },
                },

                group: {
                  $map: {
                    input: '$groupDetails',
                    as: 'group',
                    in: {
                      _id: '$$group._id',
                      name: '$$group.fieldName',
                      fieldType: '$$group.fieldType',
                    },
                  },
                },
                policyGroup: {
                  $map: {
                    input: '$policyGroupDetails',
                    as: 'policy',
                    in: {
                      _id: '$$policy._id',
                      name: '$$policy.name',
                      description: '$$policy.description',
                    },
                  },
                },
              },
            },
          ],
        },
      },
    ];

    // Use cached reference data instead of fetching every time
    const getCachedPossibleValues = async () => {
      return await this.getCachedReferenceData(locationId_string);
    };

    // Execute the aggregation and get possible values in parallel
    const [results, possibleValues] = await Promise.all([
      this.enrollmentModel.aggregate(pipeline),
      getCachedPossibleValues(),
    ]);

    // Extract total count and data
    const totalCount = results[0].metadata[0]?.totalCount || 0;
    let classData = results[0].data;

    // Get active student counts and image URLs using cached methods
    const enrichedClassData = await Promise.all(
      classData.map(async (classItem) => {
        const [studentCount, imageUrl] = await Promise.all([
          this.getCachedStudentCount(classItem._id.toString()),
          this.getCachedImageUrl(
            locationId_string,
            classItem._id.toString(),
            classItem.defaultImageUrl,
          ),
        ]);

        return {
          ...classItem,
          remainingSeats: classItem.maxSize - studentCount,
          imageUrl,
          webpImageUrl: classItem.webpImageUrl,
        };
      }),
    );

    classData = enrichedClassData;

    return {
      data: {
        classes: classData,
        ...possibleValues,
      },
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    };
  }

  async searchByPropertiesForStudio(
    locationId_string: string,
    filters: {
      name?: string;
      room?: string;
      instructor?: string;
      days?: string;
      tags?: string;
      session?: string;
      minAge?: number;
      maxAge?: number;
      pagination?: {
        page: number;
        limit: number;
      };
    },
  ) {
    const locationId = Types.ObjectId.createFromHexString(locationId_string);
    // Set default pagination
    const page = filters.pagination?.page || 1;
    const limit = filters.pagination?.limit || 10;
    const skip = (page - 1) * limit;

    const searchConditions: any = { studio: locationId, isDeleted: false };

    // If room is provided, first find the room IDs
    if (filters.room) {
      const roomDocs = await this.customFormModel.find({
        studio: locationId,
        fieldType: 'room',
        fieldName: { $regex: new RegExp(filters.room, 'i') },
      });

      if (roomDocs.length > 0) {
        // Use the found room IDs in search conditions
        searchConditions.room = { $in: roomDocs.map((room) => room._id) };
      } else {
        // If no rooms found, return empty result
        return {
          data: [],
          pagination: {
            page,
            limit,
            totalCount: 0,
            totalPages: 0,
          },
        };
      }
    }

    // Add name (title) filter
    if (filters.name) {
      searchConditions.title = { $regex: new RegExp(filters.name, 'i') };
    }

    // Add days filter
    if (filters.days) {
      searchConditions.days = {
        $elemMatch: {
          $regex: new RegExp(filters.days, 'i'),
        },
      };
    }

    // Add instructor filter
    if (filters.instructor) {
      const instructorDocs = await this.customFormModel.find({
        studio: locationId,
        fieldType: 'instructor',
        fieldName: { $regex: new RegExp(filters.instructor, 'i') },
      });

      if (instructorDocs.length > 0) {
        searchConditions.instructor = {
          $in: instructorDocs.map((instructor) => instructor._id),
        };
      } else {
        // If no matching instructors found, return empty result
        return {
          data: {
            classes: [],
            rooms: [],
            instructors: [],
            tags: [],
          },
          pagination: {
            page,
            limit,
            totalCount: 0,
            totalPages: 0,
          },
        };
      }
    }

    // Add tags filter
    if (filters.tags) {
      const tagDocs = await this.tagModel.find({
        studioId: locationId,
        fieldName: { $regex: new RegExp(filters.tags, 'i') },
      });

      if (tagDocs.length > 0) {
        searchConditions.tags = {
          $in: tagDocs.map((tag) => tag._id),
        };
      } else {
        return {
          data: {
            classes: [],
            rooms: [],
            instructors: [],
            tags: [],
          },
          pagination: {
            page,
            limit,
            totalCount: 0,
            totalPages: 0,
          },
        };
      }
    }

    // Add age range filter
    if (filters.minAge !== undefined || filters.maxAge !== undefined) {
      const ageConditions: any = { studio: locationId };

      if (filters.minAge !== undefined && filters.maxAge !== undefined) {
        // Both minAge and maxAge provided - find overlapping ranges
        ageConditions.$and = [
          { startYear: { $lte: filters.maxAge } },
          { endYear: { $gte: filters.minAge } },
        ];
      } else if (filters.minAge !== undefined) {
        // Only minAge provided - find enrollments that end at or after minAge
        ageConditions.endYear = { $gte: filters.minAge };
      } else if (filters.maxAge !== undefined) {
        // Only maxAge provided - find enrollments that start at or before maxAge
        ageConditions.startYear = { $lte: filters.maxAge };
      }

      const enrollmentsWithAge = await this.enrollmentModel
        .find(ageConditions)
        .select('_id');

      if (enrollmentsWithAge.length > 0) {
        searchConditions._id = {
          $in: enrollmentsWithAge.map((enrollment) => enrollment._id),
        };
      } else {
        // If no enrollments found for this age range, return empty result
        return {
          data: {
            classes: [],
            rooms: [],
            instructors: [],
            tags: [],
            ages: {},
          },
          pagination: {
            page,
            limit,
            totalCount: 0,
            totalPages: 0,
          },
        };
      }
    }

    // Add sessions filter
    if (filters.session) {
      const sessionDocs = await this.customFormModel.find({
        studio: locationId,
        fieldType: 'session',
        fieldName: { $regex: new RegExp(filters.session, 'i') },
      });

      if (sessionDocs.length > 0) {
        searchConditions.session = {
          $in: sessionDocs.map((session) => session._id),
        };
      } else {
        return {
          data: {
            classes: [],
            rooms: [],
            instructors: [],
            tags: [],
            sessions: [],
          },
          pagination: {
            page,
            limit,
            totalCount: 0,
            totalPages: 0,
          },
        };
      }
    }

    // Prepare the aggregation pipeline
    const pipeline: any[] = [
      // Match base conditions
      { $match: searchConditions },
      { $sort: { createdAt: -1 } },
      // Lookup location details
      {
        $lookup: {
          from: 'customforms',
          localField: 'room',
          foreignField: '_id',
          as: 'roomDocs',
        },
      },
      {
        $unwind: {
          path: '$locationDetails',
          preserveNullAndEmptyArrays: true,
        },
      },

      // Lookup room details
      {
        $lookup: {
          from: 'customforms',
          localField: 'room',
          foreignField: '_id',
          as: 'roomDocs',
        },
      },
      {
        $unwind: {
          path: '$roomDocs',
          preserveNullAndEmptyArrays: true,
        },
      },

      // Lookup location details
      {
        $lookup: {
          from: 'customforms',
          localField: 'location',
          foreignField: '_id',
          as: 'locationDocs',
        },
      },
      {
        $unwind: {
          path: '$locationDocs',
          preserveNullAndEmptyArrays: true,
        },
      },

      // Lookup instructors (multiple)
      {
        $lookup: {
          from: 'customforms',
          localField: 'instructor',
          foreignField: '_id',
          as: 'instructorDetails',
        },
      },

      // Lookup tags (multiple)
      {
        $lookup: {
          from: 'customforms',
          localField: 'tags',
          foreignField: '_id',
          as: 'tagDetails',
        },
      },

      {
        $lookup: {
          from: 'customforms',
          localField: 'session',
          foreignField: '_id',
          as: 'sessionDetails',
        },
      },

      // Lookup groups (multiple)
      {
        $lookup: {
          from: 'customforms',
          localField: 'group',
          foreignField: '_id',
          as: 'groupDetails',
        },
      },

      // Lookup policy groups (multiple)
      {
        $lookup: {
          from: 'policies',
          localField: 'policyGroup',
          foreignField: '_id',
          as: 'policyGroupDetails',
        },
      },

      // Facet for pagination and data
      {
        $facet: {
          metadata: [{ $count: 'totalCount' }],
          data: [
            // Apply pagination
            { $skip: skip },
            { $limit: limit },

            // Project to reshape the output
            {
              $project: {
                // Include all original fields first
                ...Object.fromEntries(
                  Object.keys(this.enrollmentModel.schema.obj).map((key) => [
                    key,
                    1,
                  ]),
                ),
              },
            },
          ],
        },
      },
    ];

    // Get all possible values
    const getAllPossibleValues = async () => {
      const rooms = await this.customFormModel
        .find({
          studio: locationId,
          fieldType: 'room',
        })
        .select('_id fieldName fieldType');

      const ageStats = await this.enrollmentModel.aggregate([
        {
          $match: {
            studio: locationId,
            startYear: { $exists: true },
            endYear: { $exists: true },
          },
        },
        {
          $group: {
            _id: null,
            startYear: { $min: '$startYear' },
            endYear: { $max: '$endYear' },
          },
        },
        {
          $project: {
            _id: new Types.ObjectId(),
            startYear: '$startYear',
            endYear: '$endYear',
            fieldType: 'age',
          },
        },
      ]);

      // Format the age group
      const ageGroups =
        ageStats.length > 0
          ? {
              minAge: ageStats[0].startYear,
              maxAge: ageStats[0].endYear,
            }
          : {};

      const instructors = await this.customFormModel
        .find({
          studio: locationId,
          fieldType: 'instructor',
        })
        .select('_id fieldName fieldType');

      const tags = await this.tagModel
        .find({
          studioId: locationId,
        })
        .select('_id fieldName fieldType');

      const sessions = await this.customFormModel
        .find({
          studio: locationId,
          fieldType: 'session',
        })
        .select('_id fieldName fieldType');

      return {
        rooms: rooms.map((room) => ({
          _id: room._id,
          name: room.fieldName,
          fieldType: room.fieldType,
        })),
        ages: ageGroups,
        instructors: instructors.map((instructor) => ({
          _id: instructor._id,
          name: instructor.fieldName,
          fieldType: instructor.fieldType,
        })),
        tags: tags.map((tag) => ({
          _id: tag._id,
          name: tag.fieldName,
          fieldType: tag.fieldType,
        })),
        sessions: sessions.map((session) => ({
          _id: session._id,
          name: session.fieldName,
          fieldType: session.fieldType,
        })),
      };
    };

    // Execute the aggregation and get possible values in parallel
    const [results, possibleValues] = await Promise.all([
      this.enrollmentModel.aggregate(pipeline),
      getAllPossibleValues(),
    ]);

    // Extract total count and data
    const totalCount = results[0].metadata[0]?.totalCount || 0;
    const classData = results[0].data;

    return {
      data: {
        classes: classData,
        ...possibleValues,
      },
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    };
  }

  async findAllByStudioIdCsv(locationId: string) {
    const studioId_object = Types.ObjectId.createFromHexString(locationId);
    const enrollments = await this.enrollmentModel
      .find({ studio: studioId_object })
      .populate({
        path: 'room',
        model: 'CustomForm',
        match: { fieldType: 'room' },
        select: 'fieldName',
      })
      .populate({
        path: 'instructor',
        model: 'CustomForm',
        match: { fieldType: 'instructor' },
        select: 'fieldName',
      })
      .populate({ path: 'tags', model: 'Tag', select: 'fieldName' })
      .populate({
        path: 'group',
        model: 'CustomForm',
        match: { fieldType: 'group' },
        select: 'fieldName',
      })
      .populate({
        path: 'policyGroup',
        model: 'Policy',
        select: 'name',
      })
      .populate({ path: 'session', model: 'Session', select: 'name' })
      .populate({
        path: 'location',
        model: 'CustomForm',
        match: { fieldType: 'location' },
        select: 'fieldName',
      })
      .exec();

    return enrollments.map((enrollment) => {
      // Convert to plain object if it's a mongoose document
      const plainEnrollment = enrollment.toObject();

      return {
        title: plainEnrollment.title,
        location: plainEnrollment.location,
        days: plainEnrollment.days,
        startTime: plainEnrollment.startTime,
        endTime: plainEnrollment.endTime,
        duration: plainEnrollment.duration,
        registrationStartDate: plainEnrollment.registrationStartDate,
        startDate: plainEnrollment.startDate,
        endDate: plainEnrollment.endDate,
        tuitionFee: plainEnrollment.tuitionFee,
        maxSize: plainEnrollment.maxSize,
        tuitionBillingCycle: plainEnrollment.tuitionBillingCycle,
        registrationFeeAmount: plainEnrollment.registrationFeeAmount,
        description: plainEnrollment.description,
        startYear: plainEnrollment.startYear,
        endYear: plainEnrollment.endYear,
        startMonth: plainEnrollment.startMonth,
        endMonth: plainEnrollment.endMonth,
        room: plainEnrollment.room
          ? typeof plainEnrollment.room === 'object' &&
            'fieldName' in plainEnrollment.room
            ? plainEnrollment.room.fieldName
            : plainEnrollment.room.toString()
          : '',
        instructor:
          plainEnrollment.instructor?.map((i: any) => i.fieldName) || [],
        tags: plainEnrollment.tags?.map((t: any) => t.fieldName) || [],
        group: plainEnrollment.group?.map((g: any) => g.fieldName) || [],
        policyGroup: plainEnrollment.policyGroup?.map((p: any) => p.name) || [],
      };
    });
  }

  async updateProductStripeId(ghlProductId: string, stripeProductId: string) {
    const maxRetries = 3;
    const retryDelay = 10000; // Delay in milliseconds
    let enrollment = null;
    let eventData = null;
    let retries = 0;

    while (!enrollment && retries < maxRetries) {
      try {
        enrollment = await this.enrollmentModel.findOneAndUpdate(
          { productId_ghl: ghlProductId },
          { productId_stripe: stripeProductId },
          { new: true },
        );

        if (enrollment) {
          return enrollment;
        }

        eventData = await this.eventModel.findOneAndUpdate(
          { productId_ghl: ghlProductId },
          { productId_stripe: stripeProductId },
          { new: true },
        );

        if (eventData) {
          return eventData;
        }

        if (!enrollment || !eventData) {
          retries++;
          if (retries < maxRetries) {
            await new Promise((resolve) => setTimeout(resolve, retryDelay));
          }
        }
      } catch (error) {
        console.error(`Error during update attempt ${retries + 1}:`, error);
        retries++;
        if (retries < maxRetries) {
          await new Promise((resolve) => setTimeout(resolve, retryDelay));
        }
      }
    }

    if (!enrollment || !eventData) {
      throw new Error(
        `Failed to update product stripe ID after ${maxRetries} retries.`,
      );
    }

    return enrollment;
  }

  async updateEnrollmentById(
    enrollmentId: string,
    updateData: Partial<Enrollment>,
  ) {
    return this.enrollmentModel.findByIdAndUpdate(
      enrollmentId,
      { $set: updateData },
      { new: true },
    );
  }

  async remove(id: string, studioId: string): Promise<boolean> {
    const studioObjectId = Types.ObjectId.createFromHexString(studioId);
    const enrollmentId = Types.ObjectId.createFromHexString(id);
    const result = await this.enrollmentModel.updateOne(
      {
        _id: enrollmentId,
        studio: studioObjectId,
      },
      {
        $set: { isDeleted: true },
      },
    );
    return result.modifiedCount > 0;
  }

  async removeBatch(
    ids: string[],
    studioId: string,
  ): Promise<{ deletedCount: number }> {
    const studioObjectId = Types.ObjectId.createFromHexString(studioId);
    const enrollmentIds = ids.map((id) =>
      Types.ObjectId.createFromHexString(id),
    );
    const result = await this.enrollmentModel.updateMany(
      {
        _id: { $in: enrollmentIds },
        studio: studioObjectId,
      },
      {
        $set: { isDeleted: true },
      },
    );
    return { deletedCount: result.modifiedCount };
  }

  parseCustomDateTime(dateTimeStr: string) {
    try {
      if (!dateTimeStr) {
        return null;
      }

      const parts = dateTimeStr.split(', ');
      if (parts.length < 3) {
        console.error('Invalid datetime format:', dateTimeStr);
        return null;
      }

      const year = parts[1];
      const timeParts = parts[2].split(' ');
      if (timeParts.length < 2) {
        console.error('Invalid time format:', parts[2]);
        return null;
      }

      const time = timeParts[0];
      const isPM = timeParts[1] === 'PM';

      // Convert to 24-hour format
      const [hours, minutes, seconds] = time.split(':');
      let hour = parseInt(hours);
      if (isNaN(hour)) {
        console.error('Invalid hour format:', hours);
        return null;
      }

      if (isPM && hour !== 12) hour += 12;
      if (!isPM && hour === 12) hour = 0;

      const formattedTime = `${hour.toString().padStart(2, '0')}:${minutes}:${seconds}`;

      const date = new Date(`${parts[0]}, ${year}`);
      if (isNaN(date.getTime())) {
        console.error('Invalid date:', `${parts[0]}, ${year}`);
        return null;
      }

      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const day = date.getDate().toString().padStart(2, '0');

      return `${year}-${month}-${day}T${formattedTime}`;
    } catch (error) {
      console.error('Error parsing datetime:', dateTimeStr, error);
      return null;
    }
  }

  async getCalendarEnrollments(
    locationId,
    startDate,
    endDate,
    page = 1,
    limit = 50,
  ) {
    try {
      const locationObjectId = Types.ObjectId.createFromHexString(locationId);
      const startRange = startDate
        ? new Date(startDate)
        : new Date(new Date().setDate(1));
      const endRange = endDate
        ? new Date(endDate)
        : new Date(new Date().setMonth(startRange.getMonth() + 1));

      const dateFilter = {
        studio: locationObjectId,
        $or: [
          // Classes that start within our range
          { startDate: { $lte: endRange } },
          // AND end after our range starts
          { endDate: { $gte: startRange } },
        ],
      };

      const [enrollments, total] = await Promise.all([
        this.enrollmentModel
          .find(dateFilter)
          .select('title startDate endDate color _id availability')
          .skip((page - 1) * limit)
          .limit(limit)
          .exec(),
        this.enrollmentModel.countDocuments(dateFilter),
      ]);

      const expandedEnrollments = [];

      for (const enrollment of enrollments) {
        for (const slot of enrollment.availability) {
          const slotDay = slot.day; // Day in numeric form (0 = Sun, 1 = Mon, ... 6 = Sat)
          const slotStartTime = this.parseCustomDateTime(slot.startTime);
          const slotEndTime = this.parseCustomDateTime(slot.endTime)?.split(
            'T',
          )[1];

          if (!slotStartTime || !slotEndTime) continue;
          console.log(enrollment.startDate, enrollment.endDate, slotDay);
          const occurrences = this.getOccurrencesInRange(
            enrollment.startDate,
            enrollment.endDate,
            slotDay,
          );
          occurrences.forEach((date) => {
            expandedEnrollments.push({
              title: enrollment.title,
              start: `${date.toISOString().split('T')[0]}T${slotStartTime.split('T')[1]}`,
              end: `${date.toISOString().split('T')[0]}T${slotEndTime}`,
              resourceId: enrollment._id.toString(),
              color: enrollment.color,
              day: slotDay,
            });
          });
        }
      }

      return {
        data: expandedEnrollments,
        total: expandedEnrollments.length,
        page,
        lastPage: Math.ceil(expandedEnrollments.length / limit),
      };
    } catch (error) {
      console.error('Error in getCalendarEnrollments:', error);
      return {
        data: [],
        total: 0,
        page,
        lastPage: 0,
      };
    }
  }

  getOccurrencesInRange(startDate, endDate, targetDay) {
    const dayMap = {
      Sun: 0,
      Mon: 1,
      Tue: 2,
      Wed: 3,
      Thu: 4,
      Fri: 5,
      Sat: 6,
    };

    // Convert string to number if needed
    const targetDayNum =
      typeof targetDay === 'string' ? dayMap[targetDay] : targetDay;

    if (targetDayNum === undefined) {
      console.error('Invalid targetDay:', targetDay);
      return [];
    }

    const occurrences = [];
    const current = new Date(startDate);
    current.setUTCHours(0, 0, 0, 0);
    endDate = new Date(endDate);
    endDate.setUTCHours(23, 59, 59, 999);

    // Move forward to the next correct targetDay if needed
    while (current.getUTCDay() !== targetDayNum) {
      current.setUTCDate(current.getUTCDate() + 1);
    }

    while (current <= endDate) {
      if (current.getUTCDay() === targetDayNum) {
        occurrences.push(new Date(current));
      }
      current.setUTCDate(current.getUTCDate() + 7); // Move directly to the next same weekday
    }
    return occurrences;
  }

  async getActiveStudentCount(enrollmentId: string): Promise<number> {
    const students =
      await this.studentService.findAllByEnrollmentId(enrollmentId);
    return students.filter((student) =>
      student.enrollments.some(
        (e) =>
          e.enrollmentId.toString() === enrollmentId &&
          e.subscriptionStatus === 'active',
      ),
    ).length;
  }

  async findByClassIds(classIds: string[]) {
    return this.enrollmentModel
      .find({
        _id: { $in: classIds.map((id) => new Types.ObjectId(id)) },
        isDeleted: false,
      })
      .populate('instructor')
      .exec();
  }

  // Keep the old method for backward compatibility
  async findByStudentId(studentId: string) {
    return this.enrollmentModel
      .find({
        'students.studentId': new Types.ObjectId(studentId),
      })
      .populate('instructor')
      .exec();
  }

  async populateInstructor(enrollments: Enrollment[]): Promise<Enrollment[]> {
    return await this.enrollmentModel.populate(enrollments, {
      path: 'instructor',
      select: 'firstName lastName',
    });
  }

  async findByIds(ids: string[]) {
    return this.enrollmentModel
      .find({
        _id: { $in: ids.map((id) => new Types.ObjectId(id)) },
        isDeleted: false,
      })
      .populate({
        path: 'instructor',
        model: 'CustomForm',
        match: { fieldType: 'instructor' },
        select: 'fieldName',
      })
      .exec();
  }

  async calculateTotalPayments(enrollmentId: string) {
    const enrollment = await this.enrollmentModel.findById(enrollmentId);
    if (!enrollment) {
      throw new NotFoundException('Enrollment not found');
    }

    const oneTime = enrollment.tuitionBillingCycle === 'one-time';

    if (oneTime) {
      return {
        totalPayments: 1,
      };
    }

    const startDate = calculateSubscriptionStartDate(enrollment);
    const invoiceDates = generateInvoiceDates(
      startDate,
      enrollment.endDate,
      enrollment.tuitionBillingCycle,
      enrollment.billingDay,
    );
    if (invoiceDates.length === 0) {
      return {
        totalPayments: 0,
      };
    }
    return {
      invoiceDates,
      totalPayments: invoiceDates.length,
    };
  }

  /**
   * Handles subscription date changes by voiding upcoming invoices outside the new date range
   * and generating new invoices for new periods.
   */
  private async handleSubscriptionDateChange(
    subscription: Subscription,
    newStartDate: Date,
    newEndDate: Date,
    enrollment: Enrollment,
  ): Promise<void> {
    // 1. Find all UPCOMING invoices for this subscription
    const upcomingInvoices = await this.subscriptionInvoiceModel.find({
      subscriptionId: subscription._id,
      status: InvoiceStatus.UPCOMING,
    });

    // 2. Void invoices outside the new date range
    for (const invoice of upcomingInvoices) {
      if (invoice.startDate > newEndDate) {
        await this.subscriptionInvoiceModel.findByIdAndUpdate(invoice._id, {
          status: InvoiceStatus.VOID,
        });
      }
    }

    // 3. Generate new invoice dates for the new range
    const invoiceDates = generateInvoiceDates(
      newStartDate,
      newEndDate,
      subscription.billingCycle,
      enrollment.billingDay,
    );

    // 4. For each date in the new range, create a new invoice if one doesn't exist
    for (const date of invoiceDates) {
      const exists = upcomingInvoices.some(
        (inv) =>
          inv.startDate.getTime() === date.getTime() &&
          inv.status !== InvoiceStatus.VOID,
      );
      if (!exists) {
        // Copy discount logic from previous invoices (use the first UPCOMING invoice as reference)
        const referenceInvoice = upcomingInvoices[0];
        await this.subscriptionInvoiceModel.create({
          studioId: subscription.studioId,
          subscriptionId: subscription._id,
          parentId: subscription.parentId,
          studentId: subscription.studentId,
          entityId: subscription.entityId,
          entityType: subscription.entityType,
          baseAmount: subscription.baseAmount,
          status: InvoiceStatus.UPCOMING,
          paymentProvider: referenceInvoice?.paymentProvider,
          paymentMethod: referenceInvoice?.paymentMethod,
          line_items: referenceInvoice?.line_items || [],
          type: referenceInvoice?.type || 'subscription',
          dueDate: date,
          appliedCouponId: referenceInvoice?.appliedCouponId,
          finalAmount: referenceInvoice?.finalAmount || subscription.baseAmount,
          startDate: date,
          endDate:
            invoiceDates[invoiceDates.indexOf(date) + 1] ||
            new Date(date.getTime() + 24 * 60 * 60 * 1000),
          metadata: referenceInvoice?.metadata || {},
          transactionCodeId: referenceInvoice?.transactionCodeId,
        });
      }
    }
  }

  // Add these caching methods to the EnrollmentService class

  // Cache image URLs separately with longer TTL since they change less frequently
  private async getCachedImageUrl(
    locationId: string,
    enrollmentId: string,
    defaultImageUrl?: string,
    preferredSize: string = 'webp',
  ): Promise<string | undefined> {
    const cacheKey = `enrollment:image:${locationId}:${enrollmentId}:${preferredSize}`;

    try {
      const cached = await this.redis.get(cacheKey);
      if (cached) {
        return cached === 'null' ? defaultImageUrl : cached;
      }

      let imageUrl: string | undefined;

      // First try to get the specific variant size
      try {
        imageUrl = await this.gcpStorageService.getImageVariant(
          locationId,
          'class-preview-image',
          enrollmentId,
          preferredSize,
        );
      } catch (variantError) {
        this.logger.debug(
          `Failed to get image variant ${preferredSize} for enrollment ${enrollmentId}`,
          variantError,
        );
      }

      // Fallback to original image retrieval method if no variant found
      if (!imageUrl) {
        try {
          imageUrl = await this.gcpStorageService.getPublicImage(
            locationId,
            'class-preview-image',
            enrollmentId,
          );
        } catch (originalError) {
          this.logger.debug(
            `Failed to get original image for enrollment ${enrollmentId}`,
            originalError,
          );
        }
      }

      // Final fallback to default image URL
      if (!imageUrl) {
        imageUrl = defaultImageUrl;
      }

      // Cache for 1 hour since images don't change often
      await this.redis.set(cacheKey, imageUrl || 'null', 'EX', 3600);
      return imageUrl;
    } catch (error) {
      this.logger.debug(
        `Error fetching image for enrollment ${enrollmentId}`,
        error,
      );
      return defaultImageUrl;
    }
  }

  // Cache student counts separately
  private async getCachedStudentCount(enrollmentId: string): Promise<number> {
    const cacheKey = `enrollment:studentcount:${enrollmentId}`;

    try {
      const cached = await this.redis.get(cacheKey);
      if (cached) {
        return parseInt(cached);
      }

      const students =
        await this.studentService.findAllByEnrollmentId(enrollmentId);
      const activeCount = students.filter((student) =>
        student.enrollments.some(
          (e) =>
            e.enrollmentId.toString() === enrollmentId &&
            (e.subscriptionStatus === 'active' ||
              e.subscriptionStatus === 'scheduled'),
        ),
      ).length;

      // Cache for 5 minutes since student enrollments can change
      await this.redis.set(cacheKey, activeCount.toString(), 'EX', 300);
      return activeCount;
    } catch (error) {
      this.logger.debug(
        `Error fetching student count for enrollment ${enrollmentId}`,
        error,
      );
      return 0;
    }
  }

  // Cache reference data (rooms, instructors, tags, etc.) with longer TTL
  private async getCachedReferenceData(locationId: string): Promise<any> {
    const cacheKey = `enrollment:refdata:${locationId}`;

    try {
      const cached = await this.redis.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // Fetch all reference data in parallel
      const [rooms, instructors, sessions, locations, tags, ageStats] =
        await Promise.all([
          this.customFormModel
            .find({
              studio: Types.ObjectId.createFromHexString(locationId),
              fieldType: 'room',
            })
            .select('_id fieldName fieldType'),

          this.customFormModel
            .find({
              studio: Types.ObjectId.createFromHexString(locationId),
              fieldType: 'instructor',
            })
            .select('_id fieldName fieldType'),

          this.sessionModel
            .find({
              studioId: Types.ObjectId.createFromHexString(locationId),
            })
            .select('_id name'),

          this.customFormModel
            .find({
              studio: Types.ObjectId.createFromHexString(locationId),
              fieldType: 'location',
            })
            .select('_id fieldName fieldType'),

          this.tagModel
            .find({
              studioId: Types.ObjectId.createFromHexString(locationId),
            })
            .select('_id fieldName fieldType'),

          this.enrollmentModel.aggregate([
            {
              $match: {
                studio: Types.ObjectId.createFromHexString(locationId),
                startYear: { $exists: true },
                endYear: { $exists: true },
              },
            },
            {
              $group: {
                _id: null,
                startYear: { $min: '$startYear' },
                endYear: { $max: '$endYear' },
              },
            },
          ]),
        ]);

      const referenceData = {
        rooms: rooms.map((room) => ({
          _id: room._id,
          name: room.fieldName,
          fieldType: room.fieldType,
        })),
        instructors: instructors.map((instructor) => ({
          _id: instructor._id,
          name: instructor.fieldName,
          fieldType: instructor.fieldType,
        })),
        sessions: sessions.map((session) => ({
          _id: session._id,
          name: session.name,
        })),
        locations: locations.map((location) => ({
          _id: location._id,
          name: location.fieldName,
          fieldType: location.fieldType,
        })),
        tags: tags.map((tag) => ({
          _id: tag._id,
          name: tag.fieldName,
          fieldType: tag.fieldType,
        })),
        ages:
          ageStats.length > 0
            ? {
                minAge: ageStats[0].startYear,
                maxAge: ageStats[0].endYear,
              }
            : {},
      };

      // Cache for 30 minutes since reference data changes less frequently
      await this.redis.set(cacheKey, JSON.stringify(referenceData), 'EX', 1800);
      return referenceData;
    } catch (error) {
      this.logger.debug(
        `Error fetching reference data for location ${locationId}`,
        error,
      );
      return {
        rooms: [],
        instructors: [],
        sessions: [],
        locations: [],
        tags: [],
        ages: {},
      };
    }
  }

  // Invalidate related caches when data changes
  async invalidateRelatedCaches(
    locationId: string,
    enrollmentIds: string[] = [],
  ): Promise<void> {
    try {
      const patterns = [
        `enrollment:search:${locationId}:*`,
        `enrollment:refdata:${locationId}`,
      ];

      // Add enrollment-specific cache patterns
      for (const enrollmentId of enrollmentIds) {
        patterns.push(`enrollment:image:${locationId}:${enrollmentId}`);
        patterns.push(`enrollment:studentcount:${enrollmentId}`);
      }

      // Delete all matching keys
      for (const pattern of patterns) {
        const keys = await this.redis.keys(pattern);
        if (keys.length > 0) {
          await this.redis.del(...keys);
        }
      }
    } catch (error) {
      this.logger.warn('Error invalidating related caches:', error);
    }
  }
}
