import {
  IsString,
  IsOptional,
  IsBoolean,
  IsArray,
  IsN<PERSON>ber,
  IsMongoId,
  IsDateString,
  IsISO8601,
} from 'class-validator';
import { Types } from 'mongoose';

export class CreateEnrollmentDto {
  @IsString()
  title: string;

  @IsMongoId()
  studio: Types.ObjectId;

  @IsArray()
  @IsMongoId({ each: true })
  students: Types.ObjectId[];

  @IsOptional()
  @IsArray()
  @IsISO8601({}, { each: true })
  schedules?: Date[];

  @IsOptional()
  @IsMongoId()
  location?: string;

  @IsOptional()
  @IsMongoId()
  room?: string | null;

  @IsOptional()
  @IsArray()
  @IsMongoId({ each: true })
  instructor?: string[];

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  days?: string[];

  @IsOptional()
  @IsString()
  startTime?: string;

  @IsOptional()
  @IsString()
  endTime?: string;

  @IsOptional()
  @IsNumber()
  duration?: number;

  @IsOptional()
  @IsISO8601()
  registrationStartDate?: Date;

  @IsOptional()
  @IsISO8601()
  registrationEndDate?: Date;

  @IsOptional()
  @IsISO8601()
  startDate?: Date;

  @IsOptional()
  @IsISO8601()
  endDate?: Date;

  @IsNumber()
  tuitionFee: number;

  @IsOptional()
  @IsNumber()
  costumeFee?: number;

  @IsOptional()
  @IsNumber()
  maxSize?: number;

  @IsOptional()
  @IsNumber()
  startYear?: number;

  @IsOptional()
  @IsNumber()
  endYear?: number;

  @IsOptional()
  @IsNumber()
  startMonth?: number;

  @IsOptional()
  @IsNumber()
  endMonth?: number;

  @IsOptional()
  @IsNumber()
  registrationFeeAmount?: number;

  @IsOptional()
  @IsNumber()
  maxWait?: number;

  @IsOptional()
  @IsBoolean()
  allowOnlineRegistration?: boolean;

  @IsOptional()
  @IsBoolean()
  allowPortalEnrollment?: boolean;

  @IsOptional()
  @IsBoolean()
  allowTrialEnrollment?: boolean;

  @IsOptional()
  @IsBoolean()
  displayOnWebsite?: boolean;

  @IsOptional()
  @IsString()
  tuitionBillingMethod?: string;

  @IsOptional()
  @IsString()
  tuitionBillingCycle?: string;

  @IsOptional()
  @IsNumber()
  billingDay?: number;

  @IsOptional()
  @IsString()
  tuitionDiscountRule?: string;

  @IsOptional()
  @IsBoolean()
  prorateTuition?: boolean;

  @IsOptional()
  @IsBoolean()
  registrationFee?: boolean;

  @IsOptional()
  @IsArray()
  @IsMongoId({ each: true })
  tags?: string[];

  @IsOptional()
  @IsArray()
  @IsMongoId({ each: true })
  group?: string[];

  @IsOptional()
  @IsMongoId()
  session?: string;

  @IsOptional()
  @IsArray()
  @IsMongoId({ each: true })
  policyGroup?: string[];

  @IsOptional()
  @IsString()
  mainTeacher?: string;

  @IsOptional()
  @IsString()
  subTeacher?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  calendarId_ghl?: string;

  @IsOptional()
  @IsString()
  productId_ghl?: string;

  @IsOptional()
  @IsArray()
  availability?: [
    {
      day: string;
      startTime: string;
      endTime: string;
    },
  ];

  @IsOptional()
  @IsString()
  timeConfig?: 'same' | 'different';

  @IsOptional()
  @IsBoolean()
  hide?: boolean;

  @IsOptional()
  @IsString()
  color?: string;

  @IsOptional()
  image: Buffer;

  @IsOptional()
  @IsString()
  imageName?: string;

  @IsOptional()
  @IsString()
  imageUrl?: string;

  @IsOptional()
  @IsString()
  defaultImageUrl?: string;
}
