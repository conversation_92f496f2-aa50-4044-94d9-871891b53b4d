export interface MappedEnrollmentDto {
  title: string;
  classColor: string;
  hideClassStatus: string;
  location: string;
  room: string;
  startAgeYear: string;
  startAgeMonth: string;
  endAgeYear: string;
  endAgeMonth: string;
  instructor: string;
  description: string;
  session: string;
  startDate: string;
  endDate: string;
  registrationStartDate: string;
  days: string;
  startTime: string;
  endTime: string;
  registrationFee: string;
  tuitionFee: string;
  costumeFee: string;
  classSize: string;
  group: string;
  tuitionBillingCycle: string;
  studio: string;
  productId_ghl?: string;
}

export function mapEnrollmentDtoToCamelCase(dto: any): MappedEnrollmentDto {
  return {
    title: dto.title,
    classColor: dto['class color'],
    hideClassStatus: dto['hide class status'],
    location: dto.location,
    room: dto.room,
    startAgeYear: dto['start age year'],
    startAgeMonth: dto['start age month'],
    endAgeYear: dto['end age year'],
    endAgeMonth: dto['end age month'],
    instructor: dto.instructor,
    description: dto['description (under 200 characters)'],
    session: dto.session,
    startDate: dto['start date'],
    endDate: dto['end date'],
    registrationStartDate: dto['registration start date'],
    days: dto.days,
    startTime: dto['start time'],
    endTime: dto['end time'],
    registrationFee: dto['registration fee'],
    tuitionFee: dto['tuition fee'],
    costumeFee: dto['costume fee'],
    classSize: dto['class size'],
    group: dto.group,
    tuitionBillingCycle:
      dto['tuition billing cycle'].toLowerCase() === 'one time'
        ? 'one-time'
        : dto['tuition billing cycle'],
    studio: dto.studio,
    productId_ghl: dto.productId_ghl,
  };
}
