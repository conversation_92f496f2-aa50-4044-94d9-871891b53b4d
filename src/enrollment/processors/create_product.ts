import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { Logger } from '@nestjs/common';
import { GohighlevelService } from 'src/gohighlevel/gohighlevel.service';

@Processor('enrollment-create-product')
export class CreateProductProcessor extends WorkerHost {
  private readonly logger = new Logger(CreateProductProcessor.name);

  constructor(private readonly ghlService: GohighlevelService) {
    super();
  }

  async process(job: Job) {
    this.logger.log(`Processing create-product job ${job.id}`);
    const { enrollment, locationId_ghl } = job.data;
    const product = await this.ghlService.createProduct(
      enrollment.title,
      locationId_ghl,
      enrollment['description (under 200 characters)'],
    );
    return { productId_ghl: product._id };
  }
}
