import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { Logger } from '@nestjs/common';
import { CurrencyService } from 'src/currency/currency.service';
import { GohighlevelService } from 'src/gohighlevel/gohighlevel.service';
import { Model, Types } from 'mongoose';
import { mapEnrollmentDtoToCamelCase } from '../dto/mapdto';
import { createBodyToCreatePriceForAProductInGHL } from 'src/utils/helperFunction';
import { InjectModel } from '@nestjs/mongoose';
import { Proration } from 'src/database/schema/prorations';
import { CreateEnrollmentDto } from '../dto/create-enrollment.dto';

@Processor('enrollment-create-product-price')
export class CreateProductPriceProcessor extends WorkerHost {
  private readonly logger = new Logger(CreateProductPriceProcessor.name);

  constructor(
    private readonly ghlService: GohighlevelService,
    private readonly currencyService: CurrencyService,
    @InjectModel(Proration.name)
    private readonly prorationModel: Model<Proration>,
  ) {
    super();
  }

  async process(job: Job) {
    this.logger.log(`Processing create-product-price job ${job.id}`);
    const { locationId_ghl, productId_ghl, createEnrollmentDto, studio } =
      job.data;
    const mappedDto = mapEnrollmentDtoToCamelCase(createEnrollmentDto);
    const currency = await this.currencyService.findByStudioId(
      Types.ObjectId.createFromHexString(studio._id),
    );
    const price_body = await createBodyToCreatePriceForAProductInGHL(
      mappedDto as unknown as CreateEnrollmentDto,
      locationId_ghl,
      'class',
      productId_ghl,
      this.prorationModel,
      currency.name,
    );
    const parsedPriceBody = {
      ...price_body,
      amount: parseInt(price_body.amount, 10),
    };
    await this.ghlService.createPriceForAProduct(
      locationId_ghl,
      productId_ghl,
      parsedPriceBody,
    );
    return { success: true };
  }
}
