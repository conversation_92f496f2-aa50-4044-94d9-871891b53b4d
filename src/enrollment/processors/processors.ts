import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { GohighlevelService } from 'src/gohighlevel/gohighlevel.service';
import { EnrollmentService } from '../enrollment.service';
import {
  CalendarDayArrayIndexMap,
  CalendarDto,
  CalendarType,
  MeetingLocationType,
  RecurrenceBookingOption,
  RecurrenceFrequency,
} from 'src/gohighlevel/dto/createCalendarDto';
import {
  calculateBlockDateRanges,
  calculateSlotDuration,
  convertTo24HourTime,
  JobNames,
} from 'src/utils/helperFunction';
import { createBodyToCreatePriceForAProductInGHL } from 'src/utils/helperFunction';
import { CurrencyService } from 'src/currency/currency.service';
import { InjectModel } from '@nestjs/mongoose';
import { Proration } from 'src/database/schema/prorations';
import { Model, Types } from 'mongoose';
import {
  mapEnrollmentDtoToCamelCase,
  MappedEnrollmentDto,
} from '../dto/mapdto';
import { CreateEnrollmentDto } from '../dto/create-enrollment.dto';

@Processor('enrollment-queue')
export class EnrollmentProcessor extends WorkerHost {
  private readonly logger = new Logger(EnrollmentProcessor.name);

  constructor(
    private readonly ghlService: GohighlevelService,
    private readonly enrollmentService: EnrollmentService,
    private readonly currencyService: CurrencyService,
    @InjectModel(Proration.name) private prorationModel: Model<Proration>,
  ) {
    super();
    this.logger.log('🚀 Enrollment processor initialized');
  }

  async process(job: Job) {
    const jobName = job.name.toString().trim();
    this.logger.log(`📝 Starting job ${job.id} of type: ${jobName}`);
    this.logger.log(`Job name raw: [${job.name}] Length: ${job.name.length}`);
    // this.logger.debug('Job data:', job.data);

    try {
      switch (jobName) {
        case JobNames.CREATE_PRODUCT:
          return await this.handleProductCreation(job.data);
        case JobNames.CREATE_PRODUCT_PRICE:
          return await this.handlePriceCreation(job.data);
        case JobNames.CREATE_CALENDAR:
          return await this.handleCalendarCreation(job.data);
        case JobNames.BLOCK_CALENDAR_SLOTS:
          return await this.handleCalendarBlocking(job.data);
        default:
          this.logger.log(
            `Job name raw: [${job.name}] Length: ${job.name.length}`,
          );
          this.logger.error(`Unknown job name: ${job.name}`);
          throw job.failedReason || new Error(`Unknown job name: ${job.name}`);
      }
    } catch (error) {
      this.logger.log(`Job name raw: [${job.name}] Length: ${job.name.length}`);
      this.logger.error(`❌ Error processing job ${job.id}:`, error);
      throw error;
    }
  }

  private async handleProductCreation(data: any) {
    const { enrollment, locationId_ghl } = data;
    const product = await this.ghlService.createProduct(
      enrollment.title,
      locationId_ghl,
      enrollment['description (under 200 characters)'],
    );
    return { productId_ghl: product._id };
  }

  private async handlePriceCreation(data: any) {
    const { locationId_ghl, productId_ghl, createEnrollmentDto, studio } = data;
    const mappedDto = mapEnrollmentDtoToCamelCase(createEnrollmentDto);
    const currency = await this.currencyService.findByStudioId(
      Types.ObjectId.createFromHexString(studio._id),
    );
    const price_body = await createBodyToCreatePriceForAProductInGHL(
      mappedDto as unknown as CreateEnrollmentDto,
      locationId_ghl,
      'class',
      productId_ghl,
      this.prorationModel,
      currency.name,
    );
    const parsedPriceBody = {
      ...price_body,
      amount: parseInt(price_body.amount, 10),
    };
    await this.ghlService.createPriceForAProduct(
      locationId_ghl,
      productId_ghl,
      parsedPriceBody,
    );
    return { success: true };
  }

  private async handleCalendarCreation(data: any) {
    const {
      enrollment,
      locationId_ghl,
      createEnrollmentDto,
      studio,
      availability,
    } = data;
    const calendarDetails = await this.buildCalendarDetails(data);
    const calendarId = await this.ghlService.createCalendar(
      enrollment.title,
      locationId_ghl,
      calendarDetails,
    );
    return { calendarId_ghl: calendarId };
  }

  private async handleCalendarBlocking(data: any) {
    const { locationId_ghl, calendarId_ghl, createEnrollmentDto } = data;
    const blockDateRanges = await calculateBlockDateRanges(
      createEnrollmentDto['start date'],
      createEnrollmentDto['end date'],
    );

    await Promise.all([
      this.ghlService.blockCalendarSlots(
        locationId_ghl,
        calendarId_ghl,
        createEnrollmentDto,
        blockDateRanges.firstRange.start,
        blockDateRanges.firstRange.end,
      ),
      this.ghlService.blockCalendarSlots(
        locationId_ghl,
        calendarId_ghl,
        createEnrollmentDto,
        blockDateRanges.secondRange.start,
        blockDateRanges.secondRange.end,
      ),
    ]);
    return { success: true };
  }

  private async buildCalendarDetails(data: any): Promise<any> {
    const { locationId_ghl, createEnrollmentDto, studio, availability } = data;

    const slotDuration = calculateSlotDuration(
      availability[0].startTime,
      availability[0].endTime,
    );

    const openHours = availability.map((day) => {
      const { hour: openHour, minute: openMinute } = convertTo24HourTime(
        day.startTime,
      );
      const { hour: closeHour, minute: closeMinute } = convertTo24HourTime(
        day.endTime,
      );
      return {
        daysOfTheWeek: [
          CalendarDayArrayIndexMap[
            day.day as keyof typeof CalendarDayArrayIndexMap
          ],
        ], // Map day to index
        hours: [
          {
            openHour,
            openMinute,
            closeHour,
            closeMinute,
          },
        ],
      };
    });
    const createCalendarDetails: Partial<CalendarDto> = {
      description: createEnrollmentDto['description (under 200 characters)'],
      calendarType: CalendarType.CLASS_BOOKING,
      appoinmentPerSlot: createEnrollmentDto['class size'],
      allowReschedule: false,
      enableRecurring: true,
      recurring: {
        freq: RecurrenceFrequency.WEEKLY,
        count: 24,
        bookingOption: RecurrenceBookingOption.SKIP,
      },
      teamMembers: [
        {
          userId: studio.userId,
          meetingLocation: createEnrollmentDto.location.toString(),
          meetingLocationType: MeetingLocationType.CUSTOM,
        },
      ],
      openHours: openHours,
      slotDuration: slotDuration,
      slotDurationUnit: 'mins',
    };
    this.logger.log(
      `Creating calendar body for enrollment: ${createEnrollmentDto.title}`,
    );
    return createCalendarDetails;
  }
}
