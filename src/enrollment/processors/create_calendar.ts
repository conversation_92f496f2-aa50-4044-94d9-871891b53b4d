import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { Logger } from '@nestjs/common';
import { GohighlevelService } from 'src/gohighlevel/gohighlevel.service';
import {
  CalendarDayArrayIndexMap,
  CalendarDto,
  CalendarType,
  MeetingLocationType,
  RecurrenceBookingOption,
  RecurrenceFrequency,
} from 'src/gohighlevel/dto/createCalendarDto';
import {
  calculateBlockDateRanges,
  calculateSlotDuration,
  convertTo24HourTime,
  JobNames,
} from 'src/utils/helperFunction';
import { createBodyToCreatePriceForAProductInGHL } from 'src/utils/helperFunction';
import { CurrencyService } from 'src/currency/currency.service';

@Processor('enrollment-create-calendar')
export class CreateCalendarProcessor extends WorkerHost {
  private readonly logger = new Logger(CreateCalendarProcessor.name);

  constructor(private readonly ghlService: GohighlevelService) {
    super();
  }

  async process(job: Job) {
    this.logger.log(`Processing create-calendar job ${job.id}`);
    const {
      enrollment,
      locationId_ghl,
      createEnrollmentDto,
      studio,
      availability,
    } = job.data;
    const calendarDetails = await this.buildCalendarDetails({
      createEnrollmentDto,
      studio,
      availability,
    });
    const calendarId = await this.ghlService.createCalendar(
      enrollment.title,
      locationId_ghl,
      calendarDetails,
    );

    //fetech timezone from google calendar
    const locationDetails =
      await this.ghlService.getLocationDetails(locationId_ghl);
    const timezone = locationDetails.location.timezone;

    try {
      const blockDateRanges = await calculateBlockDateRanges(
        createEnrollmentDto.startDate,
        createEnrollmentDto.endDate,
        timezone,
      );

      await Promise.all([
        this.ghlService.blockCalendarSlots(
          locationId_ghl,
          calendarId,
          createEnrollmentDto,
          blockDateRanges.firstRange.start,
          blockDateRanges.firstRange.end,
        ),
        this.ghlService.blockCalendarSlots(
          locationId_ghl,
          calendarId,
          createEnrollmentDto,
          blockDateRanges.secondRange.start,
          blockDateRanges.secondRange.end,
        ),
      ]);
    } catch (error) {
      console.error('Failed to block calendar slots:', error);
      this.logger.error({
        message: 'Failed to create GHL calendar',
        context: locationId_ghl,
      });
    }
    return { calendarId_ghl: calendarId };
  }

  private async buildCalendarDetails(data: any): Promise<any> {
    const { locationId_ghl, createEnrollmentDto, studio, availability } = data;

    const slotDuration = calculateSlotDuration(
      availability[0].startTime,
      availability[0].endTime,
    );

    const openHours = availability.map((day) => {
      const { hour: openHour, minute: openMinute } = convertTo24HourTime(
        day.startTime,
      );
      const { hour: closeHour, minute: closeMinute } = convertTo24HourTime(
        day.endTime,
      );
      return {
        daysOfTheWeek: [
          CalendarDayArrayIndexMap[
            day.day as keyof typeof CalendarDayArrayIndexMap
          ],
        ], // Map day to index
        hours: [
          {
            openHour,
            openMinute,
            closeHour,
            closeMinute,
          },
        ],
      };
    });
    const createCalendarDetails: Partial<CalendarDto> = {
      description: createEnrollmentDto['description (under 200 characters)'],
      calendarType: CalendarType.EVENT,
      appoinmentPerSlot: parseInt(createEnrollmentDto['class size'], 10),
      allowReschedule: false,
      enableRecurring: false,
      teamMembers: [
        {
          userId: studio.userId,
          meetingLocation: createEnrollmentDto.location
            ? createEnrollmentDto.location.toString()
            : null,
          meetingLocationType: MeetingLocationType.CUSTOM,
        },
      ],
      openHours: openHours,
      slotDuration: slotDuration,
      slotDurationUnit: 'mins',
    };
    this.logger.log(
      `Creating calendar body for enrollment: ${createEnrollmentDto.title}`,
    );
    return createCalendarDetails;
  }
}
