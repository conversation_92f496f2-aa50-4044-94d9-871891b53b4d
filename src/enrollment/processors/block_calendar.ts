import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { Logger } from '@nestjs/common';
import { GohighlevelService } from 'src/gohighlevel/gohighlevel.service';
import { calculateBlockDateRanges } from 'src/utils/helperFunction';

@Processor('enrollment-block-calendar-slots')
export class BlockCalendarSlotsProcessor extends WorkerHost {
  private readonly logger = new Logger(BlockCalendarSlotsProcessor.name);

  constructor(private readonly ghlService: GohighlevelService) {
    super();
  }

  async process(job: Job) {
    this.logger.log(`Processing block-calendar-slots job ${job.id}`);
    const { locationId_ghl, calendarId_ghl, createEnrollmentDto } = job.data;
    const blockDateRanges = await calculateBlockDateRanges(
      createEnrollmentDto['start date'],
      createEnrollmentDto['end date'],
    );
    await Promise.all([
      this.ghlService.blockCalendarSlots(
        locationId_ghl,
        calendarId_ghl,
        createEnrollmentDto,
        blockDateRanges.firstRange.start,
        blockDateRanges.firstRange.end,
      ),
      this.ghlService.blockCalendarSlots(
        locationId_ghl,
        calendarId_ghl,
        createEnrollmentDto,
        blockDateRanges.secondRange.start,
        blockDateRanges.secondRange.end,
      ),
    ]);
    return { success: true };
  }
}
