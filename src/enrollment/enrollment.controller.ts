import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  NotFoundException,
  Req,
  UseGuards,
  Patch,
  Query,
  UseInterceptors,
  UploadedFile,
  Delete,
  Inject,
} from '@nestjs/common';
import { EnrollmentService } from './enrollment.service';
import { CreateEnrollmentDto } from './dto/create-enrollment.dto';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { Types } from 'mongoose';
import { UpdateEnrollmentDto } from './dto/update-enrollment.dto';
import { FileInterceptor } from '@nestjs/platform-express';
import * as csv from 'csv-parse/sync';
import type { Redis } from 'ioredis';

@Controller('enrollments')
export class EnrollmentController {
  constructor(
    private readonly enrollmentService: EnrollmentService,
    @Inject('REDIS_CLIENT') private readonly redis: Redis,
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(FileInterceptor('file'))
  async createEnrollment(
    @UploadedFile() file: Express.Multer.File,
    @Body('payload') payload: string,
    @Body('fileUrl') fileUrl: string,
    @Body('fileType') fileType: 'file' | 'url',
    @Req() request: Request,
  ) {
    try {
      const locationId = request['locationId'];
      const createEnrollmentDto: CreateEnrollmentDto = JSON.parse(payload);

      if (file && fileType === 'file') {
        createEnrollmentDto.image = file.buffer;
        createEnrollmentDto.imageName = file.originalname;
      } else if (fileType === 'url') {
        createEnrollmentDto.imageUrl = fileUrl;
      }

      createEnrollmentDto.studio =
        Types.ObjectId.createFromHexString(locationId);
      const result = await this.enrollmentService.create(
        createEnrollmentDto,
        locationId,
      );

      // Invalidate search cache after creating enrollment
      await this.invalidateSearchCache(locationId);
      // Also invalidate in service for detailed cache invalidation
      await this.enrollmentService.invalidateRelatedCaches(locationId);

      return result;
    } catch (error) {
      throw new NotFoundException('Enrollment could not be created');
    }
  }

  @Post('/bulk-import')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(FileInterceptor('classes'))
  async createBulkEnrollment(
    @UploadedFile() file: Express.Multer.File,
    @Req() request: Request,
  ) {
    try {
      const locationId = request['locationId'];
      const csvString = file.buffer.toString();

      // Parse CSV to get raw data first
      const rawData: any[] = csv.parse(csvString, {
        columns: (headers) =>
          headers.map((header) => header.toLowerCase().trim()),
        trim: true,
      });

      // Check if billing day column exists
      const firstRow = rawData[0];
      const hasBillingDay = firstRow && 'billing day' in firstRow;

      // Add billingDay to each record if not present
      const processedRecords = rawData.map((record) => {
        // Add billing day if not present, default to 1
        if (
          record['tuition billing cycle'] &&
          record['tuition billing cycle'].toLowerCase() === 'monthly' &&
          (!hasBillingDay || !record['billing day'])
        ) {
          record['billing day'] = 1;
        }
        record.studio = Types.ObjectId.createFromHexString(locationId);
        return record;
      });

      console.log(
        `Original records: ${rawData.length}, Processed records: ${processedRecords.length}`,
      );

      const result = await this.enrollmentService.bulkCreateEnrollments(
        processedRecords,
        locationId,
      );

      // Invalidate search cache after bulk creating enrollments
      await this.invalidateSearchCache(locationId);

      return result;
    } catch (error) {
      console.error('Bulk enrollment error:', error);
      throw new NotFoundException('Enrollment could not be created');
    }
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(FileInterceptor('file'))
  async update(
    @Param('id') id: string,
    @UploadedFile() file: Express.Multer.File,
    @Body('payload') payload: string,
    @Body('fileUrl') fileUrl: string,
    @Body('fileType') fileType: 'file' | 'url',
    @Req() request: Request,
  ) {
    try {
      const locationId = request['locationId'];
      const updateEnrollmentDto: UpdateEnrollmentDto = JSON.parse(payload);

      if (file) {
        updateEnrollmentDto.image = file.buffer;
        updateEnrollmentDto.imageName = file.originalname;
      } else if (fileType === 'url') {
        updateEnrollmentDto.imageUrl = fileUrl;
      }

      updateEnrollmentDto.studio =
        Types.ObjectId.createFromHexString(locationId);
      const result = await this.enrollmentService.update(
        id,
        updateEnrollmentDto,
        locationId,
      );

      // Invalidate search cache after updating enrollment
      await this.invalidateSearchCache(locationId);

      return result;
    } catch (error) {
      throw new NotFoundException('Enrollment could not be updated');
    }
  }

  // @Post('locationn/:locationId')
  // async createEnrollmentByLocationId(
  //   @Body() createEnrollmentDto: CreateEnrollmentDto,
  //   @Param('locationId') locationId: string,
  // ) {
  //   try {
  //     return await this.enrollmentService.createByLocationIdParam(
  //       createEnrollmentDto,
  //       locationId,
  //     );
  //   } catch (error) {
  //     throw new NotFoundException('Enrollment could not be created');
  //   }
  // }

  @Get(':id')
  async getEnrollment(@Param('id') id: string) {
    try {
      return await this.enrollmentService.findOne(id);
    } catch (error) {
      throw new NotFoundException('Enrollment not found');
    }
  }

  @Get(':id/active')
  async getActiveEnrollment(@Param('id') id: string) {
    try {
      return await this.enrollmentService.findOneActive(id);
    } catch (error) {
      throw new NotFoundException('Enrollment not found');
    }
  }

  @Get('/get/all')
  @UseGuards(JwtAuthGuard)
  async getAllEnrollments(@Req() request: Request) {
    const locationId = request['locationId'];
    return await this.enrollmentService.findAllByStudioId(locationId);
  }

  @Get('/get/all/csv')
  @UseGuards(JwtAuthGuard)
  async getAllEnrollmentsCsv(@Req() request: Request) {
    const locationId = request['locationId'];
    return await this.enrollmentService.findAllByStudioIdCsv(locationId);
  }

  @Get('/get/all/:locationId')
  // @UseGuards(JwtAuthGuard)
  async getAllEnrollmentsById(@Param('locationId') locationId: string) {
    // const locationId = request['locationId'];
    return await this.enrollmentService.findAllByLocationId_Ghl(locationId);
  }

  @Get('search/class')
  @UseGuards(JwtAuthGuard)
  async searchEnrollments(
    @Req() request: Request,
    @Query('name') name?: string,
    @Query('minAge') minAge?: string,
    @Query('maxAge') maxAge?: string,
    @Query('room') room?: string,
    @Query('location') location?: string | string[],
    @Query('instructor') instructor?: string | string[],
    @Query('days') days?: string | string[],
    @Query('tags') tags?: string,
    @Query('session') session?: string,
    @Query('sessionId') sessionId?: string,
    @Query('studio') studio: string = 'true',
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    const locationId = request['locationId'];

    // Create cache key based on all search parameters
    const cacheKey = this.generateCacheKey(locationId, {
      name,
      minAge,
      maxAge,
      room,
      location,
      instructor,
      days,
      tags,
      session,
      sessionId,
      studio,
      page,
      limit,
    });

    try {
      // Check cache first
      const cachedResult = await this.redis.get(cacheKey);
      if (cachedResult) {
        return JSON.parse(cachedResult);
      }
    } catch (error) {
      // Log cache read error but continue with normal flow
      console.warn('Redis cache read error:', error.message);
    }

    // Handle age range parameters
    const processedMinAge = minAge ? parseInt(minAge) : undefined;
    const processedMaxAge = maxAge ? parseInt(maxAge) : undefined;

    // Handle multi-select parameters - convert comma-separated strings to arrays
    const processedInstructor = Array.isArray(instructor)
      ? instructor
      : instructor
        ? instructor.split(',').map((i) => i.trim())
        : undefined;
    const processedDays = Array.isArray(days)
      ? days
      : days
        ? days.split(',').map((d) => d.trim())
        : undefined;
    const processedLocation = Array.isArray(location)
      ? location
      : location
        ? location.split(',').map((l) => l.trim())
        : undefined;

    const result = await this.enrollmentService.searchByProperties(locationId, {
      name,
      minAge: processedMinAge,
      maxAge: processedMaxAge,
      room,
      location: processedLocation,
      instructor: processedInstructor,
      days: processedDays,
      tags,
      session,
      sessionId,
      studio,
      pagination: {
        page: Number(page),
        limit: Number(limit),
      },
    });

    // Cache the result with dynamic TTL
    try {
      const ttl = this.getCacheTTL({
        name,
        minAge,
        maxAge,
        room,
        location,
        instructor,
        days,
        tags,
        session,
        sessionId,
        studio,
        page,
        limit,
      });
      await this.redis.set(cacheKey, JSON.stringify(result), 'EX', ttl);
    } catch (error) {
      // Log cache write error but continue with normal flow
      console.warn('Redis cache write error:', error.message);
    }

    return result;
  }

  private generateCacheKey(locationId: string, filters: any): string {
    // Create a deterministic cache key from the filters
    const sortedFilters = Object.keys(filters)
      .sort()
      .reduce((result, key) => {
        if (filters[key] !== undefined && filters[key] !== null) {
          // Handle arrays by sorting and joining
          if (Array.isArray(filters[key])) {
            result[key] = filters[key].sort().join(',');
          } else {
            result[key] = filters[key];
          }
        }
        return result;
      }, {});

    const filtersString = JSON.stringify(sortedFilters);
    return `enrollment:search:${locationId}:${Buffer.from(filtersString).toString('base64')}`;
  }

  private async invalidateSearchCache(locationId: string): Promise<void> {
    try {
      // Use Redis SCAN to find all cache keys for this location
      const pattern = `enrollment:search:${locationId}:*`;
      const keys = await this.redis.keys(pattern);

      if (keys.length > 0) {
        await this.redis.del(...keys);
        console.log(
          `Invalidated ${keys.length} cache entries for location ${locationId}`,
        );
      }
    } catch (error) {
      console.warn('Redis cache invalidation error:', error.message);
    }
  }

  private getCacheTTL(filters: any): number {
    // Base TTL of 5 minutes (300 seconds)
    let ttl = 300;

    // For searches with many filters, use longer TTL since they're more specific
    const filterCount = Object.values(filters).filter(
      (v) => v !== undefined && v !== null,
    ).length;
    if (filterCount > 5) {
      ttl = 600; // 10 minutes for complex searches
    }

    // For paginated results beyond page 1, use shorter TTL since they're less frequently accessed
    if (filters.page && filters.page > 1) {
      ttl = 180; // 3 minutes for later pages
    }

    return ttl;
  }

  // Cache warming endpoint for frequently accessed searches
  @Get('search/warm-cache')
  @UseGuards(JwtAuthGuard)
  async warmSearchCache(@Req() request: Request) {
    const locationId = request['locationId'];

    // Warm cache for common search patterns
    const commonSearches = [
      { studio: 'false', page: 1, limit: 10 }, // Default search
      { studio: 'true', page: 1, limit: 10 }, // Studio search
      { page: 1, limit: 20 }, // Larger page size
    ];

    const results = await Promise.allSettled(
      commonSearches.map(async (filters) => {
        const cacheKey = this.generateCacheKey(locationId, filters);
        const cached = await this.redis.get(cacheKey);

        if (!cached) {
          const result = await this.enrollmentService.searchByProperties(
            locationId,
            filters,
          );
          await this.redis.set(
            cacheKey,
            JSON.stringify(result),
            'EX',
            this.getCacheTTL(filters),
          );
          return { warmed: true, filters };
        }
        return { warmed: false, filters };
      }),
    );

    return {
      message: 'Cache warming completed',
      results: results.map((r) =>
        r.status === 'fulfilled' ? r.value : { error: true },
      ),
    };
  }

  @Get('search/class/studio')
  @UseGuards(JwtAuthGuard)
  async searchEnrollmentsForStudio(
    @Req() request: Request,
    @Query('name') name?: string,
    @Query('minAge') minAge?: string,
    @Query('maxAge') maxAge?: string,
    @Query('room') room?: string,
    @Query('location') location?: string | string[],
    @Query('instructor') instructor?: string | string[],
    @Query('days') days?: string | string[],
    @Query('tags') tags?: string,
    @Query('session') session?: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    const locationId = request['locationId'];

    // Create cache key based on all search parameters
    const cacheKey = this.generateCacheKey(locationId, {
      name,
      minAge,
      maxAge,
      room,
      location,
      instructor,
      days,
      tags,
      session,
      page,
      limit,
      studio: true, // Add studio flag to differentiate from regular search
    });

    try {
      // Check cache first
      const cachedResult = await this.redis.get(cacheKey);
      if (cachedResult) {
        return JSON.parse(cachedResult);
      }
    } catch (error) {
      // Log cache read error but continue with normal flow
      console.warn('Redis cache read error:', error.message);
    }

    // Handle age range parameters
    const processedMinAge = minAge ? parseInt(minAge) : undefined;
    const processedMaxAge = maxAge ? parseInt(maxAge) : undefined;

    // Handle multi-select parameters - convert comma-separated strings to arrays
    const processedInstructor = Array.isArray(instructor)
      ? instructor
      : instructor
        ? instructor.split(',').map((i) => i.trim())
        : undefined;
    const processedDays = Array.isArray(days)
      ? days
      : days
        ? days.split(',').map((d) => d.trim())
        : undefined;
    const processedLocation = Array.isArray(location)
      ? location
      : location
        ? location.split(',').map((l) => l.trim())
        : undefined;

    const result = await this.enrollmentService.searchByProperties(locationId, {
      name,
      minAge: processedMinAge,
      maxAge: processedMaxAge,
      room,
      location: processedLocation,
      instructor: processedInstructor,
      days: processedDays,
      tags,
      session,
      pagination: {
        page: Number(page),
        limit: Number(limit),
      },
    });

    // Cache the result with dynamic TTL
    try {
      const ttl = this.getCacheTTL({
        name,
        minAge,
        maxAge,
        room,
        location,
        instructor,
        days,
        tags,
        session,
        page,
        limit,
        studio: true,
      });
      await this.redis.set(cacheKey, JSON.stringify(result), 'EX', ttl);
    } catch (error) {
      // Log cache write error but continue with normal flow
      console.warn('Redis cache write error:', error.message);
    }

    return result;
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  async remove(@Param('id') id: string, @Req() request: Request) {
    const studioId = request['locationId'];
    const result = await this.enrollmentService.remove(id, studioId);

    // Invalidate search cache after removing enrollment
    await this.invalidateSearchCache(studioId);

    return result;
  }

  @Delete('batch/all')
  @UseGuards(JwtAuthGuard)
  async removeBatch(@Body() ids: string[], @Req() request: Request) {
    const studioId = request['locationId'];
    const result = await this.enrollmentService.removeBatch(ids, studioId);

    // Invalidate search cache after batch removing enrollments
    await this.invalidateSearchCache(studioId);

    return result;
  }

  @Get('/calendar/all')
  @UseGuards(JwtAuthGuard)
  async getCalendarEnrollments(
    @Req() request: Request,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const locationId = request['locationId'];
    return this.enrollmentService.getCalendarEnrollments(
      locationId,
      startDate,
      endDate,
    );
  }

  @Get('/:enrollmentId/total-payments')
  async calculateTotalPayments(@Param('enrollmentId') enrollmentId: string) {
    return this.enrollmentService.calculateTotalPayments(enrollmentId);
  }
}
