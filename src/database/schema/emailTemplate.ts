import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type EmailTemplateDocument = EmailTemplate &
  Document<any, any, EmailTemplate>;

@Schema({ timestamps: true })
export class EmailTemplate extends Document {
  @Prop({ type: Types.ObjectId, required: true, ref: 'Studio' })
  studioId: Types.ObjectId;

  @Prop({ type: String, required: false, default: null })
  subject: string;

  @Prop({ type: String, required: false, default: null })
  paragraph1: string;

  @Prop({ type: String, required: false, default: null })
  paragraph2: string;

  @Prop({ type: String, required: false, default: null })
  paragraph1ImageName: string;

  @Prop({ type: String, required: false, default: null })
  paragraph2ImageName: string;
}

export const EmailTemplateSchema = SchemaFactory.createForClass(EmailTemplate);

EmailTemplateSchema.index({ studioId: 1 }); // Studio-based queries
