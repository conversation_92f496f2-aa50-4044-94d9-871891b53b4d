import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { PaymentMethod, TransactionType } from 'src/stripe/type';

export type WalletTransactionDocument = WalletTransaction & Document;

export enum ReasonType {
  CLASS_BUY = 'CLASS_BUY',
  MANUAL_PAYMENT = 'MANUAL_PAYMENT',
  BULK_CHARGE = 'BULK_CHARGE',
  EVENT_BUY = 'EVENT_BUY',
  WALLET_LOAD = 'WALLET_LOAD',
  EXCESS_LOAD = 'EXCESS_LOAD',
  INVOICE_PAYMENT = 'INVOICE_PAYMENT',
  LATE_FEE = 'LATE_FEE',
  TRANSFER_SUBSCRIPTION = 'TRANSFER_SUBSCRIPTION',
}
@Schema({ timestamps: true })
export class WalletTransaction {
  @Prop({ type: Types.ObjectId, ref: 'Parent', required: true })
  parentId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Studio', required: true })
  studioId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Student', required: false })
  studentId: Types.ObjectId;

  @Prop({
    type: String,
    enum: TransactionType,
    required: true,
  })
  transactionType: string;

  @Prop({
    type: String,
    enum: ReasonType,
    required: true,
  })
  reason: string;

  @Prop({
    type: String,
    enum: PaymentMethod,
    required: true,
  })
  paymentMethod: string;

  @Prop({ type: Number, required: true })
  amount: number;

  @Prop({ type: Number, required: true })
  balance: number;

  @Prop({ type: Object, default: {} })
  metadata: Record<string, any>;
}

export const WalletTransactionSchema =
  SchemaFactory.createForClass(WalletTransaction);

// Add indexes for common queries
WalletTransactionSchema.index({ parentId: 1, createdAt: -1 });
WalletTransactionSchema.index({ studioId: 1, createdAt: -1 });
WalletTransactionSchema.index({ studentId: 1, createdAt: -1 });
