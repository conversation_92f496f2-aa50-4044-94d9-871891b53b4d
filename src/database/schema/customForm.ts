import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type CustomFormDocument = CustomForm & Document;

@Schema()
export class CustomForm {
  @Prop({ type: Types.ObjectId, ref: 'Studio', required: true })
  studio: Types.ObjectId;

  @Prop({ type: String, required: true })
  fieldName: string;

  @Prop({
    type: String,
    enum: ['room', 'instructor', 'tags', 'group', 'location'],
    required: true,
  })
  fieldType: 'room' | 'instructor' | 'tags' | 'group' | 'location';
}

export const CustomFormSchema = SchemaFactory.createForClass(CustomForm);

CustomFormSchema.index({ studio: 1 }); // Studio-based queries
CustomFormSchema.index({ fieldType: 1 }); // Form field type lookup
