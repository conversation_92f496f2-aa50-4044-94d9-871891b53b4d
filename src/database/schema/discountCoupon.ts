import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class DiscountCoupon extends Document {
  @Prop({ required: true, type: Types.ObjectId })
  studioId: Types.ObjectId;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  type: 'percentage' | 'fixed';

  @Prop({ required: true })
  category: 'enrollment' | 'scholarship';

  @Prop({ required: true })
  value: number;

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ type: Object })
  restrictions: {
    minAmount?: number;
    maxAmount?: number;
    applicableStudents?: Types.ObjectId[];
    applicableParents?: Types.ObjectId[];
  };

  @Prop({ type: Object })
  metadata: {
    description?: string;
    notes?: string;
    createdBy?: Types.ObjectId;
    lastModifiedBy?: Types.ObjectId;
  };

  @Prop({ required: true })
  duration: 'once' | 'forever' | 'repeating';
}

export const DiscountCouponSchema =
  SchemaFactory.createForClass(DiscountCoupon);

DiscountCouponSchema.index({ studioId: 1 }); // Studio-based queries
DiscountCouponSchema.index({ name: 1 }); // Coupon name lookup
