import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class Notes {
  @Prop({ required: true })
  entityId: string;

  @Prop({ required: true, type: String })
  entityType: 'Parent' | 'Student';

  @Prop({ required: true })
  ghlNoteId: string;

  @Prop({ required: true })
  ghlContactId: string;

  @Prop({ required: true })
  studioId: string;

  @Prop({ required: false })
  note?: string;
}

export const NotesSchema = SchemaFactory.createForClass(Notes);
export type NotesDocument = Notes & Document;
