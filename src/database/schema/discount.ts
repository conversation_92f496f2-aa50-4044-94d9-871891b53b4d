import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true })
export class Discount extends Document {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Studio', required: true })
  studioId: MongooseSchema.Types.ObjectId;

  @Prop({ type: [{ type: MongooseSchema.Types.ObjectId, ref: 'Enrollment' }] })
  excludedClasses: MongooseSchema.Types.ObjectId[];

  @Prop({ type: Boolean, default: true })
  isActive: boolean;

  @Prop({
    type: String,
    enum: ['percent', 'dollars', 'by-student', 'flat', 'amount-by-student'],
  })
  type?: 'percent' | 'dollars' | 'by-student' | 'flat' | 'amount-by-student';

  @Prop({
    type: MongooseSchema.Types.Mixed,
    default: {},
    _id: false,
  })
  discounts?: {
    [key: string]: number;
  };

  @Prop({
    type: MongooseSchema.Types.Mixed,
    default: {},
    _id: false,
  })
  byStudent?: {
    [key: string]: number;
  };

  @Prop({ type: Number })
  flat?: number;

  @Prop({
    type: MongooseSchema.Types.Mixed,
    default: {},
    _id: false,
  })
  amountByStudent?: {
    [key: string]: number;
  };

  @Prop({ type: String, enum: ['all-months', 'first-month'] })
  discountRules?: 'all-months' | 'first-month';

  @Prop({
    type: String,
    enum: ['multi-student', 'multi-class'],
    required: true,
  })
  category: 'multi-student' | 'multi-class';
}

export const DiscountSchema = SchemaFactory.createForClass(Discount);

DiscountSchema.index({ studioId: 1 }); // Studio-based queries
DiscountSchema.index({ studioId: 1, isActive: 1 }); // Active discounts per studio
