import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

export type TokenRefreshHistoryDocument = TokenRefreshHistory & Document;

@Schema({ timestamps: true })
export class TokenRefreshHistory {
  @Prop({ type: String, required: true })
  locationId: string;

  @Prop({ type: String })
  studioName: string;

  @Prop({ type: Boolean, required: true })
  success: boolean;

  @Prop({ type: String })
  errorMessage: string;

  @Prop({ type: Date, required: true, default: Date.now })
  refreshedAt: Date;

  @Prop({ type: String })
  refreshedBy: string;
}

export const TokenRefreshHistorySchema =
  SchemaFactory.createForClass(TokenRefreshHistory);

// Create indexes for more efficient queries
TokenRefreshHistorySchema.index({ locationId: 1, refreshedAt: -1 });
TokenRefreshHistorySchema.index({ success: 1, refreshedAt: -1 });

// TTL index - automatically delete documents older than 3 months
const THREE_MONTHS_IN_SECONDS = 60 * 60 * 24 * 90;
TokenRefreshHistorySchema.index(
  { refreshedAt: 1 },
  { expireAfterSeconds: THREE_MONTHS_IN_SECONDS },
);
