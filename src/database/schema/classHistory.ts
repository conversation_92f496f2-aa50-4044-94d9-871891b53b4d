import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { SubscriptionStatus } from './student';

@Schema({ timestamps: true, _id: false })
class StudentRecord {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Student', required: true })
  studentId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  status: SubscriptionStatus;
}

const StudentRecordSchema = SchemaFactory.createForClass(StudentRecord);

@Schema({ timestamps: true, collection: 'classhistories' })
export class ClassHistory {
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    index: true,
    ref: 'Enrollment',
    required: true,
  })
  classId: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Studio', required: true })
  studioId: MongooseSchema.Types.ObjectId;

  @Prop({ type: [StudentRecordSchema], default: [] })
  students: StudentRecord[];
}

export type ClassHistoryDocument = ClassHistory & Document;
export const ClassHistorySchema = SchemaFactory.createForClass(ClassHistory);

ClassHistorySchema.index({ classId: 1 }); // Primary lookup
ClassHistorySchema.index({ studioId: 1 }); // Studio-based queries
ClassHistorySchema.index({ 'students.studentId': 1 }); // Student lookups
