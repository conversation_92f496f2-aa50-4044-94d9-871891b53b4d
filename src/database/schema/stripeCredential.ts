// credential.schema.ts
import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

@Schema()
export class Credential extends Document {
  @Prop({ required: true })
  studioId: string;

  @Prop({ required: true })
  apiKey: string;

  @Prop({ required: true })
  apiSecret: string;

  @Prop({ required: true })
  webhookId: string;
  @Prop({ required: true })
  webhookSecret: string;
}

export const CredentialSchema = SchemaFactory.createForClass(Credential);

CredentialSchema.index({ studioId: 1 }); // Studio-based queries
