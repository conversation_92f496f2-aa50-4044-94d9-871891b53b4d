import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class Session extends Document {
  @Prop({ type: Types.ObjectId, ref: 'Studio', required: true })
  studioId: Types.ObjectId;

  @Prop({ required: true })
  name: string;

  @Prop({ required: false, default: Date.now })
  startDate: Date;

  @Prop({ required: false, default: Date.now })
  endDate: Date;

  @Prop({ required: true, default: false })
  isRegistrationFee: boolean;

  @Prop({ required: true, default: 0 })
  registrationFeeAmount: number;

  @Prop({ required: false, default: false })
  isArchive: boolean;

  @Prop({ required: false, default: false })
  isDefault: boolean;

  @Prop({ required: false, default: false })
  isClassDefault: boolean;

  @Prop({ required: false, default: false })
  isEventDefault: boolean;

  @Prop({ required: false, default: null })
  billingDate: Date;
}

export const SessionSchema = SchemaFactory.createForClass(Session);
export type SessionDocument = Session & Document;

SessionSchema.index({ studioId: 1 }); // Studio-based queries
SessionSchema.index({ studioId: 1, isDefault: 1 }); // Default session lookup
