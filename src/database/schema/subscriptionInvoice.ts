import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import {
  InvoiceStatus,
  PaymentProvider,
  InvoiceType,
  PaymentMethod,
} from 'src/stripe/type';

export type SubscriptionInvoiceDocument = SubscriptionInvoice & Document;

@Schema({ timestamps: true })
export class SubscriptionInvoice {
  @Prop({ required: true, type: Types.ObjectId })
  studioId: Types.ObjectId;

  @Prop({ required: false, type: Types.ObjectId })
  subscriptionId: Types.ObjectId;

  @Prop({ required: true, type: Types.ObjectId })
  parentId: Types.ObjectId;

  @Prop({ required: false, type: Types.ObjectId })
  studentId: Types.ObjectId;

  @Prop({ required: false, type: Types.ObjectId })
  entityId: Types.ObjectId;

  @Prop({ required: false, type: String })
  entityType: string;

  @Prop({ default: InvoiceStatus.PENDING })
  status: InvoiceStatus;

  // Payment tracking
  @Prop({ type: String, enum: PaymentProvider })
  paymentProvider?: PaymentProvider;

  @Prop({ type: String, enum: PaymentMethod })
  paymentMethod: PaymentMethod;

  @Prop({ type: String, enum: InvoiceType })
  type: InvoiceType;

  @Prop()
  transactionId?: string; // ID from payment provider (e.g., Stripe payment intent ID)

  @Prop({ type: [Object] })
  line_items: {
    name: string;
    amount: number;
    type: string;
    quantity: number;
    total: number;
  }[];

  @Prop({ type: [Object] })
  payments: {
    method: PaymentMethod;
    amount: number;
    checkNumber?: string;
    paymentIntentId?: string;
    date: Date;
  }[];

  // Discount tracking
  @Prop({ type: Types.ObjectId, ref: 'DiscountCoupon' })
  appliedCouponId: Types.ObjectId;

  @Prop({ required: true })
  baseAmount: number; // Original amount before discounts

  @Prop({ required: true })
  finalAmount: number; // Amount after discounts

  @Prop()
  paymentDate: Date;

  @Prop()
  dueDate: Date;

  @Prop()
  startDate: Date;

  @Prop()
  endDate: Date;

  // GoHighLevel-specific fields for webhook synchronization
  @Prop({ required: false, unique: true, sparse: true })
  ghlInvoiceId?: string; // GHL invoice ID

  @Prop({ required: false, unique: true, sparse: true })
  ghlChargeId?: string; // GHL charge ID

  @Prop({ required: false })
  ghlTransactionId?: string; // GHL transaction ID

  @Prop({ required: false })
  ghlSubscriptionId?: string; // GHL subscription ID

  @Prop({ required: false })
  ghlMarketplaceAppId?: string; // GHL marketplace app ID

  @Prop({ type: Object })
  metadata: {
    isPaymentApproved?: boolean;
    paymentIntentId?: string;
    bulkPaymentId?: string;
    failureReason?: string;
    refundReason?: string;
    attemptCount?: number;
    lastAttemptDate?: Date;
    internalTransactionId?: string;
    refundId?: string;
    appliedDiscount?: number; // Track discount amount (0 if no discount)
    applyDiscountTo?: 'first' | 'all';
    discount?: number;
    discountCategory?: string;
    // GHL-specific metadata
    ghlInvoiceData?: any; // Raw GHL invoice data from webhooks
    ghlChargeSnapshot?: {
      id: string;
      status: string;
      amount: number;
      chargeId: string;
      chargedAt: number;
    };
    ghlSubscriptionSnapshot?: {
      id: string;
      status: string;
      trialEnd?: number;
      createdAt: number;
      nextCharge?: number;
    };
  };

  @Prop({ required: true, default: Date.now })
  createdAt: Date;

  @Prop({ required: true, default: Date.now })
  updatedAt: Date;

  @Prop({ required: false, default: null })
  transactionCodeId: string;
}

export const SubscriptionInvoiceSchema =
  SchemaFactory.createForClass(SubscriptionInvoice);

SubscriptionInvoiceSchema.index({ studioId: 1 }); // Studio-based queries
SubscriptionInvoiceSchema.index({ parentId: 1 }); // Parent invoices
SubscriptionInvoiceSchema.index({ status: 1 }); // Invoice status filtering
