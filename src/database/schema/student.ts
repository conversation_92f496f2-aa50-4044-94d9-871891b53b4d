import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { Subscription } from 'rxjs';

export type StudentDocument = Student & Document;

export enum SubscriptionStatus {
  ACTIVE = 'active',
  TERM_ENDED = 'term-ended',
  DROPPED = 'dropped',
  CLASS_TRANSFERRED = 'class-transferred',
  SCHEDULED = 'scheduled',
  PAUSED = 'paused',
}

@Schema({ timestamps: true })
export class Student extends Document {
  @Prop({ required: true })
  firstName: string;

  @Prop({ required: true })
  lastName: string;

  @Prop({ required: false })
  name: string;

  @Prop({ default: null })
  dob: Date;

  @Prop({ type: String, default: '' })
  gender: string;

  @Prop({ type: String, default: '' })
  tShirtSize: string;

  @Prop({ type: String, default: '' })
  mobileNo: string;

  @Prop({ type: String, default: '' })
  studentEmail: string;

  @Prop({ type: String, default: '' })
  transportation: string;

  @Prop({ type: Types.ObjectId, ref: 'Parent', default: null })
  parentId: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Studio', default: null })
  studioId: Types.ObjectId;

  @Prop({ type: String, default: '' })
  profilePicUrl: string;

  @Prop({
    type: {
      primaryDoctor: { type: String, default: '' },
      medications: { type: String, default: '' },
      allergies: { type: String, default: '' },
      disabilities: { type: String, default: '' },
      specialNeeds: { type: String, default: '' },
      healthInsuranceCarrier: { type: String, default: '' },
    },
    default: {},
    _id: false,
  })
  medical: {
    primaryDoctor: string;
    medications: string;
    allergies: string;
    disabilities: string;
    specialNeeds: string;
    healthInsuranceCarrier: string;
  };

  @Prop({
    type: [
      {
        enrollmentId: { type: Types.ObjectId, ref: 'Enrollment' },
        enrolledDate: { type: Date, default: Date.now },
        subscriptionId: { type: String, default: '' },
        subscriptionStatus: {
          type: String,
          default: SubscriptionStatus.ACTIVE,
        },
        absences: { type: Number, default: 0 },
        skills: { type: Number, default: 0 },
      },
    ],
    default: [],
    _id: false,
  })
  enrollments: {
    enrollmentId: Types.ObjectId;
    enrolledDate: Date;
    subscriptionId: string;
    subscriptionStatus: string;
    absences?: number;
    skills?: number;
  }[];

  @Prop({
    type: [
      {
        eventId: { type: Types.ObjectId, ref: 'Event' },
        eventDate: { type: Date, default: Date.now },
        subscriptionId: { type: String, default: '' },
        subscriptionStatus: {
          type: String,
          default: SubscriptionStatus.ACTIVE,
        },
        absences: { type: Number, default: 0 },
        skills: { type: Number, default: 0 },
      },
    ],
    default: [],
    _id: false,
  })
  events: {
    eventId: Types.ObjectId;
    eventDate: Date;
    subscriptionId: string;
    subscriptionStatus: string;
    absences?: number;
    skills?: number;
  }[];

  @Prop({
    type: [
      {
        attendanceId: { type: Types.ObjectId, ref: 'Attendance' },
        classId: { type: Types.ObjectId, ref: 'Enrollment' },
        date: { type: Date, required: true },
        status: { type: String, enum: ['present', 'absent'] },
      },
    ],
    default: [],
    _id: false,
  })
  attendance: {
    attendanceId: Types.ObjectId;
    classId: Types.ObjectId;
    date: Date;
    status: 'present' | 'absent';
  }[];
}

export const StudentSchema = SchemaFactory.createForClass(Student);

StudentSchema.index({ parentId: 1 }); // Parent-student relationship
StudentSchema.index({ studioId: 1 }); // Studio-based queries
StudentSchema.index({ 'enrollments.enrollmentId': 1 }); // Enrollment lookups
StudentSchema.index({ studioId: 1, createdAt: -1 }); // Studio-based queries sorted by creation date
