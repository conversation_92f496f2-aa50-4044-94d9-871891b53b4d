import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type EnrollmentDocument = Enrollment & Document;

@Schema({ timestamps: true })
export class Enrollment extends Document {
  @Prop({ required: true, default: '' })
  title: string;

  @Prop({ type: Types.ObjectId, ref: 'Studio', default: null })
  studio: Types.ObjectId;

  @Prop([{ type: Types.ObjectId, ref: 'Student', default: [] }])
  students: Types.ObjectId[];

  @Prop([{ type: Date, default: [] }])
  schedules: Date[];

  @Prop([{ type: String, default: [] }])
  days: string[];

  @Prop({ required: false, default: '' })
  productId_stripe: string;

  @Prop({ required: false, default: '' })
  startTime: string;

  @Prop({ required: false, default: '' })
  endTime: string;

  @Prop({ required: false, default: 0 })
  duration: number;

  @Prop({ required: false, default: null })
  registrationStartDate: Date;

  @Prop({ required: false, default: null })
  registrationEndDate: Date;

  @Prop({ required: false, default: null })
  startDate: Date;

  @Prop({ required: false, default: null })
  endDate: Date;

  @Prop({ required: true, default: 0 })
  tuitionFee: number;

  @Prop({ required: false, default: 0 })
  costumeFee: number;

  @Prop({ required: false, default: 0 })
  maxSize: number;

  @Prop({ required: false, default: 0 })
  maxWait: number;

  @Prop({ default: false })
  allowOnlineRegistration: boolean;

  @Prop({ default: false })
  allowPortalEnrollment: boolean;

  @Prop({ default: false })
  allowTrialEnrollment: boolean;

  @Prop({ default: false })
  displayOnWebsite: boolean;

  @Prop({ default: 'By Class Fee' })
  tuitionBillingMethod: string;

  @Prop({ default: 'Monthly' })
  tuitionBillingCycle: string;

  @Prop({ default: 1 })
  billingDay: number;

  @Prop({ default: 'No Discounts' })
  tuitionDiscountRule: string;

  @Prop({ default: false })
  prorateTuition: boolean;

  @Prop({ default: false })
  registrationFee: boolean;

  @Prop({ default: 0 })
  registrationFeeAmount: number;

  @Prop({ type: Types.ObjectId, ref: 'CustomForm', default: null })
  room: Types.ObjectId;

  @Prop([{ type: Types.ObjectId, ref: 'CustomForm', default: [] }])
  instructor: Types.ObjectId[];

  @Prop([{ type: Types.ObjectId, ref: 'tags', default: [] }])
  tags: Types.ObjectId[];

  @Prop([{ type: Types.ObjectId, ref: 'CustomForm', default: [] }])
  group: Types.ObjectId[];

  @Prop({ type: Types.ObjectId, ref: 'CustomForm', default: null })
  location: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'CustomForm', default: null })
  session: Types.ObjectId;

  @Prop([{ type: Types.ObjectId, ref: 'Policy', default: [] }])
  policyGroup: Types.ObjectId[];

  @Prop({ required: false, default: '' })
  description: string;

  @Prop({ required: false, default: '' })
  mainTeacher: string;

  @Prop({ required: false, default: '' })
  subTeacher: string;

  @Prop({ required: false, default: 0 })
  startYear: number;

  @Prop({ required: false, default: 0 })
  endYear: number;

  @Prop({ required: false, default: 0 })
  startMonth: number;

  @Prop({ required: false, default: 0 })
  endMonth: number;

  @Prop({ required: false, default: '' })
  calendarId_ghl: string;

  @Prop({ required: false, default: '' })
  productId_ghl: string;

  @Prop({ required: false, default: [] })
  availability: {
    day: string;
    startTime: string;
    endTime: string;
  }[];

  @Prop({ required: false, default: 'same' })
  timeConfig: 'same' | 'different';

  @Prop({ required: false, default: false })
  hide: boolean;

  @Prop({ required: false, default: '#FCE4EC' })
  color: string;

  @Prop([
    {
      date: Date,
      status: {
        type: String,
        enum: ['present', 'absent'],
      },
    },
  ])
  attendance: Array<{
    date: Date;
    status: string;
  }>;

  @Prop({ required: false, default: '' })
  defaultImageUrl: string;

  @Prop({ required: false, default: '' })
  webpImageUrl: string;

  @Prop({ required: false, default: '' })
  originalImageUrl: string;

  @Prop({ required: false, default: null })
  transactionCodeId: string;

  @Prop({ required: false, default: false })
  isDeleted: boolean;
}

export const EnrollmentSchema = SchemaFactory.createForClass(Enrollment);

// Essential indexes only - focus on most common query patterns
EnrollmentSchema.index({ studio: 1 }); // Studio-based queries (most common)
EnrollmentSchema.index({ students: 1 }); // Student enrollment lookups
EnrollmentSchema.index({ studio: 1, isDeleted: 1 }); // Active enrollments per studio
EnrollmentSchema.index({ studio: 1, displayOnWebsite: 1 }); // Website display queries
EnrollmentSchema.index({ startDate: 1, endDate: 1 }); // Date range queries
EnrollmentSchema.index({ productId_stripe: 1 }); // Stripe integration
EnrollmentSchema.index({ createdAt: -1 }); // Recent items sorting
