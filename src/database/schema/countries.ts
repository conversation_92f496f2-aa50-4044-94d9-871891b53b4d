import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type CountryDocument = Country & Document;

@Schema()
export class Timezone {
  @Prop({ required: true })
  zoneName: string;

  @Prop({ required: true })
  gmtOffset: number;

  @Prop({ required: true })
  gmtOffsetName: string;

  @Prop({ required: true })
  abbreviation: string;

  @Prop({ required: true })
  tzName: string;
}

@Schema()
export class State {
  @Prop({ required: true })
  id: number;

  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  state_code: string;

  @Prop({ required: true })
  latitude: string;

  @Prop({ required: true })
  longitude: string;

  @Prop()
  type: string | null;
}

@Schema()
export class Country {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  iso3: string;

  @Prop({ required: true })
  iso2: string;

  @Prop({ required: true })
  numeric_code: string;

  @Prop({ required: true })
  phone_code: string;

  @Prop({ required: true })
  capital: string;

  @Prop({ required: true })
  currency: string;

  @Prop({ required: true })
  currency_name: string;

  @Prop({ required: true })
  currency_symbol: string;

  @Prop({ required: true })
  tld: string;

  @Prop({ required: true })
  native: string;

  @Prop({ required: true })
  region: string;

  @Prop({ required: true })
  region_id: number;

  @Prop({ required: true })
  subregion: string;

  @Prop({ required: true })
  subregion_id: number;

  @Prop({ required: true })
  nationality: string;

  @Prop({ type: [Timezone], required: true })
  timezones: Timezone[];

  @Prop({ type: Map, of: String, required: true })
  translations: Record<string, string>;

  @Prop({ required: true })
  latitude: string;

  @Prop({ required: true })
  longitude: string;

  @Prop({ required: true })
  emoji: string;

  @Prop({ required: true })
  emojiU: string;

  @Prop({ type: [State], required: true })
  states: State[];
}

export const CountrySchema = SchemaFactory.createForClass(Country);
export const TimezoneSchema = SchemaFactory.createForClass(Timezone);
export const StateSchema = SchemaFactory.createForClass(State);

CountrySchema.index({ iso2: 1 }); // Country code lookup
CountrySchema.index({ name: 1 }); // Name search
