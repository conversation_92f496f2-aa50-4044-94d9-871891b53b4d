import { <PERSON>p, <PERSON>hema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class Event extends Document {
  @Prop({ required: true, default: '' })
  title: string;

  @Prop({ type: Types.ObjectId, ref: 'Studio', default: null })
  studio: Types.ObjectId;

  @Prop([{ type: Types.ObjectId, ref: 'Student', default: [] }])
  students: Types.ObjectId[];

  @Prop([{ type: Date, default: [] }])
  schedules: Date[];

  @Prop({ type: Types.ObjectId, ref: 'CustomForm', default: null })
  location: Types.ObjectId;

  @Prop([{ type: String, default: [] }])
  days: string[]; // e.g., ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]

  @Prop({ required: false, default: '' })
  startTime: string; // Store in HH:MM format

  @Prop({ required: false, default: '' })
  endTime: string; // Store in HH:MM format

  @Prop({ required: false, default: 0 })
  duration: number; // Optional if needed separately, otherwise calculated from start/end time

  @Prop({ required: false, default: null })
  registrationStartDate: Date;

  @Prop({ required: false, default: null })
  registrationEndDate: Date;

  @Prop({ required: false, default: null })
  startDate: Date;

  @Prop({ required: false, default: null })
  endDate: Date;

  @Prop({ required: false, default: 0 })
  tuitionFee: number;

  @Prop({ required: false, default: 0 })
  costumeFee: number;

  @Prop({ required: false, default: 0 })
  maxSize: number;

  @Prop({ required: false, default: 0 })
  maxWait: number;

  @Prop({ default: false })
  oneTime: boolean;

  @Prop({ default: false })
  allowOnlineRegistration: boolean;

  @Prop({ default: false })
  allowPortalEnrollment: boolean;

  @Prop({ default: false })
  allowTrialEnrollment: boolean;

  @Prop({ default: false })
  displayOnWebsite: boolean;

  @Prop({ default: 'By Class Fee' })
  tuitionBillingMethod: string;

  @Prop({ default: 'Monthly' })
  tuitionBillingCycle: string;

  @Prop({ default: 1 })
  billingDay: number;

  @Prop({ default: 'No Discounts' })
  tuitionDiscountRule: string;

  @Prop({ default: false })
  prorateTuition: boolean;

  @Prop({ default: false })
  registrationFee: boolean;

  @Prop({ default: 0 })
  registrationFeeAmount: number;

  @Prop({ type: Types.ObjectId, ref: 'CustomForm', default: null })
  room: Types.ObjectId;

  @Prop([{ type: Types.ObjectId, ref: 'CustomForm', default: [] }])
  instructor: Types.ObjectId[];

  @Prop([{ type: Types.ObjectId, ref: 'CustomForm', default: [] }])
  tags: Types.ObjectId[];

  @Prop([{ type: Types.ObjectId, ref: 'CustomForm', default: [] }])
  group: Types.ObjectId[];

  @Prop({ type: Types.ObjectId, ref: 'CustomForm', default: null })
  session: Types.ObjectId;

  @Prop([{ type: Types.ObjectId, ref: 'Policy', default: [] }])
  policyGroup: Types.ObjectId[];

  @Prop({ required: false, default: '' })
  description: string;

  @Prop({ required: false, default: '' })
  mainTeacher: string;

  @Prop({ required: false, default: '' })
  subTeacher: string;

  @Prop({ required: false, default: 0 })
  startYear: number;

  @Prop({ required: false, default: 0 })
  endYear: number;

  @Prop({ required: false, default: 0 })
  startMonth: number;

  @Prop({ required: false, default: 0 })
  endMonth: number;

  @Prop({ required: false, default: '' })
  calendarId_ghl: string;

  @Prop({ required: false, default: '' })
  productId_ghl: string;

  @Prop({ required: false, default: [] })
  availability: {
    day: string;
    startTime: string;
    endTime: string;
  }[];

  @Prop({ required: false, default: 'same' })
  timeConfig: 'same' | 'different';

  @Prop({ required: false, default: false })
  hide: boolean;

  @Prop({ required: false, default: '#FCE4EC' })
  color: string;

  @Prop({ required: false, default: '' })
  productId_stripe: string;

  @Prop({ required: false, default: '' })
  defaultImageUrl: string;

  @Prop({ required: false, default: false })
  isDeleted: boolean;
}

export const EventSchema = SchemaFactory.createForClass(Event);

EventSchema.index({ studio: 1 }); // Studio-based queries
EventSchema.index({ students: 1 }); // Student enrollment lookups
EventSchema.index({ studio: 1, isDeleted: 1 }); // Active events per studio
