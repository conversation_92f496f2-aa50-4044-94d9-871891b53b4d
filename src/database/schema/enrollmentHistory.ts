import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true, _id: false })
class EnrollmentRecord {
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: 'Enrollment',
    required: true,
  })
  enrollmentId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  status: string;
}

const EnrollmentRecordSchema = SchemaFactory.createForClass(EnrollmentRecord);

@Schema({ timestamps: true })
export class EnrollmentHistory {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Student', required: true })
  studentId: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Studio', required: true })
  studioId: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Parent', required: true })
  parentId: MongooseSchema.Types.ObjectId;

  @Prop({ type: [EnrollmentRecordSchema], default: [] })
  enrollments: EnrollmentRecord[];
}

export type EnrollmentHistoryDocument = EnrollmentHistory & Document;
export const EnrollmentHistorySchema =
  SchemaFactory.createForClass(EnrollmentHistory);

EnrollmentHistorySchema.index({ studentId: 1 }); // Student history lookup
EnrollmentHistorySchema.index({ studioId: 1 }); // Studio-based queries
