import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import {
  PaymentMethod,
  PaymentTransactionSource,
  PaymentTransactionType,
} from 'src/stripe/type';
import {
  PaymentTransactionStatus,
  PaymentProvider,
} from '../../stripe/type/index';
import { CreateParentDto } from 'src/parents/dto/create-parent.dto';

export type PaymentTransactionDocument = PaymentTransaction & Document;
@Schema({ timestamps: true })
export class PaymentTransaction extends Document {
  @Prop({ required: true, type: Types.ObjectId })
  studioId: Types.ObjectId;

  @Prop({ default: null, type: Types.ObjectId })
  parentId: Types.ObjectId | string;

  @Prop({ default: null, type: Types.ObjectId })
  studentId: Types.ObjectId | string;

  @Prop({ default: null, type: String })
  groupId: string;

  @Prop({ required: true, type: String, enum: PaymentTransactionSource })
  paymentSource: PaymentTransactionSource;

  @Prop({ required: true, type: String, enum: PaymentTransactionType })
  type: PaymentTransactionType;

  @Prop({ required: false, type: Types.ObjectId })
  typeId: Types.ObjectId;

  @Prop({ required: true, type: Number })
  amount: number;

  @Prop({ required: true, type: String, enum: PaymentTransactionStatus })
  status: PaymentTransactionStatus;

  @Prop({ type: String, enum: PaymentProvider })
  paymentProvider?: PaymentProvider;

  @Prop({ type: String, enum: PaymentMethod })
  paymentMethod?: PaymentMethod;

  @Prop({ type: Number, default: 0 })
  retryAttempts?: number;

  @Prop({ type: Date })
  lastRetryDate?: Date;

  @Prop({ type: String })
  lastError?: string;

  @Prop({ type: Boolean, default: false })
  isRetry?: boolean;

  @Prop({ type: Types.ObjectId })
  originalTransactionId?: Types.ObjectId;

  @Prop({ type: Object })
  metadata: {
    sessionId?: string;
    line_items?: {
      price_data: {
        currency: string;
        product_data: {
          name: string;
          description: string;
        };
        unit_amount: number;
      };
      quantity: number;
    }[];
    payments?: {
      method: PaymentMethod;
      amount: number;
      checkNumber?: string;
      paymentIntentId?: string;
      date: Date;
    }[];
    paymentIntentId?: string;
    description?: string;
    failureReason?: string;
    refundReason?: string;
    attemptCount?: number;
    lastAttemptDate?: Date;
    processingFee?: number;
    stripeSessionId?: string;
    billingDate?: string;
    discountSplit?: Record<string, number>;
    couponId?: string;
    createParentPayload?: CreateParentDto;
    walletAmountUsed?: number;
    migrationData?: {
      oldTransactionId: Types.ObjectId;
      migrationDate: Date;
    };
  };
}

export const PaymentTransactionSchema =
  SchemaFactory.createForClass(PaymentTransaction);

PaymentTransactionSchema.index({ studioId: 1 }); // Studio-based queries
PaymentTransactionSchema.index({ parentId: 1 }); // Parent payment history
PaymentTransactionSchema.index({ status: 1 }); // Status filtering
