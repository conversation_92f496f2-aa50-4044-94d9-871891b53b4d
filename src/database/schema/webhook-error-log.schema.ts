import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import * as mongoose from 'mongoose';

export type WebhookErrorLogDocument = WebhookErrorLog & Document;

@Schema({ timestamps: true })
export class WebhookErrorLog {
  @Prop()
  type: string; // 'signature_error', 'processing_error', 'unexpected_error', etc.

  @Prop()
  message: string;

  @Prop()
  stack?: string;

  @Prop()
  eventId?: string;

  @Prop()
  eventType?: string;

  @Prop()
  webhookType?: string; // 'GHL', 'STRIPE', etc.

  @Prop({ type: Object })
  payload: any;

  @Prop({ type: mongoose.Schema.Types.Mixed })
  rawPayload?: any;

  @Prop({ type: Object })
  headers: any;

  @Prop()
  timestamp: Date;

  @Prop()
  locationId: string;

  @Prop()
  resolved?: boolean;

  @Prop()
  resolvedAt?: Date;

  @Prop()
  notes?: string;
}

export const WebhookErrorLogSchema =
  SchemaFactory.createForClass(WebhookErrorLog);

WebhookErrorLogSchema.index({ locationId: 1 }); // Location-based queries
WebhookErrorLogSchema.index({ timestamp: -1 }); // Recent errors first
