import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type TransactionDocument = Transaction & Document;

@Schema({ timestamps: true })
export class Transaction {
  @Prop({ required: true })
  entityType: string;

  @Prop()
  entityId: string;

  @Prop({ type: Object })
  details: Record<string, any>;

  @Prop({ required: true })
  transactionType: string;

  @Prop({ required: true })
  amount: number;

  @Prop({ default: 'pending' })
  status: string;

  @Prop()
  stripeSessionId?: string;

  @Prop()
  paymentIntentId?: string;

  @Prop({ type: Object })
  discountOptions?: Record<string, any>;

  @Prop()
  createdAt: Date;

  @Prop()
  updatedAt: Date;

  @Prop()
  paymentMethod?: string;

  @Prop()
  notes?: string;
}

export const TransactionSchema = SchemaFactory.createForClass(Transaction);

TransactionSchema.index({ entityId: 1 }); // Entity lookup
TransactionSchema.index({ status: 1 }); // Status filtering
