import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type ReleaseNotificationDocument = ReleaseNotification & Document;

@Schema({ timestamps: true })
export class ReleaseNotification {
  @Prop({ required: true })
  repository: string;

  @Prop({ required: true })
  tag: string;

  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true })
  htmlUrl: string;

  @Prop({ required: true })
  createdAt: Date;
}

export const ReleaseNotificationSchema =
  SchemaFactory.createForClass(ReleaseNotification);

ReleaseNotificationSchema.index({ repository: 1 }); // Repository lookup
ReleaseNotificationSchema.index({ createdAt: -1 }); // Recent releases
