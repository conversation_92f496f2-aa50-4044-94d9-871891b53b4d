import { Schema, Prop, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({
  timestamps: true,
})
export class Policy extends Document {
  @Prop({ required: true, unique: true })
  name: string;

  @Prop({ required: true })
  description: string;

  @Prop({ type: Types.ObjectId, ref: 'Studio', required: true })
  studio: Types.ObjectId;

  @Prop({ type: [Types.ObjectId], ref: 'Enrollment' })
  class: Types.ObjectId[];

  @Prop({ type: [Types.ObjectId], ref: 'Event' })
  event: Types.ObjectId[];
}

export const PolicySchema = SchemaFactory.createForClass(Policy);
// PolicySchema.index({ name: 1 }, { unique: true });

PolicySchema.index({ studio: 1 }); // Studio-based queries
PolicySchema.index({ name: 1 }); // Policy name lookup
PolicySchema.index({ studio: 1, name: 1 });
PolicySchema.index({ createdAt: -1 });
PolicySchema.index({ updatedAt: -1 });
