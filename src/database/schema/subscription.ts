import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import { PaymentMethod } from 'src/stripe/type';
export type SubscriptionDocument = Subscription & Document;

interface ProrationType {
  amount: number;
  startDate: string;
  endDate: string;
  description: string;
  daysInMonth: number;
  remainingDays: number;
}

export enum SubscriptionStatus {
  ACTIVE = 'active',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired',
  SUSPENDED = 'suspended',
  ENDED = 'ended',
  SCHEDULED = 'scheduled',
  PAUSED = 'paused',
}

@Schema({ timestamps: true })
export class Subscription extends Document {
  @Prop({ required: true, type: Types.ObjectId })
  studioId: Types.ObjectId;

  @Prop({ required: true })
  parentId: Types.ObjectId;

  @Prop({ required: true, type: Types.ObjectId })
  studentId: Types.ObjectId;

  @Prop({ required: true, type: Types.ObjectId })
  entityId: Types.ObjectId; // class or event ID

  @Prop({ required: true })
  entityType: 'class' | 'event';

  @Prop({ required: true })
  startDate: Date;

  @Prop({ required: true })
  endDate: Date;

  @Prop({ required: true })
  billingCycle: 'weekly' | 'monthly' | 'yearly';

  @Prop({ required: true })
  baseAmount: number; // Original amount before discounts

  @Prop({ required: true })
  finalAmount: number; // Amount after discounts

  @Prop({ default: SubscriptionStatus.ACTIVE })
  status: SubscriptionStatus;

  @Prop({ type: String })
  billingDay?: string;

  @Prop()
  nextPaymentDate: Date;

  // Discount tracking
  @Prop({ type: Types.ObjectId, ref: 'DiscountCoupon' })
  appliedCouponId?: Types.ObjectId;

  // Additional metadata
  @Prop({ type: Object })
  metadata: {
    enrollmentPosition?: number; // For multi-class discount
    studentPosition?: number; // For multi-student discount
    notes?: string;
    transactionId?: string;
    appliedDiscount?: number; // Track discount amount explicitly
    paymentIntentId?: string;
    discount?: number;
    paymentMethod?: PaymentMethod;
    discountCategory?: string;
  };
}

export const SubscriptionSchema = SchemaFactory.createForClass(Subscription);

SubscriptionSchema.index({ studioId: 1 }); // Studio-based queries
SubscriptionSchema.index({ parentId: 1 }); // Parent subscriptions
SubscriptionSchema.index({ status: 1 }); // Active subscriptions
