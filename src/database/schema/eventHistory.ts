import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true, _id: false })
class EventRecord {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Event', required: true })
  eventId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  status: string;
}

const EventRecordSchema = SchemaFactory.createForClass(EventRecord);

@Schema({ timestamps: true })
export class EventHistory {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Student', required: true })
  studentId: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Studio', required: true })
  studioId: MongooseSchema.Types.ObjectId;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: 'Parent', required: true })
  parentId: MongooseSchema.Types.ObjectId;

  @Prop({ type: [EventRecordSchema], default: [] })
  events: EventRecord[];
}

export type EventHistoryDocument = EventHistory & Document;
export const EventHistorySchema = SchemaFactory.createForClass(EventHistory);

EventHistorySchema.index({ studentId: 1 }); // Student history lookup
EventHistorySchema.index({ studioId: 1 }); // Studio-based queries
