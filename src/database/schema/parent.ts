import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';
import { sendTemporaryPasswordEmail } from 'src/emailProvider/email';
import * as jwt from 'jsonwebtoken';
import * as dotenv from 'dotenv';
import mongoose from 'mongoose';
import { Studio } from './studio';
dotenv.config();
// import { sendEmail } from 'src/emailProvider/email';

export type ParentDocument = Parent & Document;

class Address {
  @Prop({ type: String, default: '' })
  street: string;

  @Prop({ type: String, default: '' })
  city: string;

  @Prop({ type: String, default: '' })
  state: string;

  @Prop({ type: String, default: '' })
  zip: string;

  @Prop({ type: String, default: '' })
  country: string;
}

class EmergencyContact {
  @Prop({ type: String, default: '' })
  firstName: string;

  @Prop({ type: String, default: '' })
  lastName: string;

  @Prop({ required: true, type: String })
  name: string;

  @Prop({ type: String, default: '' })
  phone: string;

  @Prop({ type: String, default: '' })
  emergencyContactEmail: string;

  @Prop({ type: Boolean, default: false })
  authorized: boolean;
}

// Second Parent class
class SecondParent {
  @Prop({ type: String, default: '' })
  firstName: string;

  @Prop({ type: String, default: '' })
  lastName: string;

  @Prop({ required: true, type: String })
  name: string;

  @Prop({ type: String, default: '' })
  email: string;

  @Prop({ required: false, default: '' })
  relation: string;

  @Prop({ type: String, default: '' })
  mobile: string;

  @Prop({ type: String, default: '' })
  workPhone: string;

  @Prop({ type: String, default: '' })
  homePhone: string;
}

// Policy class
class AgreedPolicies {
  @Prop({ type: String, default: '' })
  policyId: string;

  @Prop({ type: String, default: '' })
  title: string;

  @Prop({ type: Boolean, default: true }) // Assuming true as default for status
  status: boolean;
}

@Schema({ timestamps: true })
export class Parent {
  @Prop({ required: true, type: String })
  firstName: string;

  @Prop({ required: true, type: String })
  lastName: string;

  @Prop({ required: true, type: String })
  name: string;

  @Prop({ type: Types.ObjectId, ref: 'Studio', default: null, required: true })
  studioId: Types.ObjectId;

  @Prop({ required: true, unique: false, type: String })
  email: string;

  @Prop({ default: '', type: String })
  ghlContactId: string;

  @Prop({ type: String, default: '' })
  familyName: string;

  @Prop({ required: false, default: '', type: String })
  stripeCustomerId: string;

  @Prop({ required: false, default: '', type: String })
  relation: string;

  @Prop({ required: false, default: '', type: String })
  password: string;

  @Prop({ required: false, default: '', type: String })
  profilePicUrl: string;

  @Prop({ default: '', type: String })
  primaryPhone: string;

  @Prop({ default: '', type: String })
  workPhone: string;

  @Prop({ default: '', type: String })
  homePhone: string;

  @Prop({ default: null, type: Date })
  birthDate: Date;

  @Prop({ type: Address, default: {} })
  address: Address;

  @Prop([{ type: () => EmergencyContact, default: {} }])
  emergencyContacts: EmergencyContact[];

  @Prop({ type: () => SecondParent, default: {} })
  secondParent: SecondParent;

  @Prop({ type: () => [AgreedPolicies], default: [] })
  policies: AgreedPolicies[];

  @Prop({ default: Date.now, type: Date })
  dateOfRegistration: Date;

  @Prop({ default: '', type: String })
  resetPasswordToken: string;

  @Prop({ default: null, type: Number })
  resetPasswordExpires: number;

  @Prop({ default: true, type: Boolean })
  isFirstLogin: boolean;

  @Prop({ type: Object, default: {} })
  metaData: Record<string, any>;

  @Prop({ default: 0, type: Number })
  walletBalance: number;

  @Prop({ default: [], type: [Types.ObjectId], ref: 'Student' })
  walletStudentExlusion: Array<Types.ObjectId>;

  @Prop({ type: Object, default: {} })
  isSessionFeePaid: {
    [key: string]: boolean;
  };
}

export const ParentSchema = SchemaFactory.createForClass(Parent);

ParentSchema.set('toJSON', {
  transform: function (_doc, ret) {
    delete ret.password; // Remove the password field
    return ret;
  },
});

ParentSchema.set('toObject', {
  transform: function (_doc, ret) {
    delete ret.password; // Remove the password field
    return ret;
  },
});

ParentSchema.pre('save', function (next) {
  if (this.email) {
    this.email = this.email.toLowerCase();
  }
  next();
});

ParentSchema.index({ studioId: 1 }); // Studio-based queries
ParentSchema.index({ email: 1 }); // Email lookup
ParentSchema.index({ stripeCustomerId: 1 }); // Payment integration
