import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export enum AppointmentStatus {
  SCHEDULED = 'Trial Scheduled',
  COMPLETED = 'Trial Completed',
  MISSED = 'Trial Missed',
  ENROLLED = 'Enrolled',
  NOT_ENROLLED = 'Not Enrolled',
}
@Schema({ timestamps: true })
export class TrialStudent extends Document {
  @Prop({ trim: true })
  firstName?: string;

  @Prop({ trim: true })
  lastName?: string;

  @Prop()
  name?: string;

  @Prop()
  title?: string;

  @Prop()
  studentName?: string;

  @Prop()
  email?: string;

  @Prop()
  contactNo?: string;

  @Prop()
  calendarName: string;

  @Prop()
  notes?: string;

  @Prop({ type: Types.ObjectId, ref: 'Studio' })
  studioId?: Types.ObjectId;

  @Prop({ type: Types.ObjectId, ref: 'Enrollment' })
  classId?: Types.ObjectId;

  @Prop()
  className?: string;
  @Prop()
  calendarId?: string;

  @Prop()
  contactId?: string;

  @Prop({
    type: String,
    enum: AppointmentStatus,
    default: AppointmentStatus.SCHEDULED,
  })
  appointmentStatus?: AppointmentStatus;

  @Prop()
  startTime?: string;

  @Prop()
  endTime?: string;

  @Prop()
  eventId?: string;

  @Prop({
    type: Boolean,
    default: null,
    nullable: true,
  })
  isPresent?: boolean | null;
}

export const TrialStudentSchema = SchemaFactory.createForClass(TrialStudent);

TrialStudentSchema.pre('save', function (next) {
  this.firstName = this.firstName?.trim() || '';
  this.lastName = this.lastName?.trim() || '';
  this.name =
    this.firstName || this.lastName
      ? `${this.firstName} ${this.lastName}`.trim()
      : undefined;
  next();
});

TrialStudentSchema.index({ studioId: 1 }); // Studio-based queries
TrialStudentSchema.index({ contactId: 1 }); // Contact lookup
