import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type AttendanceDocument = Attendance & Document<any, any, Attendance>;

@Schema({ timestamps: true })
export class Attendance extends Document {
  @Prop({ type: Boolean, required: true, default: true })
  isPresent: boolean;

  @Prop({ type: String, required: false })
  statusDetail?: string;

  @Prop({ type: Boolean, required: false, default: false })
  markByParent?: boolean;
}

export const AttendanceSchema = SchemaFactory.createForClass(Attendance);

AttendanceSchema.index({ isPresent: 1 }); // Main query filter
AttendanceSchema.index({ createdAt: -1 }); // Sorting by date
