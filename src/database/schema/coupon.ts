import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type CouponDocument = Coupon & Document<any, any, Coupon>;

@Schema({ timestamps: true })
export class Coupon extends Document {
  @Prop({ type: Types.ObjectId, ref: 'Studio', required: true })
  studioId: Types.ObjectId;

  @Prop({ type: String, required: true })
  stripeId: string;

  @Prop({ type: String, required: true })
  name: string;

  @Prop({ type: String, enum: ['flat', 'percent', 'amount'], required: true })
  type: 'flat' | 'percent' | 'amount';

  @Prop({ type: Number, required: true })
  value: number;

  @Prop({ type: Boolean, default: true })
  isActive: boolean;

  @Prop({ type: String, enum: ['all-months', 'first-month'], required: true })
  discountRules: 'all-months' | 'first-month';

  @Prop({
    type: String,
    enum: ['multi-student', 'multi-class'],
    required: true,
  })
  category: 'multi-student' | 'multi-class';
}

export const CouponSchema = SchemaFactory.createForClass(Coupon);

CouponSchema.index({ studioId: 1 }); // Studio-based queries
CouponSchema.index({ stripeId: 1 }); // Stripe integration
CouponSchema.index({ studioId: 1, isActive: 1 }); // Active coupons per studio
