import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

// Define interfaces for nested objects
interface ProrationType {
  amount: number;
  startDate: string;
  endDate: string;
  description: string;
  daysInMonth: number;
  remainingDays: number;
}

@Schema({ timestamps: true })
export class Proration extends Document {
  @Prop({ required: false })
  ghlProductId?: string;

  @Prop({
    type: {
      amount: Number,
      startDate: String,
      endDate: String,
      description: String,
      daysInMonth: Number,
      remainingDays: Number,
    },
    required: false,
  })
  initial?: ProrationType;

  @Prop({
    type: {
      amount: Number,
      startDate: String,
      endDate: String,
      description: String,
      daysInMonth: Number,
      remainingDays: Number,
    },
    required: false,
  })
  final?: ProrationType;

  @Prop({ required: false })
  regularAmount?: number;

  @Prop({ required: false })
  billingCycleAnchor?: string;

  @Prop({ default: false })
  isProrated: boolean;
}

export const ProrationSchema = SchemaFactory.createForClass(Proration);

ProrationSchema.index({ ghlProductId: 1 }); // Product lookup
