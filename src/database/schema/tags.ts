import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type TagDocument = Tag & Document;

@Schema({ timestamps: true })
export class Tag {
  @Prop({ type: Types.ObjectId, ref: 'Studio', required: true })
  studioId: Types.ObjectId;

  @Prop({ required: true })
  fieldName: string;

  @Prop({ default: 'tags' })
  fieldType: string;

  @Prop()
  ghlId: string;
}

export const TagSchema = SchemaFactory.createForClass(Tag);

TagSchema.index({ studioId: 1 }); // Studio-based queries
TagSchema.index({ fieldType: 1 }); // Tag type lookup
