import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import {
  PaymentProcessingMethod,
  PaymentProvider,
  ProrationMode,
} from 'src/stripe/type';

export type StudioDocument = Studio & Document;

@Schema({ timestamps: true })
export class Studio {
  @Prop({ required: true, type: String })
  access_token: string;

  @Prop({ required: true, type: String })
  token_type: string;

  @Prop({ required: true, type: Number })
  expires_in: number;

  @Prop({ required: true, type: String })
  refresh_token: string;

  @Prop({ required: true, type: String })
  scope: string;

  @Prop({ required: true, type: String })
  userType: string;

  @Prop({ required: true, type: String })
  companyId: string;

  @Prop({ required: true, type: String })
  locationId: string;

  @Prop({ required: true, type: String })
  userId: string;

  @Prop({ required: true, type: String })
  subaccountName: string;

  @Prop({ type: Number, required: true, default: 3 })
  consecutiveAbsencesThreshold: number;

  @Prop({
    required: true,
    default: PaymentProvider.STRIPE,
    enum: PaymentProvider,
  })
  paymentProvider: PaymentProvider;

  @Prop({
    required: true,
    default: PaymentProcessingMethod.AUTO,
    enum: PaymentProcessingMethod,
  })
  paymentProcessingMethod: PaymentProcessingMethod;

  @Prop({
    required: true,
    default: ProrationMode.FULL_MONTH_ALWAYS,
    enum: ProrationMode,
  })
  prorationMode: ProrationMode;

  @Prop({ required: true, type: Number, default: 3 })
  failedPaymentRetryCount: number;
}

export const StudioSchema = SchemaFactory.createForClass(Studio);

StudioSchema.index({ locationId: 1 }); // Location-based lookup
StudioSchema.index({ userId: 1 }); // User-studio relationship
