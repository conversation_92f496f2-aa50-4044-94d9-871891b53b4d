import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type InvoiceDocument = Invoice & Document;

// make a enum of status

const status = ['paid', 'failed', 'pending'];

@Schema({ timestamps: true })
export class Invoice {
  @Prop()
  entityId: string;

  @Prop()
  name: string;

  @Prop()
  amount: number;

  @Prop()
  status: string;

  @Prop()
  transactionId: string;

  @Prop()
  method: string;

  @Prop()
  type: string;

  @Prop()
  product: string;

  @Prop()
  notes: string;

  @Prop()
  createdAt: string;

  @Prop()
  updatedAt: string;
}

export const InvoiceSchema = SchemaFactory.createForClass(Invoice);

InvoiceSchema.index({ entityId: 1 }); // Entity lookup
InvoiceSchema.index({ status: 1 }); // Status filtering
