import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type CurrencyDocument = Currency & Document<any, any, Currency>;

@Schema({ timestamps: true })
export class Currency extends Document {
  @Prop({ type: Types.ObjectId, required: true, ref: 'Studio' })
  studioId: Types.ObjectId;

  @Prop({ type: String, required: true })
  name: string;
}

export const CurrencySchema = SchemaFactory.createForClass(Currency);

CurrencySchema.index({ studioId: 1 }); // Studio-based queries
CurrencySchema.index({ name: 1 }); // Currency lookup
