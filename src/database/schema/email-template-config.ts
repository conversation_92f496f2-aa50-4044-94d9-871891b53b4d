import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class EmailTemplateConfig extends Document {
  @Prop({ type: Types.ObjectId, required: true, ref: 'Studio' })
  studio: Types.ObjectId;

  @Prop({ required: true })
  imageUrl: string;

  @Prop({ required: true, default: () => new Date() })
  urlGeneratedAt: Date;
}

export const EmailTemplateConfigSchema =
  SchemaFactory.createForClass(EmailTemplateConfig);

EmailTemplateConfigSchema.index({ studio: 1 }); // Studio-based queries
