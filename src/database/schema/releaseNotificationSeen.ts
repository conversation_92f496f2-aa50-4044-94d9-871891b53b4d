import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type ReleaseNotificationSeenDocument = ReleaseNotificationSeen &
  Document;

@Schema({ timestamps: true })
export class ReleaseNotificationSeen {
  @Prop({ required: true })
  studioId: string;

  @Prop({ required: true })
  releaseId: string;

  @Prop({ required: true, default: false })
  seen: boolean;

  @Prop()
  seenAt: Date;
}

export const ReleaseNotificationSeenSchema = SchemaFactory.createForClass(
  ReleaseNotificationSeen,
);

// Create a compound index for unique studio-release pairs
ReleaseNotificationSeenSchema.index(
  { studioId: 1, releaseId: 1 },
  { unique: true },
);
