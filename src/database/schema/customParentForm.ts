import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class FormParentPreference extends Document {
  @Prop({ type: Types.ObjectId, ref: 'Studio', required: true })
  studioId: Types.ObjectId;

  @Prop({ required: true })
  formFields: {
    fieldId: string;
    label: string;
    type:
      | 'text'
      | 'number'
      | 'date'
      | 'boolean'
      | 'select'
      | 'multiselect'
      | 'time';
    required: boolean;
    options?: string[];
    defaultValue?: any;
    order: number;
    visible: boolean;
    section?: string;
  }[];

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ required: true })
  entity: string;
}

export const FormParentPreferenceSchema =
  SchemaFactory.createForClass(FormParentPreference);

FormParentPreferenceSchema.index({ studioId: 1 }); // Studio-based queries
FormParentPreferenceSchema.index({ entity: 1 }); // Entity type lookup
