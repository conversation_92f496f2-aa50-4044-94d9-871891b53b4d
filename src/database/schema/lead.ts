import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

@Schema({ timestamps: true })
export class Lead extends Document {
  @Prop({ required: true })
  firstName: string;

  @Prop({ required: true })
  lastName: string;

  @Prop({ required: true })
  primaryPhone: string;

  @Prop({ required: true })
  relation: string;

  @Prop({ required: true })
  email: string;

  @Prop({ type: Types.ObjectId, ref: 'Studio', required: true })
  studioId: Types.ObjectId;

  @Prop({ required: false })
  ghlContactId: string;
}

export const LeadSchema = SchemaFactory.createForClass(Lead);

LeadSchema.index({ studioId: 1 }); // Studio-based queries
LeadSchema.index({ email: 1 }); // Email lookup
