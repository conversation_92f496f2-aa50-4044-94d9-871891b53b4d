import { IsMongoId, IsOptional, IsDate, IsNumber } from 'class-validator';
import { Types } from 'mongoose';

// Define the EnrollmentDto class
class EnrollmentDto {
  @IsMongoId()
  enrollmentId: Types.ObjectId;

  @IsOptional()
  @IsDate()
  enrolledDate?: Date;

  @IsOptional()
  @IsNumber()
  absences?: number;

  @IsOptional()
  @IsNumber()
  skills?: number;
}

// Define the StudentDetailsDto class
export class StudentDetailsDto {
  firstName: string;
  lastName: string;
  name: string;
  dob: Date;
  gender: string;

  enrollments: EnrollmentDto[]; // Use EnrollmentDto class for the enrollments array

  @IsMongoId()
  parentId: Types.ObjectId;
}
