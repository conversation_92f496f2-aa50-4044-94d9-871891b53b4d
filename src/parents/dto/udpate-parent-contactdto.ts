import { IsOptional, IsString } from 'class-validator';

export class UpdateParentContactDto {
  @IsString()
  @IsOptional()
  firstName: string;

  @IsString()
  @IsOptional()
  lastName: string;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  email?: string;

  @IsOptional()
  @IsString()
  primaryPhone?: string;

  @IsOptional()
  @IsString()
  workPhone?: string;

  @IsOptional()
  @IsString()
  homePhone?: string;

  // Address fields
  @IsOptional()
  @IsString()
  street?: string;

  @IsOptional()
  @IsString()
  city?: string;

  @IsOptional()
  @IsString()
  state?: string;

  @IsOptional()
  @IsString()
  zip?: string;

  // Emergency contact fields
  @IsOptional()
  @IsString()
  emergencyContactName?: string;

  @IsOptional()
  @IsString()
  emergencyContactPhone?: string;

  @IsOptional()
  @IsString()
  emergencyContactRelation?: string;

  @IsOptional()
  @IsString()
  emergencyContactEmail?: string;

  // Second parent fields
  @IsOptional()
  @IsString()
  secondParentName?: string;

  @IsOptional()
  @IsString()
  secondParentEmail?: string;

  @IsOptional()
  @IsString()
  secondParentMobile?: string;

  @IsOptional()
  @IsString()
  secondParentWorkPhone?: string;

  @IsOptional()
  @IsString()
  secondParentHomePhone?: string;

  // Agreed policies
  @IsOptional()
  policies?: { policyId: string; title: string; status: boolean }[];
}
