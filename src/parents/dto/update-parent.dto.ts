import {
  IsOptional,
  IsString,
  IsEmail,
  IsDateString,
  IsArray,
  ValidateNested,
  IsPostalCode,
  IsPhoneNumber,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { UpdateStudentDto } from '../../students/dto/update-student.dto';

// Address DTO
export class AddressDto {
  @IsOptional()
  @IsString()
  streetAddress?: string;

  @IsOptional()
  @IsString()
  city?: string;

  @IsOptional()
  @IsString()
  state?: string;

  @IsOptional()
  @IsPostalCode()
  postalCode?: string;

  @IsOptional()
  @IsString()
  country?: string;
}

// Emergency Contact DTO
export class EmergencyContactDto {
  @IsOptional()
  @IsString()
  firstName?: string;

  @IsOptional()
  @IsString()
  lastName?: string;

  @IsOptional()
  @IsPhoneNumber()
  primaryPhone?: string;

  @IsOptional()
  @IsPhoneNumber()
  alternatePhone?: string;

  @IsOptional()
  @IsBoolean()
  authorized?: boolean;
}

// Second Parent DTO
export class SecondParentDto {
  @IsString()
  firstName: string;

  @IsString()
  lastName: string;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsEmail()
  email?: string;

  @IsOptional()
  @IsPhoneNumber()
  primaryPhone?: string;

  @IsOptional()
  @IsString()
  relation?: string;
}

// Policy DTO
export class PolicyDto {
  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsDateString()
  effectiveDate?: Date;
}

// Main Update Parent DTO
export class UpdateParentDto {
  @IsString()
  _id?: string;

  @IsString()
  firstName: string;

  @IsString()
  lastName: string;

  @IsOptional()
  @IsEmail()
  email?: string;

  @IsString()
  relation: string;

  @IsOptional()
  @IsString()
  primaryPhone?: string;

  @IsOptional()
  @IsString()
  workPhone?: string;

  @IsOptional()
  @IsString()
  homePhone?: string;

  @IsOptional()
  @IsString()
  familyName?: string;

  @IsOptional()
  @IsDateString()
  birthDate?: Date;

  @IsOptional()
  @ValidateNested()
  @Type(() => AddressDto)
  address?: AddressDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => EmergencyContactDto)
  emergencyContact?: EmergencyContactDto;

  @IsOptional()
  @ValidateNested()
  @Type(() => SecondParentDto)
  secondParent?: SecondParentDto;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => PolicyDto)
  policies?: PolicyDto[];

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateStudentDto)
  students?: UpdateStudentDto[];

  @IsOptional()
  @IsDateString()
  dateOfRegistration?: Date;

  @IsOptional()
  @IsString()
  resetPasswordToken?: string;

  @IsOptional()
  @IsDateString()
  resetPasswordExpires?: Date;
}
