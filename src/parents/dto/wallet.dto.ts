import { PaymentMethod } from 'src/stripe/type';
import { ReasonType } from '../../database/schema/walletTransaction';

// Base metadata interfaces for each reason type
interface ClassBuyMetadata {
  classId: string;
}

interface ManualPaymentMetadata {}

interface BulkChargeMetadata {
  description: string;
  chargeIds: string[];
}

interface EventBuyMetadata {
  eventId: string;
}

interface LoadWalletMetadata {
  paymentMethod: string;
  transactionId?: string;
}

interface LateFeeMetadata {
  originalDueDate: Date;
  relatedInvoiceId: string;
}

// Type for metadata based on reason
export type WalletMetadata = {
  [ReasonType.CLASS_BUY]: ClassBuyMetadata;
  [ReasonType.MANUAL_PAYMENT]: ManualPaymentMetadata;
  [ReasonType.BULK_CHARGE]: BulkChargeMetadata;
  [ReasonType.EVENT_BUY]: EventBuyMetadata;
  [ReasonType.WALLET_LOAD]: LoadWalletMetadata;
  [ReasonType.LATE_FEE]: LateFeeMetadata;
};

// DTO for adding wallet balance
export class WalletBalanceDto {
  studioId: string;
  parentId: string;
  studentId?: string;
  amount: number;
  reason: ReasonType;
  metadata?: WalletMetadata[keyof WalletMetadata];
  paymentMethod: PaymentMethod;
}

export class LoadWalletBalanceDto extends WalletBalanceDto {
  checkNumber?: string;
}
