import { IsString, IsOptional, IsEmail, IsBoolean } from 'class-validator';
import { Types } from 'mongoose';
export class BulkImportParentDto {
  @IsString()
  studioId: Types.ObjectId;

  // Parent Info
  @IsString()
  parent_first_name: string;

  @IsString()
  parent_last_name: string;

  @IsEmail()
  parent_email: string;

  @IsString()
  @IsOptional()
  relation: string;

  @IsString()
  parent_primary_phone: string;

  @IsString()
  @IsOptional()
  address_apartment: string;

  @IsString()
  @IsOptional()
  address_street: string;

  @IsString()
  @IsOptional()
  address_city: string;

  @IsString()
  @IsOptional()
  address_state: string;

  @IsString()
  @IsOptional()
  address_zip: string;

  @IsString()
  @IsOptional()
  address_country: string;

  // Emergency Contact
  @IsString()
  emergency_contact1_first_name: string;

  @IsString()
  emergency_contact1_last_name: string;

  @IsString()
  emergency_contact1_phone: string;

  @IsEmail()
  @IsOptional()
  emergency_contact1_email: string;

  @IsString()
  emergency_contact1_authorize_pickup: string;

  // Student Info
  @IsString()
  student_first_name: string;

  @IsString()
  student_last_name: string;

  @IsString()
  student_date_of_birth_yyyy_mm_dd: string;

  @IsString()
  @IsOptional()
  student_gender: string;

  @IsEmail()
  @IsOptional()
  student_email: string;

  @IsString()
  @IsOptional()
  student_primary_phone: string;

  @IsString()
  @IsOptional()
  student_costume_size: string;

  @IsString()
  @IsOptional()
  student_transportation: string;

  @IsString()
  @IsOptional()
  student_health_insurance_carrier: string;

  @IsString()
  @IsOptional()
  student_primary_doctor: string;

  @IsString()
  @IsOptional()
  student_allergies: string;

  @IsString()
  @IsOptional()
  student_disabilities: string;

  @IsString()
  @IsOptional()
  student_medications: string;

  @IsString()
  @IsOptional()
  student_special_needs: string;
}
