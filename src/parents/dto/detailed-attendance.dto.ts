export class AttendanceRecordDto {
  date: string;
  status: string;
}

export class ClassDetailsDto {
  start_date: string;
  end_date: string;
  class_days: string[];
}

export class DetailedAttendanceDto {
  student: string;
  class: string;
  instructor: string;
  attendance_summary: {
    total_classes: number;
    present: {
      count: number;
      percentage: number;
    };
    absent: {
      count: number;
      percentage: number;
    };
    attendance_rate: string;
  };
  class_details: ClassDetailsDto;
  attendance_records: AttendanceRecordDto[];
}
