import {
  IsString,
  <PERSON><PERSON>ate,
  IsOptional,
  IsArray,
  ValidateNested,
  IsBoolean,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CreateStudentDto } from 'src/students/dto/create-student.dto';

class AddressDto {
  @IsString()
  @IsOptional()
  street: string;

  @IsString()
  @IsOptional()
  city: string;

  @IsString()
  @IsOptional()
  state: string;

  @IsString()
  @IsOptional()
  zip: string;
}

class EmergencyContactDto {
  @IsString()
  @IsOptional()
  firstName: string;

  @IsString()
  @IsOptional()
  lastName: string;

  @IsString()
  @IsOptional()
  phone: string;

  @IsString()
  @IsOptional()
  emergencyContactEmail: string;

  @IsBoolean()
  @IsOptional()
  authorize: boolean;
}

export class SecondParentDto {
  @IsString()
  firstName: string;

  @IsString()
  lastName: string;

  @IsString()
  @IsOptional()
  email: string;

  @IsOptional()
  @IsString()
  relation?: string;

  @IsString()
  @IsOptional()
  mobile: string;

  @IsString()
  @IsOptional()
  workPhone: string;

  @IsString()
  @IsOptional()
  homePhone: string;
}

class AgreedPoliciesDto {
  @IsString()
  @IsOptional()
  policyId: string;

  @IsString()
  @IsOptional()
  title: string;

  @IsString()
  status: string;
}

export class CreateParentDto {
  @IsString()
  @IsOptional()
  contactId?: string;

  @IsString()
  @IsOptional()
  studioId?: string;

  @IsString()
  @IsOptional()
  stripeCustomerId?: string;

  @IsString()
  firstName: string;

  @IsString()
  lastName: string;

  @IsString()
  @IsOptional()
  relation: string;

  @IsString()
  @IsOptional()
  familyName: string;

  @IsString()
  email: string;

  @IsString()
  @IsOptional()
  primaryPhone: string;

  @IsString()
  @IsOptional()
  workPhone: string;

  @IsString()
  @IsOptional()
  homePhone: string;

  @IsDate()
  @IsOptional()
  birthDate: Date;

  @ValidateNested()
  @Type(() => AddressDto)
  @IsOptional()
  address: AddressDto;

  @ValidateNested()
  @Type(() => EmergencyContactDto)
  @IsOptional()
  emergencyContacts: EmergencyContactDto[];

  @ValidateNested()
  @Type(() => SecondParentDto)
  @IsOptional()
  secondParent: SecondParentDto;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AgreedPoliciesDto)
  @IsOptional()
  policies: AgreedPoliciesDto[];

  @IsDate()
  @IsOptional()
  dateOfRegistration: Date;

  @IsString()
  @IsOptional()
  resetPasswordToken: string;

  @IsDate()
  @IsOptional()
  resetPasswordExpires: Date;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateStudentDto)
  @IsOptional()
  students: CreateStudentDto[];

  @IsBoolean()
  @IsOptional()
  isCreatedFromStudio: boolean = false;

  @IsBoolean()
  @IsOptional()
  isLead: boolean = false;

  @IsObject()
  @IsOptional()
  isSessionFeePaid: {
    [key: string]: boolean;
  };

  @IsString()
  @IsOptional()
  paymentMethod: string;
}
