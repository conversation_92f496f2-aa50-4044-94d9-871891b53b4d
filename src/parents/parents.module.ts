import { Module, forwardRef } from '@nestjs/common';
import { ParentsService } from './parents.service';
import { ParentsController } from './parents.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Parent, ParentSchema } from 'src/database/schema/parent';
import { JwtModule } from '@nestjs/jwt';
import { GohighlevelModule } from 'src/gohighlevel/gohighlevel.module';
import { StudentsModule } from 'src/students/students.module';
import { EnrollmentModule } from 'src/enrollment/enrollment.module';
import { StudiosModule } from 'src/studios/studios.module';
import { GcpStorageModule } from 'src/gcp-storage/gcp-storage.module';
import { Studio, StudioSchema } from 'src/database/schema/studio';
import { EmailTemplateModule } from 'src/email-template/email-template.module';
import { Student, StudentSchema } from 'src/database/schema/student';
import { BullModule } from '@nestjs/bullmq';
import { CreateContactProcessor } from './processors/ghl_contact_create';
import { RemoveTagsProcessor } from './processors/ghl_contact_tags_remove';
import {
  Credential,
  CredentialSchema,
} from 'src/database/schema/stripeCredential';
import { StripeModule } from 'src/stripe/stripe.module';
import { Attendance, AttendanceSchema } from 'src/database/schema/attendance';
import { LeadsModule } from 'src/leads/leads.module';
import { TriggersModule } from 'src/triggers/triggers.module';
import {
  WalletTransaction,
  WalletTransactionSchema,
} from 'src/database/schema/walletTransaction';
import { PaymentProcessorModule } from 'src/payment-processor/payment-processor.module';
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Parent.name, schema: ParentSchema },
      { name: Studio.name, schema: StudioSchema },
      { name: Student.name, schema: StudentSchema },
      { name: Credential.name, schema: CredentialSchema },
      { name: Attendance.name, schema: AttendanceSchema },
      { name: WalletTransaction.name, schema: WalletTransactionSchema },
    ]),
    JwtModule,
    forwardRef(() => GohighlevelModule),
    forwardRef(() => StudentsModule),
    forwardRef(() => EnrollmentModule),
    forwardRef(() => StudiosModule),
    forwardRef(() => GcpStorageModule),
    forwardRef(() => StripeModule),
    forwardRef(() => EmailTemplateModule),
    BullModule.registerQueue(
      {
        name: 'parent-create-contact',
        defaultJobOptions: {
          attempts: 3,
          backoff: { type: 'exponential', delay: 1000 },
          removeOnComplete: true,
        },
      },
      {
        name: 'parent-remove-tags',
        defaultJobOptions: {
          attempts: 3,
          backoff: { type: 'exponential', delay: 1000 },
          removeOnComplete: true,
        },
      },
    ),
    forwardRef(() => LeadsModule),
    TriggersModule,
    forwardRef(() => PaymentProcessorModule),
  ],
  controllers: [ParentsController],
  providers: [ParentsService, CreateContactProcessor, RemoveTagsProcessor],
  exports: [ParentsService],
})
export class ParentsModule {}
