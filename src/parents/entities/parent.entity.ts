export class Parent {}

export interface ParentsBulkCreatePayload {
  parent: {
    firstName: string;
    lastName: string;
    email: string;
    relation?: string;
    primaryPhone: string;
    address: {
      street: string;
      apartment: string;
      city: string;
      state: string;
      zip: string;
      country: string;
    };
    emergencyContact: {
      firstName: string;
      lastName: string;
      phone: string;
      email?: string;
      authorizePickup: boolean;
    };
  };
  students: Array<{
    firstName: string;
    lastName: string;
    dob: Date;
    gender?: string;
    studentEmail?: string;
    mobileNo?: string;
    tShirtSize?: string;
    transportation?: string;
    medical: {
      healthInsuranceCarrier?: string;
      primaryDoctor?: string;
      allergies?: string;
      disabilities?: string;
      medications?: string;
      specialNeeds?: string;
    };
  }>;
}

export const CSV_IMPORT_HEADER_MAPPING = {
  // Parent Information
  'Parent First Name': 'parent_first_name',
  'Parent Last Name': 'parent_last_name',
  'Parent Email': 'parent_email',
  Relation: 'relation',
  'Parent Primary Phone': 'parent_primary_phone',

  // Address Information
  'Address Apartment': 'address_apartment',
  'Address Street': 'address_street',
  'Address City': 'address_city',
  'Address State': 'address_state',
  'Address Zip': 'address_zip',
  'Address Country': 'address_country',

  // Emergency Contact Information
  'Emergency Contact1 First Name': 'emergency_contact1_first_name',
  'Emergency Contact1 Last Name': 'emergency_contact1_last_name',
  'Emergency Contact1 Primary Phone': 'emergency_contact1_phone',
  'Emergency Contact1 Email': 'emergency_contact1_email',
  'Emergency Contact1 Authorize Pickup': 'emergency_contact1_authorize_pickup',

  // Student 1
  'Student 1 First Name': 'student_1_first_name',
  'Student 1 Last Name': 'student_1_last_name',
  'Student 1 Date of Birth': 'student_1_date_of_birth',
  'Student 1 Gender': 'student_1_gender',
  'Student 1 Email': 'student_1_email',
  'Student 1 Primary Phone': 'student_1_primary_phone',
  'Student 1 Costume Size': 'student_1_costume_size',
  'Student 1 Transportation': 'student_1_transportation',
  'Student 1 Health Insurance Carrier': 'student_1_health_insurance_carrier',
  'Student 1 Primary Doctor': 'student_1_primary_doctor',
  'Student 1 Allergies': 'student_1_allergies',
  'Student 1 Disabilities': 'student_1_disabilities',
  'Student 1 Medications': 'student_1_medications',
  'Student 1 Special Needs': 'student_1_special_needs',

  // Student 2
  'Student 2 First Name': 'student_2_first_name',
  'Student 2 Last Name': 'student_2_last_name',
  'Student 2 Date of Birth': 'student_2_date_of_birth',
  'Student 2 Gender': 'student_2_gender',
  'Student 2 Email': 'student_2_email',
  'Student 2 Primary Phone': 'student_2_primary_phone',
  'Student 2 Costume Size': 'student_2_costume_size',
  'Student 2 Transportation': 'student_2_transportation',
  'Student 2 Health Insurance Carrier': 'student_2_health_insurance_carrier',
  'Student 2 Primary Doctor': 'student_2_primary_doctor',
  'Student 2 Allergies': 'student_2_allergies',
  'Student 2 Disabilities': 'student_2_disabilities',
  'Student 2 Medications': 'student_2_medications',
  'Student 2 Special Needs': 'student_2_special_needs',

  // Student 3
  'Student 3 First Name': 'student_3_first_name',
  'Student 3 Last Name': 'student_3_last_name',
  'Student 3 Date of Birth': 'student_3_date_of_birth',
  'Student 3 Gender': 'student_3_gender',
  'Student 3 Email': 'student_3_email',
  'Student 3 Primary Phone': 'student_3_primary_phone',
  'Student 3 Costume Size': 'student_3_costume_size',
  'Student 3 Transportation': 'student_3_transportation',
  'Student 3 Health Insurance Carrier': 'student_3_health_insurance_carrier',
  'Student 3 Primary Doctor': 'student_3_primary_doctor',
  'Student 3 Allergies': 'student_3_allergies',
  'Student 3 Disabilities': 'student_3_disabilities',
  'Student 3 Medications': 'student_3_medications',
  'Student 3 Special Needs': 'student_3_special_needs',

  // Student 4
  'Student 4 First Name': 'student_4_first_name',
  'Student 4 Last Name': 'student_4_last_name',
  'Student 4 Date of Birth': 'student_4_date_of_birth',
  'Student 4 Gender': 'student_4_gender',
  'Student 4 Email': 'student_4_email',
  'Student 4 Primary Phone': 'student_4_primary_phone',
  'Student 4 Costume Size': 'student_4_costume_size',
  'Student 4 Transportation': 'student_4_transportation',
  'Student 4 Health Insurance Carrier': 'student_4_health_insurance_carrier',
  'Student 4 Primary Doctor': 'student_4_primary_doctor',
  'Student 4 Allergies': 'student_4_allergies',
  'Student 4 Disabilities': 'student_4_disabilities',
  'Student 4 Medications': 'student_4_medications',
  'Student 4 Special Needs': 'student_4_special_needs',

  // Student 5
  'Student 5 First Name': 'student_5_first_name',
  'Student 5 Last Name': 'student_5_last_name',
  'Student 5 Date of Birth': 'student_5_date_of_birth',
  'Student 5 Gender': 'student_5_gender',
  'Student 5 Email': 'student_5_email',
  'Student 5 Primary Phone': 'student_5_primary_phone',
  'Student 5 Costume Size': 'student_5_costume_size',
  'Student 5 Transportation': 'student_5_transportation',
  'Student 5 Health Insurance Carrier': 'student_5_health_insurance_carrier',
  'Student 5 Primary Doctor': 'student_5_primary_doctor',
  'Student 5 Allergies': 'student_5_allergies',
  'Student 5 Disabilities': 'student_5_disabilities',
  'Student 5 Medications': 'student_5_medications',
  'Student 5 Special Needs': 'student_5_special_needs',
} as const;
