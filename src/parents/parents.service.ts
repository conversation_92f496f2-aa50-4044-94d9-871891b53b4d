import {
  Injectable,
  NotFoundException,
  UnauthorizedException,
  Logger,
  HttpException,
  HttpStatus,
  BadRequestException,
  Inject,
  forwardRef,
  InternalServerErrorException,
  ConflictException,
  OnModuleDestroy,
} from '@nestjs/common';
import { CreateParentDto } from './dto/create-parent.dto';
import {
  EmergencyContactDto,
  SecondParentDto,
  UpdateParentDto,
} from './dto/update-parent.dto';
import { Parent, ParentDocument } from 'src/database/schema/parent';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { LoginDto } from './dto/login_dto';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';
import { JwtService } from '@nestjs/jwt';
import { GohighlevelService } from 'src/gohighlevel/gohighlevel.service';
import { StudentsService } from 'src/students/students.service';
import { EnrollmentService } from 'src/enrollment/enrollment.service';
import { calculateAge } from 'src/utils/helperFunction';
import { StudiosService } from 'src/studios/studios.service';
import { StudioDocument } from 'src/database/schema/studio';
import { StudentDetailsDto } from './dto/studentDetails.dto';
import { GcpStorageService } from 'src/gcp-storage/gcp-storage.service';
import {
  sendPaymentMethodReminderEmail,
  sendResetPasswordEmail,
  sendTemporaryPasswordEmail,
} from 'src/emailProvider/email';
import * as dotenv from 'dotenv';
import { StudentIdsDto } from './dto/incomingStudentIdDto';
import { Studio } from '../database/schema/studio';
import { EmailTemplateService } from 'src/email-template/email-template.service';
import { Student } from 'src/database/schema/student';
import { BulkImportParentDto } from './dto/bulk-import.dto';
import { ParentsBulkCreatePayload } from './entities/parent.entity';
import Stripe from 'stripe';
import { Credential } from 'src/database/schema/stripeCredential';
import { StripeService } from 'src/stripe/stripe.service';
import { DetailedAttendanceDto } from './dto/detailed-attendance.dto';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue, QueueEvents } from 'bullmq';
import { LeadsService } from 'src/leads/leads.service';
import { TriggersService } from 'src/triggers/triggers.service';
import type { Redis } from 'ioredis';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';
import {
  WalletTransaction,
  WalletTransactionDocument,
  WalletTransactionSchema,
} from '../database/schema/walletTransaction';
import { ReasonType } from '../database/schema/walletTransaction';
import { LoadWalletBalanceDto, WalletBalanceDto } from './dto/wallet.dto';
import { PaymentMethod, TransactionType } from 'src/stripe/type';
import { PaymentProcessorService } from 'src/payment-processor/payment-processor.service';
import {
  PaymentProvider,
  PaymentTransactionSource,
  PaymentTransactionStatus,
  PaymentTransactionType,
} from '../stripe/type';
dotenv.config();

@Injectable()
export class ParentsService {
  private readonly logger = new Logger(ParentsService.name);
  private createContactQueueEvents: QueueEvents;
  private removeTagsQueueEvents: QueueEvents;
  constructor(
    @InjectModel(Parent.name) private parentModel: Model<ParentDocument>,
    @InjectModel(WalletTransaction.name)
    private walletTransactionModel: Model<WalletTransactionDocument>,
    private readonly gohighlevel: GohighlevelService,
    private jwtService: JwtService,
    @Inject(forwardRef(() => StudentsService))
    private readonly studentService: StudentsService,
    private readonly enrollmentService: EnrollmentService,
    @Inject(forwardRef(() => StudiosService))
    private readonly studioService: StudiosService,
    @Inject(forwardRef(() => GcpStorageService))
    private readonly gcpStorageService: GcpStorageService,
    @Inject(forwardRef(() => StripeService))
    private readonly stripeService: StripeService,
    @InjectModel(Studio.name) private studioModel: Model<StudioDocument>,
    private readonly emailTemplateService: EmailTemplateService,
    @InjectModel('Student') private studentModel: Model<Student>,
    @InjectModel(Credential.name) private credentialModel: Model<Credential>,
    @InjectQueue('parent-create-contact')
    private createContactQueue: Queue,
    @InjectQueue('parent-remove-tags')
    private removeTagsQueue: Queue,
    @InjectModel('Attendance') private readonly attendanceModel: Model<any>,
    @Inject(forwardRef(() => LeadsService))
    private readonly leadsService: LeadsService,
    // private readonly logger: Logger
    private readonly triggersService: TriggersService,
    @Inject(forwardRef(() => PaymentProcessorService))
    private readonly paymentProcessorService: PaymentProcessorService,
    @Inject('REDIS_CLIENT') private readonly redis: Redis,
  ) {
    // Create QueueEvents instances using the global Redis client
    this.createContactQueueEvents = new QueueEvents('parent-create-contact', {
      connection: this.redis,
    });
    this.removeTagsQueueEvents = new QueueEvents('parent-remove-tags', {
      connection: this.redis,
    });
  }

  async create(createParentDto: CreateParentDto, locationId): Promise<Parent> {
    try {
      // Set default locationId if it's undefined
      // if (!createParentDto.studioId) {
      //   createParentDto.studioId = 'VGZndtOFHrO4NIELBQYo';
      // }

      // Stripe payment logic here
      // Example: const paymentResult = await this.stripeService.makePayment(createParentDto);
      // if (!paymentResult.success) throw new HttpException('Payment failed', HttpStatus.PAYMENT_REQUIRED);
      const studio = await this.studioService.findByLocationIdString(
        createParentDto.studioId,
      );
      const studioId_mongo = Types.ObjectId.createFromHexString(locationId);
      const existingParent = await this.parentModel.findOne({
        email: createParentDto.email,
        studioId: studioId_mongo,
      });
      if (existingParent) {
        throw new ConflictException(
          'This email is already associated with a parent portal account.',
        );
      }

      const credential = await this.credentialModel.findOne({
        studioId: studio._id.toString(),
      });

      const stripe = new Stripe(credential.apiSecret, {
        apiVersion: '2025-02-24.acacia',
      });

      // If payment is successful, proceed with parent creation
      const studioDetails =
        await this.studioService.findByLocationId(locationId);
      const locationId_ghl = studioDetails.locationId;
      let customerId;
      const existingCustomers = await stripe.customers.list({
        email: createParentDto.email,
        limit: 1,
      });

      if (
        existingCustomers.data.length > 0 &&
        existingCustomers.data[0].email === createParentDto.email
      ) {
        customerId = existingCustomers.data[0].id;
      } else {
        const newCustomer = await stripe.customers.create({
          email: createParentDto.email,
          name: `${createParentDto.firstName} ${createParentDto.lastName}`,
        });
        customerId = newCustomer.id;
      }

      createParentDto.stripeCustomerId = customerId;
      const createdParent = new this.parentModel({
        ...createParentDto,
        familyName: createParentDto.lastName,
        name: `${createParentDto.firstName.trim()} ${createParentDto.lastName}`,
        studioId: studioId_mongo,
      });
      await createdParent.save();

      // Call studentService for each student in the DTO if students array is present
      if (createParentDto.students && createParentDto.students.length > 0) {
        for (const studentDto of createParentDto.students) {
          studentDto.parentId = createdParent._id as Types.ObjectId; // Assign parentId to student
          studentDto.familyName = createParentDto.familyName;
          studentDto.studioId = studioId_mongo as Types.ObjectId;
          await this.studentService.create(studentDto); // Call studentService to create the student
        }
      }

      const { firstName, lastName, email, primaryPhone } = createParentDto;
      const phone = primaryPhone;

      // Call to GoHighLevel service
      try {
        const name = `${createParentDto.firstName.trim()} ${createParentDto.lastName}`;
        await this.gohighlevel.createContact(
          { name, email: email.toLowerCase(), phone },
          locationId_ghl,
        );
      } catch (error) {
        console.log('error creating contact in ghl:', error);
      }

      // Check for existing payment methods after 10 minutes
      // setTimeout(
      //   async () => {
      //     try {
      //       // Try to get payment methods
      //       const paymentMethods = await this.stripeService.getCardsByEmail(
      //         createParentDto.email,
      //         studioDetails._id.toString(),
      //       );

      //       // Only send email if no payment methods found
      //       if (!paymentMethods || paymentMethods.length === 0) {
      //         await sendPaymentMethodReminderEmail(
      //           createParentDto.email,
      //           `${createParentDto.firstName} ${createParentDto.lastName}`,
      //           studioDetails.subaccountName,
      //           process.env.PARENT_PORTAL_URL,
      //         );
      //       }
      //     } catch (error) {
      //       console.log('error sending payment method reminder email:', error);
      //       // If error occurs (like no customer found), send the reminder email
      //       await sendPaymentMethodReminderEmail(
      //         createParentDto.email,
      //         `${createParentDto.firstName} ${createParentDto.lastName}`,
      //         studioDetails.subaccountName,
      //         process.env.PARENT_PORTAL_URL,
      //       );
      //     }
      //   },
      //   10 * 60 * 1000,
      // );

      return createdParent;
    } catch (error) {
      // Log the error (optional, good for debugging purposes)
      console.error('Error during parent creation:', error);
      if (error.code === 11000) {
        // Check if it's a duplicate email error
        if (error.keyPattern && error.keyPattern.email) {
          throw new HttpException(
            'Duplicate email error: Email is already registered.',
            HttpStatus.CONFLICT,
          );
        }
      }
      // Check if it's a specific error from the GoHighLevel or payment services
      // if (error instanceof SomeSpecificError) {
      //   throw new HttpException(
      //     'Specific error message',
      //     HttpStatus.SERVICE_UNAVAILABLE
      //   );
      // }

      // Throw a generic internal server error for other cases
      throw new HttpException(
        'An error occurred during parent creation',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createWithLocationId(
    createParentDto: CreateParentDto,
  ): Promise<Parent> {
    const studioDetails = await this.studioService.findByLocationIdString(
      createParentDto.studioId,
    );
    const existingParent = await this.parentModel.findOne({
      email: createParentDto.email,
      studioId: studioDetails._id,
    });

    if (existingParent) {
      throw new ConflictException(
        'This email is already associated with a parent portal account.',
      );
    }
    try {
      // Check if email exists for this studioId

      // Stripe payment logic here
      // Example: const paymentResult = await this.stripeService.makePayment(createParentDto);
      // if (!paymentResult.success) throw new HttpException('Payment failed', HttpStatus.PAYMENT_REQUIRED);

      // If payment is successful, proceed with parent creation
      // const studioDetails = await this.studioService.findByLocationIdString(
      //   createParentDto.studioId,
      // );
      const name = `${createParentDto.firstName.trim()} ${createParentDto.lastName}`;
      const locationId_ghl = studioDetails._id;
      const studioId_mongo = locationId_ghl as Types.ObjectId;

      const generatedPassword = crypto.randomBytes(8).toString('hex');

      // Use this.db to get the model that's already registered

      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(generatedPassword, salt);

      let paymentProviderCustomerId;
      if (studioDetails.paymentProvider === PaymentProvider.STRIPE) {
        paymentProviderCustomerId =
          await this.stripeService.createOrFetchCustomer(
            studioDetails._id.toString(),
            createParentDto.email.toLowerCase(),
            createParentDto.firstName,
            createParentDto.lastName,
          );
      }

      const { firstName, lastName, email, primaryPhone, studioId } =
        createParentDto;
      const phone = primaryPhone;

      // Call to GoHighLevel service
      let ghlContactId;
      try {
        if (createParentDto.contactId) {
          ghlContactId = createParentDto.contactId;
        } else {
          try {
            const createContactJob = await this.createContactQueue.add(
              'parent-create-contact',
              {
                name,
                email: email.toLowerCase(),
                phone,
                locationId: studioDetails.locationId,
                isParentPortal: true,
              },
            );

            const result = await createContactJob.waitUntilFinished(
              this.createContactQueueEvents,
            );
            ghlContactId = result.ghlContactId;

            if (!createParentDto.isLead) {
              // Queue tag removal operation
              await this.removeTagsQueue.add('parent-remove-tags', {
                studioId: studioDetails._id.toString(),
                contactId: ghlContactId,
                tags: ['portal_lead', 'lead_portal'],
              });
            }
          } catch (error) {
            this.logger.error(
              `Error in GHL operations: ${studioDetails._id.toString()}`,
              error,
            );
          }
        }
      } catch (error) {
        console.log(
          `error creating contact in ghl: ${studioDetails._id.toString()}`,
          error,
        );
      }

      if (createParentDto.isLead) {
        const createLeadDto = {
          email: email.toLowerCase(),
          firstName: firstName,
          lastName: lastName,
          studioId: studioDetails._id.toString(),
          primaryPhone: primaryPhone,
          relation: createParentDto.relation,
          locationId: studioDetails.locationId,
          ghlContactId: ghlContactId,
        };
        await this.leadsService.create(createLeadDto);
      }

      const createdParent = new this.parentModel({
        ...createParentDto,
        familyName: createParentDto.lastName,
        ghlContactId: ghlContactId,
        studioId: locationId_ghl,
        name: name,
        password: hashedPassword,
        stripeCustomerId: paymentProviderCustomerId?.id || null,
      });
      await createdParent.save();

      // Call studentService for each student in the DTO if students array is present
      if (createParentDto.students && createParentDto.students.length > 0) {
        for (const studentDto of createParentDto.students) {
          studentDto.parentId = createdParent._id as Types.ObjectId; // Assign parentId to student
          studentDto.familyName = createParentDto.familyName;
          studentDto.studioId = studioId_mongo as Types.ObjectId;
          await this.studentService.createFromParent(studentDto); // Call studentService to create the student
        }
      }

      const studio = await this.studioModel.findOne({ locationId: studioId });
      const emailTemplate = await this.emailTemplateService.findByStudioId(
        studio._id as Types.ObjectId,
      );
      const studioName = studio?.subaccountName || '';

      try {
        const studioLogoUrl = await this.gcpStorageService.getPublicImage(
          studio._id.toString(),
          'studio-logo',
        );

        await sendTemporaryPasswordEmail(
          email,
          generatedPassword,
          studioLogoUrl ? null : studioName,
          `${firstName} ${lastName}`,
          emailTemplate.paragraph1.replace(
            /{{ paragraph1Image }}/g,
            `<img src="${emailTemplate.paragraph1ImageUrl}" alt="Paragraph 1 Image">`,
          ),
          emailTemplate.paragraph2.replace(
            /{{ paragraph2Image }}/g,
            `<img src="${emailTemplate.paragraph2ImageUrl}" alt="Paragraph 2 Image">`,
          ),
          emailTemplate.subject,
          studio.locationId.toString(),
          studioLogoUrl ? studioLogoUrl : null,
        );
      } catch (error) {
        this.logger.error(
          `Error in generating public image: ${studioDetails._id.toString()}`,
          error,
        );

        await sendTemporaryPasswordEmail(
          email,
          generatedPassword,
          studioName,
          `${firstName} ${lastName}`,
          emailTemplate.paragraph1.replace(
            /{{ paragraph1Image }}/g,
            `<img src="${emailTemplate.paragraph1ImageUrl}" alt="Paragraph 1 Image">`,
          ),
          emailTemplate.paragraph2.replace(
            /{{ paragraph2Image }}/g,
            `<img src="${emailTemplate.paragraph2ImageUrl}" alt="Paragraph 2 Image">`,
          ),
          emailTemplate.subject,
          studio.locationId.toString(),
          'https://storage.googleapis.com/studio-class-default-images/2.png',
        );
      }

      // Schedule email with setTimeout
      // setTimeout(
      //   async () => {
      //     try {
      //       // Try to get payment methods
      //       const paymentMethods = await this.stripeService.getCardsByEmail(
      //         createParentDto.email,
      //         studioDetails._id.toString(),
      //       );

      //       // Only send email if no payment methods found
      //       if (!paymentMethods || paymentMethods.length === 0) {
      //         await sendPaymentMethodReminderEmail(
      //           createParentDto.email,
      //           `${createParentDto.firstName} ${createParentDto.lastName}`,
      //           studioDetails.subaccountName,
      //           process.env.PARENT_PORTAL_URL,
      //         );
      //       }
      //     } catch (error) {
      //       console.log('error sending payment method reminder email:', error);
      //       // If error occurs (like no customer found), send the reminder email
      //       await sendPaymentMethodReminderEmail(
      //         createParentDto.email,
      //         `${createParentDto.firstName} ${createParentDto.lastName}`,
      //         studioDetails.subaccountName,
      //         process.env.PARENT_PORTAL_URL,
      //       );
      //     }
      //   },
      //   10 * 60 * 1000,
      // );

      if (createdParent) {
        // Send data to GHL trigger after parent is created
        const triggerData = {
          triggerKey: process.env.PARENT_CREATE_TRIGGER_KEY,
          data: {
            firstName: firstName,
            lastName: lastName,
            email: email,
            phone: primaryPhone,
            contactId: ghlContactId,
          },
          locationId: studioDetails.locationId,
        };

        try {
          await this.triggersService.sendToGhl(triggerData);
        } catch (error) {
          this.logger.error(
            `Error sending trigger to GHL for parent ${createdParent._id}: ${error} `,
          );
        }
      }

      return createdParent;
    } catch (error) {
      // Log the error (optional, good for debugging purposes)
      console.error('Error during parent creation:', error);
      if (error.code === 11000) {
        // Check if it's a duplicate email error
        if (error.keyPattern && error.keyPattern.email) {
          throw new HttpException(
            'Duplicate email error: Email is already registered.',
            HttpStatus.CONFLICT,
          );
        }
      }

      // Check if it's a specific error from the GoHighLevel or payment services
      // if (error instanceof SomeSpecificError) {
      //   throw new HttpException(
      //     'Specific error message',
      //     HttpStatus.SERVICE_UNAVAILABLE
      //   );
      // }

      // Throw a generic internal server error for other cases
      throw new HttpException(
        'An error occurred during parent creation',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async validateUser(
    email: string,
    password: string,
    studioId,
  ): Promise<ParentDocument> {
    const parent = await this.parentModel
      .findOne({
        email: { $regex: new RegExp(`^${email}$`, 'i') },
        studioId: studioId,
      })
      .exec();
    if (!parent) {
      throw new UnauthorizedException('Invalid email or password');
    }

    const isPasswordMatching = await bcrypt.compare(password, parent.password);
    if (!isPasswordMatching) {
      throw new UnauthorizedException('Invalid email or password');
    }

    return parent;
  }

  async login(loginDto: LoginDto) {
    const { email, password, locationId } = loginDto;
    const studio = await this.studioService.findByLocationIdString(locationId);
    const studioId = studio._id;
    const parent = await this.validateUser(email, password, studioId);

    // Generate JWT token
    // const payload = { email: parent.email, sub: parent._id };
    // const token = this.jwtService.sign(payload);

    return parent;
  }

  findAll() {
    return `This action returns all parents`;
  }

  async findOne(id: string): Promise<any> {
    try {
      // Add maxTimeMS to prevent hanging queries
      const parent = await this.parentModel.findById(id).maxTimeMS(5000).exec();

      if (!parent) {
        throw new NotFoundException(`Parent with ID #${id} not found`);
      }

      const student = await this.studentService.getStudentsByParentId(
        parent._id.toString(),
      );

      return {
        ...parent.toObject(),
        students: student,
      };
    } catch (error) {
      this.logger.error(`Error finding parent ${id}: ${error.message}`);
      // Re-throw the error instead of returning null to maintain error flow
      throw error;
    }
  }

  async findOneByEmail(
    email: string,
    studioId: string,
  ): Promise<ParentDocument> {
    return this.parentModel
      .findOne({
        email,
        studioId: Types.ObjectId.createFromHexString(studioId),
      })
      .lean()
      .exec();
  }

  async findOneLtdDetail(id: string): Promise<any> {
    const parent = await this.parentModel
      .findById(id)
      .select('firstName lastName email primaryPhone relation')
      .exec();
    // const student = await this.studentService.getStudentsByParentId(parent.id);

    // const familyName = student.length > 0 ? student[0].familyName : null;

    return {
      firstName: parent.firstName,
      lastName: parent.lastName,
      email: parent.email,
      primaryPhone: parent.primaryPhone,
      relation: parent.relation,
      familyName: parent.familyName,
      // familyName,
    };
  }

  async findByStudioId(
    studioId: string,
    options: {
      page: number;
      limit: number;
      search?: string;
    },
  ) {
    const studioId_object = Types.ObjectId.createFromHexString(studioId);
    const skip = (options.page - 1) * options.limit;

    // Build query
    const query: any = { studioId: studioId_object };
    if (options.search) {
      query.$or = [
        { firstName: { $regex: options.search, $options: 'i' } },
        { lastName: { $regex: options.search, $options: 'i' } },
        { name: { $regex: options.search, $options: 'i' } },
      ];
    }

    // Get total count for pagination
    const total = await this.parentModel.countDocuments(query);

    // Get parents with pagination
    const parents = await this.parentModel
      .find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(options.limit);

    // Map through parents and attach their students and family name
    const parentsWithStudents = await Promise.all(
      parents.map(async (parent) => {
        const students = await this.studentService.getStudentsByParentId(
          parent._id.toString(),
        );

        // Get family name from the first student (if any students exist)
        // const familyName = students.length > 0 ? students[0].familyName : '';

        return {
          ...parent.toObject(),
          students,
          // familyName,
        };
      }),
    );

    return {
      data: parentsWithStudents,
      pagination: {
        total,
        page: options.page,
        limit: options.limit,
        pages: Math.ceil(total / options.limit),
      },
    };
  }

  async update(id: string, updateParentDto: UpdateParentDto, studioId) {
    try {
      studioId = Types.ObjectId.createFromHexString(studioId);
      let secondParent = null;
      if (updateParentDto.secondParent) {
        const { firstName, lastName } = updateParentDto.secondParent;

        secondParent = {
          ...updateParentDto.secondParent,
          name: `${firstName.trim()} ${lastName}`,
        };
      }
      const newUpdatedData = {
        ...updateParentDto,
        studioId,
        secondParent,
        name: `${updateParentDto.firstName.trim()} ${updateParentDto.lastName}`,
      };
      const updatedParent = await this.parentModel.findByIdAndUpdate(
        id,
        { $set: newUpdatedData },
        { new: true, runValidators: true }, // `new: true` returns the updated document
      );
      // const students = await this.studentService.getStudentsByParentId(
      //   updatedParent.id,
      // );
      // for (const student of students) {
      //   if ('familyName' in newUpdatedData) {
      //     student.familyName = newUpdatedData.familyName as string;
      //   }
      //   await student.save();
      // }
      return updatedParent;
    } catch (error) {
      // Handle duplicate email error
      if (error.code === 11000) {
        throw new HttpException(
          'Duplicate email error: Email is already registered.',
          HttpStatus.CONFLICT,
        );
      }

      // Log and throw generic error
      console.error('Error during parent update:', error);
      throw new HttpException(
        'An error occurred during parent update',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateByProperty(property: string, value: any, updateData: any) {
    const parent = await this.parentModel.findOneAndUpdate(
      { [property]: value },
      { $set: updateData },
      { new: true },
    );
    return parent;
  }

  async addStudentToParent(
    parentId: string,
    studentId: Types.ObjectId,
  ): Promise<void> {
    this.logger.debug(
      `Attempting to add student ID ${studentId} to parent ID ${parentId}`,
    );

    const result = await this.parentModel.updateOne(
      { _id: parentId },
      { $addToSet: { students: studentId } }, // Use $addToSet to prevent duplicates
    );

    if (result.modifiedCount === 0) {
      this.logger.warn(
        `Parent with ID ${parentId} not found or no changes made`,
      );
      throw new NotFoundException('Parent not found or no changes made');
    }

    this.logger.log(
      `Successfully added student ID ${studentId} to parent ID ${parentId}`,
    );
  }

  async requestPasswordReset(email: string) {
    const token = crypto.randomBytes(20).toString('hex');
    const resetPasswordExpires = new Date(Date.now() + 3600000);

    const parent = await this.parentModel.findOneAndUpdate(
      { email },
      {
        $set: {
          resetPasswordToken: token,
          resetPasswordExpires,
        },
      },
      { new: true },
    );

    if (!parent) {
      throw new NotFoundException('Parent not found');
    }

    // Logic to send an email with the token can be added here
    return { message: 'Reset link sent to email.' };
  }

  // async resetPassword(token: string, newPassword: string) {
  //   const parent = await this.parentModel.findOneAndUpdate(
  //     {
  //       resetPasswordToken: token,
  //       resetPasswordExpires: { $gt: Date.now() },
  //     },
  //     {
  //       $set: {
  //         password: await bcrypt.hash(newPassword, 10),
  //       },
  //       $unset: {
  //         resetPasswordToken: "",
  //         resetPasswordExpires: "",
  //       },
  //     },
  //     { new: true }
  //   );

  //   if (!parent) {
  //     throw new BadRequestException('Password reset token is invalid or has expired.');
  //   }

  //   return { message: 'Password reset successfully.' };
  // }

  async getAllStudentDetails() {
    const students: StudentDetailsDto[] =
      await this.studentService.getAllStudentDetails();

    const studentDetails = await Promise.all(
      students.map(async (student) => {
        // Fetch parent details
        const parent = await this.parentModel
          .findById(student.parentId)
          .lean()
          .select('name');

        // Fetch enrollment details for each enrollmentId in student enrollments
        const enrollments = await Promise.all(
          student.enrollments.map(async (enrollment) => {
            const enrollmentData = await this.enrollmentService.findOneDetails(
              enrollment.enrollmentId.toString(),
            );
            return {
              ...enrollment,
              location: enrollmentData?.location,
              className: enrollmentData?.title,
            };
          }),
        );
        const isActive = student.enrollments.length > 0;
        // Format individual student data
        return {
          firstName: student.firstName,
          lastName: student.lastName,
          familyName: parent?.lastName,
          active: isActive,
          class: enrollments.map((enrollment) => enrollment.className),
          gender: student.gender,
          dob: student.dob,
          age: await calculateAge(student.dob),
          enrollments,
        };
      }),
    );

    return studentDetails;
  }

  async updateContactDetails(
    parentId: string,
    updateData: Partial<Parent>,
  ): Promise<Parent> {
    const updatedParent = await this.parentModel.findByIdAndUpdate(
      parentId,
      { $set: updateData },
      { new: true },
    );

    if (!updatedParent) {
      throw new NotFoundException('Parent not found');
    }

    return updatedParent;
  }

  async updateEmergencyContact(
    parentId: string,
    newEmergencyContacts: EmergencyContactDto[],
  ): Promise<Parent> {
    const updatedContacts = newEmergencyContacts.map((contact) => ({
      ...contact,
      name: `${contact.firstName?.trim() || ''} ${contact.lastName?.trim() || ''}`.trim(),
    }));

    // Update the parent document in the database
    const updatedParent = await this.parentModel.findByIdAndUpdate(
      parentId,
      { emergencyContacts: updatedContacts },
      { new: true, runValidators: true }, // Return the updated document and validate schema
    );

    // Handle case where parent is not found
    if (!updatedParent) {
      throw new NotFoundException('Parent not found');
    }

    return updatedParent;
  }

  async uploadProfilePic(
    parentId_string: string,
    // updateProfileDto: UpdateProfilePicDto,
    url,
  ): Promise<string> {
    try {
      // const publicUrl = await this.gcpStorageService.uploadImage(
      //   updateProfileDto.base64,
      //   parentId,
      //   updateProfileDto.fileName,
      // );
      // const parentId = Types.ObjectId.createFromHexString(parentId_string);
      const updatedParent = await this.parentModel.findByIdAndUpdate(
        parentId_string,
        { profilePicUrl: url },
        { new: true },
      );

      return;
    } catch (error) {
      console.error('Error uploading profile picture:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Failed to upload profile picture. Please try again later.',
      );
    }
  }

  async initiateResetPassword(
    email: string,
    locationId: string,
  ): Promise<void> {
    this.logger.debug(`Initiating password reset for email: ${email}`);
    const studio = await this.studioModel.findOne({ locationId: locationId });
    if (!studio) {
      this.logger.warn(
        `Password reset attempted for non-existent locationId: ${locationId}`,
      );
      throw new NotFoundException('studio not found.');
    }
    const parent = await this.parentModel.findOne({
      email: { $regex: new RegExp(`^${email}$`, 'i') },
      studioId: studio._id,
    });
    if (!parent) {
      this.logger.warn(
        `Password reset attempted for non-existent email: ${email}`,
      );
      throw new NotFoundException('parent not found.');
    }

    // const studio = await this.studioService.findByLocationId(parent.studioId);
    const subaccountName = studio.subaccountName;

    // Generate token with additional data
    const tokenData = {
      email: parent.email,
      studioId: parent.studioId.toString(),
      timestamp: Date.now(),
    };
    const token = crypto.randomBytes(32).toString('hex');
    const expiry = Date.now() + 3600000; // 1 hour expiry

    // Hash token and save
    const hashedToken = crypto.createHash('sha256').update(token).digest('hex');
    await this.parentModel.findOneAndUpdate(
      {
        _id: parent._id,
        studioId: parent.studioId,
      },
      {
        resetPasswordToken: hashedToken,
        resetPasswordExpires: expiry,
      },
    );

    // Construct reset URL with encoded token data
    const resetUrl = `${process.env.PARENT_PORTAL_URL}/password-reset?token=${hashedToken}&data=${Buffer.from(JSON.stringify(tokenData)).toString('base64')}`;

    try {
      const studioLogoUrl = await this.gcpStorageService.getPublicImage(
        parent.studioId.toString(),
        'studio-logo',
      );
      await sendResetPasswordEmail(
        parent.email,
        resetUrl,
        studioLogoUrl ? null : subaccountName,
        `${parent.firstName} ${parent.lastName}`,
        studioLogoUrl ? studioLogoUrl : null,
      );
      this.logger.log(`Password reset email sent successfully to: ${email}`);
    } catch (error) {
      this.logger.error(
        `Failed to send password reset email to ${email}:`,
        error,
      );

      await sendResetPasswordEmail(
        parent.email,
        resetUrl,
        subaccountName,
        `${parent.firstName} ${parent.lastName}`,
        'https://storage.googleapis.com/studio-class-default-images/2.png',
      );
    }
  }

  async completeResetPassword(
    token: string,
    newPassword: string,
  ): Promise<{ message: string; data: { locationId: string } }> {
    this.logger.debug('Starting password reset completion');

    try {
      // Extract and validate token data
      const tokenParts = token.split('&data=');
      if (tokenParts.length !== 2) {
        throw new BadRequestException('Invalid token format');
      }

      const hashedToken = tokenParts[0];
      const tokenData = JSON.parse(
        Buffer.from(tokenParts[1], 'base64').toString(),
      );

      if (!tokenData.email || !tokenData.studioId || !tokenData.timestamp) {
        throw new BadRequestException('Invalid token data');
      }

      // Convert studioId to ObjectId
      const studioId = Types.ObjectId.createFromHexString(tokenData.studioId);

      // Find parent with email, studioId and valid token
      const parent = await this.parentModel.findOne({
        email: tokenData.email,
        studioId: studioId,
        resetPasswordToken: hashedToken,
        resetPasswordExpires: { $gt: Date.now() },
      });

      if (!parent) {
        this.logger.warn(
          `Invalid or expired token for email: ${tokenData.email}`,
        );
        throw new BadRequestException('Invalid or expired token.');
      }

      // Hash new password and update parent
      const hashedPassword = await bcrypt.hash(newPassword, 12);
      await this.parentModel.findOneAndUpdate(
        {
          email: tokenData.email,
          studioId: studioId,
        },
        {
          $set: {
            password: hashedPassword,
            isFirstLogin: false,
          },
          $unset: { resetPasswordToken: 1, resetPasswordExpires: 1 },
        },
        { new: true },
      );

      const studio = await this.studioService.findByLocationId(parent.studioId);

      this.logger.log(
        `Password reset successful for email: ${tokenData.email}`,
      );
      return {
        message: 'Password reset successful.',
        data: { locationId: studio.locationId },
      };
    } catch (error) {
      this.logger.error('Error completing password reset:', error);
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Failed to complete password reset',
      );
    }
  }

  async getParentByEmail(email, token, locationId) {
    const locationId_object = Types.ObjectId.createFromHexString(locationId);
    const parent = await this.parentModel
      .findOne({ email: email, studioId: locationId_object })
      .populate({
        path: 'studioId',
        model: 'Studio',
        select: 'subaccountName locationId',
      })
      // .select('_id name email')
      .exec();
    if (!parent) throw new NotFoundException('Parent not found');

    const parentData = parent.toObject();
    delete parentData.password;
    return {
      access_token: token,
      parent: parentData,
    };
  }

  async updateSecondParent(
    parentId: string,
    secondParentData: Partial<SecondParentDto>,
  ): Promise<ParentDocument | null> {
    const updatedParent = await this.parentModel.findByIdAndUpdate(
      parentId,
      { $set: { secondParent: secondParentData } },
      { new: true, runValidators: true },
    );

    return updatedParent;
  }

  async enrollStudentInAClass(
    studioId: string,
    classId: string,
    students: Partial<StudentIdsDto>,
    date: string,
  ) {
    const studioId_object = Types.ObjectId.createFromHexString(studioId);
    const classId_object = Types.ObjectId.createFromHexString(classId);
    const studentIds = students.studentIds || [];
    // const studentObjectIds = studentIds.map((id) => Types.ObjectId.createFromHexString(id));
    const enrollmentDate = new Date(date);
    await this.studentService.enrollStudentInAClass(
      studioId_object,
      classId_object,
      students,
      enrollmentDate,
    );
  }

  async findParent(email: string, studioId?: string): Promise<ParentDocument> {
    const studioObjectId = studioId
      ? Types.ObjectId.createFromHexString(studioId)
      : null;
    const query: any = { email };
    if (studioObjectId) {
      query.studioId = studioObjectId;
    }
    const parent = await this.parentModel.findOne(query).exec();
    if (!parent) throw new NotFoundException('Parent not found');
    return parent;
  }

  async remove(id: string, studioId: string): Promise<boolean> {
    const studioObjectId = Types.ObjectId.createFromHexString(studioId);
    const parentId = Types.ObjectId.createFromHexString(id);
    await this.studentModel.deleteMany({
      parentId: parentId,
      studioId: studioObjectId,
    });
    const result = await this.parentModel.deleteOne({
      _id: parentId,
      studioId: studioObjectId,
    });
    return result.deletedCount > 0;
  }

  async removeBatch(
    ids: string[],
    studioId: string,
  ): Promise<{ deletedCount: number }> {
    const studioObjectId = Types.ObjectId.createFromHexString(studioId);
    const parentIds = ids.map((id) => Types.ObjectId.createFromHexString(id));
    await this.studentModel.deleteMany({
      parentId: { $in: parentIds },
      studioId: studioObjectId,
    });
    const result = await this.parentModel.deleteMany({
      _id: { $in: parentIds },
      studioId: studioObjectId,
    });
    return { deletedCount: result.deletedCount };
  }

  async bulkCreateParents(
    importData: BulkImportParentDto[],
    studioId: string,
  ): Promise<any> {
    const results = {
      success: 0,
      failed: 0,
      errors: [] as string[],
    };

    // Group students by parent email with proper typing
    const studentsByParent = importData.reduce<
      Record<string, ParentsBulkCreatePayload>
    >((acc, row) => {
      if (!acc[row.parent_email]) {
        acc[row.parent_email] = {
          parent: {
            firstName: row.parent_first_name,
            lastName: row.parent_last_name,
            email: row.parent_email,
            relation: row.relation,
            primaryPhone: row.parent_primary_phone,
            address: {
              street: row.address_street,
              apartment: row.address_apartment,
              city: row.address_city,
              state: row.address_state,
              zip: row.address_zip,
              country: row.address_country,
            },
            emergencyContact: {
              firstName: row.emergency_contact1_first_name,
              lastName: row.emergency_contact1_last_name,
              phone: row.emergency_contact1_phone,
              email: row.emergency_contact1_email,
              authorizePickup: row.emergency_contact1_authorize_pickup
                ? row.emergency_contact1_authorize_pickup.toLowerCase() ===
                  'yes'
                : false,
            },
          },
          students: [],
        };

        // Process all possible students (1-5) in the row
        for (let i = 1; i <= 5; i++) {
          if (row[`student_${i}_first_name`]) {
            acc[row.parent_email].students.push({
              firstName: row[`student_${i}_first_name`],
              lastName: row[`student_${i}_last_name`],
              dob: new Date(row[`student_${i}_date_of_birth`]),
              gender: row[`student_${i}_gender`],
              studentEmail: row[`student_${i}_email`],
              mobileNo: row[`student_${i}_primary_phone`],
              tShirtSize: row[`student_${i}_costume_size`],
              transportation: row[`student_${i}_transportation`],
              medical: {
                healthInsuranceCarrier:
                  row[`student_${i}_health_insurance_carrier`],
                primaryDoctor: row[`student_${i}_primary_doctor`],
                allergies: row[`student_${i}_allergies`],
                disabilities: row[`student_${i}_disabilities`],
                medications: row[`student_${i}_medications`],
                specialNeeds: row[`student_${i}_special_needs`],
              },
            });
          }
        }
      }

      return acc;
    }, {});

    // Process each parent and their students
    for (const [email, data] of Object.entries(studentsByParent)) {
      try {
        let parent: ParentDocument | null = null;

        try {
          // Check if parent exists
          parent = await this.findParent(email, studioId);

          for (const studentData of data.students) {
            try {
              const studioObjectId =
                Types.ObjectId.createFromHexString(studioId);
              const createStudentDto = {
                ...studentData,
                parentId: parent._id,
                studioId: studioObjectId,
                familyName: parent.lastName,
              };
              await this.studentService.createFromParent(createStudentDto);
              results.success++;
            } catch (error) {
              results.failed++;
              results.errors.push(
                `Failed to create student ${studentData.firstName} ${studentData.lastName}: ${error.message}`,
              );
            }
          }
        } catch (error) {
          console.log(error);
          const studioDetails =
            await this.studioService.findByLocationId(studioId);
          const createParentDto: any = {
            firstName: data.parent.firstName,
            lastName: data.parent.lastName,
            email: data.parent.email,
            primaryPhone: data.parent.primaryPhone,
            studioId: studioDetails.locationId,
            familyName: data.parent.lastName,
            relation: data.parent.relation,
            address: data.parent.address,
            emergencyContacts: [
              {
                firstName: data.parent.emergencyContact.firstName,
                lastName: data.parent.emergencyContact.lastName,
                phone: data.parent.emergencyContact.phone,
                emergencyContactEmail: data.parent.emergencyContact.email,
                authorize: data.parent.emergencyContact.authorizePickup,
              },
            ],
            students: data.students,
          };

          await this.createWithLocationId(createParentDto);
        }

        // // Create students
      } catch (error) {
        results.failed += data.students.length;
        results.errors.push(
          `Failed to process parent ${email}: ${error.message}`,
        );
      }
    }

    return results;
  }

  async getAttendanceAnalytics(
    parentId: string,
    studioId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<{
    attendanceAnalytics: any[];
    total: number;
    currentPage: number;
    totalPages: number;
  }> {
    try {
      const pageNum = Math.max(1, page);
      const limitNum = Math.max(1, Math.min(100, limit));
      const skip = (pageNum - 1) * limitNum;

      if (!Types.ObjectId.isValid(parentId)) {
        throw new BadRequestException('Invalid parent ID format');
      }

      // Get paginated students
      const students = await this.studentModel
        .find({
          parentId: new Types.ObjectId(parentId),
        })
        .skip(skip)
        .limit(limitNum);

      if (!students || students.length === 0) {
        throw new NotFoundException('No students found for this parent');
      }

      const analytics = await Promise.all(
        students.map(async (student) => {
          // Log student attendance array length
          // console.log('Student attendance length:', student.attendance?.length);

          // Get all attendance records for this student
          const attendancePromises =
            student.attendance?.map(async (att) => {
              const record = await this.attendanceModel.findById(
                att.attendanceId,
              );
              return {
                ...record?.toObject(),
                date: att.date,
                classId: att.classId,
              };
            }) || [];

          const attendanceRecords = await Promise.all(attendancePromises);
          // console.log('Fetched attendance records:', attendanceRecords.length);

          // Get class IDs and enrollments
          const classIds = student.attendance
            ? [
                ...new Set(
                  student.attendance.map((att) => att.classId.toString()),
                ),
              ]
            : [];
          const enrollments =
            await this.enrollmentService.findByClassIds(classIds);

          // Calculate attendance statistics
          const totalClasses = student.attendance?.length || 0;
          const presentCount = attendanceRecords.filter(
            (record) => record?.isPresent === true,
          ).length;
          const absentCount = attendanceRecords.filter(
            (record) => record?.isPresent === false,
          ).length;
          const pendingCount = totalClasses - (presentCount + absentCount);
          const presentPercentage =
            totalClasses > 0
              ? Math.round((presentCount / totalClasses) * 100)
              : 0;

          return {
            studentId: student._id,
            student: `${student.firstName} ${student.lastName}`,
            classId:
              enrollments.length > 0 ? enrollments[0]._id || 'N/A' : 'N/A',
            className:
              enrollments.length > 0 ? enrollments[0].title || 'N/A' : 'N/A',
            attendance_summary: {
              total_classes: totalClasses,
              present: {
                count: presentCount,
                percentage: presentPercentage,
              },
              absent: {
                count: absentCount,
                percentage: 100 - presentPercentage,
              },
              attendance_rate: `${presentPercentage}%`,
              // pending_count: pendingCount
            },
          };
        }),
      );

      const totalCount = await this.studentModel.countDocuments({
        parentId: new Types.ObjectId(parentId),
      });

      return {
        attendanceAnalytics: analytics,
        total: totalCount,
        currentPage: pageNum,
        totalPages: Math.ceil(totalCount / limitNum),
      };
    } catch (error) {
      this.logger.error(
        `Error getting attendance analytics for parent ${parentId}:`,
        error,
      );
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Failed to get attendance analytics',
      );
    }
  }
  async getDetailedAttendanceAnalytics(
    parentId: string,
  ): Promise<DetailedAttendanceDto[]> {
    try {
      if (!Types.ObjectId.isValid(parentId)) {
        throw new BadRequestException('Invalid parent ID format');
      }

      const students = await this.studentModel.find({
        parentId: new Types.ObjectId(parentId),
      });

      if (!students || students.length === 0) {
        throw new NotFoundException('No students found for this parent');
      }

      const analytics = await Promise.all(
        students.map(async (student) => {
          const enrollments = await this.enrollmentService.findByStudentId(
            student._id.toString(),
          );

          const detailedAnalytics = await Promise.all(
            enrollments.map(async (enrollment) => {
              let totalClasses = 0;
              let presentCount = 0;
              let absentCount = 0;

              const attendance = enrollment.attendance || [];
              if (Array.isArray(attendance)) {
                totalClasses = attendance.length;
                presentCount = attendance.filter(
                  (a) => a.status === 'present',
                ).length;
                absentCount = attendance.filter(
                  (a) => a.status === 'absent',
                ).length;
              }

              const presentPercentage =
                totalClasses > 0
                  ? Math.round((presentCount / totalClasses) * 100)
                  : 0;

              // Format attendance records
              const attendanceRecords = attendance.map((record) => ({
                date: new Date(record.date).toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                  year: 'numeric',
                }),
                status:
                  record.status.charAt(0).toUpperCase() +
                  record.status.slice(1),
              }));

              // Add upcoming classes
              const today = new Date();
              const upcomingDates = this.getUpcomingClassDates(enrollment, 2); // Get next 2 upcoming classes
              attendanceRecords.push(
                ...upcomingDates.map((date) => ({
                  date: date.toLocaleDateString('en-US', {
                    month: 'short',
                    day: 'numeric',
                    year: 'numeric',
                  }),
                  status: 'Upcoming',
                })),
              );

              return {
                student: `${student.firstName} ${student.lastName}`,
                class: enrollment.title || 'N/A',
                instructor:
                  (enrollment.instructor?.[0] as any)?.firstName || 'N/A',
                attendance_summary: {
                  total_classes: totalClasses,
                  present: {
                    count: presentCount,
                    percentage: presentPercentage,
                  },
                  absent: {
                    count: absentCount,
                    percentage: 100 - presentPercentage,
                  },
                  attendance_rate: `${presentPercentage}%`,
                },
                class_details: {
                  start_date: new Date(enrollment.startDate).toLocaleDateString(
                    'en-US',
                    {
                      month: 'long',
                      day: 'numeric',
                      year: 'numeric',
                    },
                  ),
                  end_date: new Date(enrollment.endDate).toLocaleDateString(
                    'en-US',
                    {
                      month: 'long',
                      day: 'numeric',
                      year: 'numeric',
                    },
                  ),
                  class_days: enrollment.days || [],
                },
                attendance_records: attendanceRecords
                  .sort(
                    (a, b) =>
                      new Date(a.date).getTime() - new Date(b.date).getTime(),
                  )
                  .slice(-5), // Get last 5 records
              };
            }),
          );

          return detailedAnalytics;
        }),
      );

      // Flatten the array since we're getting an array for each student's enrollments
      return analytics.flat();
    } catch (error) {
      this.logger.error(
        `Error getting detailed attendance analytics for parent ${parentId}:`,
        error,
      );
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Failed to get detailed attendance analytics',
      );
    }
  }

  private getUpcomingClassDates(enrollment: any, count: number): Date[] {
    const dates: Date[] = [];
    const today = new Date();
    const endDate = new Date(enrollment.endDate);
    const currentDate = new Date();

    while (dates.length < count && currentDate <= endDate) {
      if (
        enrollment.days.includes(
          currentDate.toLocaleDateString('en-US', { weekday: 'long' }),
        )
      ) {
        dates.push(new Date(currentDate));
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dates;
  }

  async getClassIdByParentId(parentId: string): Promise<string[]> {
    const students = await this.studentModel.find({
      parentId: new Types.ObjectId(parentId),
    });
    const allClassIds = students
      .map(
        (student) =>
          student.attendance?.map((att) => att.classId.toString()) || [],
      )
      .flat();

    // Return unique class IDs
    return [...new Set(allClassIds)];
  }

  async getStudentAttendance(
    parentId: string,
    studioId: string,
    pagination: { page: number; limit: number },
  ): Promise<{
    data: any[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      if (!Types.ObjectId.isValid(parentId)) {
        throw new BadRequestException('Invalid parent ID format');
      }

      const { page, limit } = pagination;
      const skip = (page - 1) * limit;

      // Get total count
      const totalStudents = await this.studentModel.countDocuments({
        parentId: new Types.ObjectId(parentId),
      });

      // Get paginated students
      const students = await this.studentModel
        .find({ parentId: new Types.ObjectId(parentId) })
        .select('firstName lastName attendance')
        .skip(skip)
        .limit(limit)
        .lean();

      const studentAttendance = await Promise.all(
        students.map(async (student) => {
          if (!student.attendance || student.attendance.length === 0) {
            return {
              studentId: student._id,
              student: `${student.firstName} ${student.lastName}`,
              classes: [],
            };
          }

          const classIds = [
            ...new Set(student.attendance.map((att) => att.classId.toString())),
          ];

          const enrollments = await this.enrollmentService.findByIds(classIds);
          // console.log("enrollments",enrollments)
          const classes = enrollments
            .map((enrollment) => {
              if (!enrollment) return null;

              const studentAttendance = student.attendance.filter(
                (att) => att.classId.toString() === enrollment._id.toString(),
              );

              const totalClasses = studentAttendance.length;
              const presentCount = studentAttendance.filter(
                (a) => a.status === 'present',
              ).length;
              const attendancePercentage =
                totalClasses > 0
                  ? Math.round((presentCount / totalClasses) * 100)
                  : 0;

              return {
                clssId: enrollment._id,
                className: enrollment.title || 'N/A',
                instructor: enrollment.instructor || 'N/A',
                attendancePercentage,
                classDetails: {
                  startDate: new Date(enrollment.startDate).toLocaleDateString(
                    'en-US',
                    {
                      month: 'long',
                      day: 'numeric',
                      year: 'numeric',
                    },
                  ),
                  endDate: new Date(enrollment.endDate).toLocaleDateString(
                    'en-US',
                    {
                      month: 'long',
                      day: 'numeric',
                      year: 'numeric',
                    },
                  ),
                  classDays: enrollment.days || [],
                },
                attendanceRecords: [
                  ...studentAttendance.map((record) => ({
                    date: new Date(record.date).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric',
                    }),
                    status: record.status
                      ? record.status.charAt(0).toUpperCase() +
                        record.status.slice(1)
                      : 'Pending',
                  })),
                  ...this.getUpcomingClassDates(enrollment, 2).map((date) => ({
                    date: date.toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric',
                    }),
                    status: 'Upcoming',
                  })),
                ]
                  .sort(
                    (a, b) =>
                      new Date(a.date).getTime() - new Date(b.date).getTime(),
                  )
                  .slice(-5),
              };
            })
            .filter((c) => c !== null);

          return {
            studentid: student._id,
            student: `${student.firstName} ${student.lastName}`,
            classes,
          };
        }),
      );

      return {
        data: studentAttendance,
        total: totalStudents,
        page,
        totalPages: Math.ceil(totalStudents / limit),
      };
    } catch (error) {
      this.logger.error(
        `Error getting student attendance for parent ${parentId}:`,
        error,
      );
      throw new InternalServerErrorException(
        'Failed to get student attendance data',
      );
    }
  }

  async getStudentAttendanceClass(
    studentId: string,
    studioId: string,
    pagination: { page: number; limit: number },
  ): Promise<{
    data: any[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      if (!Types.ObjectId.isValid(studentId)) {
        throw new BadRequestException('Invalid parent ID format');
      }

      const { page, limit } = pagination;
      const skip = (page - 1) * limit;

      // Get total count
      const totalStudents = await this.studentModel.countDocuments({
        parentId: new Types.ObjectId(studentId),
      });

      // Get paginated students
      const students = await this.studentModel
        .find({ _id: new Types.ObjectId(studentId) })
        .select('firstName lastName attendance')
        .skip(skip)
        .limit(limit)
        .lean();

      const studentAttendance = await Promise.all(
        students.map(async (student) => {
          if (!student.attendance || student.attendance.length === 0) {
            return {
              studentId: student._id,
              student: `${student.firstName} ${student.lastName}`,
              classes: [],
            };
          }

          const classIds = [
            ...new Set(student.attendance.map((att) => att.classId.toString())),
          ];

          const enrollments = await this.enrollmentService.findByIds(classIds);

          const classes = enrollments
            .map((enrollment) => {
              if (!enrollment) return null;

              const studentAttendance = student.attendance.filter(
                (att) => att.classId.toString() === enrollment._id.toString(),
              );

              const totalClasses = studentAttendance.length;
              const presentCount = studentAttendance.filter(
                (a) => a.status === 'present',
              ).length;
              const attendancePercentage =
                totalClasses > 0
                  ? Math.round((presentCount / totalClasses) * 100)
                  : 0;

              return {
                clssId: enrollment._id,
                className: enrollment.title || 'N/A',
                instructor: enrollment.instructor || 'N/A',
                attendancePercentage,
                classDetails: {
                  startDate: new Date(enrollment.startDate).toLocaleDateString(
                    'en-US',
                    {
                      month: 'long',
                      day: 'numeric',
                      year: 'numeric',
                    },
                  ),
                  endDate: new Date(enrollment.endDate).toLocaleDateString(
                    'en-US',
                    {
                      month: 'long',
                      day: 'numeric',
                      year: 'numeric',
                    },
                  ),
                  classDays: enrollment.days || [],
                },
                attendanceRecords: [
                  ...studentAttendance.map((record) => ({
                    date: new Date(record.date).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric',
                    }),
                    status: record.status
                      ? record.status.charAt(0).toUpperCase() +
                        record.status.slice(1)
                      : 'Pending',
                  })),
                  ...this.getUpcomingClassDates(enrollment, 2).map((date) => ({
                    date: date.toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric',
                    }),
                    status: 'Upcoming',
                  })),
                ]
                  .sort(
                    (a, b) =>
                      new Date(a.date).getTime() - new Date(b.date).getTime(),
                  )
                  .slice(-5),
              };
            })
            .filter((c) => c !== null);

          return {
            studentid: student._id,
            student: `${student.firstName} ${student.lastName}`,
            classes,
          };
        }),
      );

      return {
        data: studentAttendance,
        total: totalStudents,
        page,
        totalPages: Math.ceil(totalStudents / limit),
      };
    } catch (error) {
      this.logger.error(
        `Error getting student attendance for parent ${studentId}:`,
        error,
      );
      throw new InternalServerErrorException(
        'Failed to get student attendance data',
      );
    }
  }

  async getAllClassDetails(
    parentId: string,
    page: number = 1,
    limit: number = 10,
  ) {
    try {
      if (!Types.ObjectId.isValid(parentId)) {
        throw new BadRequestException('Invalid parent ID format');
      }

      // Calculate skip value for pagination
      const skip = (page - 1) * limit;

      // Find all students for the parent with pagination
      const [students, totalStudents] = await Promise.all([
        this.studentModel
          .find({ parentId: new Types.ObjectId(parentId) })
          .select('firstName lastName enrollments attendance')
          .skip(skip)
          .limit(limit)
          .lean(),
        this.studentModel.countDocuments({
          parentId: new Types.ObjectId(parentId),
        }),
      ]);

      if (!students || students.length === 0) {
        return {
          success: true,
          message: 'No students found for this parent',
          data: [],
          pagination: {
            total: 0,
            page,
            limit,
            totalPages: 0,
          },
        };
      }

      const classDetails = await Promise.all(
        students.map(async (student) => {
          // Get enrollment details for each student
          const enrollmentDetails = await Promise.all(
            (student.enrollments || []).map(async (enrollment) => {
              const enrollmentData = await this.enrollmentService.findOne(
                enrollment.enrollmentId.toString(),
              );

              if (!enrollmentData) {
                return null;
              }

              // Get attendance records for this class
              const classAttendance = (student.attendance || []).filter(
                (att) =>
                  att.classId.toString() === enrollmentData._id.toString(),
              );

              // Calculate attendance statistics
              const totalAttendance = classAttendance.length;
              const attendanceRecords = await Promise.all(
                classAttendance.map(async (att) => {
                  const record = await this.attendanceModel.findById(
                    att.attendanceId,
                  );
                  return {
                    date: att.date,
                    status: record?.isPresent ? 'present' : 'absent',
                  };
                }),
              );

              const presentCount = attendanceRecords.filter(
                (record) => record.status === 'present',
              ).length;
              const absentCount = totalAttendance - presentCount;

              // Calculate percentages
              const presentPercentage =
                totalAttendance > 0
                  ? Math.round((presentCount / totalAttendance) * 100)
                  : 0;
              const absentPercentage =
                totalAttendance > 0
                  ? Math.round((absentCount / totalAttendance) * 100)
                  : 0;

              return {
                classId: enrollmentData._id,
                className: enrollmentData.title,
                instructor: enrollmentData.instructor,
                schedule: {
                  days: enrollmentData.days,
                  startDate: enrollmentData.startDate,
                  endDate: enrollmentData.endDate,
                  startTime: enrollmentData.startTime,
                  endTime: enrollmentData.endTime,
                },
                enrollment: {
                  status: enrollment.subscriptionStatus,
                  enrolledDate: enrollment.enrolledDate,
                  absences: enrollment.absences,
                  skills: enrollment.skills,
                },
                attendance: {
                  total: totalAttendance,
                  present: presentCount,
                  absent: absentCount,
                  presentPercentage: `${presentPercentage}%`,
                  absentPercentage: `${absentPercentage}%`,
                  attendanceRate: `${presentPercentage}%`,
                  records: attendanceRecords.sort(
                    (a, b) =>
                      new Date(b.date).getTime() - new Date(a.date).getTime(),
                  ),
                },
              };
            }),
          );

          return {
            studentId: student._id,
            studentName: `${student.firstName} ${student.lastName}`,
            classes: enrollmentDetails.filter((ed) => ed !== null),
          };
        }),
      );

      return {
        success: true,
        message: 'Class details retrieved successfully',
        data: classDetails,
        pagination: {
          total: totalStudents,
          page,
          limit,
          totalPages: Math.ceil(totalStudents / limit),
        },
      };
    } catch (error) {
      this.logger.error(
        `Error getting class details for parent ${parentId}:`,
        error,
      );
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Failed to get class details');
    }
  }

  async getWalletBalance(parentId: string, studioId: string) {
    const parent = await this.parentModel
      .findOne({
        _id: Types.ObjectId.createFromHexString(parentId),
        studioId: Types.ObjectId.createFromHexString(studioId),
      })
      .exec();
    if (!parent.walletBalance) {
      parent.walletBalance = 0;
      await parent.save();
    }
    return parent.walletBalance;
  }

  async addWalletBalance({
    studioId,
    parentId,
    studentId,
    amount,
    reason,
    metadata,
    paymentMethod,
  }: WalletBalanceDto) {
    // First update the parent's wallet balance and get the new balance
    const updatedParent = await this.parentModel.findOneAndUpdate(
      {
        _id: Types.ObjectId.createFromHexString(parentId),
        studioId: Types.ObjectId.createFromHexString(studioId),
      },
      { $inc: { walletBalance: amount } },
      { new: true },
    );

    // Create wallet transaction record
    await this.walletTransactionModel.create({
      parentId: Types.ObjectId.createFromHexString(parentId),
      studioId: Types.ObjectId.createFromHexString(studioId),
      studentId: studentId
        ? Types.ObjectId.createFromHexString(studentId)
        : undefined,
      transactionType: TransactionType.CREDIT,
      paymentMethod: paymentMethod,
      reason,
      amount,
      balance: updatedParent.walletBalance,
      metadata,
    });

    return {
      message: 'Wallet balance added successfully',
      balance: updatedParent.walletBalance,
    };
  }

  async removeWalletBalance({
    studioId,
    parentId,
    studentId,
    amount,
    reason,
    metadata,
    paymentMethod,
  }: WalletBalanceDto) {
    // First update the parent's wallet balance and get the new balance
    const updatedParent = await this.parentModel.findOneAndUpdate(
      {
        _id: Types.ObjectId.createFromHexString(parentId),
        studioId: Types.ObjectId.createFromHexString(studioId),
      },
      { $inc: { walletBalance: -amount } },
      { new: true },
    );

    // Create wallet transaction record
    await this.walletTransactionModel.create({
      parentId: Types.ObjectId.createFromHexString(parentId),
      studioId: Types.ObjectId.createFromHexString(studioId),
      studentId: studentId
        ? Types.ObjectId.createFromHexString(studentId)
        : undefined,
      transactionType: TransactionType.DEBIT,
      reason,
      amount,
      paymentMethod,
      balance: updatedParent.walletBalance,
      metadata,
    });

    return {
      message: 'Wallet balance removed successfully',
      balance: updatedParent.walletBalance,
    };
  }

  async loadWalletBalance(body: LoadWalletBalanceDto) {
    const { parentId, studioId, amount, paymentMethod, checkNumber } = body;
    if (paymentMethod === PaymentMethod.CASH) {
      await this.addWalletBalance({
        studioId,
        parentId,
        amount,
        reason: ReasonType.WALLET_LOAD,
        paymentMethod,
      });
    } else if (paymentMethod === PaymentMethod.CHECK) {
      await this.addWalletBalance({
        studioId,
        parentId,
        amount,
        reason: ReasonType.WALLET_LOAD,
        paymentMethod,
        metadata: {
          checkNumber,
        },
      });
    } else if (
      paymentMethod === PaymentMethod.US_BANK_ACCOUNT ||
      paymentMethod === PaymentMethod.CARD
    ) {
      const paymentMethodOnFile =
        await this.paymentProcessorService.checkPaymentMethodOnFile(
          studioId,
          parentId,
          paymentMethod,
        );
      if (paymentMethodOnFile.hasPaymentMethod) {
        await this.paymentProcessorService.createWalletLoad({
          studioId,
          parentId,
          amount,
          paymentMethod,
        });
      } else {
        return paymentMethodOnFile;
      }
    }
    return {
      status: 'success',
      message: 'Wallet balance loaded successfully',
    };
  }

  async fetchWalletTransactions(parentId: string, studioId: string) {
    const walletTransactions = await this.walletTransactionModel
      .find({
        parentId: Types.ObjectId.createFromHexString(parentId),
        studioId: Types.ObjectId.createFromHexString(studioId),
      })
      .populate({
        path: 'studentId',
        model: 'Student',
        select: '_id firstName lastName',
      })
      .populate({
        path: 'parentId',
        model: 'Parent',
        select: '_id firstName lastName',
      });
    return walletTransactions;
  }
}
