import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  BadRequestException,
  Req,
  Put,
  NotFoundException,
  InternalServerErrorException,
  UnauthorizedException,
  Query,
  UseInterceptors,
  UploadedFile,
  HttpCode,
} from '@nestjs/common';
import { ParentsService } from './parents.service';
import { CreateParentDto, SecondParentDto } from './dto/create-parent.dto';
import { EmergencyContactDto, UpdateParentDto } from './dto/update-parent.dto';
import { LoginDto } from './dto/login_dto';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { UpdateProfilePicDto } from './dto/updateProfilePic.dto';
import {
  CompleteResetPasswordDto,
  InitiateResetPasswordDto,
} from './dto/resetPasswordDto';
import { Parent } from 'src/database/schema/parent';
import { StudentIdsDto } from './dto/incomingStudentIdDto';
import { FileInterceptor } from '@nestjs/platform-express';
import * as csv from 'csv-parse/sync';
import { BulkImportParentDto } from './dto/bulk-import.dto';
import { CSV_IMPORT_HEADER_MAPPING } from './entities/parent.entity';
import { DetailedAttendanceDto } from './dto/detailed-attendance.dto';
import { StudentsService } from '../students/students.service';
import { ReasonType } from 'src/database/schema/walletTransaction';
import {
  WalletBalanceDto,
  WalletMetadata,
  LoadWalletBalanceDto,
} from './dto/wallet.dto';

@Controller('parents')
export class ParentsController {
  constructor(
    private readonly parentsService: ParentsService,
    private readonly studentsService: StudentsService,
  ) {}

  // @Post()
  // @UseGuards(JwtAuthGuard)
  // create(@Body() createParentDto: CreateParentDto, @Req() request: Request) {
  //   const locationId = request['locationId'];
  //   return this.parentsService.create(createParentDto, locationId);
  // }

  @Post()
  // @UseGuards(JwtAuthGuard)
  create(@Body() createParentDto: CreateParentDto) {
    // const locationId = request['locationId'];
    return this.parentsService.createWithLocationId(createParentDto);
  }

  @Post('login')
  auth(@Body() login: LoginDto) {
    return this.parentsService.login(login);
  }

  @Get()
  findAll() {
    return this.parentsService.findAll();
  }

  @Get('studio')
  @UseGuards(JwtAuthGuard)
  findByStudioId(
    @Req() request: Request,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('search') search?: string,
  ) {
    const studioId = request['locationId'];
    return this.parentsService.findByStudioId(studioId, {
      page: Number(page),
      limit: Number(limit),
      search,
    });
  }

  @UseGuards(JwtAuthGuard)
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.parentsService.findOne(id);
  }

  @Get('ltd-detail/:id')
  findOneLtdDetail(@Param('id') id: string) {
    return this.parentsService.findOneLtdDetail(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard)
  update(
    @Param('id') id: string,
    @Body() updateParentDto: UpdateParentDto,
    @Req() request: Request,
  ) {
    const studioId = request['locationId'];
    return this.parentsService.update(id, updateParentDto, studioId);
  }

  @Patch('update/parent/contact/:id')
  async updateParentContact(
    @Param('id') id: string,
    @Body() updateParentContactDto: Partial<Parent>,
  ) {
    return await this.parentsService.updateContactDetails(
      id,
      updateParentContactDto,
    );
  }

  @Patch('update/parent/emergencyContact/:id')
  async updateEmergencyContact(
    @Param('id') id: string,
    @Body() updateEmergencyContactDto: EmergencyContactDto,
  ) {
    if (!Array.isArray(updateEmergencyContactDto)) {
      throw new BadRequestException(
        'Payload must be an array of emergency contact objects.',
      );
    }
    return await this.parentsService.updateEmergencyContact(
      id,
      updateEmergencyContactDto,
    );
  }

  @Post('request-password-reset')
  async requestPasswordReset(@Body('email') email: string) {
    if (!email) {
      throw new BadRequestException('Email is required');
    }

    return this.parentsService.requestPasswordReset(email);
  }

  // Route for resetting password using token
  // @Post('reset-password')
  // async resetPassword(
  //   @Body('token') token: string,
  //   @Body('newPassword') newPassword: string,
  // ) {
  //   if (!token || !newPassword) {
  //     throw new BadRequestException('Token and new password are required');
  //   }

  //   return this.parentsService.resetPassword(token, newPassword);
  // }

  @Get('student/all')
  async getAllStudents() {
    return await this.parentsService.getAllStudentDetails();
  }

  @Put(':parentId/picture')
  async updateProfilePic(
    @Param('parentId') parentId: string,
    @Body() updateProfilePicDto: UpdateProfilePicDto,
  ): Promise<string> {
    return this.parentsService.uploadProfilePic(parentId, updateProfilePicDto);
  }

  @Post('reset-password-request')
  async initiateResetPassword(
    @Body() dto: InitiateResetPasswordDto,
  ): Promise<{ message: string }> {
    try {
      await this.parentsService.initiateResetPassword(
        dto.email,
        dto.locationId,
      );
      return { message: 'Reset password email sent.' };
    } catch (error) {
      if (error instanceof NotFoundException) throw error;
      throw new InternalServerErrorException(
        'Could not initiate password reset.',
      );
    }
  }

  @Post('reset-password')
  async completeResetPassword(
    @Body() dto: CompleteResetPasswordDto,
  ): Promise<{ message: string }> {
    try {
      return await this.parentsService.completeResetPassword(
        dto.token,
        dto.password,
      );
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      )
        throw error;
      throw new InternalServerErrorException('Could not reset password.');
    }
  }

  @Get('token/user-details')
  @UseGuards(JwtAuthGuard)
  async getUserDetailsFromToken(@Req() request: Request) {
    const authorizationHeader = request.headers['authorization'];

    if (!authorizationHeader) {
      throw new UnauthorizedException('Authorization header is missing');
    }

    // Extract the token (assumes Bearer token format)
    const token = authorizationHeader.split(' ')[1];
    if (!token) {
      throw new UnauthorizedException('Invalid Authorization header format');
    }
    const email = request['email'];
    const locationId = request['locationId'];
    return await this.parentsService.getParentByEmail(email, token, locationId);
  }

  @Put(':parentId/second-parent')
  async updateSecondParent(
    @Param('parentId') parentId: string,
    @Body() secondParentData: Partial<SecondParentDto>,
  ) {
    const updatedParent = await this.parentsService.updateSecondParent(
      parentId,
      secondParentData,
    );
    if (!updatedParent) {
      throw new NotFoundException('Parent not found');
    }
    return updatedParent;
  }

  @Put(':classId/enroll-class/:date')
  @UseGuards(JwtAuthGuard)
  async enrollIntoClass(
    @Param('classId') classId: string,
    @Param('date') date: string,
    @Body() students: Partial<StudentIdsDto>,
    @Req() request: Request,
  ) {
    const studioId = request['locationId'];
    await this.parentsService.enrollStudentInAClass(
      studioId,
      classId,
      students,
      date,
    );
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  remove(@Param('id') id: string, @Req() request: Request) {
    const studioId = request['locationId'];
    return this.parentsService.remove(id, studioId);
  }

  @Delete('batch/all')
  @UseGuards(JwtAuthGuard)
  removeBatch(@Body() ids: string[], @Req() request: Request) {
    const studioId = request['locationId'];
    return this.parentsService.removeBatch(ids, studioId);
  }

  @Post('/bulk-import')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(FileInterceptor('parents'))
  async createBulkParents(
    @UploadedFile() file: Express.Multer.File,
    @Req() request: Request,
  ) {
    try {
      const locationId = request['locationId'];
      const createParentDto: BulkImportParentDto[] = csv.parse(
        file.buffer.toString(),
        {
          columns: (headers) =>
            headers.map(
              (header) => CSV_IMPORT_HEADER_MAPPING[header] || header,
            ),
          trim: true,
          relax_column_count: true,
          skip_empty_lines: true,
        },
      );
      return await this.parentsService.bulkCreateParents(
        createParentDto,
        locationId,
      );
    } catch (error) {
      console.error('Bulk import error:', error);
      throw new NotFoundException('Parents could not be imported');
    }
  }
  @UseGuards(JwtAuthGuard)
  @Get('/analytics/:parentId')
  async getAttendanceAnalytics(
    @Param('parentId') parentId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Req() request: Request,
  ): Promise<{
    attendanceAnalytics: any[];
    total: number;
    currentPage: number;
    totalPages: number;
  }> {
    const studioId = request['locationId'] || null;
    const classIds = await this.parentsService.getClassIdByParentId(parentId);
    const classId = classIds[0];
    return this.parentsService.getAttendanceAnalytics(
      parentId,
      studioId,
      page,
      limit,
    );
  }

  @Get('/detailed-analytics/:parentId')
  @UseGuards(JwtAuthGuard)
  async getDetailedAttendanceAnalytics(
    @Param('parentId') parentId: string,
    @Req() request: Request,
  ): Promise<DetailedAttendanceDto[]> {
    return this.parentsService.getDetailedAttendanceAnalytics(parentId);
  }

  @Get(':parentId/student-attendance')
  @UseGuards(JwtAuthGuard)
  async getStudentAttendance(
    @Param('parentId') parentId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Req() request: Request,
  ): Promise<{
    data: any[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    const studioId = request['locationId'] || null;
    return this.parentsService.getStudentAttendance(parentId, studioId, {
      page: Number(page),
      limit: Number(limit),
    });
  }
  // @UseGuards(JwtAuthGuard)
  @Get('/combined-analytics/:parentId')
  async getCombinedAnalytics(
    @Param('parentId') parentId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Req() request: Request,
  ): Promise<any> {
    try {
      const studioId = request['locationId'] || null;
      const classIds = await this.parentsService.getClassIdByParentId(parentId);
      const classId = classIds[0];
      const [attendanceAnalytics, studentAttendance] = await Promise.all([
        this.parentsService.getAttendanceAnalytics(
          parentId,
          studioId,
          page,
          limit,
        ),
        this.parentsService.getStudentAttendance(parentId, studioId, {
          page: Number(page),
          limit: Number(limit),
        }),
      ]);
      console.log('attendanceAnalytics--', attendanceAnalytics);
      console.log('studentAttendance--', studentAttendance);
      // Merge classes into analytics data
      const mergedAnalytics = attendanceAnalytics.attendanceAnalytics.map(
        (analytics) => {
          const studentData = studentAttendance.data.find(
            (student) => student.student === analytics.student,
          );

          return {
            ...analytics,
            classes: studentData?.classes || [],
          };
        },
      );
      console.log('mergedAnalytics--', mergedAnalytics);
      return {
        attendanceAnalytics: {
          analytics: mergedAnalytics,
        },
        total: studentAttendance.total,
        page: studentAttendance.page,
        // totalPages: studentAttendance.totalPages
      };
    } catch (error) {
      console.error('Error fetching combined analytics:', error);
      throw new InternalServerErrorException(
        'Failed to fetch combined analytics',
      );
    }
  }
  // @Get('/children-analytics/:parentId')
  // async getChildrenAnalytics(
  //   @Param('parentId') parentId: string,
  //   @Query('page') page: number = 1,
  //   @Query('limit') limit: number = 10,
  //   @Req() request: Request,
  // ): Promise<any> {
  //   try {
  //     const studioId = request['locationId'] || null;

  //     // Get all required data in parallel
  //     const [students, attendanceAnalytics, detailedAnalytics] = await Promise.all([
  //       this.studentsService.getStudentsByParentId(parentId),
  //       this.parentsService.getAttendanceAnalytics(parentId, studioId, page, limit),
  //       this.parentsService.getDetailedAttendanceAnalytics(parentId)
  //     ]);

  //     // Transform the data into the required format
  //     const children = students.map(student => {
  //       const analytics = attendanceAnalytics.attendanceAnalytics
  //         .find(a => a.student === student.name);
  //       const detailed = detailedAnalytics
  //         .filter(d => d.student === student.name);

  //       const classes = detailed.map(d => ({
  //         classId: typeof d.class === 'string' ? d.class : (d.class as any)?._id || 'N/A',
  //         name: typeof d.class === 'string' ? d.class : (d.class as any)?.name || 'N/A',
  //         instructor: typeof d.class === 'string' ? 'N/A' : (d.class as any)?.instructor?.name || 'N/A',
  //         instructorId: typeof d.class === 'string' ? null : (d.class as any)?.instructor?._id || null,
  //         attendance: Math.round((d.attendance_records?.filter(r => r.status === 'present')?.length || 0) /
  //                     (d.attendance_records?.length || 1) * 100),
  //         startDate: typeof d.class === 'string' ? new Date() : (d.class as any)?.startDate || new Date(),
  //         endDate: typeof d.class === 'string' ? new Date() : (d.class as any)?.endDate || new Date(),
  //         classDays: typeof d.class === 'string' ? [] : (d.class as any)?.classDays || [],
  //         sessions: (d.attendance_records || []).map(s => ({
  //           date: new Date(s.date),
  //           status: s.status
  //         })).sort((a, b) => a.date.getTime() - b.date.getTime())
  //       }));

  //       return {
  //         studentId: student._id.toString(),
  //         name: student.name,
  //         attendanceSummary: {
  //           present: Math.round(analytics?.attendance_summary?.present?.percentage || 0),
  //           absent: Math.round(analytics?.attendance_summary?.absent?.percentage || 0),
  //           totalClasses: analytics?.attendance_summary?.total_classes || 0,
  //           presentClasses: analytics?.attendance_summary?.present?.count || 0,
  //           absentClasses: analytics?.attendance_summary?.absent?.count || 0
  //         },
  //         classes
  //       };
  //     });

  //     return {
  //       children,
  //       total: attendanceAnalytics.total || 0,
  //       currentPage: Number(page),
  //       totalPages: attendanceAnalytics.totalPages || 1
  //     };
  //   } catch (error) {
  //     console.error('Error in getChildrenAnalytics:', error);
  //     throw new InternalServerErrorException('Failed to fetch children analytics');
  //   }
  // }
  @UseGuards(JwtAuthGuard)
  @Get(':parentId/class-details')
  async getAllClassDetails(
    @Param('parentId') parentId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    // Fetch student attendance

    const studentAttendance = await this.parentsService.getStudentAttendance(
      parentId,
      null,
      { page: Number(page), limit: Number(limit) },
    );

    // Fetch class details
    const classDetails = await this.parentsService.getAllClassDetails(
      parentId,
      Number(page),
      Number(limit),
    );

    // Extract classes from student attendance
    const classes = studentAttendance?.data?.[0]?.classes || [];

    // Modify data to include empty attendance array if no classes exist
    const modifiedData = classDetails.data.map((student) => ({
      ...student,
      classes: student.classes.map((cls) => {
        const { attendance: { records, ...restAttendance } = {} } = cls;
        return {
          ...cls,
          attendance: restAttendance,
        };
      }),
      attendance: classes.length > 0 ? classes : [],
    }));

    return {
      ...classDetails,
      data: modifiedData,
    };
  }

  @Get(':parentId/wallet-balance')
  @UseGuards(JwtAuthGuard)
  async getWalletBalance(
    @Param('parentId') parentId: string,
    @Req() request: Request,
  ) {
    const studioId = request['locationId'];
    const walletBalance = await this.parentsService.getWalletBalance(
      parentId,
      studioId,
    );
    return {
      walletBalance: walletBalance,
    };
  }

  @Post(':parentId/wallet-balance')
  @UseGuards(JwtAuthGuard)
  @HttpCode(200)
  async loadWalletBalance(
    @Param('parentId') parentId: string,
    @Body() body: LoadWalletBalanceDto,
    @Req() request: Request,
  ) {
    const studioId = request['locationId'];
    return this.parentsService.loadWalletBalance({
      ...body,
      parentId,
      studioId,
    });
  }

  @Get(':parentId/wallet-transactions')
  @UseGuards(JwtAuthGuard)
  async getWalletTransactions(
    @Param('parentId') parentId: string,
    @Req() request: Request,
  ) {
    const studioId = request['locationId'];
    return this.parentsService.fetchWalletTransactions(parentId, studioId);
  }
}
