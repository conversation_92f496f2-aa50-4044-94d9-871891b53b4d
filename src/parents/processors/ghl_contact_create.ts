import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { Logger } from '@nestjs/common';
import { GohighlevelService } from 'src/gohighlevel/gohighlevel.service';

@Processor('parent-create-contact')
export class CreateContactProcessor extends WorkerHost {
  private readonly logger = new Logger(CreateContactProcessor.name);

  constructor(private readonly ghlService: GohighlevelService) {
    super();
  }

  async process(job: Job) {
    this.logger.log(`Processing create-contact job ${job.id}`);
    const { name, email, phone, locationId, isParentPortal } = job.data;

    const ghlContactResponse = await this.ghlService.createContact(
      { name, email: email.toLowerCase(), phone },
      locationId,
      isParentPortal,
    );

    return {
      ghlContactId: ghlContactResponse.contact?.id ?? ghlContactResponse.id,
    };
  }
}
