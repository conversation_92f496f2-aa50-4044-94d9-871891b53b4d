import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { Logger } from '@nestjs/common';
import { GohighlevelService } from 'src/gohighlevel/gohighlevel.service';

@Processor('parent-remove-tags')
export class RemoveTagsProcessor extends WorkerHost {
  private readonly logger = new Logger(RemoveTagsProcessor.name);

  constructor(private readonly ghlService: GohighlevelService) {
    super();
  }

  async process(job: Job) {
    this.logger.log(`Processing remove-tags job ${job.id}`);
    const { studioId, contactId, tags } = job.data;

    await this.ghlService.removeTagsFromContact(studioId, contactId, tags);
    return { success: true };
  }
}
