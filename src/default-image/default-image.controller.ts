import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
} from '@nestjs/common';
import { DefaultImageService } from './default-image.service';
import { CreateDefaultImageDto } from './dto/create-default-image.dto';
import { UpdateDefaultImageDto } from './dto/update-default-image.dto';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { Request } from 'express';

@Controller('default-image')
@UseGuards(JwtAuthGuard)
export class DefaultImageController {
  constructor(private readonly defaultImageService: DefaultImageService) {}

  @Post()
  create(
    @Body() createDefaultImageDto: CreateDefaultImageDto,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return this.defaultImageService.create(createDefaultImageDto, locationId);
  }

  @Get()
  findAll(@Req() request: Request) {
    const locationId = request['locationId'];
    return this.defaultImageService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Req() request: Request) {
    const locationId = request['locationId'];
    return this.defaultImageService.findOne(id, locationId);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateDefaultImageDto: UpdateDefaultImageDto,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return this.defaultImageService.update(
      id,
      updateDefaultImageDto,
      locationId,
    );
  }

  @Delete(':id')
  remove(@Param('id') id: string, @Req() request: Request) {
    const locationId = request['locationId'];
    return this.defaultImageService.remove(id, locationId);
  }
}
