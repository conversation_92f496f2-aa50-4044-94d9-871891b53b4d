import { Test, TestingModule } from '@nestjs/testing';
import { DefaultImageService } from './default-image.service';

describe('DefaultImageService', () => {
  let service: DefaultImageService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [DefaultImageService],
    }).compile();

    service = module.get<DefaultImageService>(DefaultImageService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
