import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateDefaultImageDto } from './dto/create-default-image.dto';
import { UpdateDefaultImageDto } from './dto/update-default-image.dto';
import {
  DefaultImage,
  DefaultImageDocument,
} from '../database/schema/defaultImage';

@Injectable()
export class DefaultImageService {
  constructor(
    @InjectModel(DefaultImage.name)
    private defaultImageModel: Model<DefaultImageDocument>,
  ) {}

  async create(
    createDefaultImageDto: CreateDefaultImageDto,
    locationId: string,
  ) {
    const defaultImage = new this.defaultImageModel({
      ...createDefaultImageDto,
      locationId,
    });
    return await defaultImage.save();
  }

  async findAll() {
    return await this.defaultImageModel.find().sort({ _id: -1 }).exec();
  }

  async findOne(id: string, locationId: string) {
    const defaultImage = await this.defaultImageModel
      .findOne({ _id: id, locationId })
      .exec();

    if (!defaultImage) {
      throw new NotFoundException(`Default image with ID ${id} not found`);
    }

    return defaultImage;
  }

  async update(
    id: string,
    updateDefaultImageDto: UpdateDefaultImageDto,
    locationId: string,
  ) {
    const updatedImage = await this.defaultImageModel
      .findOneAndUpdate(
        { _id: id, locationId },
        { $set: updateDefaultImageDto },
        { new: true },
      )
      .exec();

    if (!updatedImage) {
      throw new NotFoundException(`Default image with ID ${id} not found`);
    }

    return updatedImage;
  }

  async remove(id: string, locationId: string) {
    const deletedImage = await this.defaultImageModel
      .findOneAndDelete({ _id: id, locationId })
      .exec();

    if (!deletedImage) {
      throw new NotFoundException(`Default image with ID ${id} not found`);
    }

    return deletedImage;
  }
}
