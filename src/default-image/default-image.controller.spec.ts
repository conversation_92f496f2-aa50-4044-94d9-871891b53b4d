import { Test, TestingModule } from '@nestjs/testing';
import { DefaultImageController } from './default-image.controller';
import { DefaultImageService } from './default-image.service';

describe('DefaultImageController', () => {
  let controller: DefaultImageController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DefaultImageController],
      providers: [DefaultImageService],
    }).compile();

    controller = module.get<DefaultImageController>(DefaultImageController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
