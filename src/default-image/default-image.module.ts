import { Module } from '@nestjs/common';
import { DefaultImageService } from './default-image.service';
import { DefaultImageController } from './default-image.controller';
import { MongooseModule } from '@nestjs/mongoose';
import {
  DefaultImage,
  DefaultImageSchema,
} from '../database/schema/defaultImage';
import { AuthModule } from 'src/auth/auth.module';
@Module({
  imports: [
    MongooseModule.forFeature([
      { name: DefaultImage.name, schema: DefaultImageSchema },
    ]),
    AuthModule,
  ],
  controllers: [DefaultImageController],
  providers: [DefaultImageService],
})
export class DefaultImageModule {}
