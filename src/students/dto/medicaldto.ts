import { IsOptional, IsString } from 'class-validator';

export class MedicalDto {
  @IsOptional()
  @IsString()
  primaryDoctor?: string;

  @IsOptional()
  @IsString()
  medications?: string;

  @IsOptional()
  @IsString()
  allergies?: string;

  @IsOptional()
  @IsString()
  disabilities?: string;

  @IsOptional()
  @IsString()
  specialNeeds?: string;

  @IsOptional()
  @IsString()
  healthInsuranceCarrier?: string;
}
