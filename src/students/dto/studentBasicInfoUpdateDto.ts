import {
  IsOptional,
  IsString,
  IsDateString,
  ValidateNested,
  IsNotEmpty,
  Matches,
} from 'class-validator';
import { Type } from 'class-transformer';
import { MedicalDto } from './medicaldto';

export class UpdateStudentBasicInfoDto {
  @IsString()
  firstName: string;

  @IsString()
  lastName: string;

  @IsOptional()
  @IsDateString()
  dob?: Date;

  @IsOptional()
  @IsString()
  familyName?: string;

  @IsOptional()
  @IsString()
  gender?: string;

  @IsOptional()
  @IsString()
  tShirtSize?: string;

  @IsOptional()
  @IsString()
  mobileNo?: string;

  @IsOptional()
  @IsString()
  studentEmail?: string;

  @IsOptional()
  @IsString()
  transportation?: string;

  // Medical as a nested object
  @IsOptional()
  @ValidateNested()
  @Type(() => MedicalDto)
  medical?: MedicalDto;
}

export class MarkAttendanceDto {
  @IsString()
  @IsNotEmpty()
  attendanceId: string;

  @IsString()
  @IsNotEmpty()
  classId: string;

  @IsString()
  @IsNotEmpty()
  @Matches(/^\d{4}-\d{2}(-\d{2})?(?:T\d{2}:\d{2}:\d{2}(?:\.\d{3})?Z?)?$/, {
    message: 'Date must be in YYYY-MM-DD or ISO format',
  })
  date: string;

  @IsString()
  @IsNotEmpty()
  studentId: string;
}
