import {
  IsString,
  IsOptional,
  IsDate,
  IsMongoId,
  IsArray,
  IsNumber,
} from 'class-validator';
import { Types } from 'mongoose';

export class MedicalInfoDto {
  @IsOptional()
  @IsString()
  primaryDoctor?: string;

  @IsOptional()
  @IsString()
  medications?: string;

  @IsOptional()
  @IsString()
  allergies?: string;

  @IsOptional()
  @IsString()
  disabilities?: string;

  @IsOptional()
  @IsString()
  specialNeeds?: string;

  @IsOptional()
  @IsString()
  healthInsuranceCarrier?: string;
}

export class EnrollmentDto {
  @IsMongoId()
  enrollmentId: Types.ObjectId;

  @IsOptional()
  @IsString()
  subscriptionId?: string;

  @IsOptional()
  @IsDate()
  enrolledDate?: Date;

  @IsOptional()
  @IsNumber()
  absences?: number;

  @IsOptional()
  @IsNumber()
  skills?: number;
}

export class CreateStudentDto {
  @IsOptional()
  @IsString()
  _id?: string;

  @IsString()
  firstName: string;

  @IsString()
  lastName: string;

  @IsOptional()
  @IsString()
  name?: string;

  @IsOptional()
  @IsDate()
  dob?: Date;

  @IsOptional()
  @IsString()
  gender?: string;

  @IsOptional()
  @IsString()
  tShirtSize?: string;

  @IsOptional()
  @IsString()
  mobileNo?: string;

  @IsOptional()
  @IsString()
  studentEmail?: string;

  @IsOptional()
  @IsString()
  transportation?: string;

  @IsOptional()
  medical?: MedicalInfoDto;

  @IsOptional()
  @IsArray()
  enrollments?: EnrollmentDto[];

  @IsOptional()
  @IsMongoId()
  parentId?: any;

  @IsOptional()
  @IsMongoId()
  studioId?: any;

  @IsOptional()
  @IsString()
  familyName?: string;
}
