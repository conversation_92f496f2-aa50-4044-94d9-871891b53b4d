import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  Logger,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { CreateStudentDto } from './dto/create-student.dto';
import { UpdateStudentDto } from './dto/update-student.dto';
import { InjectModel } from '@nestjs/mongoose';
import { Student } from 'src/database/schema/student';
import { Model, Types, PipelineStage, ObjectId } from 'mongoose';
import { StudentDocument } from 'src/database/schema/student';
import { ParentsService } from 'src/parents/parents.service';
import { calculateAge } from 'src/utils/helperFunction';
import { StudiosService } from 'src/studios/studios.service';
import { StudentDetailsDto } from 'src/parents/dto/studentDetails.dto';
import { UpdateProfilePicDto } from 'src/parents/dto/updateProfilePic.dto';
import { GcpStorageService } from 'src/gcp-storage/gcp-storage.service';
import { Enrollment } from 'src/database/schema/enrollment';
import { StudentIdsDto } from 'src/parents/dto/incomingStudentIdDto';

import { EnrollmentHistoryService } from 'src/enrollment-history/enrollment-history.service';
import { Invoice } from 'src/database/schema/invoice';
import { Parent, ParentDocument } from 'src/database/schema/parent';
import { MarkAttendanceDto } from './dto/studentBasicInfoUpdateDto';
import { Attendance } from 'src/database/schema/attendance';
import { format, eachDayOfInterval, parseISO } from 'date-fns';
import { Studio } from 'src/database/schema/studio';
import PDFDocument from 'pdfkit';
import { CustomForm } from 'src/database/schema/customForm';
import { TriggersService } from 'src/triggers/triggers.service';

import {
  Subscription,
  SubscriptionDocument,
  SubscriptionStatus,
} from 'src/database/schema/subscription';
import { InvoiceStatus, PaymentTransactionStatus } from 'src/stripe/type';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import { SubscriptionInvoiceDocument } from 'src/database/schema/subscriptionInvoice';
import {
  PaymentTransaction,
  PaymentTransactionDocument,
} from 'src/database/schema/paymentTransaction';
import { DiscountService } from 'src/discount/discount.service';
import { DiscountCouponService } from 'src/discount-coupon/discount-coupon.service';
@Injectable()
export class StudentsService {
  private readonly logger = new Logger(StudentsService.name);
  constructor(
    @InjectModel(Student.name)
    private readonly studentModel: Model<StudentDocument>,
    @InjectModel(Subscription.name)
    private readonly subscriptionModel: Model<SubscriptionDocument>,
    @InjectModel(SubscriptionInvoice.name)
    private readonly subscriptionInvoiceModel: Model<SubscriptionInvoiceDocument>,
    @InjectModel(PaymentTransaction.name)
    private readonly paymentTransactionModel: Model<PaymentTransactionDocument>,
    // private readonly parentService: ParentsService
    @Inject(forwardRef(() => StudiosService))
    private readonly studioService: StudiosService,
    private readonly gcpStorageService: GcpStorageService,
    @InjectModel(Enrollment.name)
    private readonly enrollmentModel: Model<Enrollment>,
    private readonly enrollmentHistoryService: EnrollmentHistoryService,
    @InjectModel(Invoice.name)
    private readonly invoiceModel: Model<Invoice>,
    @InjectModel(Parent.name)
    private readonly parentModel: Model<ParentDocument>,
    @InjectModel(Attendance.name) private attendanceModel: Model<Attendance>,
    @InjectModel(Studio.name) private readonly studioModel: Model<Studio>,
    @InjectModel(CustomForm.name) private readonly customFormModel: Model<any>,
    private readonly triggersService: TriggersService,
    private readonly discountService: DiscountService,
    private readonly discountCouponService: DiscountCouponService,
  ) {}
  async create(createStudentDto: CreateStudentDto) {
    const studioId = Types.ObjectId.createFromHexString(
      createStudentDto.studioId,
    );
    const parentId = Types.ObjectId.createFromHexString(
      createStudentDto.parentId,
    );
    let processedEnrollments = [];
    if (
      createStudentDto.enrollments &&
      createStudentDto.enrollments.length > 0
    ) {
      processedEnrollments = createStudentDto.enrollments.map((enrollment) => {
        if (!Types.ObjectId.isValid(enrollment.enrollmentId)) {
          throw new BadRequestException(
            `Invalid enrollmentId: ${enrollment.enrollmentId}`,
          );
        }

        return {
          ...enrollment,
          enrollmentId: new Types.ObjectId(enrollment.enrollmentId), // Convert to ObjectId
        };
      });
    }
    const newStudent = new this.studentModel({
      ...createStudentDto,
      name: `${createStudentDto.firstName.trim()} ${createStudentDto.lastName}`,
      studioId, // Assign the converted studioId
      parentId, // Assign the converted parentId
      enrollments: processedEnrollments,
    });
    await newStudent.save();
    return newStudent;
  }

  async createWithLocationId(
    createStudentDto: CreateStudentDto,
    locationId: string,
  ) {
    const studioId = Types.ObjectId.createFromHexString(locationId);
    const parentId = Types.ObjectId.createFromHexString(
      createStudentDto.parentId,
    );
    let processedEnrollments = [];
    if (
      createStudentDto.enrollments &&
      createStudentDto.enrollments.length > 0
    ) {
      processedEnrollments = createStudentDto.enrollments.map((enrollment) => {
        if (!Types.ObjectId.isValid(enrollment.enrollmentId)) {
          throw new BadRequestException(
            `Invalid enrollmentId: ${enrollment.enrollmentId}`,
          );
        }

        return {
          ...enrollment,
          enrollmentId: new Types.ObjectId(enrollment.enrollmentId), // Convert to ObjectId
        };
      });
    }
    const newStudent = new this.studentModel({
      ...createStudentDto,
      name: `${createStudentDto.firstName.trim()} ${createStudentDto.lastName}`,
      studioId, // Assign the converted studioId
      parentId, // Assign the converted parentId
      enrollments: processedEnrollments,
    });
    await newStudent.save();
    return newStudent;
  }

  async createFromParent(createStudentDto: CreateStudentDto) {
    // const studioId = Types.ObjectId.createFromHexString(createStudentDto.studioId);
    // const parentId = Types.ObjectId.createFromHexString(createStudentDto.parentId);
    const newStudent = new this.studentModel({
      name: `${createStudentDto.firstName.trim()} ${createStudentDto.lastName}`,
      ...createStudentDto,
      // studioId,  // Assign the converted studioId
      // parentId,  // Assign the converted parentId
    });
    await newStudent.save();
    // await this.parentService.addStudentToParent(createStudentDto.parentId, newStudent.id);
    return newStudent;
  }

  findAll() {
    return `This action returns all students`;
  }

  async findAllByEnrollmentId(enrollmentId: string) {
    const enrollmentIdObject = Types.ObjectId.createFromHexString(enrollmentId);
    return this.studentModel
      .find({
        'enrollments.enrollmentId': enrollmentIdObject,
      })
      .exec();
  }

  async findAllByEventId(eventId: string) {
    const eventIdObject = Types.ObjectId.createFromHexString(eventId);
    return this.studentModel
      .find({
        'events.eventId': eventIdObject,
      })
      .exec();
  }

  async findAllByEnrollmentIdActive(enrollmentId: string) {
    const enrollmentIdObject = Types.ObjectId.createFromHexString(enrollmentId);
    const students = await this.studentModel
      .find({
        enrollments: {
          $elemMatch: {
            enrollmentId: enrollmentIdObject,
            subscriptionStatus: 'active',
          },
        },
      })
      .populate('parentId', 'familyName')
      .transform((docs) =>
        docs.map((doc) => ({
          ...doc.toObject(),
          familyName: (doc.parentId as any).familyName,
        })),
      )
      .exec();
    return students as unknown as Student[];
  }

  async getActiveStudentsByEventId(eventId: string) {
    const eventIdObject = Types.ObjectId.createFromHexString(eventId);
    const students = await this.studentModel
      .find({
        'events.eventId': eventIdObject,
        'events.subscriptionStatus': 'active',
      })
      .populate('parentId', 'familyName')
      .transform((docs) =>
        docs.map((doc) => ({
          ...doc.toObject(),
          familyName: (doc.parentId as any).familyName,
        })),
      )
      .exec();
    return students as unknown as Student[];
  }

  async findOne(id) {
    try {
      const student = await this.studentModel
        .findById(id)
        .populate({
          path: 'enrollments.enrollmentId',
          model: 'Enrollment',
          populate: [
            {
              path: 'instructor',
              model: 'CustomForm',
              select: 'fieldName',
            },
            {
              path: 'tags',
              model: 'CustomForm',
              select: 'fieldName',
            },
            {
              path: 'group',
              model: 'CustomForm',
              select: 'fieldName',
            },
            {
              path: 'policyGroup',
              model: 'Policy',
              select: 'name description',
            },
          ],
        })
        .exec();
      if (!student) {
        throw new NotFoundException(`Student with ID ${id} not found`);
      }
      return student;
    } catch (error) {
      // Handle possible errors, such as invalid ObjectId format
      if (error.kind === 'ObjectId') {
        throw new BadRequestException(`Invalid student ID format: ${id}`);
      }
      throw new BadRequestException(
        'An unexpected error occurred while fetching the student.',
      );
    }
  }

  async findStudentDetails(id) {
    try {
      const student = await this.studentModel
        .findById(id)
        .populate({
          path: 'enrollments.enrollmentId',
          model: 'Enrollment',
          populate: [
            {
              path: 'instructor',
              model: 'CustomForm',
              select: 'fieldName',
            },
            {
              path: 'tags',
              model: 'CustomForm',
              select: 'fieldName',
            },
            {
              path: 'group',
              model: 'CustomForm',
              select: 'fieldName',
            },
            {
              path: 'policyGroup',
              model: 'Policy',
              select: 'name description',
            },
          ],
        })
        .populate({
          path: 'events.eventId',
          model: 'Event',
          populate: [
            {
              path: 'instructor',
              model: 'CustomForm',
              select: 'fieldName',
            },
            {
              path: 'tags',
              model: 'CustomForm',
              select: 'fieldName',
            },
            {
              path: 'group',
              model: 'CustomForm',
              select: 'fieldName',
            },
            {
              path: 'policyGroup',
              model: 'Policy',
              select: 'name description',
            },
          ],
        })
        .populate({
          path: 'parentId',
          model: 'Parent',
          select: 'firstName lastName email phone address stripeCustomerId', // Add the parent fields you need
        })
        .exec();

      if (!student) {
        throw new NotFoundException(`Student with ID ${id} not found`);
      }
      return student;
    } catch (error) {
      // Handle possible errors, such as invalid ObjectId format
      if (error.kind === 'ObjectId') {
        throw new BadRequestException(`Invalid student ID format: ${id}`);
      }
      throw new BadRequestException(
        'An unexpected error occurred while fetching the student.',
      );
    }
  }

  async findById(id: string) {
    return this.studentModel.findOne({
      _id: Types.ObjectId.createFromHexString(id),
    });
  }

  async update(id: string, updateStudentDto: UpdateStudentDto) {
    try {
      const existingStudent = await this.studentModel.findById(id).lean();
      if (!existingStudent) {
        throw new HttpException('Student not found', HttpStatus.NOT_FOUND);
      }

      // Only process provided fields
      const updates: Partial<typeof updateStudentDto> = {};

      // Handle name-related fields
      if (updateStudentDto.firstName || updateStudentDto.lastName) {
        const firstName =
          updateStudentDto.firstName?.trim() || existingStudent.firstName;
        const lastName =
          updateStudentDto.lastName?.trim() || existingStudent.lastName;
        updates.name = `${firstName} ${lastName}`.trim();
        if (updateStudentDto.firstName) updates.firstName = firstName;
        if (updateStudentDto.lastName) updates.lastName = lastName;
      }

      // Handle basic string fields
      const stringFields = [
        'gender',
        'tShirtSize',
        'mobileNo',
        'studentEmail',
        'transportation',
        'familyName',
      ];
      stringFields.forEach((field) => {
        if (updateStudentDto[field] !== undefined) {
          updates[field] = updateStudentDto[field].trim();
        }
      });

      // Handle date fields
      if (updateStudentDto.dob) {
        updates.dob = new Date(updateStudentDto.dob);
      }

      // Handle medical info
      if (updateStudentDto.medical) {
        const existingMedical = existingStudent.medical;
        console.log('existingMedical', existingMedical);
        updates.medical = {
          ...existingMedical,
          ...updateStudentDto.medical,
        };
      }

      // Handle MongoDB ObjectId fields
      if (updateStudentDto.studioId) {
        if (!Types.ObjectId.isValid(updateStudentDto.studioId)) {
          throw new BadRequestException(
            `Invalid studioId: ${updateStudentDto.studioId}`,
          );
        }
        updates.studioId = Types.ObjectId.createFromHexString(
          updateStudentDto.studioId,
        );
      }

      if (updateStudentDto.parentId) {
        if (!Types.ObjectId.isValid(updateStudentDto.parentId)) {
          throw new BadRequestException(
            `Invalid parentId: ${updateStudentDto.parentId}`,
          );
        }
        updates.parentId = Types.ObjectId.createFromHexString(
          updateStudentDto.parentId,
        );
      }

      // Handle enrollments array
      if (Array.isArray(updateStudentDto.enrollments)) {
        updates.enrollments = updateStudentDto.enrollments.map((enrollment) => {
          if (!Types.ObjectId.isValid(enrollment.enrollmentId)) {
            throw new BadRequestException(
              `Invalid enrollmentId: ${enrollment.enrollmentId}`,
            );
          }

          const processedEnrollment = {
            ...enrollment,
            enrollmentId: new Types.ObjectId(enrollment.enrollmentId),
          };

          // Handle date fields in enrollment
          if (enrollment.enrolledDate) {
            processedEnrollment.enrolledDate = new Date(
              enrollment.enrolledDate,
            );
          }

          return processedEnrollment;
        });
      }

      const updatedStudent = await this.studentModel.findByIdAndUpdate(
        id,
        { $set: updates },
        { new: true, runValidators: true },
      );

      return updatedStudent;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      if (error.code === 11000) {
        throw new HttpException(
          'Duplicate key error: A student with these details already exists.',
          HttpStatus.CONFLICT,
        );
      }

      console.error('Error during student update:', error);
      throw new HttpException(
        'An error occurred during student update',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getStudentsByParentId(parentId_string: string): Promise<Student[]> {
    const parentId = Types.ObjectId.createFromHexString(parentId_string);
    return this.studentModel
      .find({ parentId })
      .populate({
        path: 'enrollments.enrollmentId',
        model: 'Enrollment',
        populate: [
          {
            path: 'instructor',
            model: 'CustomForm',
          },
          {
            path: 'location',
            model: 'CustomForm',
          },
          {
            path: 'policyGroup',
            model: 'Policy',
          },
        ],
      })
      .populate({
        path: 'events.eventId',
        model: 'Event',
        populate: [
          {
            path: 'instructor',
            model: 'CustomForm',
          },
          {
            path: 'location',
            model: 'CustomForm',
          },
          {
            path: 'policyGroup',
            model: 'Policy',
          },
        ],
      })
      .exec();
  }

  async getStudentsBystudioId(locationId: string): Promise<Student[]> {
    try {
      const studioId = await this.studioService.findByLocationId(locationId);
      return this.studentModel
        .find({ studioId: studioId._id })
        .populate({
          path: 'enrollments.enrollmentId',
          model: 'Enrollment',
        })
        .exec();
    } catch (error) {
      console.error('Error while getting all students:', error);
    }
  }

  async getAllStudentDetails(): Promise<StudentDetailsDto[]> {
    const students = await this.studentModel
      .find()
      .lean()
      .select('name dob gender enrollments parentId');
    return students;
  }

  async updateBasicInfo(
    studentId: string,
    updateData: Partial<Student>,
  ): Promise<Student> {
    const newData = {
      ...updateData,
      name: `${updateData.firstName.trim()} ${updateData.lastName}`,
    };
    const updatedStudent = await this.studentModel.findByIdAndUpdate(
      studentId,
      { $set: newData },
      { new: true },
    );

    if (!updatedStudent) {
      throw new NotFoundException('Student not found');
    }

    return updatedStudent;
  }

  async findByIdAndUpdate(studentId: string, update: any) {
    return this.studentModel.findByIdAndUpdate(studentId, update, {
      new: true,
    });
  }

  async getStudentsByFilters(
    locationId,
    enrollmentId,
    date,
  ): Promise<Student[]> {
    try {
      const correctedDateString = date.replace(' ', '+');
      // Convert the date string to a Date object
      const dateObj = new Date(correctedDateString);
      const studioId = Types.ObjectId.createFromHexString(locationId);
      const enrollmentObjectId =
        Types.ObjectId.createFromHexString(enrollmentId);
      const parsedDate = new Date(dateObj); // `date` is already in UTC
      const startOfDay = new Date(
        Date.UTC(
          parsedDate.getUTCFullYear(),
          parsedDate.getUTCMonth(),
          parsedDate.getUTCDate(),
        ),
      );
      const endOfDay = new Date(
        Date.UTC(
          parsedDate.getUTCFullYear(),
          parsedDate.getUTCMonth(),
          parsedDate.getUTCDate(),
          23,
          59,
          59,
          999,
        ),
      );
      console.log('Start of Day (UTC):', startOfDay.toISOString());
      console.log('End of Day (UTC):', endOfDay.toISOString());
      // Ensure date is in the right format (if necessary) and query accordingly
      return this.studentModel.aggregate([
        {
          $match: {
            studioId: studioId,
            enrollments: {
              $elemMatch: {
                enrollmentId: enrollmentObjectId,
              },
            },
          },
        },
        {
          $addFields: {
            filteredAttendance: {
              $filter: {
                input: '$attendance',
                as: 'item',
                cond: {
                  $and: [
                    { $eq: ['$$item.classId', enrollmentObjectId] },
                    { $gte: ['$$item.date', startOfDay] },
                    { $lt: ['$$item.date', endOfDay] },
                  ],
                },
              },
            },
          },
        },
        {
          $lookup: {
            from: 'attendances', // Correct collection name
            localField: 'filteredAttendance.attendanceId',
            foreignField: '_id',
            as: 'populatedAttendance',
          },
        },
        {
          $addFields: {
            filteredAttendance: {
              $map: {
                input: '$filteredAttendance',
                as: 'attendance',
                in: {
                  $mergeObjects: [
                    '$$attendance',
                    {
                      attendanceId: {
                        $arrayElemAt: [
                          {
                            $filter: {
                              input: '$populatedAttendance',
                              as: 'populated',
                              cond: {
                                $eq: [
                                  '$$populated._id',
                                  '$$attendance.attendanceId',
                                ],
                              },
                            },
                          },
                          0,
                        ],
                      },
                    },
                  ],
                },
              },
            },
          },
        },
        {
          $project: {
            _id: 1,
            name: 1,
            dob: 1,
            familyName: 1,
            gender: 1,
            tShirtSize: 1,
            mobileNo: 1,
            studentEmail: 1,
            transportation: 1,
            medical: 1,
            enrollments: 1,
            parentId: 1,
            studioId: 1,
            profilePicUrl: 1,
            createdAt: 1,
            updatedAt: 1,
            attendance: '$filteredAttendance', // Include modified attendance
          },
        },
      ]);
    } catch (error) {
      console.error('Error getting students by filter:', error);
    }
  }

  async getStudentsActiveClassByFilters(
    locationId,
    enrollmentId,
    date,
  ): Promise<Student[]> {
    try {
      const correctedDateString = date.replace(' ', '+');
      const dateObj = new Date(correctedDateString);
      const studioId = Types.ObjectId.createFromHexString(locationId);
      const enrollmentObjectId =
        Types.ObjectId.createFromHexString(enrollmentId);
      const parsedDate = new Date(dateObj);
      const startOfDay = new Date(
        Date.UTC(
          parsedDate.getUTCFullYear(),
          parsedDate.getUTCMonth(),
          parsedDate.getUTCDate(),
        ),
      );
      const endOfDay = new Date(
        Date.UTC(
          parsedDate.getUTCFullYear(),
          parsedDate.getUTCMonth(),
          parsedDate.getUTCDate(),
          23,
          59,
          59,
          999,
        ),
      );

      return this.studentModel.aggregate([
        {
          $match: {
            studioId: studioId,
            enrollments: {
              $elemMatch: {
                enrollmentId: enrollmentObjectId,
                subscriptionStatus: { $in: ['active', 'free'] }, // Add this condition to only match active enrollments
              },
            },
          },
        },
        {
          $addFields: {
            filteredAttendance: {
              $filter: {
                input: '$attendance',
                as: 'item',
                cond: {
                  $and: [
                    { $eq: ['$$item.classId', enrollmentObjectId] },
                    { $gte: ['$$item.date', startOfDay] },
                    { $lt: ['$$item.date', endOfDay] },
                  ],
                },
              },
            },
          },
        },
        {
          $lookup: {
            from: 'attendances',
            localField: 'filteredAttendance.attendanceId',
            foreignField: '_id',
            as: 'populatedAttendance',
          },
        },
        {
          $addFields: {
            filteredAttendance: {
              $map: {
                input: '$filteredAttendance',
                as: 'attendance',
                in: {
                  $mergeObjects: [
                    '$$attendance',
                    {
                      attendanceId: {
                        $arrayElemAt: [
                          {
                            $filter: {
                              input: '$populatedAttendance',
                              as: 'populated',
                              cond: {
                                $eq: [
                                  '$$populated._id',
                                  '$$attendance.attendanceId',
                                ],
                              },
                            },
                          },
                          0,
                        ],
                      },
                    },
                  ],
                },
              },
            },
          },
        },
        {
          $project: {
            _id: 1,
            name: 1,
            dob: 1,
            familyName: 1,
            gender: 1,
            tShirtSize: 1,
            mobileNo: 1,
            studentEmail: 1,
            transportation: 1,
            medical: 1,
            enrollments: 1,
            parentId: 1,
            studioId: 1,
            profilePicUrl: 1,
            createdAt: 1,
            updatedAt: 1,
            attendance: '$filteredAttendance', // Include modified attendance
          },
        },
      ]);
    } catch (error) {
      console.error('Error getting students by filter:', error);
    }
  }

  async uploadProfilePic(
    studentId_string: string,
    // updateProfileDto: UpdateProfilePicDto,
    url,
  ): Promise<string> {
    try {
      // const publicUrl = await this.gcpStorageService.uploadImage(
      //   updateProfileDto.base64,
      //   studentId,
      //   updateProfileDto.fileName,
      // );
      // const publicUrl = await this.gcpStorageService.generateSignedUrl(studentId, updateProfileDto.fileName);
      // const studentId = Types.ObjectId.createFromHexString(studentId_string);
      const updatedParent = await this.studentModel.findByIdAndUpdate(
        studentId_string,
        { profilePicUrl: url },
        { new: true },
      );

      return;
    } catch (error) {
      console.error('Error uploading profile picture:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        'Failed to upload profile picture. Please try again later.',
      );
    }
  }

  async getStudentsByEnrollmentId(
    studio: string,
    classId: string,
  ): Promise<Student[]> {
    try {
      const classId_ObjectId = Types.ObjectId.createFromHexString(classId);
      const students = await this.studentModel
        .find({
          'enrollments.enrollmentId': classId_ObjectId,
        })
        .exec();
      return students;
    } catch (error) {
      throw new Error(`Failed to fetch students: ${error.message}`);
    }
  }

  async getStudentsByEnrollmentIdActive(
    studio: string,
    classId: string,
  ): Promise<Student[]> {
    try {
      const classId_ObjectId = Types.ObjectId.createFromHexString(classId);
      const students = await this.studentModel
        .find({
          enrollments: {
            $elemMatch: {
              enrollmentId: classId_ObjectId,
              subscriptionStatus: 'active', // Only match active enrollments
            },
          },
        })
        .populate('parentId', 'familyName')
        .transform((docs) =>
          docs.map((doc) => ({
            ...doc.toObject(),
            familyName: (doc.parentId as any).familyName,
          })),
        )
        .exec();
      return students as unknown as Student[];
    } catch (error) {
      throw new Error(`Failed to fetch students: ${error.message}`);
    }
  }

  async getStudentsByEventId(
    studio: string,
    eventId: string,
  ): Promise<Student[]> {
    try {
      const eventId_ObjectId = Types.ObjectId.createFromHexString(eventId);
      const students = await this.studentModel
        .find({
          'events.eventId': eventId_ObjectId,
        })
        .exec();
      return students;
    } catch (error) {
      throw new Error(`Failed to fetch students: ${error.message}`);
    }
  }

  async createStudentAttendance(
    studentId: Types.ObjectId,
    attendanceId: Types.ObjectId,
    date: Date,
    classObjectId: Types.ObjectId,
    status: string,
  ) {
    try {
      const isEnrolled = await this.studentModel
        .findOne({
          _id: studentId,
          enrollments: {
            $elemMatch: { enrollmentId: classObjectId },
          },
        })
        .lean()
        .exec();

      if (!isEnrolled) {
        throw new Error(
          `Student ${studentId} is not enrolled in class ${classObjectId}`,
        );
      }

      // Find if attendance for this class/date already exists
      const existingAttendanceDoc = await this.studentModel
        .findOne({
          _id: studentId,
          attendance: {
            $elemMatch: { classId: classObjectId, date },
          },
        })
        .lean()
        .exec();

      const studioId = isEnrolled.studioId;
      let shouldIncrementAbsence = false;

      if (existingAttendanceDoc) {
        // Find the specific attendance record
        const attendanceRecord = existingAttendanceDoc.attendance.find(
          (a) =>
            a.classId.toString() === classObjectId.toString() &&
            new Date(a.date).getTime() === new Date(date).getTime(),
        );
        const wasAbsent = attendanceRecord?.status === 'absent';

        // Update the attendance record
        await this.studentModel.updateOne(
          {
            _id: studentId,
            'attendance.classId': classObjectId,
            'attendance.date': date,
          },
          {
            $set: {
              'attendance.$.attendanceId': attendanceId,
              'attendance.$.status': status,
            },
          },
        );

        // Only increment absences if changing from not absent to absent
        if (!wasAbsent && status === 'absent') {
          shouldIncrementAbsence = true;
        }
      } else {
        // New attendance record
        await this.studentModel.updateOne(
          {
            _id: studentId,
            'enrollments.enrollmentId': classObjectId,
          },
          [
            {
              $set: {
                attendance: {
                  $concatArrays: [
                    '$attendance',
                    [
                      {
                        attendanceId: attendanceId,
                        classId: classObjectId,
                        date: date,
                        status: status,
                      },
                    ],
                  ],
                },
                enrollments: {
                  $map: {
                    input: '$enrollments',
                    as: 'enrollment',
                    in: {
                      $mergeObjects: [
                        '$$enrollment',
                        {
                          absences:
                            status === 'absent'
                              ? {
                                  $cond: [
                                    {
                                      $eq: [
                                        '$$enrollment.enrollmentId',
                                        classObjectId,
                                      ],
                                    },
                                    {
                                      $cond: [
                                        {
                                          $eq: [
                                            {
                                              $add: [
                                                {
                                                  $ifNull: [
                                                    '$$enrollment.absences',
                                                    0,
                                                  ],
                                                },
                                                1,
                                              ],
                                            },
                                            4,
                                          ],
                                        },
                                        1,
                                        {
                                          $add: [
                                            {
                                              $ifNull: [
                                                '$$enrollment.absences',
                                                0,
                                              ],
                                            },
                                            1,
                                          ],
                                        },
                                      ],
                                    },
                                    '$$enrollment.absences',
                                  ],
                                }
                              : '$$enrollment.absences',
                        },
                      ],
                    },
                  },
                },
              },
            },
          ],
        );
        // Only increment if status is absent
        if (status === 'absent') {
          shouldIncrementAbsence = false; // Already incremented in pipeline
        }
      }

      // If we need to increment absences due to status change on update
      if (shouldIncrementAbsence) {
        // Fetch the current absences count
        const student = await this.studentModel.findById(studentId).lean();
        const enrollment = student.enrollments.find(
          (e) => e.enrollmentId.toString() === classObjectId.toString(),
        );
        let absences = enrollment ? enrollment.absences : 0;
        absences = absences + 1;
        if (absences === 4) absences = 1;

        // Update the absences count
        await this.studentModel.updateOne(
          {
            _id: studentId,
            'enrollments.enrollmentId': classObjectId,
          },
          {
            $set: { 'enrollments.$.absences': absences },
          },
        );
      }

      // Fetch the updated student and get the absences count for the class
      const updatedStudent = await this.studentModel.findById(studentId).lean();
      const enrollment = updatedStudent.enrollments.find(
        (e) => e.enrollmentId.toString() === classObjectId.toString(),
      );
      const absencesCount = enrollment ? enrollment.absences : null;

      const parentId = updatedStudent.parentId;
      const parent = await this.parentModel.findById(parentId).lean();
      const parentName = parent.name;
      const parentEmail = parent.email;
      const studentName = updatedStudent.name;

      //find by enrollmentId
      const classEnrolled = await this.enrollmentModel
        .findById(classObjectId)
        .lean();
      const className = classEnrolled.title;

      const numberOfDays = absencesCount;
      const datesAbsent = updatedStudent.attendance
        .filter((a) => a.status === 'absent')
        .map((a) => a.date);

      //find location from studioId
      const location = await this.studioModel.findById(studioId).lean();
      const locationId = location.locationId;

      if (absencesCount === 3) {
        const triggerDataAbsence = {
          triggerKey: process.env.STUDENT_ABSENT_TRIGGER_KEY,
          data: {
            parentName: parentName,
            parentEmail: parentEmail,
            studentName: studentName,
            className: className,
            numberOfDays: numberOfDays,
            datesAbsent: datesAbsent,
          },
          locationId: locationId,
        };
        try {
          await this.triggersService.sendToGhl(triggerDataAbsence);
        } catch (error) {
          this.logger.error(
            `Error sending trigger to GHL for class drop with classId ${classObjectId} Error: ${error} `,
          );
        }
      }
    } catch (error) {
      this.logger.error(
        `Error updating attendance for student: ${error.message}`,
      );
      throw error; // Re-throw the error for higher-level handling
    }
  }

  async bulkUpdateAttendance(
    bulkPullOps: any[],
    bulkPushOps: any[],
  ): Promise<any> {
    try {
      await this.studentModel.bulkWrite(bulkPullOps);
      await this.studentModel.bulkWrite(bulkPushOps);
    } catch (error) {
      console.error('Bulk update failed:', error);
      throw new Error('Failed to perform bulk update');
    }
  }

  async searchByName(query: string, locationId_string) {
    const locationId = Types.ObjectId.createFromHexString(locationId_string);
    return this.studentModel.find({
      studioId: locationId,
      name: { $regex: new RegExp(query, 'i') },
    });
  }

  async searchByProperties(
    searchParams: {
      name?: string;
      familyName?: string;
    },
    locationId: string,
    pagination?: {
      page?: number;
      limit?: number;
    },
  ) {
    // Set default pagination
    const page = pagination?.page || 1;
    const limit = pagination?.limit || 10;
    const skip = (page - 1) * limit;

    // Convert locationId to ObjectId
    const locationObjectId = Types.ObjectId.createFromHexString(locationId);

    // Prepare base search conditions
    const searchConditions: any = {
      studioId: locationObjectId,
    };

    // Add name filter
    if (searchParams.name) {
      searchConditions.name = {
        $regex: new RegExp(searchParams.name, 'i'),
      };
    }

    let parentIds = [];
    if (searchParams.familyName) {
      const matchingParents = await this.parentModel
        .find({
          studioId: locationObjectId,
          familyName: { $regex: new RegExp(searchParams.familyName, 'i') },
        })
        .select('_id');
      parentIds = matchingParents.map((parent) => parent._id);

      // Add parent IDs to search conditions
      if (parentIds.length > 0) {
        searchConditions.parentId = { $in: parentIds };
      } else {
        // If no matching parents found, return empty result
        return {
          data: {
            students: [],
            familyNames: [],
            costumeSizes: [],
          },
          pagination: {
            page,
            limit,
            totalCount: 0,
            totalPages: 0,
          },
        };
      }
    }

    // Aggregation pipeline
    const pipeline: any[] = [
      // Initial match stage
      { $match: searchConditions },
      { $sort: { createdAt: -1 } },
      // Add lookup to get parent information
      {
        $lookup: {
          from: 'parents',
          localField: 'parentId',
          foreignField: '_id',
          as: 'parent',
        },
      },
      // Unwind the parent array (converts array to object)
      {
        $unwind: {
          path: '$parent',
          preserveNullAndEmptyArrays: true,
        },
      },
      // Facet for pagination and data
      {
        $facet: {
          metadata: [{ $count: 'totalCount' }],
          data: [
            // Apply pagination
            { $skip: skip },
            { $limit: limit },

            // Project to shape the output
            {
              $project: {
                _id: 1,
                name: 1,
                familyName: '$parent.familyName',
                dob: 1,
                gender: 1,
                profilePicUrl: 1,
                parentId: 1,
                tShirtSize: 1,
                transportation: 1,
                active: {
                  $cond: {
                    if: {
                      $and: [
                        {
                          $gt: [
                            { $size: { $ifNull: ['$enrollments', []] } },
                            0,
                          ],
                        },
                        {
                          $gt: [
                            {
                              $size: {
                                $filter: {
                                  input: '$enrollments',
                                  as: 'enrollment',
                                  cond: {
                                    $ne: [
                                      '$$enrollment.subscriptionStatus',
                                      'dropped',
                                    ],
                                  },
                                },
                              },
                            },
                            0,
                          ],
                        },
                      ],
                    },
                    then: true,
                    else: false,
                  },
                },
              },
            },
          ],
        },
      },
    ];

    // Execute the aggregation
    const results = await this.studentModel.aggregate(pipeline);

    // Get all possible values
    const getAllPossibleValues = async () => {
      // Get unique family names
      const familyNames = await this.parentModel.distinct('familyName', {
        studioId: locationObjectId,
        familyName: { $exists: true, $ne: '' },
      });

      // Get unique costume sizes
      const costumeSizes = await this.studentModel.distinct('tShirtSize', {
        studioId: locationObjectId,
        tShirtSize: { $exists: true, $ne: '' },
      });

      return {
        familyNames: familyNames,
        costumeSizes: costumeSizes,
      };
    };

    // Extract total count, data, and get possible values in parallel
    const [possibleValues, totalCount, studentData] = await Promise.all([
      getAllPossibleValues(),
      results[0].metadata[0]?.totalCount || 0,
      results[0].data,
    ]);

    const finalStudentData = await Promise.all(
      studentData.map(async (student) => {
        const outstandingBalances = await this.subscriptionInvoiceModel
          .find({
            studentId: student._id,
            status: {
              $in: [
                InvoiceStatus.PENDING,
                InvoiceStatus.FAILED,
                InvoiceStatus.SCHEDULED,
              ],
            },
          })
          .lean()
          .exec();

        const totalOutstanding = outstandingBalances.reduce((acc, invoice) => {
          return acc + invoice.finalAmount;
        }, 0);

        //max 2 decimal places
        const totalOutstandingFormatted = Number(totalOutstanding.toFixed(2));

        return { ...student, totalOutstanding: totalOutstandingFormatted };
      }),
    );

    return {
      data: {
        students: finalStudentData,
        ...possibleValues,
      },
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    };
  }

  async getStudentsBystudioIdCsv(locationId: string) {
    const locationId_object = Types.ObjectId.createFromHexString(locationId);
    const students = await this.studentModel
      .find({ studioId: locationId_object })
      .populate({
        path: 'enrollments.enrollmentId', // Assuming 'classes' is the field to populate
        model: this.enrollmentModel, // Assuming 'Class' is the model name for classes
        select: 'title', // Selecting fields to include in the population
      })
      .populate({
        path: 'parentId', // Assuming 'parentId' refers to a document in a 'parents' collection
        select: 'name', // Adjust the field name if necessary
      })
      .populate({
        path: 'studioId', // Assuming 'studioId' refers to a document in a 'studios' collection
        select: 'name', // Adjust the field name if necessary
      })
      .exec();

    return students.map((student) => {
      return {
        firstName: student.firstName,
        lastName: student.lastName,
        dob: student.dob,
        gender: student.gender,
        tShirtSize: student.tShirtSize,
        transportation: student.transportation,
        medical: student.medical,
        enrollments: student.enrollments
          ? student.enrollments
              .map(
                (enrollment) =>
                  enrollment.enrollmentId &&
                  typeof enrollment.enrollmentId === 'object' &&
                  'title' in enrollment.enrollmentId
                    ? enrollment.enrollmentId.title
                    : null, // Use null or another placeholder for missing titles
              )
              .filter((title) => title !== null)
          : null, // Filter out any null values
        parent:
          student.parentId &&
          typeof student.parentId === 'object' &&
          'name' in student.parentId
            ? student.parentId.name
            : '', // Default or error handling case if name is not available

        studio:
          student.studioId &&
          typeof student.studioId === 'object' &&
          'name' in student.studioId
            ? student.studioId.name
            : '', // Default or error handling case if name is not available
      };
    });
  }

  async enrollStudentInAClass(
    studioId,
    classId,
    students: Partial<StudentIdsDto>,
    enrollmentDate: Date, // Add the date parameter
  ) {
    // const studioId_object = Types.ObjectId.createFromHexString(studioId);
    // const classId_object = Types.ObjectId.createFromHexString(classId);

    const studentIds = students.studentIds || [];

    // Create an array of bulk operations
    const bulkOps = studentIds.map((studentId) => {
      const studentObjectId = Types.ObjectId.createFromHexString(studentId);

      return {
        updateOne: {
          filter: { _id: studentObjectId },
          update: {
            $push: {
              enrollments: {
                enrollmentId: classId,
                enrolledDate: enrollmentDate,
                absences: 0,
                skills: 0,
              },
            },
          },
          upsert: false,
        },
      };
    });

    // Execute the bulk write operation
    if (bulkOps.length > 0) {
      await this.studentModel.bulkWrite(bulkOps);
    }
  }

  async remove(id: string, studioId: string): Promise<boolean> {
    const studioObjectId = Types.ObjectId.createFromHexString(studioId);
    const studentId = Types.ObjectId.createFromHexString(id);
    const result = await this.studentModel.deleteOne({
      _id: studentId,
      studioId: studioObjectId,
    });
    return result.deletedCount > 0;
  }

  async removeBatch(
    ids: string[],
    studioId: string,
  ): Promise<{ deletedCount: number }> {
    const studioObjectId = Types.ObjectId.createFromHexString(studioId);
    const studentIds = ids.map((id) => Types.ObjectId.createFromHexString(id));
    const result = await this.studentModel.deleteMany({
      _id: { $in: studentIds },
      studioId: studioObjectId,
    });
    return { deletedCount: result.deletedCount };
  }

  async getEnrollmentCount(
    enrollmentId: string,
    studioId: string,
  ): Promise<{ count: number }> {
    const studioObjectId = Types.ObjectId.createFromHexString(studioId);
    const enrollmentObjectId = Types.ObjectId.createFromHexString(enrollmentId);

    const count = await this.studentModel.countDocuments({
      studioId: studioObjectId,
      'enrollments.enrollmentId': enrollmentObjectId,
    });

    return { count };
  }

  async getEventCount(
    eventId: string,
    studioId: string,
  ): Promise<{ count: number }> {
    const studioObjectId = Types.ObjectId.createFromHexString(studioId);
    const eventObjectId = Types.ObjectId.createFromHexString(eventId);

    const count = await this.studentModel.countDocuments({
      studioId: studioObjectId,
      'events.eventId': eventObjectId,
    });

    return { count };
  }

  async getStudentsRelationsByStudentId(parentId: string) {
    const parentId_ObjectId = Types.ObjectId.createFromHexString(parentId);

    return await this.studentModel.aggregate([
      // Match students directly by parentId
      { $match: { parentId: parentId_ObjectId } },
      {
        $lookup: {
          from: 'parents',
          let: { parentId: '$parentId' },
          pipeline: [
            { $match: { $expr: { $eq: ['$_id', '$$parentId'] } } },
            { $project: { name: 1, email: 1, _id: 0 } },
          ],
          as: 'parent',
        },
      },
      { $unwind: '$parent' },
      {
        $addFields: {
          active: {
            $cond: {
              if: {
                $and: [
                  { $gt: [{ $size: { $ifNull: ['$enrollments', []] } }, 0] },
                  {
                    $gt: [
                      {
                        $size: {
                          $filter: {
                            input: '$enrollments',
                            as: 'enrollment',
                            cond: {
                              $ne: [
                                '$$enrollment.subscriptionStatus',
                                'dropped',
                              ],
                            },
                          },
                        },
                      },
                      0,
                    ],
                  },
                ],
              },
              then: true,
              else: false,
            },
          },
        },
      },
    ]);
  }

  async getStudentCountByParentId(
    parentId: string,
  ): Promise<{ count: number; studentIds: string[] }> {
    try {
      const parentObjectId = Types.ObjectId.createFromHexString(parentId);

      const students = await this.studentModel
        .find(
          {
            parentId: parentObjectId,
            $or: [
              // Case 1: Has at least one non-dropped enrollment
              {
                enrollments: {
                  $elemMatch: {
                    subscriptionStatus: { $ne: 'dropped' },
                  },
                },
              },
              // Case 2: Has no enrollments at all
              {
                enrollments: { $size: 0 },
              },
            ],
          },
          { _id: 1 },
        )
        .exec();

      return {
        count: students.length,
        studentIds: students.map((student) => student._id.toString()),
      };
    } catch (error) {
      if (error.name === 'BSONTypeError') {
        throw new BadRequestException('Invalid parent ID format');
      }
      throw new InternalServerErrorException('Failed to fetch student count');
    }
  }

  async getActiveStudentCountByParentId(
    parentId: string,
  ): Promise<{ count: number; studentIds: string[] }> {
    try {
      const parentObjectId = Types.ObjectId.createFromHexString(parentId);

      const students = await this.studentModel
        .find(
          {
            parentId: parentObjectId,
            enrollments: {
              $elemMatch: {
                subscriptionStatus: { $in: ['active', 'scheduled'] },
              },
            },
          },
          { _id: 1 },
        )
        .exec();

      return {
        count: students.length,
        studentIds: students.map((student) => student._id.toString()),
      };
    } catch (error) {
      if (error.name === 'BSONTypeError') {
        throw new BadRequestException('Invalid parent ID format');
      }
      throw new InternalServerErrorException('Failed to fetch student count');
    }
  }

  // ##################################################Class Attendance  Analtics API#################################################

  // async getAttendanceAnalytics(enrollmentId: string,page: number, limit: number): Promise<any> {
  //   const pageNum = Math.max(1, page);
  //   const limitNum = Math.max(1, Math.min(100, limit));
  //   const skip = (pageNum - 1) * limitNum;
  //   const enrollmentObjectId = Types.ObjectId.createFromHexString(enrollmentId);
  //   const [totalEnrolledStudents, classDetails, totalClasses] =
  //     await Promise.all([
  //       this.studentModel.countDocuments({
  //         'enrollments.enrollmentId': enrollmentObjectId,
  //         'enrollments.subscriptionStatus': 'active',
  //       }),
  //       this.enrollmentModel.findById(enrollmentId, 'duration'),
  //       this.enrollmentModel
  //         .aggregate([
  //           {
  //             $match: {
  //               startDate: {
  //                 $gte: new Date(
  //                   new Date().setMonth(new Date().getMonth() - 1),
  //                 ),
  //               },
  //             },
  //           },
  //           {
  //             $count: 'total',
  //           },
  //         ])
  //         .then((result) => result[0]?.total || 0),
  //     ]);

  //   // Get attendance data
  //   const aggregateData = await this.studentModel.aggregate([
  //     {
  //       $match: {
  //         'attendance.classId': enrollmentObjectId,
  //       },
  //     },
  //     {
  //       $unwind: '$attendance',
  //     },
  //     {
  //       $match: {
  //         'attendance.classId': enrollmentObjectId,
  //       },
  //     },
  //     {
  //       $lookup: {
  //         from: 'attendances',
  //         localField: 'attendance.attendanceId',
  //         foreignField: '_id',
  //         as: 'attendanceDetail',
  //       },
  //     },
  //     {
  //       $unwind: '$attendanceDetail',
  //     },
  //     {
  //       $group: {
  //         _id: null,
  //         totalAttendance: { $sum: 1 },
  //         presentCount: {
  //           $sum: {
  //             $cond: [{ $eq: ['$attendanceDetail.isPresent', true] }, 1, 0],
  //           },
  //         },
  //         attendanceByDate: {
  //           $push: {
  //             date: '$attendance.date',
  //             isPresent: '$attendanceDetail.isPresent',
  //           },
  //         },
  //       },
  //     },
  //     {
  //       $addFields: {
  //         attendanceRate: {
  //           $multiply: [
  //             { $divide: ['$presentCount', '$totalAttendance'] },
  //             100,
  //           ],
  //         },
  //       },
  //     },
  //     {
  //       $project: {
  //         _id: 0,
  //         totalAttendance: 1,
  //         presentCount: 1,
  //         attendanceRate: 1,
  //         attendanceByDate: {
  //           $map: {
  //             input: {
  //               $sortArray: {
  //                 input: {
  //                   $reduce: {
  //                     input: '$attendanceByDate',
  //                     initialValue: [],
  //                     in: {
  //                       $concatArrays: [
  //                         '$$value',
  //                         [
  //                           {
  //                             date: '$$this.date',
  //                             totalPresent: {
  //                               $cond: ['$$this.isPresent', 1, 0],
  //                             },
  //                           },
  //                         ],
  //                       ],
  //                     },
  //                   },
  //                 },
  //                 sortBy: { date: -1 },
  //               },
  //             },
  //             as: 'dateGroup',
  //             in: {
  //               date: '$$dateGroup.date',
  //               totalPresent: {
  //                 $sum: '$$dateGroup.totalPresent',
  //               },
  //             },
  //           },
  //         },
  //       },
  //     },
  //     {
  //       $addFields: {
  //         attendanceByDate: {
  //           $slice: ['$attendanceByDate', 0, 10],
  //         },
  //       },
  //     },
  //   ]);

  //   return {
  //     totalEnrolledStudents,
  //     classDuration: classDetails?.duration || 60,
  //     totalClasses,
  //     aggregateData,
  //   };
  // }

  async getSudentDaat(enrollmentId: string): Promise<any> {
    const enrollmentObjectId = Types.ObjectId.createFromHexString(enrollmentId);
    const totalEnrolledStudents = await this.studentModel.countDocuments({
      'enrollments.enrollmentId': enrollmentObjectId,
      'enrollments.subscriptionStatus': 'active',
    });
    return totalEnrolledStudents;
  }
  async getClassduration(enrollmentId: string): Promise<any> {
    const enrollmentObjectId = Types.ObjectId.createFromHexString(enrollmentId);
    const calssDuration = await this.enrollmentModel.findById(
      enrollmentId,
      'duration',
    );
    return calssDuration?.duration || 60;
  }
  async getAttendanceAnalytics(
    enrollmentId: string,
    page: number,
    limit: number,
  ): Promise<any> {
    const pageNum = Math.max(1, page);
    const limitNum = Math.max(1, Math.min(100, limit));
    const skip = (pageNum - 1) * limitNum;
    const enrollmentObjectId = Types.ObjectId.createFromHexString(enrollmentId);

    // Get basic stats
    const [totalEnrolledStudents, classDetails, totalClasses] =
      await Promise.all([
        this.studentModel.countDocuments({
          'enrollments.enrollmentId': enrollmentObjectId,
          'enrollments.subscriptionStatus': { $in: ['active', 'free'] },
        }),
        this.enrollmentModel.findById(enrollmentId, 'duration'),
        this.enrollmentModel
          .aggregate([
            {
              $match: {
                startDate: {
                  $gte: new Date(
                    new Date().setMonth(new Date().getMonth() - 1),
                  ),
                },
              },
            },
            {
              $count: 'total',
            },
          ])
          .then((result) => result[0]?.total || 0),
      ]);

    // Get attendance data with pagination
    const aggregateData = await this.studentModel.aggregate([
      {
        $match: {
          'attendance.classId': enrollmentObjectId,
        },
      },
      {
        $unwind: '$attendance',
      },
      {
        $match: {
          'attendance.classId': enrollmentObjectId,
        },
      },
      {
        $lookup: {
          from: 'attendances',
          localField: 'attendance.attendanceId',
          foreignField: '_id',
          as: 'attendanceDetail',
        },
      },
      {
        $unwind: '$attendanceDetail',
      },
      {
        $facet: {
          // Get overall stats
          stats: [
            {
              $group: {
                _id: null,
                totalAttendance: { $sum: 1 },
                presentCount: {
                  $sum: {
                    $cond: [
                      { $eq: ['$attendanceDetail.isPresent', true] },
                      1,
                      0,
                    ],
                  },
                },
              },
            },
            {
              $addFields: {
                attendanceRate: {
                  $multiply: [
                    { $divide: ['$presentCount', '$totalAttendance'] },
                    100,
                  ],
                },
              },
            },
          ],
          // Get paginated attendance records
          attendanceRecords: [
            {
              $group: {
                _id: '$attendance.date',
                totalPresent: {
                  $sum: {
                    $cond: [
                      { $eq: ['$attendanceDetail.isPresent', true] },
                      1,
                      0,
                    ],
                  },
                },
              },
            },
            { $sort: { _id: -1 } },
            { $skip: skip },
            { $limit: limitNum },
          ],
          // Get total count for pagination
          totalCount: [
            {
              $group: {
                _id: '$attendance.date',
              },
            },
            { $count: 'count' },
          ],
        },
      },
    ]);

    const stats = aggregateData[0].stats[0] || {
      totalAttendance: 0,
      presentCount: 0,
      attendanceRate: 0,
    };

    const totalRecords = aggregateData[0].totalCount[0]?.count || 0;
    const attendanceRecords = aggregateData[0].attendanceRecords || [];

    return {
      resData: {
        totalStudents: totalEnrolledStudents,
        averageAttendance: `${Math.round(stats.attendanceRate)}%`,
        totalClasses,
        RecentattendanceRecords: attendanceRecords.map((record) => ({
          date: new Date(record._id).toLocaleDateString('en-US', {
            weekday: 'short',
            month: 'short',
            day: 'numeric',
            year: 'numeric',
          }),
          studentPresent: `${record.totalPresent}/${totalEnrolledStudents}`,
          attendanceRate: `${Math.round((record.totalPresent / totalEnrolledStudents) * 100)}%`,
        })),
        pagination: {
          currentPage: pageNum,
          totalPages: Math.ceil(totalRecords / limitNum),
          totalItems: totalRecords,
          itemsPerPage: limitNum,
          hasNextPage: pageNum < Math.ceil(totalRecords / limitNum),
          hasPreviousPage: pageNum > 1,
        },
      },
    };
  }
  async getStudentPerformance(
    enrollmentId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<any> {
    const pageNum = Math.max(1, page);
    const limitNum = Math.max(1, Math.min(100, limit));
    const skip = (pageNum - 1) * limitNum;

    const enrollmentObjectId = Types.ObjectId.createFromHexString(enrollmentId);

    // Get total count of enrolled students
    const totalCount = await this.studentModel.countDocuments({
      'enrollments.enrollmentId': enrollmentObjectId,
      'enrollments.subscriptionStatus': { $in: ['active', 'free'] },
    });

    // Calculate total pages
    const totalPages = Math.ceil(totalCount / limitNum);

    // Ensure page number doesn't exceed total pages
    if (pageNum > totalPages && totalPages > 0) {
      throw new BadRequestException(
        `Page ${pageNum} exceeds total pages ${totalPages}`,
      );
    }

    // Get performance data for paginated students
    const performanceData = await this.studentModel.aggregate([
      // First match all enrolled students
      {
        $match: {
          'enrollments.enrollmentId': enrollmentObjectId,
          'enrollments.subscriptionStatus': 'active',
        },
      },
      // Add attendance array field with filtered attendance for this class
      {
        $addFields: {
          classAttendance: {
            $filter: {
              input: { $ifNull: ['$attendance', []] },
              as: 'att',
              cond: { $eq: ['$$att.classId', enrollmentObjectId] },
            },
          },
        },
      },
      // Lookup attendance details
      {
        $lookup: {
          from: 'attendances',
          localField: 'classAttendance.attendanceId',
          foreignField: '_id',
          as: 'attendanceDetails',
        },
      },
      // Calculate metrics
      {
        $project: {
          _id: 0,
          name: 1,
          classesAttended: {
            $size: {
              $filter: {
                input: '$classAttendance',
                as: 'att',
                cond: {
                  $in: [
                    '$$att.attendanceId',
                    {
                      $map: {
                        input: {
                          $filter: {
                            input: '$attendanceDetails',
                            as: 'detail',
                            cond: { $eq: ['$$detail.isPresent', true] },
                          },
                        },
                        as: 'presentDetail',
                        in: '$$presentDetail._id',
                      },
                    },
                  ],
                },
              },
            },
          },
          classesDone: { $size: '$classAttendance' },
        },
      },
      // Calculate percentage
      {
        $addFields: {
          attendancePercentage: {
            $cond: [
              { $eq: ['$classesDone', 0] },
              0,
              {
                $multiply: [
                  { $divide: ['$classesAttended', '$classesDone'] },
                  100,
                ],
              },
            ],
          },
        },
      },
      // Add status
      {
        $addFields: {
          status: {
            $switch: {
              branches: [
                {
                  case: { $gte: ['$attendancePercentage', 90] },
                  then: 'Excellent',
                },
                { case: { $gte: ['$attendancePercentage', 75] }, then: 'Good' },
                {
                  case: { $gte: ['$attendancePercentage', 60] },
                  then: 'Average',
                },
              ],
              default: '0',
            },
          },
        },
      },
      // Format output
      {
        $project: {
          name: 1,
          classesAttended: 1,
          classesDone: 1,
          attendancePercentage: {
            $concat: [
              { $toString: { $round: ['$attendancePercentage', 0] } },
              '%',
            ],
          },
          status: 1,
          percentageValue: { $round: ['$attendancePercentage', 0] },
        },
      },
      { $sort: { name: 1 } },
      { $skip: skip },
      { $limit: limitNum },
    ]);

    // Calculate distribution for all enrolled students
    // ... existing code ...

    // Calculate distribution for all enrolled students
    const allStudentsDistribution = await this.studentModel.aggregate([
      {
        $match: {
          'enrollments.enrollmentId': enrollmentObjectId,
          'enrollments.subscriptionStatus': 'active',
        },
      },
      // Add attendance array field
      {
        $addFields: {
          classAttendance: {
            $filter: {
              input: { $ifNull: ['$attendance', []] },
              as: 'att',
              cond: { $eq: ['$$att.classId', enrollmentObjectId] },
            },
          },
        },
      },
      // Lookup attendance details
      {
        $lookup: {
          from: 'attendances',
          localField: 'classAttendance.attendanceId',
          foreignField: '_id',
          as: 'attendanceDetails',
        },
      },
      // Calculate attendance percentage
      {
        $project: {
          attendancePercentage: {
            $multiply: [
              {
                $cond: [
                  { $eq: [{ $size: '$classAttendance' }, 0] },
                  0,
                  {
                    $divide: [
                      {
                        $size: {
                          $filter: {
                            input: '$classAttendance',
                            as: 'att',
                            cond: {
                              $in: [
                                '$$att.attendanceId',
                                {
                                  $map: {
                                    input: {
                                      $filter: {
                                        input: '$attendanceDetails',
                                        as: 'detail',
                                        cond: {
                                          $eq: ['$$detail.isPresent', true],
                                        },
                                      },
                                    },
                                    as: 'presentDetail',
                                    in: '$$presentDetail._id',
                                  },
                                },
                              ],
                            },
                          },
                        },
                      },
                      { $size: '$classAttendance' },
                    ],
                  },
                ],
              },
              100,
            ],
          },
        },
      },
      // Group and count by attendance ranges
      {
        $group: {
          _id: null,
          excellent: {
            $sum: {
              $cond: [{ $gte: ['$attendancePercentage', 90] }, 1, 0],
            },
          },
          good: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $gte: ['$attendancePercentage', 75] },
                    { $lt: ['$attendancePercentage', 90] },
                  ],
                },
                1,
                0,
              ],
            },
          },
          average: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $gte: ['$attendancePercentage', 60] },
                    { $lt: ['$attendancePercentage', 75] },
                  ],
                },
                1,
                0,
              ],
            },
          },
          needsImprovement: {
            $sum: {
              $cond: [{ $lt: ['$attendancePercentage', 60] }, 1, 0],
            },
          },
        },
      },
    ]);

    const distribution = allStudentsDistribution[0] || {
      excellent: 0,
      good: 0,
      average: 0,
      needsImprovement: 0,
    };

    // Remove the temporary percentageValue field and return the result
    const cleanedPerformanceData = performanceData.map(
      ({ percentageValue, ...rest }) => rest,
    );

    return {
      studentPerformance: cleanedPerformanceData,
      studentDistribution: {
        'Excellent (90-100%)': distribution.excellent,
        'Good (75-89%)': distribution.good,
        'Average (60-74%)': distribution.average,
        'Needs Improvement (<60%)': distribution.needsImprovement,
      },
      pagination: {
        currentPage: pageNum,
        totalPages,
        totalItems: totalCount,
        itemsPerPage: limitNum,
        hasNextPage: pageNum < totalPages,
        hasPreviousPage: pageNum > 1,
      },
    };
  }

  async getClassSessions(
    enrollmentId: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<any> {
    const pageNum = Math.max(1, page);
    const limitNum = Math.max(1, Math.min(100, limit));

    const enrollmentDetails = await this.enrollmentModel
      .aggregate([
        {
          $match: {
            _id: new Types.ObjectId(enrollmentId),
          },
        },
        {
          $lookup: {
            from: 'customforms',
            let: { roomId: '$room' },
            pipeline: [
              {
                $match: {
                  $expr: { $eq: ['$_id', '$$roomId'] },
                },
              },
              {
                $project: {
                  fieldName: 1,
                },
              },
            ],
            as: 'roomDetails',
          },
        },
        {
          $lookup: {
            from: 'customforms',
            let: { instructorIds: '$instructor' },
            pipeline: [
              {
                $match: {
                  $expr: { $in: ['$_id', '$$instructorIds'] },
                },
              },
              {
                $project: {
                  fieldName: 1,
                },
              },
            ],
            as: 'instructorDetails',
          },
        },
        {
          $project: {
            startTime: 1,
            endTime: 1,
            startDate: 1,
            endDate: 1,
            days: 1,
            room: { $arrayElemAt: ['$roomDetails.fieldName', 0] },
            instructor: { $arrayElemAt: ['$instructorDetails.fieldName', 0] },
            duration: 1,
          },
        },
      ])
      .then((results) => results[0]);

    if (!enrollmentDetails) {
      return null;
    }

    // Generate all session dates based on schedule
    const sessions = [];
    const startDate = new Date(enrollmentDetails.startDate);
    const endDate = new Date(enrollmentDetails.endDate);
    const days = enrollmentDetails.days;

    // Map day strings to day numbers
    const dayMap = {
      Sun: 0,
      Mon: 1,
      Tue: 2,
      Wed: 3,
      Thu: 4,
      Fri: 5,
      Sat: 6,
    };

    // Generate all sessions
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      const dayOfWeek = currentDate.getDay();
      const dayName = Object.keys(dayMap).find(
        (key) => dayMap[key] === dayOfWeek,
      );

      if (days.includes(dayName)) {
        sessions.push({
          date: new Date(currentDate),
        });
      }

      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Sort sessions by date (ascending)
    sessions.sort((a, b) => a.date.getTime() - b.date.getTime());

    // Calculate total count
    const totalCount = sessions.length;

    // Apply pagination
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    const paginatedSessions = sessions.slice(startIndex, endIndex);

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Format the sessions
    const formattedSessions = paginatedSessions.map((session) => {
      const dateStr = session.date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        weekday: 'short',
      });

      const [weekday, month, day, year] = dateStr.split(' ');
      const formattedDate = `${month} ${day.replace(',', '')}, ${year} ${weekday.replace(',', '')}`;

      return {
        date: formattedDate,
        startTime: enrollmentDetails.startTime,
        endTime: enrollmentDetails.endTime,
        room: enrollmentDetails.room,
        instructor: enrollmentDetails.instructor,
        status: session.date < today ? 'Completed' : 'Upcoming',
      };
    });

    return {
      classSessions: formattedSessions,
      pagination: {
        currentPage: pageNum,
        totalPages: Math.ceil(totalCount / limitNum),
        totalItems: totalCount,
        itemsPerPage: limitNum,
        hasNextPage: pageNum * limitNum < totalCount,
        hasPreviousPage: pageNum > 1,
      },
    };
  }
  private addOneHour(timeStr: string): string {
    const [time, period] = timeStr.split(' ');
    const [hours, minutes] = time.split(':');
    let hour = parseInt(hours);

    if (period === 'PM' && hour !== 12) {
      hour += 12;
    }
    if (period === 'AM' && hour === 12) {
      hour = 0;
    }

    hour = (hour + 1) % 24;

    let newPeriod = period;
    if (hour >= 12) {
      newPeriod = 'PM';
      if (hour > 12) hour -= 12;
    } else {
      newPeriod = 'AM';
      if (hour === 0) hour = 12;
    }

    return `${hour}:${minutes} ${newPeriod}`;
  }
  async getClassCountInDateRange(enrollmentId: string) {
    // Get enrollment details from database
    const enrollment = await this.enrollmentModel.findById(enrollmentId);

    if (!enrollment) {
      throw new NotFoundException(
        `Enrollment with ID ${enrollmentId} not found`,
      );
    }

    const startDate = new Date(enrollment.startDate);
    const endDate = new Date(enrollment.endDate);
    const classDays = enrollment.days; // Array of days ["Mon", "Tue", etc]

    // Map day strings to numbers (0 = Sunday, 1 = Monday, etc)
    const dayMapping = {
      Sun: 0,
      Mon: 1,
      Tue: 2,
      Wed: 3,
      Thu: 4,
      Fri: 5,
      Sat: 6,
    };

    // Convert class days to numbers
    const classDayNumbers = classDays.map((day) => dayMapping[day]);

    let totalClasses = 0;
    let currentDate = new Date(startDate);

    // Loop through each day between start and end date
    while (currentDate <= endDate) {
      // Check if current day is a class day
      if (classDayNumbers.includes(currentDate.getDay())) {
        totalClasses++;
      }
      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Group classes by month
    const monthlyClasses = {};
    currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      if (classDayNumbers.includes(currentDate.getDay())) {
        const monthYear = currentDate.toLocaleString('default', {
          month: 'long',
          year: 'numeric',
        });
        monthlyClasses[monthYear] = (monthlyClasses[monthYear] || 0) + 1;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return {
      totalClasses,
      monthlyBreakdown: monthlyClasses,
      classDays,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
    };
  }
  async markAttendance(markAttendanceDto: MarkAttendanceDto) {
    try {
      console.log('markAttendanceDto', markAttendanceDto);
      const studentId = Types.ObjectId.createFromHexString(
        markAttendanceDto.studentId,
      );
      const classId = Types.ObjectId.createFromHexString(
        markAttendanceDto.classId,
      );
      const attendanceId = Types.ObjectId.createFromHexString(
        markAttendanceDto.attendanceId,
      );

      // Parse the date string and set to midnight UTC+0
      const dateString = markAttendanceDto.date; // 'Mon, Feb 16, 2026'
      const parts = dateString.split(', '); // ['Mon', 'Feb 16', '2026']
      console.log('parts', parts);
      const [monthStr, dayStr] = parts[1].split(' '); // ['Feb', '16']
      console.log('monthStr', monthStr);
      const year = parseInt(parts[2]);
      console.log('year', year);
      const month = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
      ].indexOf(monthStr);
      const day = parseInt(dayStr);

      // Create date at midnight UTC+0
      const inputDate = new Date(Date.UTC(year, month, day, 0, 0, 0, 0));
      // const inputDate = new Date(dateString);
      console.log('inputDate', inputDate);
      // First check   if student exists
      const studentExists = await this.studentModel.findById(studentId);
      if (!studentExists) {
        throw new NotFoundException('Student not found');
      }
      console.log('studentExists', studentExists);
      // Verify student is enrolled in the class
      const isEnrolled = studentExists.enrollments?.some(
        (enrollment) =>
          enrollment.enrollmentId.toString() === markAttendanceDto.classId,
      );

      if (!isEnrolled) {
        throw new BadRequestException('Student is not enrolled in this class');
      }

      // Check for existing attendance with standardized date comparison
      const existingAttendance = await this.studentModel.findOne({
        _id: studentId,
        attendance: {
          $elemMatch: {
            classId: classId,
            date: {
              $gte: inputDate,
              $lt: new Date(inputDate.getTime() + 24 * 60 * 60 * 1000),
            },
          },
        },
      });

      if (existingAttendance) {
        throw new BadRequestException(
          `Attendance has already been marked for this student on ${inputDate.toLocaleDateString()}`,
        );
      }

      // Create new attendance record with standardized date
      const updatedStudent = await this.studentModel.findByIdAndUpdate(
        studentId,
        {
          $push: {
            attendance: {
              attendanceId: attendanceId,
              classId: classId,
              date: inputDate,
              updatedAt: new Date(),
            },
          },
        },
        { new: true },
      );

      return updatedStudent;
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      this.logger.error(`Failed to mark attendance: ${error.message}`);
      throw new InternalServerErrorException('Failed to mark attendance');
    }
  }

  async getStudentAttendanceRecords(
    studentId: string,
    classId: string,
    page: number = 1,
    limit: number = 10,
  ) {
    try {
      const pageNum = Math.max(1, page);
      const limitNum = Math.max(1, Math.min(100, limit));
      const skip = (pageNum - 1) * limitNum;

      // First verify the enrollment exists
      const enrollment = await this.enrollmentModel.findById(classId);
      if (!enrollment) {
        throw new NotFoundException(`Enrollment with ID ${classId} not found`);
      }

      // Get student data with attendance records
      const student = await this.studentModel.findById(studentId);
      if (!student) {
        throw new NotFoundException(`Student with ID ${studentId} not found`);
      }
      console.log('student', student);
      // Get all attendance records for this student and class
      const attendanceRecords = await this.attendanceModel.find({
        _id: {
          $in: student.attendance
            .filter((att) => att.classId.toString() === classId)
            .map((att) => att.attendanceId),
        },
      });

      // Create a map of attendance records by date string
      const attendanceMap = new Map();
      student.attendance
        .filter((att) => att.classId.toString() === classId)
        .forEach((att) => {
          const dateStr = new Date(att.date).toDateString();
          const attendanceRecord = attendanceRecords.find(
            (record) => record._id.toString() === att.attendanceId.toString(),
          );
          if (attendanceRecord) {
            attendanceMap.set(dateStr, {
              isPresent: attendanceRecord.isPresent,
              statusDetail: attendanceRecord.statusDetail,
            });
          }
        });
      console.log('attendanceMap', attendanceMap);
      // Generate all sessions
      const sessions = [];
      const startDate = new Date(enrollment.startDate);
      const endDate = new Date(enrollment.endDate);
      const days = enrollment.days;

      // Map day strings to day numbers
      const dayMap = {
        Sun: 0,
        Mon: 1,
        Tue: 2,
        Wed: 3,
        Thu: 4,
        Fri: 5,
        Sat: 6,
      };

      // Generate all sessions
      const currentDate = new Date(startDate);
      while (currentDate <= endDate) {
        const dayOfWeek = currentDate.getDay();
        const dayName = Object.keys(dayMap).find(
          (key) => dayMap[key] === dayOfWeek,
        );

        if (days.includes(dayName)) {
          sessions.push({
            date: new Date(currentDate),
          });
        }

        currentDate.setDate(currentDate.getDate() + 1);
      }

      // Sort sessions by date (most recent first)
      sessions.sort((a, b) => a.date.getTime() - b.date.getTime());

      // Apply pagination
      const paginatedSessions = sessions.slice(skip, skip + limitNum);

      // Format the records with attendance status
      const formattedRecords = paginatedSessions.map((session) => {
        const sessionDate = new Date(session.date);
        sessionDate.setHours(0, 0, 0, 0);

        // const attendanceInfo = attendanceMap.get(sessionDate.toDateString());
        // let status = 'Pending';
        // let statusDetail = '';

        // if (attendanceInfo) {
        //   status = attendanceInfo.isPresent ? 'Present' : 'Absent';
        //   statusDetail = attendanceInfo.statusDetail || '';
        // } else if (sessionDate < new Date()) {
        //   status = 'Absent';
        // }
        const attendanceInfo = attendanceMap.get(sessionDate.toDateString());
        let status = 'Pending';

        if (attendanceInfo !== undefined) {
          // Only use the database value if attendance record exists
          status = attendanceInfo.isPresent ? 'Present' : 'Absent';
        }
        return {
          date: sessionDate.toLocaleDateString('en-US', {
            weekday: 'short',
            month: 'short',
            day: 'numeric',
            year: 'numeric',
          }),
          status,
        };
      });

      return {
        attendanceRecords: formattedRecords,
        pagination: {
          currentPage: pageNum,
          totalPages: Math.ceil(sessions.length / limitNum),
          totalItems: sessions.length,
          itemsPerPage: limitNum,
          hasNextPage: pageNum * limitNum < sessions.length,
          hasPreviousPage: pageNum > 1,
        },
      };
    } catch (error) {
      console.error('Error fetching attendance records:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to fetch attendance records: ${error.message}`,
      );
    }
  }

  async getStudentAttendanceRecordsfilter(
    studentId: string,
    classId: string,
    startDate?: string, // Optional start date parameter
    endDate?: string, // Optional end date parameter
  ) {
    try {
      // First verify the enrollment exists
      const enrollment = await this.enrollmentModel.findById(classId);
      if (!enrollment) {
        throw new NotFoundException(`Enrollment with ID ${classId} not found`);
      }

      // Get student data with attendance records
      const student = await this.studentModel.findById(studentId);
      if (!student) {
        throw new NotFoundException(`Student with ID ${studentId} not found`);
      }

      // Get all attendance records for this student and class
      const attendanceRecords = await this.attendanceModel.find({
        _id: {
          $in: student.attendance
            .filter((att) => att.classId.toString() === classId)
            .map((att) => att.attendanceId),
        },
      });

      // Create a map of attendance records by date string
      const attendanceMap = new Map();
      student.attendance
        .filter((att) => att.classId.toString() === classId)
        .forEach((att) => {
          const dateStr = new Date(att.date).toDateString();
          const attendanceRecord = attendanceRecords.find(
            (record) => record._id.toString() === att.attendanceId.toString(),
          );
          if (attendanceRecord) {
            attendanceMap.set(dateStr, {
              isPresent: attendanceRecord.isPresent,
              statusDetail: attendanceRecord.statusDetail || '',
            });
          }
        });

      // If no dates provided, use current week
      let weekStart: Date, weekEnd: Date;
      if (startDate && endDate) {
        weekStart = new Date(startDate);
        weekEnd = new Date(endDate);
      } else {
        // Get current week's start (Sunday) and end (Saturday)
        const today = new Date();
        weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay()); // Start of week (Sunday)
        weekEnd = new Date(today);
        weekEnd.setDate(weekStart.getDate() + 6); // End of week (Saturday)
      }

      // Generate sessions for the week
      const sessions = [];
      const currentDate = new Date(weekStart);
      const days = enrollment.days;

      // Map day strings to day numbers
      const dayMap = {
        Sun: 0,
        Mon: 1,
        Tue: 2,
        Wed: 3,
        Thu: 4,
        Fri: 5,
        Sat: 6,
      };

      while (currentDate <= weekEnd) {
        const dayOfWeek = currentDate.getDay();
        const dayName = Object.keys(dayMap).find(
          (key) => dayMap[key] === dayOfWeek,
        );

        if (days.includes(dayName)) {
          const sessionDate = new Date(currentDate);
          const dateStr = sessionDate.toDateString();
          const attendanceInfo = attendanceMap.get(dateStr);

          sessions.push({
            date: sessionDate.toLocaleDateString('en-US', {
              weekday: 'short',
              month: 'short',
              day: 'numeric',
              year: 'numeric',
            }),
            status: attendanceInfo
              ? attendanceInfo.isPresent
                ? 'Present'
                : 'Absent'
              : 'Pending',
          });
        }

        currentDate.setDate(currentDate.getDate() + 1);
      }

      return {
        weeklyAttendance: [
          {
            weekRange: `${weekStart.toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
              year: 'numeric',
            })} - ${weekEnd.toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
              year: 'numeric',
            })}`,
            sessions,
          },
        ],
      };
    } catch (error) {
      console.error('Error fetching attendance records:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException(
        `Failed to fetch attendance records: ${error.message}`,
      );
    }
  }

  // async generateScheduleReport(
  //   parentId: string,
  //   studioId: string,
  //   startDate: string,
  //   endDate: string,
  // ): Promise<any> {
  //   try {
  //     const parentIdObjectId = Types.ObjectId.createFromHexString(parentId);
  //     const studioIdObjectId = Types.ObjectId.createFromHexString(studioId);

  //     // Get all students for this parent and studio
  //     const students = await this.studentModel.find({
  //       parentId: parentIdObjectId,
  //       studioId: studioIdObjectId,
  //     });

  //     const studio = await this.studioModel.findById(studioIdObjectId);
  //     if (!students || students.length === 0) {
  //       throw new NotFoundException('No students found for this parent');
  //     }

  //     const enrollmentIds = students.flatMap((student) =>
  //       student.enrollments
  //         .filter((e) => e.subscriptionStatus === 'active')
  //         .map((e) => e.enrollmentId),
  //     );

  //     // Get all enrollments
  //     const enrollments = await this.enrollmentModel.find({
  //       _id: { $in: enrollmentIds },
  //     });

  //     // Create a map of enrollments by student ID
  //     const studentEnrollmentsMap = new Map();
  //     students.forEach((student) => {
  //       const studentEnrollments = enrollments.filter((enrollment) =>
  //         student.enrollments.some(
  //           (e) =>
  //             e.enrollmentId.toString() === enrollment._id.toString() &&
  //             e.subscriptionStatus === 'active',
  //         ),
  //       );
  //       studentEnrollmentsMap.set(student._id.toString(), studentEnrollments);
  //     });

  //     // Get location names
  //     const locationIds = [
  //       ...new Set(enrollments.map((e) => e.location).filter(Boolean)),
  //     ];
  //     const locations = await this.customFormModel
  //       .find({
  //         _id: { $in: locationIds },
  //         fieldType: 'location',
  //       })
  //       .select('_id fieldName');

  //     const locationMap = locations.reduce((map, loc) => {
  //       map[loc._id.toString()] = loc.fieldName;
  //       return map;
  //     }, {});

  //     // Generate date range
  //     const requestStart = parseISO(startDate);
  //     const requestEnd = parseISO(endDate);
  //     const dateRange = eachDayOfInterval({
  //       start: requestStart,
  //       end: requestEnd,
  //     });

  //     // Initialize schedule
  //     const schedule = {};
  //     dateRange.forEach((date) => {
  //       const formattedDate = format(date, 'EEEE MMMM d');
  //       schedule[formattedDate] = [];
  //     });

  //     // Process each student's enrollments
  //     students.forEach((student) => {
  //       const studentEnrollments =
  //         studentEnrollmentsMap.get(student._id.toString()) || [];

  //       studentEnrollments.forEach((enrollment) => {
  //         const enrollmentStart = new Date(enrollment.startDate);
  //         const enrollmentEnd = new Date(enrollment.endDate);

  //         dateRange.forEach((date) => {
  //           if (date >= enrollmentStart && date <= enrollmentEnd) {
  //             const dayName = format(date, 'EEE');

  //             if (enrollment.days.includes(dayName)) {
  //               const formattedDate = format(date, 'EEEE MMMM d');
  //               schedule[formattedDate].push({
  //                 student: student.name,
  //                 className: enrollment.title,
  //                 location:
  //                   locationMap[enrollment.location?.toString()] ||
  //                   'No Location',
  //                 time: `${format(new Date(enrollment.startTime), 'h:mm a')} to ${format(new Date(enrollment.endTime), 'h:mm a')}`,
  //                 startDate: format(enrollmentStart, 'MMM dd, yyyy'),
  //                 endDate: format(enrollmentEnd, 'MMM dd, yyyy'),
  //               });
  //             }
  //           }
  //         });
  //       });
  //     });

  //     // Sort classes by time within each day
  //     Object.keys(schedule).forEach((day) => {
  //       schedule[day].sort((a, b) => {
  //         const timeA = a.time.split(' to ')[0];
  //         const timeB = b.time.split(' to ')[0];
  //         return timeA.localeCompare(timeB);
  //       });
  //     });

  //     return {
  //       studioName: studio?.subaccountName || 'Unknown Studio',
  //       schedule,
  //     };
  //   } catch (error) {
  //     console.error('Error generating schedule report:', error);
  //     throw new InternalServerErrorException(
  //       'Failed to generate schedule report',
  //     );
  //   }
  // }

  async generateScheduleReport(
    parentId: string,
    studioId: string,
    startDate: string,
    endDate: string,
  ): Promise<any> {
    try {
      // Convert string IDs to ObjectIds
      const parentIdObjectId = Types.ObjectId.createFromHexString(parentId);
      const studioIdObjectId = Types.ObjectId.createFromHexString(studioId);

      // Get all students for this parent and studio
      const students = await this.studentModel.find({
        parentId: parentIdObjectId,
        studioId: studioIdObjectId,
      });

      const studio = await this.studioModel.findById(studioIdObjectId);
      if (!students || students.length === 0) {
        throw new NotFoundException('No students found for this parent');
      }

      // Get all active enrollment IDs
      const enrollmentIds = students.flatMap((student) =>
        student.enrollments
          .filter((e) => e.subscriptionStatus === 'active')
          .map((e) => e.enrollmentId),
      );

      // Get all enrollments
      const enrollments = await this.enrollmentModel.find({
        _id: { $in: enrollmentIds },
      });

      // Create a map of enrollments by student ID
      const studentEnrollmentsMap = new Map();
      students.forEach((student) => {
        const studentEnrollments = enrollments.filter((enrollment) =>
          student.enrollments.some(
            (e) =>
              e.enrollmentId.toString() === enrollment._id.toString() &&
              e.subscriptionStatus === 'active',
          ),
        );
        studentEnrollmentsMap.set(student._id.toString(), studentEnrollments);
      });

      // Get location names
      const locationIds = [
        ...new Set(enrollments.map((e) => e.location).filter(Boolean)),
      ];
      const locations = await this.customFormModel
        .find({
          _id: { $in: locationIds },
          fieldType: 'location',
        })
        .select('_id fieldName');

      const locationMap = locations.reduce((map, loc) => {
        map[loc._id.toString()] = loc.fieldName;
        return map;
      }, {});

      const formatClassTime = (startTime: string, endTime: string): string => {
        try {
          let startHours: number, startMinutes: number;
          let endHours: number, endMinutes: number;

          // Check if time is in ISO format (contains 'T' and 'Z')
          if (startTime.includes('T')) {
            // Parse ISO format
            const startDate = new Date(startTime);
            const endDate = new Date(endTime);
            startHours = startDate.getUTCHours();
            startMinutes = startDate.getUTCMinutes();
            endHours = endDate.getUTCHours();
            endMinutes = endDate.getUTCMinutes();
          } else {
            // Parse simple time format (HH:MM)
            [startHours, startMinutes] = startTime.split(':').map(Number);
            [endHours, endMinutes] = endTime.split(':').map(Number);
          }

          // Create Date objects for formatting
          const start = new Date();
          start.setHours(startHours, startMinutes, 0);

          const end = new Date();
          end.setHours(endHours, endMinutes, 0);

          // Format the times
          const formattedStart = format(start, 'h:mm a');
          const formattedEnd = format(end, 'h:mm a');

          return `${formattedStart} to ${formattedEnd}`;
        } catch (error) {
          console.error('Error formatting time:', error, {
            startTime,
            endTime,
          });
          return 'Time not available';
        }
      };

      // Generate date range
      const requestStart = parseISO(startDate);
      const requestEnd = parseISO(endDate);
      const dateRange = eachDayOfInterval({
        start: requestStart,
        end: requestEnd,
      });

      // Initialize schedule
      const schedule = {};
      dateRange.forEach((date) => {
        const formattedDate = format(date, 'EEEE MMMM d');
        schedule[formattedDate] = [];
      });

      students.forEach((student) => {
        const studentEnrollments =
          studentEnrollmentsMap.get(student._id.toString()) || [];

        studentEnrollments.forEach((enrollment) => {
          const enrollmentStart = new Date(enrollment.startDate);
          const enrollmentEnd = new Date(enrollment.endDate);

          dateRange.forEach((date) => {
            if (date >= enrollmentStart && date <= enrollmentEnd) {
              const dayName = format(date, 'EEE');

              if (enrollment.days.includes(dayName)) {
                const formattedDate = format(date, 'EEEE MMMM d');

                // Handle both time formats
                const startTimeStr =
                  typeof enrollment.startTime === 'string' &&
                  enrollment.startTime.includes('T')
                    ? enrollment.startTime
                    : enrollment.startTime;
                const endTimeStr =
                  typeof enrollment.endTime === 'string' &&
                  enrollment.endTime.includes('T')
                    ? enrollment.endTime
                    : enrollment.endTime;

                const classTime = formatClassTime(startTimeStr, endTimeStr);

                schedule[formattedDate].push({
                  student: student.name,
                  className: enrollment.title,
                  location:
                    locationMap[enrollment.location?.toString()] ||
                    'No Location',
                  time: classTime,
                  startDate: format(enrollmentStart, 'MMM dd, yyyy'),
                  endDate: format(enrollmentEnd, 'MMM dd, yyyy'),
                });
              }
            }
          });
        });
      });

      // Sort classes by time within each day
      Object.keys(schedule).forEach((day) => {
        schedule[day].sort((a, b) => {
          // Parse times for proper comparison
          const getTimeValue = (timeStr: string) => {
            const [time, period] = timeStr.split(' to ')[0].split(' ');
            const [hours, minutes] = time.split(':');
            let hour = parseInt(hours);

            // Convert to 24-hour format for sorting
            if (period.toLowerCase() === 'pm' && hour !== 12) {
              hour += 12;
            } else if (period.toLowerCase() === 'am' && hour === 12) {
              hour = 0;
            }

            return hour * 60 + parseInt(minutes);
          };

          return getTimeValue(a.time) - getTimeValue(b.time);
        });
      });

      return {
        studioName: studio?.subaccountName || 'Unknown Studio',
        schedule,
      };
    } catch (error) {
      console.error('Error generating schedule report:', error);
      throw new InternalServerErrorException(
        'Failed to generate schedule report',
      );
    }
  }

  async generateSchedulePDF(scheduleData: any): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      try {
        console.log('scheduleData', scheduleData);
        const doc = new PDFDocument({ margin: 50 });
        const chunks: Buffer[] = [];

        doc.on('data', (chunk) => chunks.push(chunk));
        doc.on('end', () => resolve(Buffer.concat(chunks)));

        // Studio Name
        doc.fontSize(24).text(scheduleData.studioName, { align: 'center' });
        doc.moveDown();

        // Title
        doc.fontSize(20).text('Class Schedule', { align: 'center' });
        doc.moveDown();

        // For each day in the schedule
        Object.entries(scheduleData.schedule).forEach(
          ([date, classes]: [string, any[]]) => {
            if (classes.length > 0) {
              // Date header
              doc.fontSize(14).text(date, { underline: true });
              doc.moveDown(0.5);

              // Classes for this day
              classes.forEach((classInfo: any) => {
                doc.fontSize(12);
                doc.text(`Student: ${classInfo.student}`);
                doc.text(`Class: ${classInfo.className}`);
                doc.text(`Time: ${classInfo.time}`);
                doc.text(`Location: ${classInfo.location}`);
                doc.text(
                  `Period: ${classInfo.startDate} - ${classInfo.endDate}`,
                );
                doc.moveDown();
              });

              doc.moveDown();
            }
          },
        );

        doc.end();
      } catch (error) {
        reject(error);
      }
    });
  }

  async getStudentById(studentId: Types.ObjectId): Promise<Student> {
    return this.studentModel
      .findById(studentId)
      .populate({
        path: 'enrollments.enrollmentId',
        model: 'Enrollment',
        populate: [
          {
            path: 'instructor',
            model: 'CustomForm',
          },
          {
            path: 'location',
            model: 'CustomForm',
          },
          {
            path: 'policyGroup',
            model: 'Policy',
          },
        ],
      })
      .populate({
        path: 'events.eventId',
        model: 'Event',
        populate: [
          {
            path: 'instructor',
            model: 'CustomForm',
          },
          {
            path: 'location',
            model: 'CustomForm',
          },
          {
            path: 'policyGroup',
            model: 'Policy',
          },
        ],
      })
      .exec();
  }
  async fetchStudentsTuitionDetails(studentId: string, status: string) {
    const searchQuery: {
      studentId: Types.ObjectId;
      entityType: string;
      status?: SubscriptionStatus;
    } = {
      studentId: Types.ObjectId.createFromHexString(studentId),
      entityType: 'class',
    };

    if (status) {
      searchQuery.status = status as SubscriptionStatus;
    }

    const subscriptions = await this.subscriptionModel
      .find(searchQuery)
      .populate({
        path: 'entityId',
        model: 'Enrollment',
        select: 'title',
      });

    return subscriptions.map((subscription) => {
      return {
        enrollmentName: (subscription.entityId as any).title,
        enrollmentId: (subscription.entityId as any)._id,
        amount: subscription.baseAmount,
        billingCycle: subscription.billingCycle,
      };
    });
  }

  async updateTuitionFee(studentId: string, tuitionDetails: any) {
    const subscription = await this.subscriptionModel.findOne({
      studentId: Types.ObjectId.createFromHexString(studentId),
      entityType: 'class',
      entityId: Types.ObjectId.createFromHexString(tuitionDetails.enrollmentId),
    });

    // Check if current tuition is greater than 0 and new amount is 0
    if (subscription.baseAmount > 0 && tuitionDetails.amount === 0) {
      throw new Error('Monthly tuition cannot be set to zero');
    }

    // Allow updating from 0 to a valid amount
    if (subscription.baseAmount === 0 && tuitionDetails.amount > 0) {
      // This is valid, continue with the update
    } else if (tuitionDetails.amount === 0) {
      throw new Error('Monthly tuition cannot be set to zero');
    }

    // Get all students under the same parent to determine student position
    const student = await this.studentModel.findById(studentId);
    const allStudents = await this.studentModel
      .find({
        parentId: student.parentId,
        'enrollments.subscriptionStatus': 'active',
      })
      .sort({ 'enrollments.enrolledDate': 1 });

    // Find student position (1-based index)
    const studentPosition =
      allStudents.findIndex((s) => s._id.toString() === studentId) + 1;

    // Get all enrollments for this student to determine class position
    const activeEnrollments = student.enrollments
      .filter((e) => e.subscriptionStatus === 'active')
      .sort(
        (a, b) =>
          new Date(a.enrolledDate).getTime() -
          new Date(b.enrolledDate).getTime(),
      );

    // Find class position (1-based index)
    const classPosition =
      activeEnrollments.findIndex(
        (e) => e.enrollmentId.toString() === tuitionDetails.enrollmentId,
      ) + 1;

    // Calculate multi-class discount
    const multiClassDiscount = await this.discountService.calculateDiscount({
      studioId: subscription.studioId.toString(),
      student: {
        firstName: student.firstName,
        lastName: student.lastName,
        classPosition,
        studentPosition: 0, // Not needed for multi-class
        tuitionFee: tuitionDetails.amount,
        enrollmentId: tuitionDetails.enrollmentId,
      },
      category: 'multi-class',
    });

    // Calculate multi-student discount
    const multiStudentDiscount = await this.discountService.calculateDiscount({
      studioId: subscription.studioId.toString(),
      student: {
        firstName: student.firstName,
        lastName: student.lastName,
        classPosition: 0, // Not needed for multi-student
        studentPosition,
        tuitionFee: tuitionDetails.amount,
        enrollmentId: tuitionDetails.enrollmentId,
      },
      category: 'multi-student',
    });

    // Calculate total discount
    const totalDiscount =
      multiClassDiscount.totalDiscount + multiStudentDiscount.totalDiscount;

    // Create new coupon if discount applies
    let couponId = null;
    if (totalDiscount > 0) {
      const coupon = await this.discountCouponService.createCoupon({
        type: 'fixed',
        value: totalDiscount,
        name: `Fixed Discount of ${totalDiscount}`,
        studioId: subscription.studioId,
        category: 'enrollment',
      });
      couponId = coupon._id;
    }

    // Update subscription with new amounts and coupon
    subscription.baseAmount = tuitionDetails.amount;
    subscription.finalAmount = tuitionDetails.amount - totalDiscount;
    if (!subscription.metadata) subscription.metadata = {};
    subscription.metadata.appliedDiscount = totalDiscount;
    if (couponId) {
      subscription.appliedCouponId = couponId;
    }
    await subscription.save();

    // Update upcoming invoices
    const invoices = await this.subscriptionInvoiceModel.find({
      subscriptionId: subscription._id,
      status: { $in: [InvoiceStatus.UPCOMING, InvoiceStatus.PENDING] },
    });

    await Promise.all(
      invoices.map(async (invoice) => {
        let amountDifference = 0;

        // Update line items where type is 'Tuition Fee'
        const updatedLineItems = invoice.line_items.map((item) => {
          if (item.type === 'Tuition Fee') {
            const oldAmount = item.amount;
            const newAmount = tuitionDetails.amount;
            amountDifference += (newAmount - oldAmount) * item.quantity;

            return {
              ...item,
              amount: newAmount,
              total: newAmount * item.quantity,
            };
          }
          return item;
        });

        const updatedData = {
          baseAmount: invoice.baseAmount + amountDifference,
          finalAmount: invoice.baseAmount + amountDifference - totalDiscount,
          metadata: {
            ...invoice.metadata,
            appliedDiscount: totalDiscount,
          },
          line_items: updatedLineItems,
          updatedAt: new Date(),
        };

        return this.subscriptionInvoiceModel.findByIdAndUpdate(
          invoice._id,
          { $set: updatedData },
          { new: true },
        );
      }),
    );

    // Update scheduled payment transactions
    const childTransactions = await this.paymentTransactionModel.find({
      studentId: Types.ObjectId.createFromHexString(studentId),
      entityType: 'enrollment',
      entityId: Types.ObjectId.createFromHexString(tuitionDetails.enrollmentId),
      status: PaymentTransactionStatus.SCHEDULED,
    });

    await Promise.all(
      childTransactions.map(async (childTransaction) => {
        let amountDifference = 0;

        if (childTransaction.metadata?.line_items) {
          childTransaction.metadata.line_items =
            childTransaction.metadata.line_items.map((item) => {
              if (item.price_data.product_data.description === 'Tuition Fee') {
                const oldAmount = item.price_data.unit_amount;
                const newAmount = tuitionDetails.amount * 100; // Convert to cents
                amountDifference += (newAmount - oldAmount) * item.quantity;

                return {
                  ...item,
                  price_data: {
                    ...item.price_data,
                    unit_amount: newAmount,
                  },
                };
              }
              return item;
            });
        }

        // Adjust the total amount
        childTransaction.amount =
          childTransaction.amount + amountDifference / 100 - totalDiscount;
        await childTransaction.save();
      }),
    );

    return {
      success: true,
      message: 'Tuition fee updated successfully',
    };
  }

  async getStudentOutstandingBalance({
    studioId,
    studentId,
  }: {
    studioId: string;
    studentId: string;
  }) {
    const studio = await this.studioModel.findById(studioId).lean().exec();

    if (!studio) {
      throw new NotFoundException('Studio not found');
    }

    const studioObjectId = new Types.ObjectId(studioId);
    const studentObjectId = new Types.ObjectId(studentId);

    const outstandingBalances = await this.subscriptionInvoiceModel
      .find({
        studioId: studioObjectId,
        studentId: studentObjectId,
        status: {
          $in: [
            InvoiceStatus.PENDING,
            InvoiceStatus.FAILED,
            InvoiceStatus.SCHEDULED,
          ],
        },
      })
      .lean()
      .exec();

    const totalOutstanding = outstandingBalances.reduce((acc, invoice) => {
      return acc + invoice.finalAmount;
    }, 0);

    //max 2 decimal places
    const totalOutstandingFormatted = totalOutstanding.toFixed(2);

    return {
      totalOutstanding: totalOutstandingFormatted,
    };
  }
}
