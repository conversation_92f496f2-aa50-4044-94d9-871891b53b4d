import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
  Put,
  NotFoundException,
  InternalServerErrorException,
  Res,
} from '@nestjs/common';
import { StudentsService } from './students.service';
import { CreateStudentDto } from './dto/create-student.dto';
import {
  UpdateStudentDto,
  UpdateTuitionDetailsDto,
} from './dto/update-student.dto';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { Types } from 'mongoose';
import { UpdateProfilePicDto } from 'src/parents/dto/updateProfilePic.dto';
import { MarkAttendanceDto } from './dto/studentBasicInfoUpdateDto';
import { request, Response } from 'express';

@Controller('students')
export class StudentsController {
  constructor(private readonly studentsService: StudentsService) {}

  @Post()
  create(@Body() createStudentDto: CreateStudentDto) {
    return this.studentsService.create(createStudentDto);
  }

  @Post('with-location')
  @UseGuards(JwtAuthGuard)
  createWithLocationId(
    @Body() createStudentDto: CreateStudentDto,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return this.studentsService.createWithLocationId(
      createStudentDto,
      locationId,
    );
  }

  @Get()
  findAll() {
    return this.studentsService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.studentsService.findOne(id);
  }

  @Get(':id/details')
  findStudentDetails(@Param('id') id: string) {
    return this.studentsService.findStudentDetails(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateStudentDto: UpdateStudentDto) {
    return this.studentsService.update(id, updateStudentDto);
  }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.studentsService.remove(id);
  // }

  @Get('/:parentId/relations')
  async getStudentsRelationsByStudentId(@Param('parentId') parentId: string) {
    return await this.studentsService.getStudentsRelationsByStudentId(parentId);
  }

  @Get('/studio/all')
  @UseGuards(JwtAuthGuard)
  async getStudentsBystudioId(@Req() request: Request) {
    const locationId = request['locationId'];
    return await this.studentsService.getStudentsBystudioId(locationId);
  }

  @Get('/studio/all/csv')
  @UseGuards(JwtAuthGuard)
  async getStudentsBystudioIdCsv(@Req() request: Request) {
    const locationId = request['locationId'];
    return await this.studentsService.getStudentsBystudioIdCsv(locationId);
  }

  @Get('/class/studio/all')
  @UseGuards(JwtAuthGuard)
  async getStudentsByFilters(
    @Req() request: Request,
    @Query('enrollmentId') enrollmentId: string,
    @Query('date') date: string,
  ) {
    const locationId = request['locationId'];
    // const studio = new Types.ObjectId(locationId);
    // const enrollmentObjectId = new Types.ObjectId(enrollmentId);
    console.log(date);
    return await this.studentsService.getStudentsByFilters(
      locationId,
      enrollmentId,
      date,
    );
  }

  @Get('/class/studio/all/active')
  @UseGuards(JwtAuthGuard)
  async getStudentsActiveClassByFilters(
    @Req() request: Request,
    @Query('enrollmentId') enrollmentId: string,
    @Query('date') date: string,
  ) {
    const locationId = request['locationId'];
    // const studio = new Types.ObjectId(locationId);
    // const enrollmentObjectId = new Types.ObjectId(enrollmentId);
    console.log(date);
    return await this.studentsService.getStudentsActiveClassByFilters(
      locationId,
      enrollmentId,
      date,
    );
  }

  @Put(':studentId/picture')
  async updateProfilePic(
    @Param('studentId') studentId: string,
    @Body() updateProfilePicDto: UpdateProfilePicDto,
  ): Promise<string> {
    return this.studentsService.uploadProfilePic(
      studentId,
      updateProfilePicDto,
    );
  }

  @Get(':classId/class')
  @UseGuards(JwtAuthGuard)
  async getStudentsByEnrollmentId(
    @Param('classId') classId: string,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return await this.studentsService.getStudentsByEnrollmentId(
      locationId,
      classId,
    );
  }

  @Get(':classId/class/active')
  @UseGuards(JwtAuthGuard)
  async getStudentsByEnrollmentIdActive(
    @Param('classId') classId: string,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return await this.studentsService.getStudentsByEnrollmentIdActive(
      locationId,
      classId,
    );
  }

  @Get(':eventId/event')
  @UseGuards(JwtAuthGuard)
  async getStudentsByEventId(
    @Param('eventId') eventId: string,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return await this.studentsService.getStudentsByEventId(locationId, eventId);
  }

  @Get(':eventId/event/active')
  @UseGuards(JwtAuthGuard)
  async getStudentsByEventIdActive(
    @Param('eventId') eventId: string,
    @Req() request: Request,
  ) {
    return await this.studentsService.getActiveStudentsByEventId(eventId);
  }

  @Get('search/student')
  @UseGuards(JwtAuthGuard)
  async searchStudents(
    @Req() request: Request,
    @Query('name') name?: string,
    @Query('familyName') familyName?: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    const locationId = request['locationId'];
    return this.studentsService.searchByProperties(
      {
        name,
        familyName,
      },
      locationId,
      {
        page: Number(page),
        limit: Number(limit),
      },
    );
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  remove(@Param('id') id: string, @Req() request: Request) {
    const studioId = request['locationId'];
    return this.studentsService.remove(id, studioId);
  }

  @Delete('batch/all')
  @UseGuards(JwtAuthGuard)
  removeBatch(@Body() ids: string[], @Req() request: Request) {
    const studioId = request['locationId'];
    return this.studentsService.removeBatch(ids, studioId);
  }

  @Get('count/enrollment/:enrollmentId')
  @UseGuards(JwtAuthGuard)
  async getEnrollmentCount(
    @Param('enrollmentId') enrollmentId: string,
    @Req() request: Request,
  ) {
    const studioId = request['locationId'];
    return this.studentsService.getEnrollmentCount(enrollmentId, studioId);
  }

  @Get('count/event/:eventId')
  @UseGuards(JwtAuthGuard)
  async getEventCount(
    @Param('eventId') eventId: string,
    @Req() request: Request,
  ) {
    const studioId = request['locationId'];
    return this.studentsService.getEventCount(eventId, studioId);
  }

  @Get('all/:parentId')
  @UseGuards(JwtAuthGuard)
  async getStudentsByParentId(
    @Param('parentId') parentId: string,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return await this.studentsService.getStudentsByParentId(parentId);
  }

  @Get('count/active-students/parent')
  @UseGuards(JwtAuthGuard)
  async getStudentCountByParentId(
    @Query('parentId') parentId: string,
  ): Promise<{ count: number; studentIds: string[] }> {
    return this.studentsService.getStudentCountByParentId(parentId);
  }

  @Post('mark-attendance')
  @UseGuards(JwtAuthGuard)
  async markAttendance(
    @Body() markAttendanceDto: MarkAttendanceDto,
    @Req() request: Request,
  ) {
    return await this.studentsService.markAttendance(markAttendanceDto);
  }

  /** ##################################################Class Attendance  Analtics API#################################################*/
  /**
   *
   * for all class i need to get from enrollment table
   * GET API -enrollments/search/class
   * Total student Get from STUdent tables
   */

  // @Get('/attendance/analytics/:enrollmentId')
  // async getAttendanceAnalytics(@Param('enrollmentId') enrollmentId: string, @Query('page') page?: string, @Query('limit') limit?: string): Promise<any> {
  //   const pageNum = page ? parseInt(page, 10) : 1;
  //   const limitNum = limit ? parseInt(limit, 10) : 10;

  //   const result = await Promise.all([
  //     this.studentsService.getAttendanceAnalytics(enrollmentId,pageNum, limitNum),
  //     // this.studentsService.getStudentPerformance(enrollmentId, pageNum, limitNum),
  //     // this.studentsService.getClassSessions(enrollmentId, pageNum, limitNum),
  //     this.studentsService.getClassCountInDateRange(enrollmentId),
  //   ]);
  //   console.log(result[0]);
  //    const resData= {
  //     "resData": {
  //       "totalStudents": 0,
  //       "averageAttendance": "0%",
  //       "classesThisMonth": {
  //           "totalEnrolledStudents": 0,
  //           "classDuration": 0,
  //           "totalClasses": 0,
  //           "aggregateData": []
  //       },
  //       "classDuration": "0 min",
  //       "RecentattendanceRecords": [],
  //       "studentPerformance": {
  //           "data": [],
  //           "distribution": {
  //               "Excellent (90-100%)": 0,
  //               "Good (75-89%)": 0,
  //               "Average (60-74%)": 0,
  //               "Needs Improvement (<60%)": 0
  //           },
  //           "sessioData": [],
  //           "pagination": {
  //               "pages": null
  //           }
  //       }
  //     }
  //   }
  //   if (!result || !result[0] || !result[0].aggregateData || !result[0].aggregateData.length) {
  //     return resData
  //   }

  //   // Group attendance records by date
  //   const groupedAttendance = result[0].aggregateData[0].attendanceByDate.reduce((acc: any, curr: any) => {
  //     const dateKey = new Date(curr.date).toLocaleDateString('en-US', {
  //       weekday: 'short',
  //       month: 'short',
  //       day: 'numeric',
  //       year: 'numeric'
  //     });

  //     if (!acc[dateKey]) {
  //       acc[dateKey] = { totalPresent: 0 };
  //     }
  //     acc[dateKey].totalPresent += curr.totalPresent;
  //     return acc;
  //   }, {});

  //   // Calculate classes for last 3 months
  //   // const now = new Date();
  //   // const threeMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate());

  //   // const classesInLast3Months = result[2].classSessions.filter(session => {
  //   //   const sessionDate = new Date(session.date);
  //   //   return sessionDate >= threeMonthsAgo && sessionDate <= now;
  //   // }).length;

  //   // const data = {
  //   //   resData: {
  //   //     totalStudents: result[0].totalEnrolledStudents,
  //   //     averageAttendance: `${Math.round(result[0].aggregateData[0].attendanceRate)}%`,
  //   //     classesThisMonth: result[0],
  //   //     classDuration: `${result[0].classDuration} min`,
  //   //     RecentattendanceRecords: Object.entries(groupedAttendance).map(([date, record]: [string, any]) => ({
  //   //       date,
  //   //       studentPresent: `${record.totalPresent}/${result[0].totalEnrolledStudents}`,
  //   //       attendanceRate: `${Math.round((record.totalPresent / result[0].totalEnrolledStudents) * 100)}%`
  //   //     })),
  //   //     studentPerformance: {
  //   //       data: result[1].studentPerformance.map(student => ({
  //   //         name: student.name,
  //   //         classesAttended: student.classesAttended,
  //   //         status: student.status,
  //   //         classesMarked: 0,
  //   //         attendancePercentage: student.attendancePercentage
  //   //       })),
  //   //       distribution: {
  //   //         "Excellent (90-100%)": result[1].studentDistribution["Excellent (90-100%)"] || 0,
  //   //         "Good (75-89%)": result[1].studentDistribution["Good (75-89%)"] || 0,
  //   //         "Average (60-74%)": result[1].studentDistribution["Average (60-74%)"] || 0,
  //   //         "Needs Improvement (<60%)": result[1].studentDistribution["Needs Improvement (<60%)"] || 0
  //   //       },
  //   //       sessioData: result[2].classSessions.map(session => ({
  //   //         date: session.date,
  //   //         time: `${session.time || '2:00 AM'}`, // Just use the time from DB without modification
  //   //         room: session.room,
  //   //         instructor: session.instructor,
  //   //         status: session.status
  //   //       })),
  //   //       pagination: {
  //   //         total: result[1].total,
  //   //         pages: Math.ceil(result[1].total / limitNum)
  //   //       }
  //   //     }
  //   //   }
  //   // };
  //   const data = {
  //     resData: {
  //       totalStudents: result[0].totalEnrolledStudents,
  //       averageAttendance: `${Math.round(result[0].aggregateData[0].attendanceRate)}%`,
  //       totalClasses: result[0].totalClasses || 0, // Make sure this field exists in your result
  //       RecentattendanceRecords: Object.entries(groupedAttendance)
  //         .map(([date, record]: [string, any]) => ({
  //           date: new Date(date).toLocaleDateString('en-US', {
  //             weekday: 'short',
  //             month: 'short',
  //             day: 'numeric',
  //             year: 'numeric'
  //           }),
  //           studentPresent: `${record.totalPresent}/${result[0].totalEnrolledStudents}`,
  //           attendanceRate: `${Math.round((record.totalPresent / result[0].totalEnrolledStudents) * 100)}%`
  //         }))
  //         .slice(0, 5), // Only take the 5 most recent records
  //       pagination: {
  //         // totalPages: Math.ceil(result[1].total / limitNum),
  //         // totalItems: result[1].total
  //       }
  //     }
  //   };

  //   return data;
  // }
  @UseGuards(JwtAuthGuard)
  @Get('/attendance/analytics/:enrollmentId')
  async getAttendanceAnalytics(
    @Param('enrollmentId') enrollmentId: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ): Promise<any> {
    try {
      const pageNum = page ? parseInt(page, 10) : 1;
      const limitNum = limit ? parseInt(limit, 10) : 10;
      //         const totalEnrolledStudents = await this.studentsService.getSudentDaat(enrollmentId);
      //         if (totalEnrolledStudents === 0) {
      //           return {
      //             "resData": {
      //       "totalStudents": 0,
      //       "averageAttendance": "0%",
      //       "totalClasses": 0,
      //       "RecentattendanceRecords": [],
      //       "pagination": {
      //         "totalPages": 0,
      //         "totalItems": 0
      //     }
      // }
      //           }
      //       }

      const [analyticsData, classCount, classDuration] = await Promise.all([
        this.studentsService.getAttendanceAnalytics(
          enrollmentId,
          pageNum,
          limitNum,
        ),
        this.studentsService.getClassCountInDateRange(enrollmentId),
        this.studentsService.getClassduration(enrollmentId),
      ]);
      //  console.log("testst",classCount,classDuration);

      const data = {
        resData: {
          totalStudents: analyticsData.resData.totalStudents,
          averageAttendance: analyticsData.resData.averageAttendance,
          totalClasses: classCount.totalClasses,
          classDuration: classDuration,
          RecentattendanceRecords:
            analyticsData.resData.RecentattendanceRecords,
          pagination: {
            totalPages: analyticsData.resData.pagination.totalPages,
            totalItems: analyticsData.resData.pagination.totalItems,
          },
        },
      };

      return data;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new NotFoundException(error.message);
      }
      throw new NotFoundException(
        `Enrollment with ID ${enrollmentId} not found`,
      );
    }
  }
  @UseGuards(JwtAuthGuard)
  @Get('/attendance/performance/:enrollmentId')
  async getStudentPerformance(
    @Param('enrollmentId') enrollmentId: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ): Promise<any> {
    console.log('Requested student performance for:', enrollmentId);
    const pageNum = page ? parseInt(page, 10) : 1;
    const limitNum = limit ? parseInt(limit, 10) : 10;
    const result = await this.studentsService.getStudentPerformance(
      enrollmentId,
      pageNum,
      limitNum,
    );

    if (
      !result ||
      !result.studentPerformance ||
      result.studentPerformance.length === 0
    ) {
      return {
        studentPerformance: [],
        studentDistribution: {
          'Excellent (90-100%)': 0,
          'Good (75-89%)': 0,
          'Average (60-74%)': 0,
          'Needs Improvement (<60%)': 0,
        },
        pagination: {
          currentPage: 1,
          totalPages: 0,
          totalItems: 0,
          itemsPerPage: 10,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      };
    }
    return result;
  }

  //  @Get('/attendance/sessions/:enrollmentId')
  //  async getClassSessions(
  //    @Param('enrollmentId') enrollmentId: string,
  //    @Query('page') page?: string,
  //    @Query('limit') limit?: string
  //  ): Promise<any> {
  //    try{
  //    const pageNum = page ? parseInt(page, 10) : 1;
  //    const limitNum = limit ? parseInt(limit, 10) : 10;

  //    const result = await this.studentsService.getClassSessions(enrollmentId, pageNum, limitNum);
  //    if (!result) {
  //     return {
  //       data: {
  //         classSessions: [],
  //         pagination: { total: 0, pages: 0 }
  //       }
  //     };
  //   }

  //    return {
  //      data: {
  //        classSessions: result.classSessions.map(session => ({
  //          date: session.date,
  //          startTime: session.time || '11:30 PM',
  //          endTime: session.time ? addOneHour(session.time) : '12:30 AM',
  //          room: session.room,
  //          instructor: session.instructor,
  //          status: session.status
  //        })),
  //        pagination: {
  //          total: result.total,
  //          pages: Math.ceil(result.total / limitNum)
  //        }
  //      }
  //    };
  //  } catch (error) {
  //   console.error('Error fetching class sessions:', error);
  //   throw new InternalServerErrorException('Failed to fetch class sessions');
  // }
  //  }
  // @Get('/attendance/sessions/:enrollmentId')
  // async getClassSessions(
  //   @Param('enrollmentId') enrollmentId: string,
  //   @Query('page') page?: string,
  //   @Query('limit') limit?: string
  // ): Promise<any> {
  //   try {
  //     const pageNum = page ? parseInt(page, 10) : 1;
  //     const limitNum = limit ? parseInt(limit, 10) : 10;

  //     const result = await this.studentsService.getClassSessions(enrollmentId, pageNum, limitNum);
  //     if (!result) {
  //       return {
  //         data: {
  //           classSessions: [],
  //           pagination: { total: 0, pages: 0 }
  //         }
  //       };
  //     }

  //     return {
  //       data: {
  //         classSessions: result.classSessions,
  //         pagination: {
  //           total: result.total,
  //           pages: Math.ceil(result.total / limitNum)
  //         }
  //       }
  //     };
  //   } catch (error) {
  //     console.error('Error fetching class sessions:', error);
  //     throw new InternalServerErrorException('Failed to fetch class sessions');
  //   }
  // }
  @UseGuards(JwtAuthGuard)
  @Get(':studentId/attendance/filter/:classId')
  async getStudentAttendanceFilter(
    @Param('studentId') studentId: string,
    @Param('classId') classId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return await this.studentsService.getStudentAttendanceRecordsfilter(
      studentId,
      classId,
      startDate,
      endDate,
    );
  }

  // @UseGuards(JwtAuthGuard)
  @Get('/attendance/sessions/:enrollmentId')
  async getClassSessions(
    @Param('enrollmentId') enrollmentId: string,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ): Promise<any> {
    try {
      const pageNum = page ? parseInt(page, 10) : 1;
      const limitNum = limit ? parseInt(limit, 10) : 10;

      const result = await this.studentsService.getClassSessions(
        enrollmentId,
        pageNum,
        limitNum,
      );
      if (!result) {
        return {
          data: {
            classSessions: [],
            pagination: {
              currentPage: 1,
              totalPages: 0,
              totalItems: 0,
              itemsPerPage: limitNum,
              hasNextPage: false,
              hasPreviousPage: false,
            },
          },
        };
      }

      return {
        data: {
          classSessions: result.classSessions,
          pagination: {
            currentPage: result.pagination.currentPage,
            totalPages: result.pagination.totalPages,
            totalItems: result.pagination.totalItems,
            itemsPerPage: result.pagination.itemsPerPage,
            hasNextPage: result.pagination.hasNextPage,
            hasPreviousPage: result.pagination.hasPreviousPage,
          },
        },
      };
    } catch (error) {
      console.error('Error fetching class sessions:', error);
      throw new InternalServerErrorException('Failed to fetch class sessions');
    }
  }
  @Get('/class/count/:enrollmentId')
  @UseGuards(JwtAuthGuard)
  async getClassCountInDateRange(@Param('enrollmentId') enrollmentId: string) {
    return await this.studentsService.getClassCountInDateRange(enrollmentId);
  }

  @Get(':studentId/attendance/:classId')
  @UseGuards(JwtAuthGuard)
  async getStudentAttendanceRecords(
    @Param('studentId') studentId: string,
    @Param('classId') classId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    return await this.studentsService.getStudentAttendanceRecords(
      studentId,
      classId,
      page,
      limit,
    );
  }
  // @UseGuards(JwtAuthGuard)
  @Get('schedule/report')
  async generateScheduleReport(
    @Query('parentId') parentId: string,
    @Query('studioId') studioId: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('format') format?: string,
    // @Res() res?: Response,
  ) {
    const scheduleData = await this.studentsService.generateScheduleReport(
      parentId,
      studioId,
      startDate,
      endDate,
    );
    return scheduleData;
    // if (format === 'pdf') {
    //   const pdfBuffer =
    //     await this.studentsService.generateSchedulePDF(scheduleData);

    //   res.set({
    //     'Content-Type': 'application/pdf',
    //     'Content-Disposition': 'attachment; filename=schedule.pdf',
    //     'Content-Length': pdfBuffer.length,
    //   });

    //   res.end(pdfBuffer);
    // } else {
    //   return scheduleData;
    // }
  }

  @Get(':studentId/tuition-details')
  async fetchStudentsTuitionDetails(
    @Param('studentId') studentId: string,
    @Query('status') status: string = null,
  ) {
    return await this.studentsService.fetchStudentsTuitionDetails(
      studentId,
      status,
    );
  }

  @Put(':studentId/tuition-fee')
  async updateTuitionFee(
    @Param('studentId') studentId: string,
    @Body() tuitionDetails: UpdateTuitionDetailsDto,
  ) {
    return await this.studentsService.updateTuitionFee(
      studentId,
      tuitionDetails,
    );
  }

  @Get(':studentId/outstanding-balance')
  @UseGuards(JwtAuthGuard)
  async getStudentOutstandingBalance(
    @Param('studentId') studentId: string,
    @Req() request: Request,
  ) {
    const studioId = request['locationId'];
    return await this.studentsService.getStudentOutstandingBalance({
      studentId,
      studioId,
    });
  }
}
