import { forwardRef, Module } from '@nestjs/common';
import { StudentsService } from './students.service';
import { StudentsController } from './students.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Student, StudentSchema } from 'src/database/schema/student';
import { StudiosModule } from 'src/studios/studios.module';
import { JwtModule } from '@nestjs/jwt';
import { GcpStorageModule } from 'src/gcp-storage/gcp-storage.module';
import { EnrollmentSchema } from 'src/database/schema/enrollment';
import { Enrollment } from 'src/database/schema/enrollment';
import { EnrollmentHistoryModule } from 'src/enrollment-history/enrollment-history.module';
import { Invoice } from 'src/database/schema/invoice';
import { InvoiceSchema } from 'src/database/schema/invoice';
import { Parent, ParentSchema } from 'src/database/schema/parent';
import { Attendance, AttendanceSchema } from 'src/database/schema/attendance';
import { Studio, StudioSchema } from 'src/database/schema/studio';
import { CustomForm, CustomFormSchema } from 'src/database/schema/customForm';
import { TriggersModule } from 'src/triggers/triggers.module';
import {
  Subscription,
  SubscriptionSchema,
} from 'src/database/schema/subscription';
import { SubscriptionInvoiceSchema } from 'src/database/schema/subscriptionInvoice';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import {
  PaymentTransaction,
  PaymentTransactionSchema,
} from 'src/database/schema/paymentTransaction';
import { DiscountModule } from 'src/discount/discount.module';
import { DiscountCouponModule } from 'src/discount-coupon/discount-coupon.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Student.name, schema: StudentSchema },
      { name: Enrollment.name, schema: EnrollmentSchema },
      { name: Invoice.name, schema: InvoiceSchema },
      { name: Parent.name, schema: ParentSchema },
      { name: Attendance.name, schema: AttendanceSchema },
      { name: Studio.name, schema: StudioSchema },
      { name: CustomForm.name, schema: CustomFormSchema },
      { name: Subscription.name, schema: SubscriptionSchema },
      {
        name: SubscriptionInvoice.name,
        schema: SubscriptionInvoiceSchema,
      },
      { name: PaymentTransaction.name, schema: PaymentTransactionSchema },
    ]),
    forwardRef(() => StudiosModule),
    JwtModule,
    GcpStorageModule,
    EnrollmentHistoryModule,
    TriggersModule,
    DiscountModule,
    DiscountCouponModule,
  ],
  controllers: [StudentsController],
  providers: [StudentsService],
  exports: [StudentsService],
})
export class StudentsModule {}
