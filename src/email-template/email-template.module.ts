import { forwardRef, Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { EmailTemplateService } from './email-template.service';
import { EmailTemplateController } from './email-template.controller';
import {
  EmailTemplate,
  EmailTemplateSchema,
} from 'src/database/schema/emailTemplate';
import { JwtModule } from '@nestjs/jwt';
import { GcpStorageModule } from 'src/gcp-storage/gcp-storage.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: EmailTemplate.name, schema: EmailTemplateSchema },
    ]),
    JwtModule,
    forwardRef(() => GcpStorageModule),
  ],
  controllers: [EmailTemplateController],
  providers: [EmailTemplateService],
  exports: [EmailTemplateService],
})
export class EmailTemplateModule {}
