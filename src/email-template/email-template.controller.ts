import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  UseGuards,
  Req,
  UseInterceptors,
  UploadedFiles,
} from '@nestjs/common';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { EmailTemplateService } from './email-template.service';
import { CreateEmailTemplateDto } from './dto/create-email-template.dto';
import { UpdateEmailTemplateDto } from './dto/update-email-template.dto';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { Types } from 'mongoose';

@UseGuards(JwtAuthGuard)
@Controller('email-template')
export class EmailTemplateController {
  constructor(private readonly emailTemplateService: EmailTemplateService) {}

  @Post()
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'paragraph1Image', maxCount: 1 },
      { name: 'paragraph2Image', maxCount: 1 },
    ]),
  )
  create(
    @Body() createEmailTemplateDto: CreateEmailTemplateDto,
    @Req() request: Request,
    @UploadedFiles()
    files: {
      paragraph1Image?: Express.Multer.File[];
      paragraph2Image?: Express.Multer.File[];
    },
  ) {
    const studioId_string = request['locationId'];
    const studioId = Types.ObjectId.createFromHexString(studioId_string);
    return this.emailTemplateService.create(
      createEmailTemplateDto,
      studioId,
      files,
    );
  }

  @Get()
  findOne(@Req() request: Request) {
    const studioId_string = request['locationId'];
    const studioId = Types.ObjectId.createFromHexString(studioId_string);
    return this.emailTemplateService.findByStudioId(studioId);
  }

  @Patch()
  @UseInterceptors(
    FileFieldsInterceptor([
      { name: 'paragraph1Image', maxCount: 1 },
      { name: 'paragraph2Image', maxCount: 1 },
    ]),
  )
  update(
    @Body() updateEmailTemplateDto: UpdateEmailTemplateDto,
    @Req() request: Request,
    @UploadedFiles()
    files: {
      paragraph1Image?: Express.Multer.File[];
      paragraph2Image?: Express.Multer.File[];
    },
  ) {
    const studioId_string = request['locationId'];
    const studioId = Types.ObjectId.createFromHexString(studioId_string);
    return this.emailTemplateService.update(
      updateEmailTemplateDto,
      studioId,
      files,
    );
  }
}
