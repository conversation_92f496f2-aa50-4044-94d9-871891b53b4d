import {
  forwardRef,
  Inject,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CreateEmailTemplateDto } from './dto/create-email-template.dto';
import { UpdateEmailTemplateDto } from './dto/update-email-template.dto';
import {
  EmailTemplate,
  EmailTemplateDocument,
} from 'src/database/schema/emailTemplate';
import { GcpStorageService } from 'src/gcp-storage/gcp-storage.service';

@Injectable()
export class EmailTemplateService {
  constructor(
    @InjectModel(EmailTemplate.name)
    private emailTemplateModel: Model<EmailTemplateDocument>,
    @Inject(forwardRef(() => GcpStorageService))
    private readonly gcpStorageService: GcpStorageService,
  ) {}

  async create(
    createEmailTemplateDto: CreateEmailTemplateDto,
    studioId: Types.ObjectId,
    files?: {
      paragraph1Image?: Express.Multer.File[];
      paragraph2Image?: Express.Multer.File[];
    },
  ): Promise<EmailTemplate> {
    try {
      // Handle file uploads if they exist
      let paragraph1ImageUrl: string | undefined;
      let paragraph2ImageUrl: string | undefined;

      if (files) {
        if (files.paragraph1Image?.[0]) {
          paragraph1ImageUrl = await this.gcpStorageService.uploadFileToGCP(
            undefined,
            files.paragraph1Image[0].buffer,
            files.paragraph1Image[0].originalname,
            studioId.toString(),
            'email-template',
          );
        }

        if (files.paragraph2Image?.[0]) {
          paragraph2ImageUrl = await this.gcpStorageService.uploadFileToGCP(
            undefined,
            files.paragraph2Image[0].buffer,
            files.paragraph2Image[0].originalname,
            studioId.toString(),
            'email-template',
          );
        }

        const createdEmailTemplate = new this.emailTemplateModel({
          ...createEmailTemplateDto,
          studioId,
          paragraph1ImageName: files?.paragraph1Image?.[0]?.originalname,
          paragraph2ImageName: files?.paragraph2Image?.[0]?.originalname,
        });

        return createdEmailTemplate.save();
      }

      const createdEmailTemplate = new this.emailTemplateModel({
        ...createEmailTemplateDto,
        studioId,
      });

      return createdEmailTemplate.save();
    } catch (error) {
      throw new InternalServerErrorException(
        'Error creating email template',
        error,
      );
    }
  }

  async findAll(studioId: Types.ObjectId): Promise<EmailTemplate[]> {
    return this.emailTemplateModel.find({ studioId }).exec();
  }

  async findByStudioId(studioId: Types.ObjectId): Promise<any> {
    const emailTemplate = await this.emailTemplateModel
      .findOne({
        studioId,
      })
      .exec();

    if (emailTemplate) {
      const paragraph1ImageUrl = emailTemplate.paragraph1ImageName
        ? await this.gcpStorageService.getEmailTemplateImageUrl(
            studioId.toString(),
            emailTemplate.paragraph1ImageName,
          )
        : undefined;

      const paragraph2ImageUrl = emailTemplate.paragraph2ImageName
        ? await this.gcpStorageService.getEmailTemplateImageUrl(
            studioId.toString(),
            emailTemplate.paragraph2ImageName,
          )
        : undefined;

      return {
        ...emailTemplate.toObject(),
        paragraph1: emailTemplate.paragraph1.replace(
          /{{ paragraph1Image }}/g,
          `${paragraph1ImageUrl}`,
        ),
        paragraph1ImageUrl: paragraph1ImageUrl,
        paragraph2: emailTemplate.paragraph2.replace(
          /{{ paragraph2Image }}/g,
          `${paragraph2ImageUrl}`,
        ),
        paragraph2ImageUrl: paragraph2ImageUrl,
      };
    }

    return emailTemplate;
  }

  async update(
    updateEmailTemplateDto: UpdateEmailTemplateDto,
    studioId: Types.ObjectId,
    files: {
      paragraph1Image?: Express.Multer.File[];
      paragraph2Image?: Express.Multer.File[];
    },
  ): Promise<EmailTemplate> {
    // First, get the existing template to check current images
    const existingTemplate = await this.emailTemplateModel
      .findOne({ studioId })
      .exec();

    if (!existingTemplate) {
      throw new NotFoundException(`Email template not found`);
    }

    const paragraph1ContainsImage = updateEmailTemplateDto.paragraph1?.includes(
      '{{ paragraph1Image }}',
    );
    const paragraph2ContainsImage = updateEmailTemplateDto.paragraph2?.includes(
      '{{ paragraph2Image }}',
    );

    // Handle paragraph1Image
    let paragraph1ImageUrl: string | undefined;
    if (files?.paragraph1Image?.[0]) {
      // Upload new image
      paragraph1ImageUrl = await this.gcpStorageService.uploadFileToGCP(
        undefined,
        files.paragraph1Image[0].buffer,
        files.paragraph1Image[0].originalname,
        studioId.toString(),
        'email-template',
      );
    } else if (
      existingTemplate.paragraph1ImageName &&
      !files?.paragraph1Image &&
      !paragraph1ContainsImage
    ) {
      // Delete existing image if it exists and no new image is provided
      await this.gcpStorageService.deleteFileFromGCP(
        studioId.toString(),
        'email-template',
        existingTemplate.paragraph1ImageName,
      );
    }

    // Handle paragraph2Image
    let paragraph2ImageUrl: string | undefined;
    if (files?.paragraph2Image?.[0]) {
      // Upload new image
      paragraph2ImageUrl = await this.gcpStorageService.uploadFileToGCP(
        undefined,
        files.paragraph2Image[0].buffer,
        files.paragraph2Image[0].originalname,
        studioId.toString(),
        'email-template',
      );
    } else if (
      existingTemplate.paragraph2ImageName &&
      !files?.paragraph2Image &&
      !paragraph2ContainsImage
    ) {
      // Delete existing image if it exists and no new image is provided
      await this.gcpStorageService.deleteFileFromGCP(
        studioId.toString(),
        'email-template',
        existingTemplate.paragraph2ImageName,
      );
    }

    // Only include image URLs in the update if new files were uploaded
    const updateData = {
      ...updateEmailTemplateDto,
      paragraph1ImageName: paragraph1ContainsImage
        ? files?.paragraph1Image?.[0]?.originalname ||
          existingTemplate.paragraph1ImageName
        : null,
      paragraph2ImageName: paragraph2ContainsImage
        ? files?.paragraph2Image?.[0]?.originalname ||
          existingTemplate.paragraph2ImageName
        : null,
    };

    const updatedEmailTemplate = await this.emailTemplateModel
      .findOneAndUpdate({ studioId }, updateData, {
        new: true,
      })
      .exec();

    if (!updatedEmailTemplate) {
      throw new NotFoundException(`Email template not found`);
    }
    return updatedEmailTemplate;
  }
}
