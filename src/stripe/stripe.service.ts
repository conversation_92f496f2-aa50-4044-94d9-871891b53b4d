import { StripeAddStudentToEntityService } from './services/add-student-to-entity.service';
import { StripeCancelSubscriptionByStudentService } from './services/cancel-subscription-by-student.service';
import { StripeCardsService } from './services/cards.service';
import { StripeCustomerService } from './services/customers.service';
import { StripeCouponService } from './services/stripe-coupon.service';
import { StripeCheckoutService } from './services/parent-checkout.service';
import { StripeProductCheckoutService } from './services/product-checkout.service';
import { StripeHistoryService } from './services/remove-and-create-history.service';
import { StripeTransferSubscriptionService } from './services/transfer-subscription.service';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { StudiosService } from 'src/studios/studios.service';
import { StripeCaptureManualPaymentService } from './services/capture-manual-payment';
import { StripeBulkPaymentsService } from './services/bulk-payments';
import { StripeRefundPaymentsService } from './services/refund-payments';
import { StripeCommonService } from './services/stripe-common.service';
import { PaymentMethod } from './type';
import { StripeWalletService } from './services/wallet.service';
import { ConfigService } from '@nestjs/config';
import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import Stripe from 'stripe';
import { Credential } from 'src/database/schema/stripeCredential';

/**
 * Facade service that provides a unified interface to all Stripe-related services
 *
 * This service acts as a central access point for all Stripe functionality,
 * delegating to specialized services while providing a simplified API.
 */
@Injectable()
export class StripeService {
  private readonly logger = new Logger(StripeService.name);

  constructor(
    @Inject(forwardRef(() => StripeAddStudentToEntityService))
    public readonly AddStudentToEntity: StripeAddStudentToEntityService,

    @Inject(forwardRef(() => StripeCancelSubscriptionByStudentService))
    public readonly cancelSubscription: StripeCancelSubscriptionByStudentService,

    @Inject(forwardRef(() => StripeCardsService))
    public readonly cards: StripeCardsService,

    @Inject(forwardRef(() => StripeCustomerService))
    public readonly customers: StripeCustomerService,

    @Inject(forwardRef(() => StripeCouponService))
    public readonly coupons: StripeCouponService,

    @Inject(forwardRef(() => StripeCheckoutService))
    public readonly checkout: StripeCheckoutService,

    @Inject(forwardRef(() => StripeProductCheckoutService))
    public readonly productCheckout: StripeProductCheckoutService,

    @Inject(forwardRef(() => StripeHistoryService))
    public readonly history: StripeHistoryService,

    @Inject(forwardRef(() => StripeTransferSubscriptionService))
    public readonly stripeTransferSubscription: StripeTransferSubscriptionService,

    @InjectModel(Credential.name) private credentialModel: Model<Credential>,
    @Inject(forwardRef(() => StudiosService))
    private readonly studioService: StudiosService,

    @Inject(forwardRef(() => StripeCaptureManualPaymentService))
    public readonly stripeCaptureManualPayment: StripeCaptureManualPaymentService,

    @Inject(forwardRef(() => StripeBulkPaymentsService))
    public readonly stripeBulkPayments: StripeBulkPaymentsService,

    @Inject(forwardRef(() => StripeRefundPaymentsService))
    public readonly stripeRefundPayments: StripeRefundPaymentsService,

    @Inject(forwardRef(() => StripeCommonService))
    public readonly stripeCommonService: StripeCommonService,

    @Inject(forwardRef(() => StripeWalletService))
    public readonly stripeWalletService: StripeWalletService,

    private readonly configService: ConfigService,
  ) {}

  // CUSTOMER MANAGEMENT

  /**
   * Creates a new Stripe customer for a studio
   *
   * @param studioId - ID of the studio
   * @param email - Customer email address
   * @param firstName - Customer first name
   * @param lastName - Customer last name
   * @returns Stripe customer object
   */
  async createCustomer(
    studioId: string,
    email: string,
    firstName: string,
    lastName: string,
  ) {
    return this.customers.createCustomer(studioId, email, firstName, lastName);
  }

  async createOrFetchCustomer(
    studioId: string,
    email: string,
    firstName: string,
    lastName: string,
  ) {
    return this.customers.createOrFetchCustomer(
      studioId,
      email,
      firstName,
      lastName,
    );
  }

  // PAYMENT METHODS

  /**
   * Retrieves all payment cards for a customer
   *
   * @param email - Customer email address
   * @param studioId - ID of the studio
   * @returns Array of payment method objects
   */
  async getCardsByEmail(email: string, studioId: string) {
    return this.cards.getCardsByEmail(email, studioId);
  }

  async generateUpdateCardLink(email: string, studioId: string) {
    return this.cards.generateUpdateCardLink(email, studioId);
  }

  // ENROLLMENT & SUBSCRIPTION MANAGEMENT

  /**
   * Adds a student to a class or event and processes payment
   *
   * @param params - Object containing enrollment details
   * @returns Enrollment result with payment information
   */
  async addStudentToEntity(body: any) {
    return this.AddStudentToEntity.addStudentToEntity(body);
  }

  /**
   * Cancels a student's subscription to a class
   *
   * @param classId - ID of the class
   * @param locationId - ID of the studio location
   * @param studentId - ID of the student
   * @param skipEmail - Whether to skip sending confirmation emails
   * @returns Result of the cancellation operation
   */
  async cancelStudentSubscription(
    classId: string,
    locationId: string,
    studentId: string,
    skipEmail: boolean = false,
  ) {
    return this.cancelSubscription.cancelSubscriptionByStudent(
      classId,
      locationId,
      studentId,
      skipEmail,
    );
  }

  /**
   * Transfers a subscription from one student to another
   *
   * @param params - Object containing transfer details
   * @returns Result of the transfer operation
   */
  async transferSubscription(
    currentClassId: string,
    newClassId: string,
    studentId: string,
    locationId: string,
    noCreditRequired: boolean,
    newCreditAmount: number,
  ) {
    return this.stripeTransferSubscription.transferSubscription(
      currentClassId,
      newClassId,
      studentId,
      locationId,
      noCreditRequired,
      newCreditAmount,
    );
  }

  /**
   * Gets the remaining credit for a student
   *
   * @param currentClassId - ID of the current class
   * @param newClassId - ID of the new class
   * @param studentId - ID of the student
   * @param locationId - ID of the studio location
   * @returns Result of the get remaining credit operation
   */
  async getRemainingCredit(
    currentClassId: string,
    newClassId: string,
    studentId: string,
    locationId: string,
  ) {
    return this.stripeTransferSubscription.getRemainingCredit(
      currentClassId,
      newClassId,
      studentId,
      locationId,
    );
  }

  // CHECKOUT SESSIONS

  /**
   * Creates a checkout session for parent registration
   *
   * @param createParentDto - Parent registration data
   * @returns Checkout session URL and details
   */
  async createParentCheckoutSession(createParentDto: any) {
    return this.checkout.createParentCheckoutSession(createParentDto);
  }

  /**
   * Creates a checkout session for product purchases
   *
   * @param params - Product checkout parameters
   * @returns Checkout session URL and details
   */
  async createProductCheckoutSession(params: any) {
    return this.productCheckout.createProductCheckoutSession(params);
  }

  // COUPONS & DISCOUNTS

  /**
   * Creates a new coupon in Stripe
   *
   * @param params - Coupon creation parameters
   * @returns Created coupon object
   */
  async createCoupon(params: any) {
    return this.coupons.createStripeCoupon(params);
  }

  async createWebhook(apiKey: string, locationId: string) {
    try {
      const stripe = new Stripe(apiKey, {
        apiVersion: '2025-02-24.acacia',
      });

      const backendUrl = this.configService.get<string>('BACKEND_URL');

      const webhook = await stripe.webhookEndpoints.create({
        url: `${backendUrl}/webhooks/stripe?locationId=${locationId}`,
        enabled_events: [
          // 'checkout.session.completed',
          'product.created',
          'payment_intent.succeeded',
          'payment_intent.payment_failed',
          'charge.refunded',
        ],
      });

      return {
        webhookId: webhook.id,
        webhookSecret: webhook.secret,
      };
    } catch (error) {
      throw new Error(`Failed to create webhook: ${error.message}`);
    }
  }

  async findOne(credentialId: string): Promise<Credential> {
    return this.credentialModel.findOne({ credentialId }).exec();
  }

  async findOneByStudioId(studioId: string): Promise<boolean> {
    const credential = await this.credentialModel
      .findOne({ studioId })
      .lean()
      .exec();

    if (credential && credential.apiKey && credential.apiSecret) {
      return true;
    }
    return false;
  }
  async deleteByStudioId(studioId: string): Promise<{ deletedCount: number }> {
    const credential = await this.credentialModel
      .findOne({ studioId })
      .lean()
      .exec();
    if (!credential) {
      throw new Error('Credential not found');
    }

    const stripe = new Stripe(credential.apiSecret, {
      apiVersion: '2025-02-24.acacia',
    });

    if (credential.webhookId) {
      try {
        await stripe.webhookEndpoints.del(credential.webhookId);
      } catch (error) {
        console.error('Error deleting webhook:', error);
      }
    }

    return this.credentialModel.deleteOne({ studioId });
  }

  async captureManualPayment(body: { invoiceId: string; studioId: string }) {
    return this.stripeCaptureManualPayment.captureManualPayment(body);
  }

  async deleteWebhook(credential: Credential) {
    const stripe = new Stripe(credential.apiSecret, {
      apiVersion: '2025-02-24.acacia',
    });

    if (credential.webhookId) {
      await stripe.webhookEndpoints.del(credential.webhookId);
    }

    return {
      status: 'success',
      message: 'Webhook deleted successfully',
    };
  }

  async captureManualTransaction(body: {
    transactionId: string;
    studioId: string;
    metadata: any;
  }) {
    return this.stripeCaptureManualPayment.captureManualTransaction(body);
  }

  async bulkChargeStudents(body: any, studioId: string) {
    return this.stripeBulkPayments.bulkChargeStudents(body, studioId);
  }

  async refundPayment(
    paymentIntentId: string,
    amount: number,
    locationId: string,
  ) {
    return this.stripeRefundPayments.refundPayment(
      paymentIntentId,
      amount,
      locationId,
    );
  }

  async checkPaymentMethodOnFile(
    studioId: string,
    customerId: string,
    paymentMethod: string,
  ) {
    const studio = await this.studioService.findOne(studioId);
    if (!studio) {
      throw new Error('Studio not found');
    }

    const { stripe, credential } =
      await this.stripeCommonService.initializeStripe(studioId);

    return await this.stripeCommonService.checkPaymentMethod(
      customerId,
      paymentMethod,
      stripe,
      credential,
    );
  }

  async createWalletLoad(body: {
    studioId: string;
    parentId: string;
    amount: number;
    paymentMethod: PaymentMethod;
  }) {
    return this.stripeWalletService.createWalletLoad(body);
  }

  async getCustomerDefaultPaymentMethod(customerId: string, studioId: string) {
    const studio = await this.studioService.findOne(studioId);
    if (!studio) {
      throw new Error('Studio not found');
    }

    const { stripe } =
      await this.stripeCommonService.initializeStripe(studioId);

    return this.stripeCommonService.getCustomerDefaultPaymentMethod(
      customerId,
      stripe,
    );
  }

  async processStudentInvoicesWithWallet(body: {
    parentId: string;
    studentId: string;
    studioId: string;
  }) {
    return this.stripeWalletService.processStudentInvoicesWithWallet(body);
  }
}
