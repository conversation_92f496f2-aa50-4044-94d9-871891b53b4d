import { forwardRef, Module } from '@nestjs/common';
import { StripeService } from './stripe.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  Credential,
  CredentialSchema,
} from 'src/database/schema/stripeCredential';
import { StudiosModule } from 'src/studios/studios.module';
import { ParentsModule } from 'src/parents/parents.module';
import { EnrollmentModule } from 'src/enrollment/enrollment.module';
import { StudentsModule } from 'src/students/students.module';
import { ScheduleModule } from '@nestjs/schedule';
import { Student, StudentSchema } from 'src/database/schema/student';
import { Enrollment, EnrollmentSchema } from 'src/database/schema/enrollment';
import { Proration, ProrationSchema } from 'src/database/schema/prorations';
import { CurrencyModule } from 'src/currency/currency.module';
import { CouponModule } from 'src/coupon/coupon.module';
import { DiscountModule } from 'src/discount/discount.module';
import { JwtModule } from '@nestjs/jwt';
import { EnrollmentHistoryModule } from 'src/enrollment-history/enrollment-history.module';
import {
  EnrollmentHistory,
  EnrollmentHistorySchema,
} from 'src/database/schema/enrollmentHistory';
import { EventsModule } from 'src/events/events.module';
import { Event, EventSchema } from 'src/database/schema/event';
import { EventHistoryModule } from 'src/event-history/event-history.module';
import { GohighlevelModule } from 'src/gohighlevel/gohighlevel.module';
import { PoliciesModule } from 'src/policies/policies.module';
import { Session } from 'src/database/schema/session';
import { SessionSchema } from 'src/database/schema/session';
import { GcpStorageModule } from 'src/gcp-storage/gcp-storage.module';
import { LeadsModule } from 'src/leads/leads.module';
import { BullModule } from '@nestjs/bullmq';
import { Invoice, InvoiceSchema } from 'src/database/schema/invoice';
import { WebhookErrorLogsModule } from 'src/webhook-error-logs/webhook-error-logs.module';
import { TriggersModule } from 'src/triggers/triggers.module';
import { TransactionModule } from 'src/transaction/transaction.module';
import { StripeCouponService } from './services/stripe-coupon.service';
import { ProductCreatedHandler } from './services/event-handlers/product-created.handler';
import { PaymentIntentSucceededHandler } from './services/event-handlers/payment-intent-succeeded.handler';
import { PaymentIntentFailedHandler } from './services/event-handlers/payment-intent-failed.handler';
import { CheckoutSessionCompletedHandler } from './services/event-handlers/checkout-session-completed.handler';
import { StripeTransferSubscriptionService } from './services/transfer-subscription.service';
import { StripeCommonService } from './services/stripe-common.service';
import { StripeHistoryService } from './services/remove-and-create-history.service';
import { StripeProductCheckoutService } from './services/product-checkout.service';
import { StripeCustomerService } from './services/customers.service';
import { StripeCardsService } from './services/cards.service';
import { StripeAddStudentToEntityService } from './services/add-student-to-entity.service';
import { StripeCancelSubscriptionByStudentService } from './services/cancel-subscription-by-student.service';
import { WebhooksModule } from 'src/webhooks/webhooks.module';
import { DiscountCouponModule } from 'src/discount-coupon/discount-coupon.module';
import { SubscriptionModule } from 'src/subscription/subscription.module';
import { SubscriptionInvoiceModule } from 'src/subscription-invoice/subscription-invoice.module';
import { StripeCheckoutService } from './services/parent-checkout.service';
import { PaymentTransactionService } from 'src/payment-transaction/payment-transaction.service';
import { PaymentTransactionSchema } from 'src/database/schema/paymentTransaction';
import { PaymentTransaction } from 'src/database/schema/paymentTransaction';
import { PaymentTransactionModule } from 'src/payment-transaction/payment-transaction.module';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import { SubscriptionInvoiceSchema } from 'src/database/schema/subscriptionInvoice';
import { StripeCaptureManualPaymentService } from './services/capture-manual-payment';
import { StripeBulkPaymentsService } from './services/bulk-payments';
import { StripeRefundPaymentsService } from './services/refund-payments';
import { ChargeRefundedHandler } from './services/event-handlers/charge-refunded.handler';
import { TransactionCodeSchema } from 'src/transaction-code/entities/transaction-code.entity';
import { TransactionCode } from 'src/transaction-code/entities/transaction-code.entity';
import { StripeWalletService } from './services/wallet.service';
import { ClassHistoryModule } from 'src/class-history/class-history.module';
import {
  ClassHistory,
  ClassHistorySchema,
} from 'src/database/schema/classHistory';
import { Subscription } from 'src/database/schema/subscription';
import { SubscriptionSchema } from 'src/database/schema/subscription';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Credential.name, schema: CredentialSchema },
      { name: Proration.name, schema: ProrationSchema },
      { name: Student.name, schema: StudentSchema },
      { name: Enrollment.name, schema: EnrollmentSchema },
      { name: EnrollmentHistory.name, schema: EnrollmentHistorySchema },
      { name: Event.name, schema: EventSchema },
      { name: Session.name, schema: SessionSchema },
      { name: PaymentTransaction.name, schema: PaymentTransactionSchema },
      { name: Proration.name, schema: ProrationSchema },
      { name: SubscriptionInvoice.name, schema: SubscriptionInvoiceSchema },
      { name: TransactionCode.name, schema: TransactionCodeSchema },
      { name: ClassHistory.name, schema: ClassHistorySchema },
      { name: Subscription.name, schema: SubscriptionSchema },
    ]),
    BullModule.registerQueue({
      name: 'parent-remove-tags',
    }),
    BullModule.registerQueue({
      name: 'invoice-status-update',
    }),
    forwardRef(() => ParentsModule),
    forwardRef(() => StudentsModule),
    forwardRef(() => TransactionModule),
    forwardRef(() => StudiosModule),
    forwardRef(() => EnrollmentModule),
    forwardRef(() => CurrencyModule),
    forwardRef(() => DiscountModule),
    forwardRef(() => CouponModule),
    forwardRef(() => EnrollmentHistoryModule),
    forwardRef(() => EventsModule),
    forwardRef(() => EventHistoryModule),
    forwardRef(() => GohighlevelModule),
    forwardRef(() => PoliciesModule),
    forwardRef(() => GcpStorageModule),
    forwardRef(() => LeadsModule),
    forwardRef(() => WebhookErrorLogsModule),
    forwardRef(() => WebhooksModule),
    forwardRef(() => DiscountCouponModule),
    forwardRef(() => SubscriptionModule),
    forwardRef(() => SubscriptionInvoiceModule),
    ScheduleModule.forRoot(),
    JwtModule,
    TriggersModule,
    PaymentTransactionModule,
    ClassHistoryModule,
  ],
  controllers: [],
  providers: [
    StripeService,
    StripeAddStudentToEntityService,
    StripeCancelSubscriptionByStudentService,
    StripeCardsService,
    StripeCustomerService,
    StripeProductCheckoutService,
    StripeHistoryService,
    StripeCommonService,
    StripeCouponService,
    StripeTransferSubscriptionService,
    StripeCheckoutService,
    StripeCaptureManualPaymentService,
    CheckoutSessionCompletedHandler,
    PaymentIntentFailedHandler,
    PaymentIntentSucceededHandler,
    ProductCreatedHandler,
    PaymentTransactionService,
    StripeBulkPaymentsService,
    StripeRefundPaymentsService,
    ChargeRefundedHandler,
    StripeWalletService,
  ],
  exports: [
    StripeService,
    StripeAddStudentToEntityService,
    StripeCancelSubscriptionByStudentService,
    StripeCardsService,
    StripeCustomerService,
    StripeProductCheckoutService,
    StripeHistoryService,
    StripeCommonService,
    StripeCouponService,
    StripeTransferSubscriptionService,
    StripeCheckoutService,
    StripeCaptureManualPaymentService,
    StripeBulkPaymentsService,
    StripeRefundPaymentsService,
    CheckoutSessionCompletedHandler,
    PaymentIntentFailedHandler,
    PaymentIntentSucceededHandler,
    ProductCreatedHandler,
    ChargeRefundedHandler,
    StripeWalletService,
  ],
})
export class StripeModule {}
