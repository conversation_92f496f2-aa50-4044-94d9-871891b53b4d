export enum PaymentTransactionEntityType {
  EVENT = 'event',
  CLASS = 'class',
}

export enum PaymentTransactionSource {
  PARENT_REGISTRATION_FORM = 'parent_registration_form',
  PARENT_PORTAL_PRODUCT_BUY = 'parent_portal_product_buy',
  STUDIO_PORTAL_PRODUCT_BUY = 'studio_portal_product_buy',
  WALLET_LOAD = 'wallet_load',
}

export enum PaymentTransactionType {
  REGISTRATION = 'registration', // root
  ENROLLMENT = 'enrollment', // child
  EVENT = 'event', // child
  WALLET_LOAD = 'wallet_load', // root
  UPFRONT = 'upfront', // root
}

export enum PaymentTransactionStatus {
  PENDING = 'pending',
  PAID = 'paid',
  FAILED = 'failed',
  FULL_REFUND = 'full refund',
  PARTIAL_REFUND = 'partial refund',
  SCHEDULED = 'scheduled',
  FREE = 'free',
  PAUSED = 'paused',
  PROCESSING = 'processing',
  CANCELLED = 'cancelled',
  UPCOMING = 'upcoming',
}

export enum InvoiceStatus {
  PAID = 'paid',
  PARTIALLY_PAID = 'partially paid',
  UPCOMING = 'upcoming',
  PENDING = 'pending',
  FULL_REFUND = 'full refund',
  PARTIAL_REFUND = 'partial refund',
  PENDING_REFUND = 'refund processing',
  REFUND_FAILED = 'refund failed',
  VOID = 'void',
  FAILED = 'failed',
  SCHEDULED = 'scheduled',
  PAUSED = 'paused',
  PROCESSING = 'processing',
  CANCELLED = 'cancelled',
  FREE = 'free',
}

export enum PaymentProvider {
  STRIPE = 'stripe',
  PAYPAL = 'paypal',
  MANUAL = 'manual',
  CASH = 'cash',
  GHL = 'ghl',
}

export enum PaymentMethod {
  CARD = 'card',
  LINK = 'link',
  US_BANK_ACCOUNT = 'us_bank_account',
  CASH = 'cash',
  CHECK = 'check',
  WALLET = 'wallet',
}

export enum InvoiceType {
  SUBSCRIPTION = 'subscription',
  ONE_TIME = 'one_time',
  UPFRONT = 'upfront',
  MANUAL = 'manual',
  BULK_PAYMENT = 'bulk_payment',
}

export enum TransactionType {
  CREDIT = 'credit',
  DEBIT = 'debit',
}

export enum PaymentProcessingMethod {
  AUTO = 'auto',
  MANUAL = 'manual',
}

export enum ProrationMode {
  FULL_MONTH_ALWAYS = 'full_month_always',
  FIRST_MONTH_ONLY = 'first_month_only',
  FIRST_AND_LAST_MONTH = 'first_and_last_month',
}

export enum SubscriptionStatus {
  ACTIVE = 'active',
  CANCELLED = 'cancelled',
  TERM_ENDED = 'term_ended',
  PAUSED = 'paused',
  ENDED = 'ended',
  SCHEDULED = 'scheduled',
}
