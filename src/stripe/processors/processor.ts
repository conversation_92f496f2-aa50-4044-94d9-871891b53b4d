// import {
//   Injectable,
//   Logger,
//   OnModuleDestroy,
//   OnModuleInit,
// } from '@nestjs/common';
// import { Worker, Job, QueueEvents } from 'bullmq';
// import { InjectModel } from '@nestjs/mongoose';
// import { Model } from 'mongoose';
// import { Enrollment } from '../../enrollment/entities/enrollment.entity';
// import { Proration } from '../../database/schema/prorations';
// import { Stripe } from 'stripe';
// import { Process, Processor } from '@nestjs/bull';

// export enum StripeJobType {
//   UPDATE_PRODUCT_ID = 'update-product-id',
// }

// interface StripeJobData {
//   type: StripeJobType;
//   payload: any;
// }

// @Injectable()
// export class StripeProcessor implements OnModuleInit, OnModuleDestroy {
//   private stripeWorker: Worker;
//   private stripeQueueEvents: QueueEvents;

//   constructor(
//     @InjectModel(Enrollment.name) private enrollmentModel: Model<Enrollment>,
//     @InjectModel(Proration.name) private prorationModel: Model<Proration>,
//     private readonly stripe: Stripe,
//   ) { }

//   onModuleInit() {
//     this.stripeWorker = new Worker(
//       'stripe-queue',
//       async (job: Job<StripeJobData>) => {
//         console.log(`Processing stripe job: ${job.data.type}`);

//         try {
//           switch (job.data.type) {
//             case StripeJobType.UPDATE_PRODUCT_ID:
//               return await this.handleUpdateProductId(job.data.payload);
//             // Add more cases for different job types
//             default:
//               throw new Error(`Unsupported job type: ${job.data.type}`);
//           }
//         } catch (error) {
//           console.error(`Error processing stripe job ${job.data.type}:`, error);
//           if (job.attemptsMade >= job.opts.attempts - 1) {
//             console.error(`Job ${job.id} has failed maximum attempts`);
//           }
//           throw error;
//         }
//       },
//       {
//         connection: {
//           host: '**************',
//           port: 6379,
//         },
//         limiter: {
//           max: 10,
//           duration: 5000,
//         },
//         settings: {
//           backoffStrategy: (attemptsMade: number) => {
//             return Math.min(Math.pow(2, attemptsMade) * 1000, 120000);
//           },
//         },
//       },
//     );

//     this.stripeWorker.on('failed', (job, err) => {
//       console.error(`Stripe job ${job?.id} failed:`, err);
//     });

//     this.stripeQueueEvents = new QueueEvents('stripe-queue');

//     this.stripeQueueEvents.on('failed', ({ jobId, failedReason }) => {
//       console.error(`Stripe job ${jobId} failed with reason: ${failedReason}`);
//     });
//   }

//   private async handleUpdateProductId(payload: {
//     ghlProductId: string;
//     stripeProductId: string;
//   }) {
//     const { ghlProductId, stripeProductId } = payload;
//     const maxAttempts = 10;
//     let attempt = 1;

//     while (attempt <= maxAttempts) {
//       try {
//         const enrollment = await this.enrollmentModel.findOneAndUpdate(
//           { productId_ghl: ghlProductId },
//           { productId_stripe: stripeProductId },
//           { new: true },
//         );

//         if (enrollment) {
//           return enrollment;
//         }

//         console.log(
//           `Attempt ${attempt}: No enrollment found for product ${ghlProductId}`,
//         );
//         await new Promise((resolve) => setTimeout(resolve, 5000));
//         attempt++;
//       } catch (error) {
//         console.error(`Attempt ${attempt} failed:`, error);
//         if (attempt >= maxAttempts) {
//           throw error;
//         }
//         attempt++;
//         await new Promise((resolve) => setTimeout(resolve, 5000));
//       }
//     }

//     throw new Error(
//       `Failed to update product stripe ID after ${maxAttempts} attempts`,
//     );
//   }

//   onModuleDestroy() {
//     this.stripeWorker?.close();
//     this.stripeQueueEvents?.close();
//   }
// }
