import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { Types } from 'mongoose';

import { ParentsService } from 'src/parents/parents.service';

import { CurrencyService } from 'src/currency/currency.service';

import { StripeCommonService } from './stripe-common.service';

import { SubscriptionInvoiceService } from 'src/subscription-invoice/subscription-invoice.service';
import { InvoiceType, PaymentMethod } from 'src/stripe/type';
import { InvoiceStatus, PaymentProvider } from 'src/stripe/type';
import { StudentsService } from 'src/students/students.service';
import { StudiosService } from 'src/studios/studios.service';
import { generateId } from 'src/utils/helperFunction';
import { getGcpIpAddress } from 'src/utils/helperFunction';
import { EnrollmentService } from 'src/enrollment/enrollment.service';

@Injectable()
export class StripeBulkPaymentsService {
  private readonly logger = new Logger(StripeBulkPaymentsService.name);
  constructor(
    @Inject(forwardRef(() => ParentsService))
    private readonly parentsService: ParentsService,

    @Inject(forwardRef(() => CurrencyService))
    private readonly currencyService: CurrencyService,

    @Inject(forwardRef(() => StripeCommonService))
    private readonly stripeCommonService: StripeCommonService,

    @Inject(forwardRef(() => SubscriptionInvoiceService))
    private readonly subscriptionInvoiceService: SubscriptionInvoiceService,

    @Inject(forwardRef(() => StudentsService))
    private readonly studentsService: StudentsService,

    @Inject(forwardRef(() => StudiosService))
    private readonly studiosService: StudiosService,

    @Inject(forwardRef(() => EnrollmentService))
    private readonly enrollmentService: EnrollmentService,
  ) {}

  /**
   * Captures a manual payment for an invoice
   *
   * This method handles the complete payment flow including:
   * - Retrieving invoice, parent, and studio information
   * - Creating a Stripe Payment Intent
   * - Processing payments through Stripe Payment Intent
   *
   * @param body - Invoice data containing invoiceId, studioId
   * @returns Object with status information and payment details
   * @throws HttpException if payment processing fails or payment method is missing
   */

  async bulkChargeStudents(body: any, studioId: string) {
    const {
      studentId: studentIds,
      amount,
      chargeName,
      isSingleStudent,
      transactionCodeId,
    } = body;
    try {
      const { stripe, credential } =
        await this.stripeCommonService.initializeStripe(studioId);
      const currency = await this.currencyService.findByStudioId(
        Types.ObjectId.createFromHexString(studioId),
      );

      const bulkPaymentId = generateId();

      studentIds.forEach(async (studentId) => {
        const student =
          await this.studentsService.findStudentDetails(studentId);
        const studio = await this.studiosService.findOne(studioId);

        const defaultPaymentMethod =
          await this.stripeCommonService.getCustomerDefaultPaymentMethod(
            (student.parentId as any).stripeCustomerId,
            stripe,
          );

        const line_items = [
          {
            name: chargeName,
            amount: amount,
            quantity: 1,
            type: InvoiceType.MANUAL,
            total: amount,
          },
        ];

        let metadata: any = {
          attemptCount: 1,
          manualChargeName: 'Bulk Payment',
        };

        if (!isSingleStudent) {
          metadata = {
            ...metadata,
            bulkPaymentId: bulkPaymentId,
          };
        }
        const invoice = await this.subscriptionInvoiceService.create({
          studioId: Types.ObjectId.createFromHexString(studioId),
          parentId: Types.ObjectId.createFromHexString(
            student.parentId._id.toString(),
          ),
          subscriptionId: null,
          studentId: Types.ObjectId.createFromHexString(student._id.toString()),
          baseAmount: amount,
          status: defaultPaymentMethod
            ? InvoiceStatus.PENDING
            : InvoiceStatus.FAILED,
          type: InvoiceType.MANUAL,
          paymentProvider: studio.paymentProvider as PaymentProvider,
          paymentMethod: !defaultPaymentMethod
            ? null
            : (defaultPaymentMethod.type as PaymentMethod),
          line_items: line_items,
          finalAmount: amount,
          metadata: metadata,
          transactionCodeId: transactionCodeId || null,
        });

        //converted amount to cents
        if (defaultPaymentMethod) {
          let paymentIntent;
          try {
            if (defaultPaymentMethod.type === PaymentMethod.US_BANK_ACCOUNT) {
              // First create without confirming
              paymentIntent = await stripe.paymentIntents.create({
                amount: Math.floor(amount * 100),
                currency: currency.name,
                customer: (student.parentId as any).stripeCustomerId,
                payment_method: defaultPaymentMethod.id,
                off_session: false,
                confirm: false,
                description: `Payment for ${chargeName || 'Enrollio'} - ${student.firstName} ${student.lastName}`,
                metadata: {
                  processInvoiceUsingPaymentIntent: 'true',
                },
              });

              // Then confirm with mandate data
              const serverIp = await getGcpIpAddress();
              paymentIntent = await stripe.paymentIntents.confirm(
                paymentIntent.id,
                {
                  payment_method: defaultPaymentMethod.id,
                  mandate_data: {
                    customer_acceptance: {
                      type: 'online',
                      online: {
                        ip_address: serverIp || '127.0.0.1',
                        user_agent: 'Enrollio Server Process',
                      },
                    },
                  },
                },
              );
            } else if (
              defaultPaymentMethod.type === PaymentMethod.CARD ||
              defaultPaymentMethod.type === PaymentMethod.LINK
            ) {
              const paymentMethodTypes = [defaultPaymentMethod.type];
              if (defaultPaymentMethod.type === PaymentMethod.LINK) {
                paymentMethodTypes.push(PaymentMethod.CARD);
              }
              paymentIntent = await stripe.paymentIntents.create({
                amount: Math.floor(amount * 100),
                currency: currency.name,
                customer: (student.parentId as any).stripeCustomerId,
                payment_method: defaultPaymentMethod.id,
                off_session: true,
                confirm: true,
                payment_method_types: paymentMethodTypes,
                description: `Payment for ${chargeName || 'Enrollio'} - ${student.firstName} ${student.lastName}`,
                metadata: {
                  processInvoiceUsingPaymentIntent: 'true',
                },
              });
            }

            await this.subscriptionInvoiceService.updateMetadata(
              invoice._id.toString(),
              {
                ...invoice.metadata,
                paymentIntentId: paymentIntent.id,
              },
            );
          } catch (error) {
            this.logger.error(error);
          }
        }
      });
      return {
        success: true,
        message: 'Bulk payment was successful',
      };
    } catch (error) {
      this.logger.error(error);
    }
  }
}
