import {
  forwardRef,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import Stripe from 'stripe';
import { Credential } from 'src/database/schema/stripeCredential';
import { StudiosService } from 'src/studios/studios.service';
import { CreateParentDto } from 'src/parents/dto/create-parent.dto';
import { EnrollmentService } from 'src/enrollment/enrollment.service';
import { TransactionService } from 'src/transaction/transaction.service';
import { ParentsService } from 'src/parents/parents.service';

import { Proration } from 'src/database/schema/prorations';

import { CurrencyService } from 'src/currency/currency.service';
import { DiscountService } from 'src/discount/discount.service';
import { StripeCouponService } from './stripe-coupon.service';
import { CheckoutSessionCompletedHandler } from './event-handlers/checkout-session-completed.handler';
import { StripeCommonService } from './stripe-common.service';
import { PaymentMethod, PaymentProvider } from 'src/stripe/type';
import { PaymentTransactionService } from 'src/payment-transaction/payment-transaction.service';
import {
  PaymentTransactionSource,
  PaymentTransactionStatus,
  PaymentTransactionType,
} from '../type';
import { generateId } from 'src/utils/helperFunction';
import {
  calculateProratedAmount,
  calculateProratedAmountFromEntity,
  isScheduledEntity,
} from 'src/utils/helpers/billing-proration';
import * as dayjs from 'dayjs';
import { getPaymentMethodTypes } from '../../utils/helperFunction';

@Injectable()
export class StripeCheckoutService {
  private readonly logger = new Logger(StripeCheckoutService.name);
  constructor(
    @InjectModel(Credential.name) private credentialModel: Model<Credential>,
    @Inject(forwardRef(() => ParentsService))
    private readonly parentsService: ParentsService,
    @Inject(forwardRef(() => TransactionService))
    private readonly transactionService: TransactionService,
    @Inject(forwardRef(() => EnrollmentService))
    private readonly enrollmentService: EnrollmentService,
    @Inject(forwardRef(() => StudiosService))
    private readonly studioService: StudiosService,
    @InjectModel(Proration.name) private prorationModel: Model<Proration>,
    @Inject(forwardRef(() => CurrencyService))
    private readonly currencyService: CurrencyService,
    @Inject(forwardRef(() => DiscountService))
    private readonly discountService: DiscountService,
    @Inject(forwardRef(() => StripeCouponService))
    private readonly stripeCouponService: StripeCouponService,
    @Inject(forwardRef(() => CheckoutSessionCompletedHandler))
    private readonly CheckoutSessionCompletedHandler: CheckoutSessionCompletedHandler,
    @Inject(forwardRef(() => StripeCommonService))
    private readonly stripeCommonService: StripeCommonService,
    @Inject(forwardRef(() => PaymentTransactionService))
    private readonly paymentTransactionService: PaymentTransactionService,
  ) {}

  /**
   * Creates a checkout session for a new parent registration
   *
   * This method handles the entire parent registration flow including:
   * - Creating parent records in the database
   * - Setting up Stripe customer profiles
   * - Calculating prorated tuition fees
   * - Generating checkout sessions for payment
   * - Handling multi-class and multi-student discounts
   *
   * @param createParentDto - Parent registration data including student information
   * @returns URL to the Stripe checkout session or parent portal
   * @throws Error if checkout session creation fails
   */
  async createParentCheckoutSession(createParentDto: CreateParentDto) {
    try {
      const tempParentId = createParentDto.email;
      if (createParentDto.isCreatedFromStudio) {
        await this.parentsService.createWithLocationId(createParentDto);
        return process.env.PARENT_PORTAL_URL;
      } else {
        const studio = await this.studioService.findByLocationIdString(
          createParentDto.studioId,
        );

        const { stripe, credential } =
          await this.stripeCommonService.initializeStripe(
            studio._id.toString(),
          );

        const enrollmentsId = [];
        for (const studentDto of createParentDto.students) {
          const ids = studentDto.enrollments;
          for (const id of ids) {
            enrollmentsId.push(id);
          }
        }

        const products = [];
        for (const studentDto of createParentDto.students) {
          for (const enrollmentId of studentDto.enrollments) {
            const enrollment = await this.enrollmentService.findOne(
              enrollmentId.enrollmentId.toString(),
            );

            const isScheduled = isScheduledEntity(enrollment);

            // Calculate prorated tuition fee
            const proratedAmount =
              calculateProratedAmountFromEntity(enrollment);
            const product = await stripe.prices.list({
              product: enrollment.productId_stripe,
            });
            let currency;
            if (product.data.length > 0) {
              currency = product.data[0].currency;
            } else {
              //TODO: we don't need a collection for currency, we can store it in studio model
              //TODO: we need to create a new function to get the currency from the studio model
              currency = (
                await this.currencyService.findByStudioId(
                  Types.ObjectId.createFromHexString(studio._id.toString()),
                )
              ).name.toLowerCase();
            }
            // Add product with student info instead of incrementing quantity
            products.push({
              id: enrollment.productId_ghl,
              enrollmentId: enrollment._id.toString(),
              studentName: `${studentDto.firstName} ${studentDto.lastName}`,
              currency,
              title: enrollment.title,
              tuitionFee: proratedAmount,
              registrationFeeAmount: enrollment.registrationFeeAmount,
              costumeFee: enrollment.costumeFee ?? 0,
              session: enrollment.session,
              isScheduled,
            });
          }
        }

        // Group enrollments by student
        const studentsWithEnrollments = createParentDto.students.map(
          (student) => {
            const studentEnrollments = student.enrollments.map(
              (enrollmentId) => {
                const enrollment = products.find(
                  (p) => p.enrollmentId === enrollmentId.enrollmentId,
                );
                return {
                  enrollmentId: enrollment.enrollmentId,
                  tuitionFee: enrollment.tuitionFee,
                  subscriptionStatus: 'active',
                };
              },
            );

            // Sort enrollments by tuition fee descending to determine class position
            studentEnrollments.sort((a, b) => b.tuitionFee - a.tuitionFee);

            return {
              studentId:
                student.firstName +
                '-' +
                student.lastName +
                '-' +
                student.dob +
                '-' +
                student.gender,
              firstName: student.firstName,
              lastName: student.lastName,
              tuitionFee: studentEnrollments[0].tuitionFee, // Use highest tuition fee
              enrollmentId: studentEnrollments[0].enrollmentId, // Use first enrollment as current
              existingEnrollments: studentEnrollments.slice(1).map((e) => ({
                enrollmentId: e.enrollmentId,
                subscriptionStatus: e.subscriptionStatus, // Assume existing enrollments are active
              })),
            };
          },
        );

        // Sort students by their highest tuition fee descending to determine student position
        studentsWithEnrollments.sort((a, b) => b.tuitionFee - a.tuitionFee);

        // Calculate total discount combining both multi-class and multi-student discounts
        const { discountSplit } =
          await this.stripeCommonService.calculateDiscounts({
            students: studentsWithEnrollments,
            studioId: studio._id.toString(),
          });

        const sessionIdList = new Set();
        const line_items = products.flatMap((product) => {
          const items = [];
          const sessionId = product.session?._id?.toString();

          // Add session registration fee if enabled and not paid
          if (
            sessionId &&
            product.session?.isRegistrationFee &&
            !sessionIdList.has(sessionId)
          ) {
            sessionIdList.add(sessionId);
            items.push({
              price_data: {
                currency: product.currency,
                studentName: product.studentName,
                product_data: {
                  name: product.session.name,
                  description: `Session Registration Fee (${product.studentName})`,
                },
                unit_amount: product.session.registrationFeeAmount * 100,
              },

              quantity: 1,
              paymentType: product.isScheduled ? 'scheduled' : 'current',
              billingDate: product.session.billingDate,
            });
          }

          // Add class registration fee
          if (product.registrationFeeAmount > 0) {
            items.push({
              price_data: {
                currency: product.currency,
                studentName: product.studentName,
                product_data: {
                  name: product.title,
                  description: `Registration Fee (${product.studentName})`,
                },
                unit_amount: product.registrationFeeAmount * 100,
              },
              quantity: 1,
              paymentType: product.isScheduled ? 'scheduled' : 'current',
              billingDate: product.session.billingDate,
            });
          }

          // Add costume fee
          if (product.costumeFee > 0) {
            items.push({
              price_data: {
                currency: product.currency,
                studentName: product.studentName,
                product_data: {
                  name: product.title,
                  description: `Costume Fee (${product.studentName})`,
                },
                unit_amount: product.costumeFee * 100,
              },
              quantity: 1,
              paymentType: product.isScheduled ? 'scheduled' : 'current',
              billingDate: product.session.billingDate,
            });
          }

          // Add tuition fee
          if (product.tuitionFee) {
            items.push({
              price_data: {
                currency: product.currency,
                studentName: product.studentName,
                product_data: {
                  name: product.title,
                  description: `Tuition Fee (${product.studentName})`,
                  billingDate: product.session.billingDate,
                  id: product._id.toString(),
                },
                unit_amount: Math.round(product.tuitionFee * 100),
              },
              billingDate: product.session.billingDate,
              quantity: 1,
              paymentType:
                product.tuitionFee > 0
                  ? product.isScheduled
                    ? 'scheduled'
                    : 'current'
                  : 'free',
            });
          }

          return items;
        });

        // Group line items by payment type
        const groupedLineItems = {
          current: line_items.filter((item) => item.paymentType === 'current'),
          scheduled: line_items.filter(
            (item) => item.paymentType === 'scheduled',
          ),
          free: line_items.filter((item) => item.paymentType === 'free'),
        };

        let customerId;
        const existingCustomers = await stripe.customers.list({
          email: createParentDto.email,
          limit: 1,
        });

        if (
          existingCustomers.data.length > 0 &&
          existingCustomers.data[0].email === createParentDto.email
        ) {
          customerId = existingCustomers.data[0].id;
        } else {
          const newCustomer = await stripe.customers.create({
            email: createParentDto.email,
            name: `${createParentDto.firstName} ${createParentDto.lastName}`,
          });
          customerId = newCustomer.id;
        }

        createParentDto.stripeCustomerId = customerId;

        let discountOptions;
        if (groupedLineItems.scheduled.length > 0) {
          if (!createParentDto.paymentMethod) {
            return null;
          }
          let billingDate = null;

          if (
            groupedLineItems.scheduled &&
            groupedLineItems.scheduled.length > 0
          ) {
            // Get all billing dates from scheduled items
            const billingDates = groupedLineItems.scheduled
              .map((item) => item.price_data.product_data.billingDate)
              .filter((date) => date); // Filter out undefined dates

            if (billingDates.length > 0) {
              // Find the earliest billing date
              billingDate = billingDates.reduce(
                (earliest, date) =>
                  dayjs(date).isBefore(dayjs(earliest)) ? date : earliest,
                billingDates[0],
              );
            }
          }

          // Calculate total amount from scheduled items
          const scheduledTotalAmount =
            groupedLineItems.scheduled.reduce((total, item) => {
              const unitAmount = item.price_data?.unit_amount || 0;
              const quantity = item.quantity || 1;
              return total + unitAmount * quantity;
            }, 0) / 100;

          const customer_id = createParentDto.stripeCustomerId;
          const paymentMethods = await stripe.paymentMethods.list({
            customer: customer_id,
            type:
              createParentDto.paymentMethod === PaymentMethod.CARD
                ? PaymentMethod.CARD
                : PaymentMethod.US_BANK_ACCOUNT,
          });
          const paymentMethod =
            createParentDto.paymentMethod === PaymentMethod.CARD
              ? PaymentMethod.CARD
              : PaymentMethod.US_BANK_ACCOUNT;
          if (
            billingDate &&
            dayjs(billingDate).isAfter(dayjs(), 'day') &&
            scheduledTotalAmount > 0
          ) {
            // If no payment methods exist, generate and return the card update URL
            if (paymentMethods.data.length === 0) {
              const setupIntent = await stripe.setupIntents.create({
                payment_method_types: [paymentMethod],
                customer: customer_id,
                usage: 'off_session',
              });

              return {
                status: HttpStatus.OK,
                message: 'Payment method not found',
                checkoutUrl: null,
                clientSecret: setupIntent.client_secret,
                stripePublicApiKey: credential.apiKey,
              };
            }
          }
          if (
            discountSplit[
              groupedLineItems.scheduled[0].price_data.studentName
            ] > 0
          ) {
            const stripeCouponId =
              await this.stripeCouponService.createStripeCoupon({
                type: 'amount',
                totalDiscount:
                  discountSplit[
                    groupedLineItems.scheduled[0].price_data.studentName
                  ],
                name: 'Discount',
                studioId: studio._id.toString(),
              });

            discountOptions = [
              {
                coupon: stripeCouponId,
              },
            ];
          } else {
            discountOptions = [];
          }

          const groupId = generateId();
          for (const student of createParentDto.students) {
            const tempStudentId =
              student.firstName +
              '-' +
              student.lastName +
              '-' +
              student.dob +
              '-' +
              student.gender;
            for (const enrollment of student.enrollments) {
              // Find line items that match this student and enrollment
              const studentName = `${student.firstName} ${student.lastName}`;
              const enrollmentId = enrollment.enrollmentId;

              // Filter line items for this specific student and enrollment
              const matchingLineItems = line_items.filter((item) => {
                return (
                  item.price_data.studentName === studentName &&
                  item.price_data.product_data.id === enrollmentId
                );
              });

              // Calculate the total amount for this student-enrollment combination
              const enrollmentAmount =
                matchingLineItems.reduce(
                  (acc, item) =>
                    acc + item.price_data.unit_amount * item.quantity,
                  0,
                ) / 100;

              // Get the billing date from the line items
              // First try to find it in the price_data.product_data
              let enrollmentBillingDate;

              // Look for a line item with a billing date in product_data
              const itemWithBillingDate = matchingLineItems.find(
                (item) => item.price_data?.product_data?.billingDate,
              );

              if (itemWithBillingDate) {
                enrollmentBillingDate =
                  itemWithBillingDate.price_data.product_data.billingDate;
              } else {
                // Try to find it at the line item level
                const itemWithTopLevelBillingDate = matchingLineItems.find(
                  (item) => item.billingDate,
                );

                if (itemWithTopLevelBillingDate) {
                  enrollmentBillingDate =
                    itemWithTopLevelBillingDate.billingDate;
                } else {
                  const enrollment = await this.enrollmentService.findOne(
                    enrollmentId.toString(),
                  );
                  enrollmentBillingDate = enrollment.session.billingDate;
                }
              }

              // Create a transaction for this specific student-enrollment
              await this.paymentTransactionService.create({
                studioId: Types.ObjectId.createFromHexString(
                  studio._id.toString(),
                ),
                parentId: tempParentId, // Will be set after parent creation
                studentId: tempStudentId, // Will be set after student creation
                groupId: groupId,
                type: PaymentTransactionType.ENROLLMENT,
                amount: enrollmentAmount,
                status: PaymentTransactionStatus.SCHEDULED,
                paymentProvider: PaymentProvider.STRIPE,
                paymentMethod: createParentDto.paymentMethod as PaymentMethod,
                metadata: {
                  discountSplit: discountSplit,
                  line_items: matchingLineItems.filter((item) => item !== null),
                  stripeSessionId: '',
                  paymentIntentId: '',
                  billingDate: enrollmentBillingDate,
                  description: `Registration Enrollment payment for ${studentName}`,
                },
              });
            }
          }

          this.CheckoutSessionCompletedHandler.handleEvent(
            null,
            stripe,
            groupId,
            true,
          );
          return (
            process.env.PARENT_PORTAL_URL +
            '/login?studioId=' +
            studio.locationId
          );
        }
        if (groupedLineItems.free.length > 0) {
          if (
            discountSplit[groupedLineItems.free[0].price_data.studentName] > 0
          ) {
            const stripeCouponId =
              await this.stripeCouponService.createStripeCoupon({
                type: 'amount',
                totalDiscount:
                  discountSplit[
                    groupedLineItems.free[0].price_data.studentName
                  ],
                name: 'Discount',
                studioId: studio._id.toString(),
              });

            discountOptions = [
              {
                coupon: stripeCouponId,
              },
            ];
          } else {
            discountOptions = [];
          }

          // Generate groupId for free payments instead of creating root transaction
          const groupId = generateId();

          for (const student of createParentDto.students) {
            const tempStudentId =
              student.firstName +
              '-' +
              student.lastName +
              '-' +
              student.dob +
              '-' +
              student.gender;
            for (const enrollment of student.enrollments) {
              // Find line items that match this student and enrollment
              const studentName = `${student.firstName} ${student.lastName}`;
              const enrollmentId = enrollment.enrollmentId;

              // Filter line items for this specific student and enrollment
              const matchingLineItems = line_items.filter((item) => {
                return (
                  item.price_data.studentName === studentName &&
                  item.price_data.product_data.id === enrollmentId
                );
              });

              // Calculate the total amount for this student-enrollment combination
              const enrollmentAmount =
                matchingLineItems.reduce(
                  (acc, item) =>
                    acc + item.price_data.unit_amount * item.quantity,
                  0,
                ) / 100;

              // Get the billing date from the line items
              // First try to find it in the price_data.product_data
              let enrollmentBillingDate;

              // Look for a line item with a billing date in product_data
              const itemWithBillingDate = matchingLineItems.find(
                (item) => item.price_data?.product_data?.billingDate,
              );

              if (itemWithBillingDate) {
                enrollmentBillingDate =
                  itemWithBillingDate.price_data.product_data.billingDate;
              } else {
                // Try to find it at the line item level
                const itemWithTopLevelBillingDate = matchingLineItems.find(
                  (item) => item.billingDate,
                );

                if (itemWithTopLevelBillingDate) {
                  enrollmentBillingDate =
                    itemWithTopLevelBillingDate.billingDate;
                } else {
                  const enrollment = await this.enrollmentService.findOne(
                    enrollmentId.toString(),
                  );
                  enrollmentBillingDate = enrollment.session.billingDate;
                }
              }

              // Create a transaction for this specific student-enrollment
              await this.paymentTransactionService.create({
                studioId: Types.ObjectId.createFromHexString(
                  studio._id.toString(),
                ),
                parentId: tempParentId, // Will be set after parent creation
                studentId: tempStudentId, // Will be set after student creation
                groupId: groupId,
                type: PaymentTransactionType.ENROLLMENT,
                typeId: Types.ObjectId.createFromHexString(
                  enrollmentId.toString(),
                ),
                amount: enrollmentAmount,
                status: PaymentTransactionStatus.FREE,
                paymentProvider: PaymentProvider.STRIPE,
                paymentMethod: createParentDto.paymentMethod as PaymentMethod,
                paymentSource:
                  PaymentTransactionSource.PARENT_REGISTRATION_FORM,
                metadata: {
                  discountSplit: discountSplit,
                  line_items: matchingLineItems.filter((item) => item !== null),
                  stripeSessionId: '',
                  paymentIntentId: '',
                  billingDate: enrollmentBillingDate,
                  description: `Registration Enrollment payment for ${studentName}`,
                  createParentPayload: createParentDto,
                },
              });
            }
          }

          this.CheckoutSessionCompletedHandler.handleEvent(
            null,
            stripe,
            groupId,
            true,
          );
          return (
            process.env.PARENT_PORTAL_URL +
            '/login?studioId=' +
            studio.locationId
          );
        }
        let coupon;
        if (groupedLineItems.current.length > 0) {
          // Get the currency from the first product or fallback to currency service
          let studioCurrency;
          if (products.length > 0) {
            studioCurrency = products[0].currency.toUpperCase();
          } else {
            const currencyData = await this.currencyService.findByStudioId(
              Types.ObjectId.createFromHexString(studio._id.toString()),
            );
            studioCurrency = currencyData.name.toUpperCase();
          }

          // Get appropriate payment method types based on studio currency
          const paymentMethodTypes = getPaymentMethodTypes(studioCurrency);

          if (
            discountSplit[groupedLineItems.current[0].price_data.studentName] >
            0
          ) {
            coupon = await this.stripeCouponService.createStripeCoupon({
              type: 'amount',
              totalDiscount:
                discountSplit[
                  groupedLineItems.current[0].price_data.studentName
                ],
              name: 'Discount',
              studioId: studio._id.toString(),
            });

            discountOptions = [
              {
                coupon: coupon.stripeCouponId,
              },
            ];
          } else {
            discountOptions = [];
          }

          let session;
          try {
            session = await stripe.checkout.sessions.create({
              customer: customerId,
              payment_method_types: paymentMethodTypes as any,
              payment_method_options: {
                ...(paymentMethodTypes.includes('us_bank_account') && {
                  us_bank_account: {
                    financial_connections: {
                      permissions: ['payment_method'],
                    },
                    verification_method: 'instant', // Can be 'instant' or 'microdeposits'
                  },
                }),
                card: {
                  setup_future_usage: 'off_session',
                },
              },
              mode: 'payment',
              line_items: groupedLineItems.current
                .filter((item) => item !== null)
                .map((item) => {
                  const { paymentType: _paymentType, ...restItem } = item;

                  if (restItem.price_data) {
                    // Remove studentName from price_data if it exists
                    const { studentName: _studentName, ...restPriceData } =
                      restItem.price_data;
                    return {
                      ...restItem,
                      price_data: restPriceData,
                    };
                  }
                  return restItem;
                }),
              discounts: discountOptions,
              success_url: `${process.env.PARENT_PORTAL_URL}/login?studioId=${studio.locationId.toString()}`,
              cancel_url: `${process.env.PARENT_PORTAL_URL}/login?studioId=${studio.locationId.toString()}`,
              payment_intent_data: {
                setup_future_usage: 'off_session',
                metadata: {
                  checkout_session_id: '{{CHECKOUT_SESSION_ID}}', // Stripe will replace this with the actual session ID
                  // other metadata
                },
              },
            });
          } catch (stripeError) {
            // Check if the error is related to US bank account not being enabled
            if (
              stripeError.message &&
              stripeError.message.includes('us_bank_account')
            ) {
              this.logger.warn(
                `US Bank Account payment method not enabled for studio ${studio._id}`,
              );

              // Throw error to be handled by caller
              throw new Error('Studio doesnt have ACH payment enabled');
            } else {
              // Re-throw other Stripe errors
              throw stripeError;
            }
          }

          // Generate groupId for current payments instead of creating root transaction
          const groupId = generateId();

          for (const student of createParentDto.students) {
            const tempStudentId =
              student.firstName +
              '-' +
              student.lastName +
              '-' +
              student.dob +
              '-' +
              student.gender;
            for (const enrollment of student.enrollments) {
              // Find line items that match this student and enrollment
              const studentName = `${student.firstName} ${student.lastName}`;
              const enrollmentId = enrollment.enrollmentId;

              // Filter line items for this specific student and enrollment
              const matchingLineItems = line_items.filter((item) => {
                return (
                  item.price_data.studentName === studentName &&
                  item.price_data.product_data.id === enrollmentId
                );
              });

              // Calculate the total amount for this student-enrollment combination
              const enrollmentAmount =
                matchingLineItems.reduce(
                  (acc, item) =>
                    acc + item.price_data.unit_amount * item.quantity,
                  0,
                ) / 100;

              // Get the billing date from the line items
              // First try to find it in the price_data.product_data
              let enrollmentBillingDate;

              // Look for a line item with a billing date in product_data
              const itemWithBillingDate = matchingLineItems.find(
                (item) => item.price_data?.product_data?.billingDate,
              );

              if (itemWithBillingDate) {
                enrollmentBillingDate =
                  itemWithBillingDate.price_data.product_data.billingDate;
              } else {
                // Try to find it at the line item level
                const itemWithTopLevelBillingDate = matchingLineItems.find(
                  (item) => item.billingDate,
                );

                if (itemWithTopLevelBillingDate) {
                  enrollmentBillingDate =
                    itemWithTopLevelBillingDate.billingDate;
                } else {
                  const enrollment = await this.enrollmentService.findOne(
                    enrollmentId.toString(),
                  );
                  enrollmentBillingDate = enrollment.session.billingDate;
                }
              }

              // Create a transaction for this specific student-enrollment
              await this.paymentTransactionService.create({
                studioId: Types.ObjectId.createFromHexString(
                  studio._id.toString(),
                ),
                parentId: tempParentId, // Will be set after parent creation
                studentId: tempStudentId, // Will be set after student creation
                groupId: groupId,
                type: PaymentTransactionType.ENROLLMENT,
                amount: enrollmentAmount,
                status: PaymentTransactionStatus.PENDING,
                paymentProvider: PaymentProvider.STRIPE,
                paymentMethod: createParentDto.paymentMethod as PaymentMethod,
                paymentSource:
                  PaymentTransactionSource.PARENT_REGISTRATION_FORM,
                metadata: {
                  discountSplit: discountSplit,
                  line_items: matchingLineItems.filter((item) => item !== null),
                  stripeSessionId: session.id,
                  paymentIntentId: session.payment_intent as string,
                  billingDate: enrollmentBillingDate,
                  description: `Registration Enrollment payment for ${studentName}`,
                  couponId: coupon?.couponId,
                },
              });
            }
          }
          return session.url;
        }
      }
    } catch (error) {
      throw new Error(`Failed to create checkout session: ${error.message}`);
    }
  }
}
