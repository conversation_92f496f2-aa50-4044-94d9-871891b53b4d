import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { Model, Types } from 'mongoose';

import { ParentsService } from 'src/parents/parents.service';

import { CurrencyService } from 'src/currency/currency.service';

import { StripeCommonService } from './stripe-common.service';

import { SubscriptionInvoiceService } from 'src/subscription-invoice/subscription-invoice.service';
import { InvoiceType, PaymentMethod } from 'src/stripe/type';
import { InvoiceStatus, PaymentProvider } from 'src/stripe/type';
import { StudentsService } from 'src/students/students.service';
import { StudiosService } from 'src/studios/studios.service';
import { generateId } from 'src/utils/helperFunction';
import Stripe from 'stripe';
import { InjectModel } from '@nestjs/mongoose';
import { Credential } from 'src/database/schema/stripeCredential';

@Injectable()
export class StripeRefundPaymentsService {
  private readonly logger = new Logger(StripeRefundPaymentsService.name);
  constructor(
    @InjectModel(Credential.name)
    private readonly credentialModel: Model<Credential>,
  ) {}

  /**
   * Refunds an invoice
   *
   * This method handles the complete payment flow including:
   * - Retrieving invoice, parent, and studio information
   * - Creating a Stripe Payment Intent
   * - Processing payments through Stripe Payment Intent
   *
   * @param paymentIntentId - Payment Intent ID
   * @param amount - Amount to refund
   * @returns Object with status information and payment details
   * @throws HttpException if payment processing fails or payment method is missing
   */

  async refundPayment(
    paymentIntentId: string,
    amount: number,
    locationId: string,
  ) {
    const credential = await this.credentialModel.findOne({
      studioId: locationId,
    });
    const stripe = new Stripe(credential.apiSecret, {
      apiVersion: '2025-02-24.acacia',
    });

    const refund = await stripe.refunds.create({
      payment_intent: paymentIntentId,
      amount: Math.round(amount * 100),
    });

    return refund;
  }
}
