import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import Stripe from 'stripe';
import { Credential } from 'src/database/schema/stripeCredential';

import { ParentsService } from 'src/parents/parents.service';
import { PaymentMethod } from '../type';

@Injectable()
export class StripeCardsService {
  private readonly logger = new Logger(StripeCardsService.name);

  constructor(
    @InjectModel(Credential.name) private credentialModel: Model<Credential>,
    @Inject(forwardRef(() => ParentsService))
    private readonly parentsService: ParentsService,
  ) {}
  async getCardsByEmail(email: string, locationId: string): Promise<any[]> {
    const credential = await this.credentialModel.findOne({
      studioId: locationId,
    });
    const stripe = new Stripe(credential.apiSecret, {
      apiVersion: '2025-02-24.acacia',
    });

    if (!email) {
      throw new BadRequestException('Email is required');
    }

    try {
      // Get parent from database
      let customerId;
      const parent = await this.parentsService.findOneByEmail(
        email,
        locationId,
      );
      customerId = parent.stripeCustomerId;
      if (!parent || !parent.stripeCustomerId) {
        const customers = await stripe.customers.list({
          email: email,
          limit: 1,
        });

        // If no customer found, return empty array
        if (customers.data.length === 0) {
          return [];
        }

        customerId = customers.data[0].id;
      }

      // Get customer to check default payment method
      const customer = (await stripe.customers.retrieve(
        customerId,
      )) as Stripe.Customer;
      const defaultPaymentMethodId =
        customer.invoice_settings?.default_payment_method;

      if (!defaultPaymentMethodId) {
        return [];
      }

      // Get the default payment method
      const paymentMethod = (await stripe.paymentMethods.retrieve(
        defaultPaymentMethodId as string,
      )) as Stripe.PaymentMethod;

      if (paymentMethod.type === PaymentMethod.CARD) {
        return [
          {
            id: paymentMethod.id,
            type: PaymentMethod.CARD,
            brand: paymentMethod.card.brand,
            last4: paymentMethod.card.last4,
            exp_month: paymentMethod.card.exp_month,
            exp_year: paymentMethod.card.exp_year,
            isDefault: true,
          },
        ];
      } else if (paymentMethod.type === PaymentMethod.US_BANK_ACCOUNT) {
        return [
          {
            id: paymentMethod.id,
            type: PaymentMethod.US_BANK_ACCOUNT,
            brand: 'US Bank Account',
            last4: paymentMethod.us_bank_account.last4,
            bankName: paymentMethod.us_bank_account.bank_name,
            isDefault: true,
          },
        ];
      }

      return [];
    } catch (error) {
      if (error instanceof BadRequestException) throw error;
      throw new Error(`Error fetching payment method: ${error.message}`);
    }
  }

  async generateUpdateCardLink(
    email: string,
    studioId: string,
  ): Promise<string> {
    const credential = await this.credentialModel.findOne({ studioId });
    const stripe = new Stripe(credential.apiSecret, {
      apiVersion: '2025-02-24.acacia',
    });

    if (!email) {
      throw new BadRequestException('Email is required');
    }

    try {
      // Get parent from database
      let customerId;
      const parent = await this.parentsService.findOneByEmail(email, studioId);
      customerId = parent.stripeCustomerId;
      if (!parent || !parent.stripeCustomerId) {
        const customers = await stripe.customers.list({
          email: email,
          limit: 1,
        });

        // If no customer found, throw error
        if (customers.data.length === 0) {
          throw new BadRequestException('No customer found with this email');
        }

        customerId = customers.data[0].id; // Get the actual customer ID
      }

      // List existing configurations
      const configs = await stripe.billingPortal.configurations.list();
      const configId =
        configs.data.length > 0
          ? configs.data[0].id
          : (
              await stripe.billingPortal.configurations.create({
                business_profile: {
                  headline: 'Manage your subscription',
                  privacy_policy_url: process.env.PRIVACY_POLICY_URL,
                  terms_of_service_url: process.env.TERMS_OF_SERVICE_URL,
                },
                features: {
                  invoice_history: { enabled: false },
                  payment_method_update: { enabled: true },
                  subscription_cancel: { enabled: false },
                  customer_update: { enabled: false },
                },
              })
            ).id;

      // Create portal session
      const session = await stripe.billingPortal.sessions.create({
        customer: customerId,
        return_url: process.env.PARENT_PORTAL_URL,
        configuration: configId,
      });

      return session.url;
    } catch (error) {
      if (error instanceof BadRequestException) throw error;
      throw new BadRequestException(
        `Error generating update card link: ${error.message}`,
      );
    }
  }
}
