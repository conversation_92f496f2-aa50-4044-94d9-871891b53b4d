import {
  HttpException,
  forwardRef,
  Inject,
  Injectable,
  BadRequestException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { EnrollmentService } from 'src/enrollment/enrollment.service';
import { StudentsService } from 'src/students/students.service';
import { StripeCancelSubscriptionByStudentService } from './cancel-subscription-by-student.service';
import { StripeAddStudentToEntityService } from './add-student-to-entity.service';
import { PaymentMethod, PaymentTransactionEntityType } from '../type';
import { ParentsService } from 'src/parents/parents.service';
import { ReasonType } from 'src/database/schema/walletTransaction';
import { StripeCommonService } from './stripe-common.service';
import { StripeService } from '../stripe.service';

@Injectable()
export class StripeTransferSubscriptionService {
  private readonly logger = new Logger(StripeTransferSubscriptionService.name);

  constructor(
    @Inject(forwardRef(() => StudentsService))
    private readonly studentsService: StudentsService,
    @Inject(forwardRef(() => EnrollmentService))
    private readonly enrollmentService: EnrollmentService,
    @Inject(forwardRef(() => StripeCancelSubscriptionByStudentService))
    private readonly stripeCancelSubscriptionByStudent: StripeCancelSubscriptionByStudentService,
    @Inject(forwardRef(() => StripeAddStudentToEntityService))
    private readonly stripeAddStudentToEntity: StripeAddStudentToEntityService,
    @Inject(forwardRef(() => ParentsService))
    private readonly parentsService: ParentsService,
    @Inject(forwardRef(() => StripeService))
    private readonly stripeService: StripeService,
  ) {}

  /**
   * Transfers a student's subscription from one class to another
   *
   * This method handles the transfer of a student's subscription from one class to another
   * following proration rules based on the class billing cycle.
   *
   * @param currentClassId - ID of the current class
   * @param newClassId - ID of the new class
   * @param studentId - ID of the student
   * @param locationId - ID of the studio location
   * @param noCreditRequired - If true, no credit will be transferred
   * @param newCreditAmount - The amount of credit to transfer
   * @returns Void
   * @throws Error if subscription transfer fails
   */
  async transferSubscription(
    currentClassId: string,
    newClassId: string,
    studentId: string,
    locationId: string,
    noCreditRequired: boolean,
    newCreditAmount: number,
  ) {
    try {
      // Get initial data
      const student = await this.studentsService.findOne(studentId);
      const currentEnrollment = student.enrollments.find(
        (e) => e.enrollmentId._id.toString() === currentClassId,
      );

      if (!currentEnrollment?.subscriptionId) {
        throw new BadRequestException('Current subscription ID not found');
      }

      // Get the enrollment details to check dates and billing info
      const enrollmentData =
        await this.enrollmentService.findOne(currentClassId);
      let remainingCredit = 0;

      // GUARDRAIL 1: Before class starts and payment is scheduled
      const today = new Date();
      const classStarted =
        enrollmentData.startDate && new Date(enrollmentData.startDate) <= today;

      if (!classStarted) {
        // If class hasn't started yet, transfer the full amount
        remainingCredit = enrollmentData.tuitionFee;
        this.logger.log(
          `Class hasn't started yet. Transferring full amount: ${remainingCredit}`,
        );
      }
      // GUARDRAIL 2: In between billing dates
      else if (
        currentEnrollment?.subscriptionStatus === 'active' &&
        !noCreditRequired
      ) {
        try {
          // Calculate remaining credit using the proration rules
          const { remainingCredit: calculatedCredit } =
            await this.getRemainingCredit(
              currentClassId,
              newClassId,
              studentId,
              locationId,
            );
          remainingCredit = calculatedCredit;
          this.logger.log(`Calculated prorated credit: ${remainingCredit}`);
        } catch (error) {
          // GUARDRAIL 4: If calculation fails, default to $0 credit
          this.logger.error(
            `Credit calculation failed: ${error.message}`,
            error.stack,
          );
          remainingCredit = 0;
        }
      }

      // GUARDRAIL 3: Classes priced at $0
      if (enrollmentData.tuitionFee <= 0) {
        remainingCredit = 0;
        this.logger.log('Class is free (zero fee). No credit to transfer.');
      }

      // Override calculated credit if newCreditAmount is provided
      if (newCreditAmount > 0) {
        remainingCredit = newCreditAmount;
        this.logger.log(`Using manual credit amount: ${remainingCredit}`);
      }

      const parent = await this.parentsService.findOne(
        student.parentId.toString(),
      );

      const defaultPaymentMethod =
        await this.stripeService.getCustomerDefaultPaymentMethod(
          parent.stripeCustomerId,
          locationId,
        );

      // Add credit to wallet if needed
      if (remainingCredit > 0) {
        await this.parentsService.addWalletBalance({
          studioId: locationId,
          parentId: student.parentId.toString(),
          studentId,
          paymentMethod: defaultPaymentMethod.type as PaymentMethod,
          amount: remainingCredit,
          reason: ReasonType.TRANSFER_SUBSCRIPTION,
          metadata: {
            remainingCredit,
            currentClassId,
            newClassId,
          },
        });
        this.logger.log(
          `Added ${remainingCredit} to parent wallet for student ${studentId}`,
        );
      }

      // Cancel current subscription
      await this.stripeCancelSubscriptionByStudent.cancelSubscriptionByStudent(
        currentClassId,
        locationId,
        studentId,
        true, // skipEmail flag
      );
      this.logger.log(
        `Cancelled subscription for student ${studentId} in class ${currentClassId}`,
      );

      // Add student to new class with remaining credit
      await this.stripeAddStudentToEntity.addStudentToEntity({
        entityId: newClassId,
        parentId: student.parentId.toString(),
        studentIds: [studentId],
        studioId: locationId,
        entityType: PaymentTransactionEntityType.CLASS,
      });
      this.logger.log(`Added student ${studentId} to new class ${newClassId}`);

      throw new HttpException(
        {
          message: 'Class transferred successfully',
          creditAmount: remainingCredit,
        },
        HttpStatus.OK,
      );
    } catch (error) {
      this.logger.error(
        `Failed to transfer subscription: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        { message: `Failed to transfer subscription: ${error.message}` },
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Gets the remaining credit for a student based on subscription type
   *
   * This method calculates the prorated credit amount when a student leaves a class,
   * following different rules based on the class's billing cycle.
   *
   * @param currentClassId - ID of the current class
   * @param newClassId - ID of the new class
   * @param studentId - ID of the student
   * @param locationId - ID of the studio location
   * @returns Object containing the remaining credit amount
   * @throws Error if calculation fails
   */
  async getRemainingCredit(
    currentClassId: string,
    newClassId: string,
    studentId: string,
    locationId: string,
  ) {
    try {
      // Get initial data
      const student = await this.studentsService.findOne(studentId);
      const currentEnrollment = student.enrollments.find(
        (e) => e.enrollmentId._id.toString() === currentClassId,
      );

      if (!currentEnrollment?.subscriptionId) {
        throw new BadRequestException('Current subscription ID not found');
      }

      // Get detailed enrollment data
      const enrollmentData =
        await this.enrollmentService.findOne(currentClassId);

      // If class fee is zero, return zero credit
      if (!enrollmentData.tuitionFee || enrollmentData.tuitionFee <= 0) {
        return { remainingCredit: 0 };
      }

      // If enrollment hasn't started yet and payment is scheduled
      // Simply transfer the full amount
      const today = new Date();
      if (
        enrollmentData.startDate &&
        new Date(enrollmentData.startDate) > today
      ) {
        return { remainingCredit: enrollmentData.tuitionFee };
      }

      // Get meeting days as array of weekday numbers (0-6, where 0 is Sunday)
      const meetingDays = this.getDayNumbers(enrollmentData.days);
      if (!meetingDays.length) {
        this.logger.warn(`No meeting days found for class ${currentClassId}`);
        return { remainingCredit: 0 };
      }

      const leaveDate = new Date(); // Student is leaving today
      let remainingCredit = 0;

      // Calculate credit based on billing cycle
      const billingCycle = enrollmentData.tuitionBillingCycle.toLowerCase();

      switch (billingCycle) {
        case 'one-time':
          // One-time camp/workshop
          remainingCredit = this.calculateOneTimeCredit(
            enrollmentData,
            meetingDays,
            leaveDate,
          );
          break;

        case 'monthly':
          // Monthly billing plan
          remainingCredit = this.calculateMonthlyCredit(
            enrollmentData,
            meetingDays,
            leaveDate,
          );
          break;

        case 'weekly':
          // Weekly billing plan
          remainingCredit = this.calculateWeeklyCredit(
            enrollmentData,
            meetingDays,
            leaveDate,
          );
          break;

        case 'bi-weekly':
          // Bi-weekly billing plan
          remainingCredit = this.calculateBiWeeklyCredit(
            enrollmentData,
            meetingDays,
            leaveDate,
          );
          break;

        default:
          this.logger.warn(
            `Unknown billing cycle: ${billingCycle} for class ${currentClassId}`,
          );
          remainingCredit = 0;
      }

      return { remainingCredit };
    } catch (error) {
      this.logger.error(
        `Error calculating remaining credit: ${error.message}`,
        error.stack,
      );
      // If the calculation fails, return zero credit but log the error
      return { remainingCredit: 0 };
    }
  }

  /**
   * Converts string day names to day numbers (0-6, where 0 is Sunday)
   */
  private getDayNumbers(days: string[]): number[] {
    const dayMap = {
      sunday: 0,
      monday: 1,
      tuesday: 2,
      wednesday: 3,
      thursday: 4,
      friday: 5,
      saturday: 6,
    };

    return days
      .map((day) => dayMap[day.toLowerCase()])
      .filter((day) => day !== undefined);
  }

  /**
   * Calculates credit for one-time classes/workshops
   */
  private calculateOneTimeCredit(
    enrollment: any,
    meetingDays: number[],
    leaveDate: Date,
  ): number {
    const startDate = new Date(enrollment.startDate);
    const endDate = new Date(enrollment.endDate);

    // Count total meetings
    const totalMeetings = this.countMeetingsBetweenDates(
      startDate,
      endDate,
      meetingDays,
    );

    // Count unused meetings (after leave date)
    const unusedMeetings = this.countMeetingsBetweenDates(
      new Date(leaveDate.getTime() + 86400000), // Day after leave date
      endDate,
      meetingDays,
    );

    // Calculate credit: (unused ÷ total) × total_fee
    if (totalMeetings === 0) return 0;
    const credit = (unusedMeetings / totalMeetings) * enrollment.tuitionFee;
    return parseFloat(credit.toFixed(2));
  }

  /**
   * Calculates credit for monthly subscription
   */
  private calculateMonthlyCredit(
    enrollment: any,
    meetingDays: number[],
    leaveDate: Date,
  ): number {
    // Get the billing anchor day
    const billingDay = enrollment.billingDay || 1;

    // Calculate the billing period window
    const { start, end } = this.getMonthlyBillingWindow(leaveDate, billingDay);

    // Count total meetings in window
    const totalMeetings = this.countMeetingsBetweenDates(
      start,
      end,
      meetingDays,
    );

    // Count unused meetings (after leave date)
    const unusedMeetings = this.countMeetingsBetweenDates(
      new Date(leaveDate.getTime() + 86400000), // Day after leave date
      end,
      meetingDays,
    );

    // Calculate credit: (unused ÷ total) × monthly_fee
    if (totalMeetings === 0) return 0;
    const credit = (unusedMeetings / totalMeetings) * enrollment.tuitionFee;
    return parseFloat(credit.toFixed(2));
  }

  /**
   * Calculates credit for weekly subscription
   */
  private calculateWeeklyCredit(
    enrollment: any,
    meetingDays: number[],
    leaveDate: Date,
  ): number {
    // Calculate the week window (Monday-Sunday) containing the leave date
    const { start, end } = this.getWeeklyWindow(leaveDate);

    // Count total meetings in window
    const totalMeetings = this.countMeetingsBetweenDates(
      start,
      end,
      meetingDays,
    );

    // Count unused meetings (after leave date)
    const unusedMeetings = this.countMeetingsBetweenDates(
      new Date(leaveDate.getTime() + 86400000), // Day after leave date
      end,
      meetingDays,
    );

    // Calculate credit: (unused ÷ total) × weekly_fee
    if (totalMeetings === 0) return 0;
    const credit = (unusedMeetings / totalMeetings) * enrollment.tuitionFee;
    return parseFloat(credit.toFixed(2));
  }

  /**
   * Calculates credit for bi-weekly subscription
   */
  private calculateBiWeeklyCredit(
    enrollment: any,
    meetingDays: number[],
    leaveDate: Date,
  ): number {
    // Get start date of the class (or closest approximation)
    const startDate = enrollment.startDate || new Date(enrollment.createdAt);

    // Calculate the bi-weekly window containing the leave date
    const { start, end } = this.getBiWeeklyWindow(leaveDate, startDate);

    // Count total meetings in window
    const totalMeetings = this.countMeetingsBetweenDates(
      start,
      end,
      meetingDays,
    );

    // Count unused meetings (after leave date)
    const unusedMeetings = this.countMeetingsBetweenDates(
      new Date(leaveDate.getTime() + 86400000), // Day after leave date
      end,
      meetingDays,
    );

    // Calculate credit: (unused ÷ total) × biweekly_fee
    if (totalMeetings === 0) return 0;
    const credit = (unusedMeetings / totalMeetings) * enrollment.tuitionFee;
    return parseFloat(credit.toFixed(2));
  }

  /**
   * Counts the number of specific weekdays between two dates
   */
  private countMeetingsBetweenDates(
    startDate: Date,
    endDate: Date,
    meetingDays: number[],
  ): number {
    let count = 0;
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Reset time component to ensure we're just comparing days
    start.setHours(0, 0, 0, 0);
    end.setHours(23, 59, 59, 999);

    const current = new Date(start);

    while (current <= end) {
      if (meetingDays.includes(current.getDay())) {
        count++;
      }
      current.setDate(current.getDate() + 1);
    }

    return count;
  }

  /**
   * Calculates the monthly billing window surrounding a date
   */
  private getMonthlyBillingWindow(
    date: Date,
    billingDay: number,
  ): { start: Date; end: Date } {
    const currentDate = new Date(date);

    // Find previous anchor day
    const prevAnchor = new Date(currentDate);
    prevAnchor.setDate(billingDay);
    if (prevAnchor > currentDate) {
      // If billing day hasn't occurred yet this month, go back to previous month
      prevAnchor.setMonth(prevAnchor.getMonth() - 1);
    }

    // Find next anchor day
    const nextAnchor = new Date(prevAnchor);
    nextAnchor.setMonth(nextAnchor.getMonth() + 1);

    return { start: prevAnchor, end: nextAnchor };
  }

  /**
   * Calculates the weekly window (Monday-Sunday) containing a date
   */
  private getWeeklyWindow(date: Date): { start: Date; end: Date } {
    const currentDate = new Date(date);
    const dayOfWeek = currentDate.getDay(); // 0 for Sunday, 1 for Monday, etc.

    // Find the Monday (start of week)
    const mondayOffset = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;
    const start = new Date(currentDate);
    start.setDate(currentDate.getDate() + mondayOffset);
    start.setHours(0, 0, 0, 0);

    // Find the Sunday (end of week)
    const end = new Date(start);
    end.setDate(start.getDate() + 6);
    end.setHours(23, 59, 59, 999);

    return { start, end };
  }

  /**
   * Calculates the bi-weekly window containing a date, anchored to the plan start date
   */
  private getBiWeeklyWindow(
    date: Date,
    anchorDate: Date,
  ): { start: Date; end: Date } {
    const current = new Date(date);
    const anchor = new Date(anchorDate);

    // Reset time components
    current.setHours(0, 0, 0, 0);
    anchor.setHours(0, 0, 0, 0);

    // Calculate days since anchor
    const diffTime = Math.abs(current.getTime() - anchor.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    // Calculate which bi-weekly period we're in
    const periodNumber = Math.floor(diffDays / 14);

    // Calculate start of current period
    const start = new Date(anchor);
    start.setDate(anchor.getDate() + periodNumber * 14);

    // Calculate end of current period
    const end = new Date(start);
    end.setDate(start.getDate() + 13);
    end.setHours(23, 59, 59, 999);

    return { start, end };
  }
}
