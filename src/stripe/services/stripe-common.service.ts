import {
  forwardRef,
  Inject,
  Injectable,
  Logger,
  HttpStatus,
  HttpException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import Stripe from 'stripe';
import { Proration } from 'src/database/schema/prorations';
import { CurrencyService } from 'src/currency/currency.service';
import { DiscountService } from 'src/discount/discount.service';
import { StripeCouponService } from './stripe-coupon.service';
import { Credential } from 'src/database/schema/stripeCredential';
import { StudiosService } from 'src/studios/studios.service';
import { ParentsService } from 'src/parents/parents.service';
import { CouponService } from 'src/coupon/coupon.service';
import { PaymentMethod } from '../type';

@Injectable()
export class StripeCommonService {
  private readonly logger = new Logger(StripeCommonService.name);

  constructor(
    @InjectModel(Proration.name) private prorationModel: Model<Proration>,
    @InjectModel(Credential.name) private credentialModel: Model<Credential>,
    @Inject(forwardRef(() => CurrencyService))
    private readonly currencyService: CurrencyService,
    @Inject(forwardRef(() => DiscountService))
    private readonly discountService: DiscountService,
    @Inject(forwardRef(() => StripeCouponService))
    private readonly stripeCouponService: StripeCouponService,
    @Inject(forwardRef(() => StudiosService))
    private readonly studioService: StudiosService,
    @Inject(forwardRef(() => ParentsService))
    private readonly parentsService: ParentsService,
    private readonly couponService: CouponService,
  ) {}

  /**
   * Creates a Stripe instance for a given studio
   */
  async createStripeInstance(studioId: string): Promise<Stripe> {
    const credential = await this.credentialModel.findOne({ studioId });
    return new Stripe(credential.apiSecret, {
      apiVersion: '2025-02-24.acacia',
    });
  }

  /**
   * Creates or retrieves a Stripe customer
   */
  async getOrCreateCustomer(
    studioId: string,
    email: string,
    firstName: string,
    lastName: string,
  ): Promise<Stripe.Customer> {
    const stripe = await this.createStripeInstance(studioId);
    const existingCustomers = await stripe.customers.list({
      email: email,
      limit: 1,
    });

    if (existingCustomers.data.length > 0) {
      return existingCustomers.data[0];
    }

    return await stripe.customers.create({
      email: email,
      name: `${firstName} ${lastName}`,
    });
  }

  /**
   * Creates a Stripe coupon with proper error handling
   */
  async createStripeCoupon(params: {
    type: 'flat' | 'percent' | 'amount';
    totalDiscount: number;
    name: string;
    studioId: string;
  }): Promise<string | null> {
    const { type, totalDiscount, name, studioId } = params;
    const stripe = await this.createStripeInstance(studioId);

    const studentIdObj = Types.ObjectId.createFromHexString(studioId);
    const currency = await this.currencyService.findByStudioId(studentIdObj);
    const discount = await this.discountService.findOne(studioId);

    if (!discount) return null;

    const existingCoupon = await this.couponService.findByAmount({
      studioId: studentIdObj,
      amount: totalDiscount,
      category: discount.category,
      discountRules: discount.discountRules,
    });

    if (existingCoupon) {
      return existingCoupon.stripeId;
    }

    const couponData = {
      name,
      duration: (discount.discountRules &&
      discount.discountRules === 'first-month'
        ? 'once'
        : 'forever') as Stripe.CouponCreateParams.Duration,
      amount_off: Math.round(totalDiscount * 100),
      currency: currency.name.toLowerCase() || 'usd',
    };

    const stripeCoupon = await stripe.coupons.create(couponData);
    await this.couponService.create({
      studioId: studentIdObj,
      type,
      value: totalDiscount,
      name,
      category: discount.category,
      discountRules: discount.discountRules,
      stripeId: stripeCoupon.id,
    });

    return stripeCoupon.id;
  }

  /**
   * Gets payment method details from a payment intent
   */
  async getPaymentMethodDetails(
    paymentIntent: Stripe.PaymentIntent,
    stripe: Stripe,
  ): Promise<{ type: string; details: any }> {
    let paymentMethodType = 'unknown';
    let paymentMethodDetails = null;

    if (paymentIntent.payment_method_types?.length > 0) {
      paymentMethodType = paymentIntent.payment_method_types[0];
    }

    if (paymentIntent.payment_method) {
      try {
        const paymentMethod = await stripe.paymentMethods.retrieve(
          paymentIntent.payment_method as string,
        );
        paymentMethodType = paymentMethod.type;
        paymentMethodDetails = paymentMethod[paymentMethod.type];
      } catch (error) {
        this.logger.error('Error retrieving payment method:', error);
      }
    }

    return {
      type:
        paymentMethodType === PaymentMethod.US_BANK_ACCOUNT
          ? PaymentMethod.US_BANK_ACCOUNT
          : paymentMethodType,
      details: paymentMethodDetails,
    };
  }

  /**
   * Initializes a Stripe instance with the provided credentials
   * @param studioId - The studio ID to retrieve credentials for
   * @returns A Stripe instance
   */
  async initializeStripe(
    studioId: string,
  ): Promise<{ stripe: Stripe; credential: any }> {
    const credential = await this.credentialModel.findOne({
      studioId,
    });

    if (!credential) {
      throw new Error(`No Stripe credentials found for studio ${studioId}`);
    }

    const stripe = new Stripe(credential.apiSecret, {
      apiVersion: '2025-02-24.acacia',
    });

    return { stripe, credential };
  }

  /**
   * Calculates discounts for a student
   * @param student - Student information
   * @param studioId - Studio ID
   * @returns Total discount and discount split
   */
  async calculateDiscounts(params: {
    students: Array<{
      studentId: string;
      firstName: string;
      lastName: string;
      tuitionFee: number;
      enrollmentId: string; // Current enrollment being processed
      existingEnrollments?: Array<{
        // For multi-class positioning
        enrollmentId: string;
        subscriptionStatus: string;
      }>;
    }>;
    existingStudentIds?: string[]; // For multi-student positioning
    studioId: string;
  }): Promise<{
    totalDiscount: number;
    discountSplit: Record<string, number>;
  }> {
    const { studioId } = params;
    let totalDiscount = 0;
    const discountSplit: Record<string, number> = {};

    // Loop through all students
    for (let i = 0; i < params.students.length; i++) {
      const student = params.students[i];

      // Calculate student position for multi-student discounts
      const studentPosition = params.existingStudentIds
        ? params.existingStudentIds.length + i + 1
        : i + 1;

      // Calculate class position for multi-class discounts
      const existingActiveEnrollments =
        student.existingEnrollments?.filter(
          (e) =>
            e.subscriptionStatus === 'active' ||
            e.subscriptionStatus === 'scheduled',
        ) || [];
      const classPosition = existingActiveEnrollments.length + 1;

      const studentData = {
        studentId: student.studentId,
        firstName: student.firstName,
        lastName: student.lastName,
        classPosition,
        studentPosition,
        tuitionFee: student.tuitionFee,
        enrollmentId: student.enrollmentId,
      };

      // Calculate both multi-class and multi-student discounts
      const [multiClassDiscount, multiStudentDiscount] = await Promise.all([
        this.discountService.calculateDiscount({
          studioId,
          student: studentData,
          category: 'multi-class',
        }),
        this.discountService.calculateDiscount({
          studioId,
          student: studentData,
          category: 'multi-student',
        }),
      ]);

      // Combine discounts
      const combinedDiscount =
        multiClassDiscount.totalDiscount + multiStudentDiscount.totalDiscount;
      discountSplit[student.studentId] = combinedDiscount;
      totalDiscount += combinedDiscount;
    }

    return { totalDiscount, discountSplit };
  }

  /**
   * Creates a Stripe coupon for discounts
   * @param totalDiscount - Total discount amount
   * @param studioId - Studio ID
   * @returns Stripe coupon ID
   */
  async createDiscountCoupon(
    totalDiscount: number,
    studioId: string,
  ): Promise<string | null> {
    if (totalDiscount <= 0) {
      return null;
    }

    const stripeCouponId = await this.stripeCouponService.createStripeCoupon({
      type: 'amount',
      totalDiscount,
      name: 'Combined Discount',
      studioId,
    });

    if (!stripeCouponId) {
      return null;
    }

    return stripeCouponId;
  }

  /**
   * Gets or creates a Stripe customer for a parent
   * @param parent - Parent information
   * @param stripe - Stripe instance
   * @returns Stripe customer ID
   */
  async getOrCreateStripeCustomer(
    parent: any,
    stripe: Stripe,
  ): Promise<string> {
    if (parent.stripeCustomerId) {
      return parent.stripeCustomerId;
    }

    // Look up customer by email
    const customers = await stripe.customers.list({
      email: parent.email,
      limit: 1,
    });

    let customerId;
    if (customers.data.length > 0 && customers.data[0].email === parent.email) {
      customerId = customers.data[0].id;
    } else {
      const newCustomer = await stripe.customers.create({
        email: parent.email,
        name: `${parent.firstName} ${parent.lastName}`,
      });
      customerId = newCustomer.id;
    }

    // Update parent with customer ID if needed
    if (!parent.stripeCustomerId) {
      await this.parentsService.updateByProperty('_id', parent._id, {
        stripeCustomerId: customerId,
      });
    }

    return customerId;
  }

  /**
   * Checks if a parent has a payment method and sets up a new one if needed
   * @param customerId - Stripe customer ID
   * @param paymentMethod - Payment method type
   * @param stripe - Stripe instance
   * @param credential - Stripe credential
   * @returns Payment method information
   */
  async checkPaymentMethod(
    customerId: string,
    paymentMethod: string,
    stripe: Stripe,
    credential: any,
  ): Promise<{
    hasPaymentMethod: boolean;
    setupIntent?: any;
    stripePublicApiKey?: string;
  }> {
    // Get payment methods
    const paymentMethods = await stripe.paymentMethods.list({
      customer: customerId,
      type: paymentMethod as Stripe.PaymentMethodListParams.Type,
    });

    if (paymentMethods.data.length === 0) {
      // Create setup intent for adding payment method
      const setupIntent = await stripe.setupIntents.create({
        customer: customerId,
        usage: 'off_session',
      });

      return {
        hasPaymentMethod: false,
        setupIntent: setupIntent,
        stripePublicApiKey: credential.apiKey,
      };
    }

    // Set default payment method
    await stripe.customers.update(customerId, {
      invoice_settings: {
        default_payment_method: paymentMethods.data[0].id,
      },
    });

    return { hasPaymentMethod: true };
  }

  /**
   * Checks if a parent has a payment method and sets up a new one if needed
   * @param customerId - Stripe customer ID
   * @param paymentMethod - Payment method type
   * @param stripe - Stripe instance
   * @param credential - Stripe credential
   * @returns Payment method information
   */
  async getCustomerDefaultPaymentMethod(customerId: string, stripe: Stripe) {
    // Get payment methods
    const paymentMethods = await stripe.paymentMethods.list({
      customer: customerId,
    });

    // Get customer to check default payment method
    const customer = await stripe.customers.retrieve(customerId);

    const defaultPaymentMethod = paymentMethods.data.find(
      (pm) =>
        pm.id === (customer as any).invoice_settings?.default_payment_method,
    );

    return defaultPaymentMethod || paymentMethods.data[0];
  }

  /**
   * Gets the currency for a product or studio
   * @param product - Product information
   * @param studioId - Studio ID
   * @returns Currency code
   */
  async getCurrency(product: any, studioId: string): Promise<string> {
    let currency;

    if (product && product.data && product.data.length > 0) {
      currency = product.data[0].currency;
    } else {
      const studioCurrency = await this.currencyService.findByStudioId(
        typeof studioId === 'string'
          ? Types.ObjectId.createFromHexString(studioId)
          : studioId,
      );
      currency = studioCurrency.name.toLowerCase();
    }

    return currency;
  }

  /**
   * Handles payment method errors
   * @param error - Error object
   * @throws HttpException with appropriate status and message
   */
  handlePaymentMethodError(error: any): never {
    if (
      error.type === 'StripeInvalidRequestError' &&
      error.message.includes('missing a payment method')
    ) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message:
            "The parent doesn't have a payment method on file. Please add a card to their account to proceed with the payment.",
          error: 'MISSING_PAYMENT_METHOD',
        },
        HttpStatus.BAD_REQUEST,
      );
    } else if (
      error.type === 'StripeInvalidRequestError' &&
      error.message.includes('empty string')
    ) {
      throw new HttpException(
        {
          status: HttpStatus.BAD_REQUEST,
          message:
            'Payment processor is not available. Please check your payment method.',
          error: 'PAYMENT_PROCESSOR_NOT_AVAILABLE',
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    if (error.status === 200) {
      throw new HttpException(
        {
          status: HttpStatus.PAYMENT_REQUIRED,
          message: 'Payment required',
        },
        HttpStatus.PAYMENT_REQUIRED,
      );
    }

    throw new HttpException(
      {
        status: HttpStatus.INTERNAL_SERVER_ERROR,
        message: 'Failed to process payment',
        error: error.message,
      },
      HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }
}
