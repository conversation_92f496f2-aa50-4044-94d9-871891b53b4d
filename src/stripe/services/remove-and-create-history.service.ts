import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Credential } from 'src/database/schema/stripeCredential';
import { EnrollmentService } from 'src/enrollment/enrollment.service';
import { TransactionService } from 'src/transaction/transaction.service';
import { ParentsService } from 'src/parents/parents.service';
import { StudentsService } from 'src/students/students.service';

import { EnrollmentHistoryService } from 'src/enrollment-history/enrollment-history.service';

import { EventHistoryService } from 'src/event-history/event-history.service';
import { Student } from 'src/database/schema/student';

@Injectable()
export class StripeHistoryService {
  private readonly logger = new Logger(StripeHistoryService.name);
  constructor(
    @InjectModel(Credential.name) private credentialModel: Model<Credential>,
    @Inject(forwardRef(() => ParentsService))
    private readonly parentsService: ParentsService,
    @Inject(forwardRef(() => StudentsService))
    private readonly studentsService: StudentsService,
    @Inject(forwardRef(() => TransactionService))
    private readonly transactionService: TransactionService,
    @Inject(forwardRef(() => EnrollmentService))
    private readonly enrollmentService: EnrollmentService,
    @Inject(forwardRef(() => EnrollmentHistoryService))
    private readonly enrollmentHistoryService: EnrollmentHistoryService,
    @Inject(forwardRef(() => EventHistoryService))
    private readonly eventHistoryService: EventHistoryService,
    @InjectModel(Student.name) private studentModel: Model<Student>,
  ) {}
  /**
   * Removes an enrollment from a student and creates an enrollment history record
   *
   * This method handles the removal of an enrollment from a student's record and the creation of a corresponding history entry
   *
   * @param studentId - ID of the student to remove the enrollment from
   * @param parentId - ID of the parent associated with the student
   * @param classId - ID of the class to remove from the student's enrollments
   * @param locationId - ID of the studio location
   * @param status - Status of the enrollment (e.g., 'cancelled', 'completed')
   * @returns Void
   * @throws Error if enrollment removal or history creation fails
   */
  async removeEnrollmentAndCreateHistory(
    studentId: Types.ObjectId,
    parentId: Types.ObjectId,
    classId: string,
    locationId: string,
    status: string,
  ) {
    // Remove enrollment using findOneAndUpdate with $pull
    await this.studentModel.findOneAndUpdate(
      { _id: studentId },
      {
        $pull: {
          enrollments: {
            enrollmentId: Types.ObjectId.createFromHexString(classId),
          },
        },
      },
      { new: true },
    );

    try {
      await this.enrollmentHistoryService.create(
        {
          studentId: studentId.toString(),
          parentId: parentId.toString(),
          enrollments: [
            {
              enrollmentId: classId,
              status: status,
            },
          ],
        },
        locationId,
      );
    } catch (error) {
      console.error('Failed to create enrollment history:', error);
      throw new Error('Failed to create enrollment history record');
    }
  }
  async removeEventAndCreateHistory(
    studentId: Types.ObjectId,
    parentId: Types.ObjectId,
    eventId: string,
    locationId: string,
    status: string,
  ) {
    // Remove event using findOneAndUpdate with $pull
    await this.studentModel.findOneAndUpdate(
      { _id: studentId },
      {
        $pull: {
          events: {
            eventId: Types.ObjectId.createFromHexString(eventId),
          },
        },
      },
      { new: true },
    );

    try {
      await this.eventHistoryService.create(
        {
          studentId: studentId.toString(),
          parentId: parentId.toString(),
          events: [
            {
              eventId: eventId,
              status: status,
            },
          ],
        },
        locationId,
      );
    } catch (error) {
      console.error('Failed to create enrollment history:', error);
      throw new Error('Failed to create enrollment history record');
    }
  }
}
