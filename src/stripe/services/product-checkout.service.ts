import {
  forwardRef,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import Stripe from 'stripe';
import { Credential } from 'src/database/schema/stripeCredential';
import { StudiosService } from 'src/studios/studios.service';
import { EnrollmentService } from 'src/enrollment/enrollment.service';
import { TransactionService } from 'src/transaction/transaction.service';
import { ParentsService } from 'src/parents/parents.service';
import { StudentsService } from 'src/students/students.service';
import { Proration } from 'src/database/schema/prorations';
import { CurrencyService } from 'src/currency/currency.service';
import { DiscountService } from 'src/discount/discount.service';
import { EventsService } from 'src/events/events.service';
import { CheckoutSessionCompletedHandler } from './event-handlers/checkout-session-completed.handler';
import { StripeCouponService } from './stripe-coupon.service';
import { StripeCommonService } from './stripe-common.service';
import { PaymentTransactionService } from 'src/payment-transaction/payment-transaction.service';
import { PaymentMethod, PaymentProvider } from 'src/stripe/type';
import { PaymentTransactionSource, PaymentTransactionStatus } from '../type';
import { PaymentTransactionType } from '../type';
import { generateId } from 'src/utils/helperFunction';
import { PaymentTransaction } from 'src/database/schema/paymentTransaction';
import {
  calculateProratedAmountFromEntity,
  isScheduledEntity,
} from 'src/utils/helpers/billing-proration';
import { getPaymentMethodTypes } from '../../utils/helperFunction';

@Injectable()
export class StripeProductCheckoutService {
  private readonly logger = new Logger(StripeProductCheckoutService.name);
  constructor(
    @InjectModel(Credential.name) private credentialModel: Model<Credential>,
    @InjectModel(PaymentTransaction.name)
    private readonly paymentTransactionModel: Model<PaymentTransaction>,
    @Inject(forwardRef(() => ParentsService))
    private readonly parentsService: ParentsService,
    @Inject(forwardRef(() => StudentsService))
    private readonly studentsService: StudentsService,
    @Inject(forwardRef(() => TransactionService))
    private readonly transactionService: TransactionService,
    @Inject(forwardRef(() => EnrollmentService))
    private readonly enrollmentService: EnrollmentService,
    @Inject(forwardRef(() => EventsService))
    private readonly eventService: EventsService,
    @Inject(forwardRef(() => StudiosService))
    private readonly studioService: StudiosService,
    @InjectModel(Proration.name) private prorationModel: Model<Proration>,
    @Inject(forwardRef(() => CurrencyService))
    private readonly currencyService: CurrencyService,
    @Inject(forwardRef(() => DiscountService))
    private readonly discountService: DiscountService,
    @Inject(forwardRef(() => StripeCouponService))
    private readonly stripeCouponService: StripeCouponService,
    @Inject(forwardRef(() => CheckoutSessionCompletedHandler))
    private readonly CheckoutSessionCompletedHandler: CheckoutSessionCompletedHandler,
    @Inject(forwardRef(() => StripeCommonService))
    private readonly stripeCommonService: StripeCommonService,
    @Inject(forwardRef(() => PaymentTransactionService))
    private readonly paymentTransactionService: PaymentTransactionService,
  ) {}
  /**
   * Creates a Stripe checkout session for product purchases
   *
   * This method handles the checkout process for class enrollments or events including:
   * - Retrieving studio credentials and customer information
   * - Calculating prorated tuition fees based on billing cycles
   * - Applying multi-class and multi-student discounts
   * - Creating or retrieving Stripe customers
   * - Generating line items with appropriate fees (tuition, registration, costume)
   * - Creating and returning checkout sessions
   *
   * @param body - Checkout data containing studioId, parentId, studentIds, checkoutType, and checkoutId
   * @returns Object containing checkout URL and status information
   * @throws Error if credential retrieval or checkout session creation fails
   */
  async createProductCheckoutSession(body: any) {
    try {
      const { stripe, credential } =
        await this.stripeCommonService.initializeStripe(body.studioId);

      const studio = await this.studioService.findByLocationId(body.studioId);
      const parent = await this.parentsService.findOne(body.parentId);
      const existingStudents =
        await this.studentsService.getActiveStudentCountByParentId(
          body.parentId,
        );

      let checkoutEntity;
      if (body.checkoutType === 'event') {
        checkoutEntity = await this.eventService.findOne(body.checkoutId);
      } else {
        checkoutEntity = await this.enrollmentService.findOne(body.checkoutId);
      }

      // Prepare student enrollments data with tuition-based sorting
      const studentsWithEnrollments = await Promise.all(
        body.studentIds.map(async (studentId) => {
          const student = await this.studentsService.findById(studentId);
          const tuitionFee = checkoutEntity.tuitionFee;

          return {
            studentId,
            firstName: student.firstName,
            lastName: student.lastName,
            tuitionFee,
            enrollmentId: checkoutEntity._id.toString(),
            existingEnrollments:
              body.checkoutType === 'event'
                ? student.events?.filter(
                    (e) =>
                      e.subscriptionStatus === 'active' ||
                      e.subscriptionStatus === 'scheduled',
                  ) || []
                : student.enrollments?.filter(
                    (e) =>
                      e.subscriptionStatus === 'active' ||
                      e.subscriptionStatus === 'scheduled',
                  ) || [],
          };
        }),
      );

      // Sort students by tuition fee descending
      studentsWithEnrollments.sort((a, b) => b.tuitionFee - a.tuitionFee);

      const { totalDiscount, discountSplit } =
        await this.stripeCommonService.calculateDiscounts({
          students: studentsWithEnrollments,
          existingStudentIds: existingStudents.studentIds,
          studioId: body.studioId,
        });

      let discountOptions;
      if (totalDiscount > 0) {
        const stripeCouponId =
          await this.stripeCouponService.createStripeCoupon({
            type: 'amount',
            totalDiscount,
            name: 'Combined Discount',
            studioId: body.studioId,
          });
        this.logger.log('stripeCouponId', stripeCouponId);
        discountOptions = [
          {
            coupon: stripeCouponId,
          },
        ];
      }

      let product = null;
      if (checkoutEntity.productId_stripe) {
        product = await stripe.prices.list({
          product: checkoutEntity.productId_stripe,
        });
      }
      let currency;
      if (product && product.data.length > 0) {
        currency = product.data[0].currency;
      } else {
        currency = (
          await this.currencyService.findByStudioId(
            Types.ObjectId.createFromHexString(body.studioId),
          )
        ).name.toLowerCase();
      }

      // First find or create customer
      const customers = await stripe.customers.list({
        email: parent.email,
        limit: 1,
      });

      let customer_id;
      if (parent.stripeCustomerId) {
        customer_id = parent.stripeCustomerId;
      } else {
        if (
          customers.data.length > 0 &&
          customers.data[0].email === parent.email
        ) {
          customer_id = customers.data[0].id;
        } else {
          const newCustomer = await stripe.customers.create({
            email: parent.email,
            name: `${parent.firstName} ${parent.lastName}`,
          });
          customer_id = newCustomer.id;
        }
        await this.parentsService.updateByProperty('_id', parent._id, {
          stripeCustomerId: customer_id,
        });
      }

      // Get parent document
      const sessionId = checkoutEntity.session?._id?.toString();

      // Create line items array
      let line_items = [];
      let walletAmountUsed = 0;
      const sessionIdList = new Set();
      const walletBalance = await this.parentsService.getWalletBalance(
        body.parentId,
        body.studioId,
      );

      // Check for existing transactions with session fees
      const existingTransactions = await this.paymentTransactionModel.find({
        parentId: Types.ObjectId.createFromHexString(parent._id.toString()),
        'metadata.sessionId': sessionId,
        'metadata.line_items': {
          $elemMatch: {
            'price_data.product_data.description': 'Session Registration Fee',
          },
        },
      });

      // Add session fee if not already paid and no existing transactions with session fee
      if (
        sessionId &&
        checkoutEntity.session?.isRegistrationFee &&
        !parent.isSessionFeePaid?.[sessionId] &&
        existingTransactions.length === 0
      ) {
        sessionIdList.add(sessionId);
        line_items.push({
          price_data: {
            currency: currency,
            product_data: {
              name: checkoutEntity.session.name,
              description: 'Session Registration Fee',
            },
            unit_amount: checkoutEntity.session.registrationFeeAmount * 100,
          },
          quantity: 1,
        });
      }

      if (checkoutEntity.registrationFeeAmount > 0) {
        // Add class registration fee
        line_items.push({
          price_data: {
            currency: currency,
            product_data: {
              name: checkoutEntity.title,
              description: 'Registration Fee',
            },
            unit_amount: checkoutEntity.registrationFeeAmount * 100,
          },
          quantity: body.studentIds.length,
        });
      }

      // Add costume fee if applicable
      if (checkoutEntity.costumeFee > 0) {
        line_items.push({
          price_data: {
            currency: currency,
            product_data: {
              name: checkoutEntity.title,
              description: 'Costume Fee',
            },
            unit_amount: checkoutEntity.costumeFee * 100,
          },
          quantity: body.studentIds.length,
        });
      }

      // Calculate prorated tuition fee
      const proratedAmount = calculateProratedAmountFromEntity(checkoutEntity);

      if (proratedAmount > 0) {
        // Add tuition fee with appropriate proration
        line_items.push({
          price_data: {
            currency: currency,
            product_data: {
              name: checkoutEntity.title,
              description: 'Tuition Fee',
            },
            unit_amount: Math.round(proratedAmount * 100),
          },
          quantity: body.studentIds.length,
        });
      }

      let session_url;
      const totalAmount =
        line_items.reduce(
          (acc, item) => acc + item.price_data.unit_amount * item.quantity,
          0,
        ) / 100;

      const isScheduled = isScheduledEntity(checkoutEntity);

      let paymentMethod: PaymentMethod = null;
      let paymentMethodId;
      if (body.paymentMethod === null) {
        const defaultPaymentMethod =
          await this.stripeCommonService.getCustomerDefaultPaymentMethod(
            customer_id,
            stripe,
          );
        if (defaultPaymentMethod) {
          if (
            Object.values(PaymentMethod).includes(
              defaultPaymentMethod.type as PaymentMethod,
            )
          ) {
            paymentMethod = defaultPaymentMethod.type as PaymentMethod;
          } else {
            paymentMethod = PaymentMethod.CARD;
          }
        } else {
          paymentMethod = PaymentMethod.CARD;
        }
      } else {
        if (Object.values(PaymentMethod).includes(body.paymentMethod)) {
          paymentMethod = body.paymentMethod as PaymentMethod;
        } else {
          paymentMethod = PaymentMethod.CARD;
        }

        // Get payment methods
      }

      const paymentMethods = await stripe.paymentMethods.list({
        customer: customer_id,
        type: paymentMethod.toLowerCase() as Stripe.PaymentMethodListParams.Type,
      });

      if (isScheduled && totalAmount > 0) {
        // If no payment methods exist, generate and return the card update URL
        if (paymentMethods.data.length === 0) {
          const setupIntent = await stripe.setupIntents.create({
            payment_method_types: [paymentMethod],
            customer: customer_id,
            usage: 'off_session',
          });

          return {
            status: HttpStatus.OK,
            message: 'Payment method not found',
            checkoutUrl: null,
            clientSecret: setupIntent.client_secret,
            stripePublicApiKey: credential.apiKey,
          };
        }
      }

      if (paymentMethods.data.length > 0) {
        paymentMethodId = paymentMethods.data[0].id;
        // Set the first payment method as the default
        await stripe.customers.update(customer_id, {
          invoice_settings: {
            default_payment_method: paymentMethodId,
          },
        });
      }

      let stripeSession = null;
      // Initialize wallet variables - will only be used for immediate payments
      const originalLineItems = [...line_items]; // Keep original for transaction records

      // Generate groupId for all transactions instead of creating root transaction
      const groupId = generateId();

      if (!isScheduled) {
        // Apply wallet balance for immediate payments only
        if (walletBalance > 0 && totalAmount > 0) {
          walletAmountUsed = Math.min(walletBalance, totalAmount);

          if (walletAmountUsed > 0) {
            // Adjust line items for Stripe checkout
            const remainingAmount = totalAmount - walletAmountUsed;
            if (remainingAmount > 0) {
              // Only create Stripe session if there's remaining amount after wallet deduction
              const adjustmentRatio = remainingAmount / totalAmount;
              line_items = line_items.map((item) => ({
                ...item,
                price_data: {
                  ...item.price_data,
                  unit_amount: Math.round(
                    item.price_data.unit_amount * adjustmentRatio,
                  ),
                },
              }));
            } else {
              // If wallet covers entire amount, no need for Stripe session
              line_items = [];
            }
          }
        }

        const paymentMetadata = {
          line_items: originalLineItems,
          discountAmount: totalDiscount,
          totalAmount: totalAmount - totalDiscount,
          remainingAmount: totalAmount - walletAmountUsed - totalDiscount,
          walletAmountUsed: walletAmountUsed,
          originalAmount: totalAmount,
        };

        if (line_items.length > 0) {
          // Get appropriate payment method types based on studio currency
          const paymentMethodTypes = getPaymentMethodTypes(
            currency.toUpperCase(),
          );

          // Create Stripe session for remaining amount after wallet deduction
          try {
            stripeSession = await stripe.checkout.sessions.create({
              customer: customer_id,
              payment_method_types: paymentMethodTypes,
              payment_method_options: {
                ...(paymentMethodTypes.includes('us_bank_account') && {
                  us_bank_account: {
                    financial_connections: {
                      permissions: ['payment_method'],
                    },
                    verification_method: 'instant', // Can be 'instant' or 'microdeposits'
                  },
                }),
                card: {
                  setup_future_usage: 'off_session',
                },
              },
              mode: 'payment',
              line_items: line_items.filter((item) => item !== null),
              discounts: discountOptions,
              success_url: `${process.env.PARENT_PORTAL_URL}/login?studioId=${studio.locationId.toString()}`,
              cancel_url: `${process.env.PARENT_PORTAL_URL}/login?studioId=${studio.locationId.toString()}`,
              payment_intent_data: {
                setup_future_usage: 'off_session',
                metadata: {
                  groupId: groupId,
                },
              },
            } as Stripe.Checkout.SessionCreateParams);
          } catch (stripeError) {
            // Check if the error is related to US bank account not being enabled
            if (
              stripeError.message &&
              stripeError.message.includes('us_bank_account')
            ) {
              this.logger.warn(
                `US Bank Account payment method not enabled for studio ${studio._id}`,
              );

              // Return error response to frontend
              return {
                status: HttpStatus.BAD_REQUEST,
                message: 'Studio doesnt have ACH payment enabled',
                checkoutUrl: null,
                clientSecret: null,
                stripePublicApiKey: null,
              };
            } else {
              // Re-throw other Stripe errors
              throw stripeError;
            }
          }
          session_url = stripeSession.url;
        } else {
          // Wallet covers entire amount - no Stripe session needed
          session_url = null;
        }

        // First, let's modify the transaction creation logic
        const amountPerStudent = totalAmount / body.studentIds.length;
        await Promise.all(
          body.studentIds.map(async (studentId, index) => {
            // For the first student, include all line items
            // For other students, filter out the session registration fee
            const studentLineItems =
              index === 0
                ? originalLineItems.map((item) => ({ ...item, quantity: 1 }))
                : originalLineItems
                    .filter(
                      (item) =>
                        item.price_data?.product_data?.description !==
                        'Session Registration Fee',
                    )
                    .map((item) => ({ ...item, quantity: 1 }));

            return this.paymentTransactionService.create({
              studioId: Types.ObjectId.createFromHexString(
                studio._id.toString(),
              ),
              parentId: Types.ObjectId.createFromHexString(body.parentId),
              studentId: Types.ObjectId.createFromHexString(studentId),
              groupId: groupId,
              paymentSource: PaymentTransactionSource.PARENT_PORTAL_PRODUCT_BUY,
              type:
                body.checkoutType === 'event'
                  ? PaymentTransactionType.EVENT
                  : PaymentTransactionType.ENROLLMENT,
              typeId: Types.ObjectId.createFromHexString(
                checkoutEntity._id.toString(),
              ),
              amount: amountPerStudent, // Use recalculated amount
              status:
                totalAmount > 0
                  ? PaymentTransactionStatus.PENDING
                  : PaymentTransactionStatus.FREE,
              paymentProvider: PaymentProvider.STRIPE,
              paymentMethod: paymentMethod,
              metadata: {
                ...paymentMetadata,
                line_items: studentLineItems, // Use filtered line items
                discountSplit,
                sessionId: checkoutEntity.session._id.toString(),
                stripeSessionId: stripeSession ? stripeSession.id : '',
                paymentIntentId: '',
                description: `${body.checkoutType} payment`,
                billingDate:
                  checkoutEntity.session?.billingDate ||
                  new Date().toISOString(),
              },
            });
          }),
        );
      } else {
        // Scheduled payments - no wallet balance applied
        const paymentMetadata = {
          line_items: originalLineItems,
          discountAmount: totalDiscount,
          totalAmount: totalAmount - totalDiscount,
          remainingAmount: totalAmount - totalDiscount, // No wallet deduction for scheduled payments
          walletAmountUsed: 0, // No wallet balance used for scheduled payments
          originalAmount: totalAmount,
        };

        // First, let's modify the transaction creation logic
        const amountPerStudent = totalAmount / body.studentIds.length;
        await Promise.all(
          body.studentIds.map(async (studentId, index) => {
            // For the first student, include all line items
            // For other students, filter out the session registration fee
            const studentLineItems =
              index === 0
                ? originalLineItems.map((item) => ({ ...item, quantity: 1 }))
                : originalLineItems
                    .filter(
                      (item) =>
                        item.price_data?.product_data?.description !==
                        'Session Registration Fee',
                    )
                    .map((item) => ({ ...item, quantity: 1 }));

            return await this.paymentTransactionService.create({
              studioId: Types.ObjectId.createFromHexString(
                studio._id.toString(),
              ),
              parentId: Types.ObjectId.createFromHexString(body.parentId),
              studentId: Types.ObjectId.createFromHexString(studentId),
              groupId: groupId,
              paymentSource: PaymentTransactionSource.PARENT_PORTAL_PRODUCT_BUY,
              type:
                body.checkoutType === 'event'
                  ? PaymentTransactionType.EVENT
                  : PaymentTransactionType.ENROLLMENT,
              typeId: Types.ObjectId.createFromHexString(
                checkoutEntity._id.toString(),
              ),
              amount: amountPerStudent, // Use recalculated amount
              status:
                totalAmount > 0
                  ? PaymentTransactionStatus.SCHEDULED
                  : PaymentTransactionStatus.FREE,
              paymentProvider: PaymentProvider.STRIPE,
              paymentMethod: paymentMethod,
              metadata: {
                ...paymentMetadata,
                line_items: studentLineItems, // Use filtered line items
                discountSplit,
                sessionId: checkoutEntity.session._id.toString(),
                stripeSessionId: '',
                paymentIntentId: '',
                description: `${body.checkoutType} payment`,
                billingDate:
                  checkoutEntity.session?.billingDate ||
                  new Date().toISOString(),
              },
            });
          }),
        );

        session_url = null;

        await this.CheckoutSessionCompletedHandler.handleEvent(
          null,
          stripe,
          groupId,
          true,
        );
      }

      if (stripeSession) {
        await stripe.checkout.sessions.update(stripeSession.id, {
          metadata: {
            groupId: groupId,
            checkoutSessionId: stripeSession.id,
          },
        });
      }

      // Handle wallet-only payments for immediate transactions
      if (!isScheduled && line_items.length === 0) {
        await this.CheckoutSessionCompletedHandler.handleEvent(
          null, //no session id for wallet only payments
          stripe,
          groupId,
          false,
        );
      }

      return {
        status: HttpStatus.OK,
        message: 'Checkout session successfully created',
        checkoutUrl: session_url,
        clientSecret: null,
        stripePublicApiKey: null,
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}
