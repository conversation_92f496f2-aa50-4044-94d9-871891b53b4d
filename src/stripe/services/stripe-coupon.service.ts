import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import Stripe from 'stripe';
import { Credential } from 'src/database/schema/stripeCredential';
import { CurrencyService } from 'src/currency/currency.service';
import { DiscountService } from 'src/discount/discount.service';
import { CouponService } from 'src/coupon/coupon.service';

@Injectable()
export class StripeCouponService {
  private readonly logger = new Logger(StripeCouponService.name);

  constructor(
    @InjectModel(Credential.name) private credentialModel: Model<Credential>,
    @Inject(forwardRef(() => CurrencyService))
    private readonly currencyService: CurrencyService,
    @Inject(forwardRef(() => DiscountService))
    private readonly discountService: DiscountService,
    @Inject(forwardRef(() => CouponService))
    private readonly couponService: CouponService,
  ) {}
  async createStripeCoupon(params: {
    type: 'flat' | 'percent' | 'amount';
    totalDiscount: number;
    name: string;
    studioId: string;
  }) {
    const { type, totalDiscount, name } = params;

    const credential = await this.credentialModel.findOne({
      studioId: params.studioId,
    });

    const stripe = new Stripe(credential.apiSecret, {
      apiVersion: '2025-02-24.acacia',
    });

    const studentIdObj = Types.ObjectId.createFromHexString(
      params.studioId.toString(),
    );
    const currency = await this.currencyService.findByStudioId(studentIdObj);

    const discount = await this.discountService.findOne(params.studioId);

    if (!discount) {
      return null;
    }

    const existingCoupon = await this.couponService.findByAmount({
      studioId: studentIdObj,
      amount: totalDiscount,
      category: discount.category,
      discountRules: discount.discountRules,
    });
    if (existingCoupon) {
      return existingCoupon.stripeId;
    } else {
      const couponData = {
        name,
        duration: (discount.discountRules &&
        discount.discountRules === 'first-month'
          ? 'once'
          : 'forever') as Stripe.CouponCreateParams.Duration,
        ...{
          // stripe expects cents
          amount_off: Math.round(totalDiscount * 100),
          currency: currency.name.toLowerCase() || 'usd',
        },
      };

      const stripeCoupon = await stripe.coupons.create(couponData);

      await this.couponService.create({
        studioId: studentIdObj,
        type: type,
        value: totalDiscount,
        name: name,
        category: discount.category, // Use category of first non-zero discount
        discountRules: discount.discountRules,
        stripeId: stripeCoupon.id,
      });

      return stripeCoupon.id || null;
    }
  }
}
