import { Inject, forwardRef, Injectable, Logger } from '@nestjs/common';
import { StripeEventHandler } from '../../../webhooks/interfaces/stripe-event-handler.interface';
import Stripe from 'stripe';
import { Model, Types } from 'mongoose';
import { calculateSubscriptionStartDate } from 'src/utils/helperFunction';
import { sendTransactionEmail } from 'src/emailProvider/email';
import { sendPoliciesEmail, PolicyDetail } from '../../../emailProvider/email';
import { TransactionService } from 'src/transaction/transaction.service';
import { DiscountService } from 'src/discount/discount.service';
import { StripeCouponService } from '../stripe-coupon.service';
import { StudiosService } from 'src/studios/studios.service';
import { EnrollmentService } from 'src/enrollment/enrollment.service';
import { ParentsService } from 'src/parents/parents.service';
import { StudentsService } from 'src/students/students.service';
import { GcpStorageService } from 'src/gcp-storage/gcp-storage.service';
import { CurrencyService } from 'src/currency/currency.service';
import { GohighlevelService } from 'src/gohighlevel/gohighlevel.service';
import { PoliciesService } from 'src/policies/policies.service';
import { StripeCommonService } from '../stripe-common.service';
import { CreateParentDto } from 'src/parents/dto/create-parent.dto';
import { Proration } from 'src/database/schema/prorations';
import { InjectModel } from '@nestjs/mongoose';
import { EventsService } from 'src/events/events.service';
import { TriggersService } from 'src/triggers/triggers.service';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { formatTime } from 'src/utils/datetime';
import { DiscountCouponService } from 'src/discount-coupon/discount-coupon.service';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { SubscriptionInvoiceService } from 'src/subscription-invoice/subscription-invoice.service';
import {
  InvoiceStatus,
  InvoiceType,
  PaymentMethod,
  PaymentProvider,
  PaymentTransactionEntityType,
  PaymentTransactionStatus,
  PaymentTransactionType,
} from 'src/stripe/type';
import { PaymentTransactionService } from '../../../payment-transaction/payment-transaction.service';
import { SubscriptionStatus } from 'src/database/schema/subscription';
import { ClassHistory } from 'src/database/schema/classHistory';
import { ClassHistoryService } from 'src/class-history/class-history.service';
import { SubscriptionStatus as StudentSubscriptionStatus } from 'src/database/schema/student';

@Injectable()
export class CheckoutSessionCompletedHandler
  implements StripeEventHandler<Stripe.Checkout.Session>
{
  private readonly logger = new Logger(CheckoutSessionCompletedHandler.name);

  constructor(
    @Inject(forwardRef(() => TransactionService))
    private readonly transactionService: TransactionService,
    @Inject(forwardRef(() => StudiosService))
    private readonly studioService: StudiosService,
    @Inject(forwardRef(() => DiscountService))
    private readonly discountService: DiscountService,
    @Inject(forwardRef(() => StripeCouponService))
    private readonly stripeCouponService: StripeCouponService,
    @Inject(forwardRef(() => ParentsService))
    private readonly parentsService: ParentsService,
    @Inject(forwardRef(() => EnrollmentService))
    private readonly enrollmentService: EnrollmentService,
    @Inject(forwardRef(() => EventsService))
    private readonly eventService: EventsService,
    @Inject(forwardRef(() => StudentsService))
    private readonly studentsService: StudentsService,
    @Inject(forwardRef(() => GcpStorageService))
    private readonly gcpStorageService: GcpStorageService,
    @Inject(forwardRef(() => GohighlevelService))
    private readonly gohighlevelService: GohighlevelService,
    @Inject(forwardRef(() => CurrencyService))
    private readonly currencyService: CurrencyService,
    @Inject(forwardRef(() => PoliciesService))
    private readonly policyService: PoliciesService,
    @Inject(forwardRef(() => StripeCommonService))
    private readonly stripeCommonService: StripeCommonService,
    @InjectModel(Proration.name) private prorationModel: Model<Proration>,
    @Inject(forwardRef(() => TriggersService))
    private readonly triggersService: TriggersService,
    @InjectQueue('parent-remove-tags')
    private removeTagsQueue: Queue,
    @Inject(forwardRef(() => DiscountCouponService))
    private readonly discountCouponService: DiscountCouponService,
    @Inject(forwardRef(() => SubscriptionService))
    private readonly subscriptionService: SubscriptionService,
    @Inject(forwardRef(() => SubscriptionInvoiceService))
    private readonly subscriptionInvoiceService: SubscriptionInvoiceService,
    @Inject(forwardRef(() => PaymentTransactionService))
    private readonly paymentTransactionService: PaymentTransactionService,
    @InjectModel(ClassHistory.name)
    private classHistoryModel: Model<ClassHistory>,
    private readonly classhistoryService: ClassHistoryService,
  ) {}

  /**
   * Processes a Stripe checkout.session.completed webhook event
   *
   * This method handles the complete checkout session completion flow including:
   * - Retrieving transaction data from the database
   * - Linking payment intents to transactions
   * - Processing invoice payments
   * - Creating subscriptions for recurring payments
   * - Updating student enrollment records
   * - Applying discounts and coupons
   * - Sending confirmation emails
   * - Updating GHL contact tags
   *
   * @param session - The Stripe checkout session object from the webhook
   * @param stripe - The initialized Stripe client instance
   * @param groupId - Optional groupId for scheduled payments
   * @param isScheduled - Whether this is a scheduled transaction (default: false)
   * @throws Error if transaction processing fails
   */
  async handleEvent(
    session: Stripe.Checkout.Session,
    stripe: Stripe,
    groupId: string = null,
    isScheduled: boolean = false,
  ): Promise<void> {
    let childTransactions = [];

    //this is the case where checkout session is completed
    if (session) {
      groupId = session.metadata.groupId;
    }

    childTransactions =
      await this.paymentTransactionService.findChildTransactions(groupId);

    if (childTransactions.length === 0) {
      this.logger.error('No child transactions found');
      return;
    }

    // Get shared information from the first child transaction
    const firstTransaction = childTransactions[0];
    const sharedMetadata = firstTransaction.metadata;
    const studioId = firstTransaction.studioId;

    // If the session has a payment intent, link them to all child transactions
    if (session && session.payment_intent) {
      for (const childTransaction of childTransactions) {
        await this.transactionService.updateByProperty(
          '_id',
          childTransaction._id,
          {
            'metadata.paymentIntentId': session.payment_intent as string,
          },
        );
      }
      console.log(
        `Linked session ${session.id} to payment intent ${session.payment_intent}`,
      );
    }

    // Extract shared discount information
    let discountAmount = {};
    if (sharedMetadata.discountSplit) {
      discountAmount = sharedMetadata.discountSplit;
    }

    const studio = await this.studioService.findByLocationId(studioId);
    const studioDiscountDetails = await this.discountService.findOne(
      studio._id.toString(),
    );

    // Check if this is a new parent registration or existing parent purchase
    const isNewParentRegistration = !!sharedMetadata.createParentPayload;

    if (!isNewParentRegistration) {
      // Existing parent product purchase flow
      try {
        await this.handleExistingParentFlow(
          childTransactions,
          stripe,
          studio,
          studioDiscountDetails,
          discountAmount,
          isScheduled,
        );
      } catch (error) {
        this.logger.error(`Error handling existing parent flow: ${error}`);
      }
    } else {
      // New parent registration flow
      await this.handleNewParentRegistrationFlow(
        childTransactions,
        stripe,
        studio,
        studioDiscountDetails,
        discountAmount,
        isScheduled,
        sharedMetadata.createParentPayload as CreateParentDto,
      );
    }
  }

  async handleExistingParentFlow(
    childTransactions: any[],
    stripe: Stripe,
    studio: any,
    studioDiscountDetails: any,
    discountAmount: any,
    isScheduled: boolean,
  ): Promise<void> {
    const firstTransaction = childTransactions[0];
    const parent = await this.parentsService.findOne(firstTransaction.parentId);

    // First find or create customer
    const customers = await stripe.customers.list({
      email: parent.email,
      limit: 1,
    });

    let customer_id;
    if (parent.stripeCustomerId) {
      customer_id = parent.stripeCustomerId;
    } else {
      if (
        customers.data.length > 0 &&
        customers.data[0].email === parent.email
      ) {
        customer_id = customers.data[0].id;
      } else {
        const newCustomer = await stripe.customers.create({
          email: parent.email,
          name: `${parent.firstName} ${parent.lastName}`,
        });
        customer_id = newCustomer.id;
      }
      await this.parentsService.updateByProperty('_id', parent._id, {
        stripeCustomerId: customer_id,
      });
    }

    let checkoutEntity;
    const studentEnrollments = [];
    const allPolicies: PolicyDetail[] = [];
    const students: {
      studentId: string;
      status: StudentSubscriptionStatus;
    }[] = [];

    for (const childTransaction of childTransactions) {
      if (childTransaction.type === PaymentTransactionType.ENROLLMENT) {
        checkoutEntity = await this.enrollmentService.findOne(
          childTransaction.typeId.toString(),
        );
      } else {
        checkoutEntity = await this.eventService.findOne(
          childTransaction.typeId.toString(),
        );
      }

      const student = await this.studentsService.findOne(
        childTransaction.studentId,
      );

      // Create coupon if discount exists
      let couponId = null;
      let effectiveApplyDiscountTo: 'first' | 'all' = 'all';

      if (
        studioDiscountDetails?.discountRules &&
        studioDiscountDetails?.discountRules === 'all-months'
      ) {
        if (discountAmount[student._id.toString()] > 0) {
          couponId = await this.discountCouponService.createCoupon({
            type: 'fixed',
            value: discountAmount[student._id.toString()],
            name: `Fixed Discount of ${discountAmount[student._id.toString()]}`,
            studioId: Types.ObjectId.createFromHexString(studio._id.toString()),
            category: 'enrollment',
          });
        }
        effectiveApplyDiscountTo = 'all';
      } else if (studioDiscountDetails?.discountRules === 'first-month') {
        effectiveApplyDiscountTo = 'first';
      }

      await this.processStudentEnrollment(
        childTransaction,
        checkoutEntity,
        childTransaction.type,
        student,
        parent,
        studio,
        couponId,
        discountAmount,
        effectiveApplyDiscountTo,
        isScheduled,
      );

      // Collect student enrollment details for emails
      const enrollmentDetails = [];
      if (childTransaction.type === PaymentTransactionType.ENROLLMENT) {
        enrollmentDetails.push({
          name: checkoutEntity.title,
          startDate: new Date(checkoutEntity.startDate).toLocaleDateString(),
          endDate: new Date(checkoutEntity.endDate).toLocaleDateString(),
          days: checkoutEntity.days.join(', '),
          time: `${formatTime(checkoutEntity.startTime)}-${formatTime(checkoutEntity.endTime)}`,
          location: checkoutEntity.location?.fieldName || '',
        });

        // Collect policies
        if (checkoutEntity.policyGroup?.length > 0) {
          for (const policyId of checkoutEntity.policyGroup) {
            const policy = await this.policyService.findOne(policyId);
            const existingPolicy = allPolicies.find(
              (p) => p.name === policy.name,
            );
            if (existingPolicy) {
              existingPolicy.applicableClasses += `, ${checkoutEntity.title}`;
            } else {
              allPolicies.push({
                name: policy.name.trim(),
                description: policy.description.trim(),
                applicableClasses: checkoutEntity.title,
              });
            }
          }
        }
      }

      studentEnrollments.push({
        studentName: `${student.firstName} ${student.lastName}`,
        classes: enrollmentDetails,
      });

      students.push({
        studentId: student._id.toString(),
        status: isScheduled
          ? StudentSubscriptionStatus.SCHEDULED
          : StudentSubscriptionStatus.ACTIVE,
      });
    }

    // Update GHL tags
    if (checkoutEntity) {
      const tagsArray = checkoutEntity.tags.map((tag) => tag.fieldName);
      for (const tag of tagsArray) {
        try {
          const tagAdded = await this.gohighlevelService.addTagToContactByEmail(
            studio._id.toString(),
            parent.email,
            tag,
          );
          if (!tagAdded) {
            this.logger.warn(
              `Failed to add tag "${tag}" - contact not found for email: ${parent.email}`,
            );
          }
        } catch (error) {
          this.logger.error(
            `Error adding tag "${tag}" to contact: ${error.message}`,
          );
        }
      }
    }

    // Remove tags from contact by searching with email
    try {
      const contactId = await this.gohighlevelService.searchContactByEmail(
        studio._id.toString(),
        parent.email,
      );
      if (contactId) {
        await this.removeTagsQueue.add('parent-remove-tags', {
          studioId: studio._id.toString(),
          contactId: contactId,
          tags: ['portal_lead', 'lead_portal'],
        });
      } else {
        this.logger.warn(
          `Cannot remove tags - contact not found for email: ${parent.email}`,
        );
      }
    } catch (error) {
      this.logger.error(`Error removing tags from contact: ${error.message}`);
    }

    // Send emails
    let studioLogoUrl = null;
    try {
      studioLogoUrl = await this.gcpStorageService.getPublicImage(
        studio._id.toString(),
        'studio-logo',
      );
    } catch (error) {
      this.logger.error(`Error getting studio logo: ${error}`);
    }

    try {
      if (!isScheduled && firstTransaction.metadata.line_items) {
        const transactions = firstTransaction.metadata.line_items.map(
          (item) => ({
            type: item.price_data.product_data.description,
            name: item.price_data.product_data.name,
            product: item.price_data.product_data.description,
            amount: (item.price_data.unit_amount * item.quantity) / 100,
            status: 'paid',
            transactionId: firstTransaction.metadata.paymentIntentId,
          }),
        );

        await sendTransactionEmail(
          parent.email,
          transactions,
          firstTransaction.metadata.line_items[0].price_data.currency.toUpperCase(),
          `${parent.firstName} ${parent.lastName}`,
          studio.subaccountName,
          studioLogoUrl,
        );
      }
    } catch (error) {
      this.logger.error(`Error sending transaction email: ${error}`);
    }

    // Update class history
    try {
      if (students.length > 0 && checkoutEntity) {
        await this.classhistoryService.upsertClassHistoryStudentRecord({
          enrollmentId: checkoutEntity._id?.toString(),
          students: students.map((s) => ({
            status: s.status,
            studentId: s.studentId.toString(),
          })),
          studioId: studio._id.toString(),
        });
      }
    } catch (error) {
      this.logger.error(
        `Error upserting class history for transaction: ${error}`,
      );
    }

    await sendPoliciesEmail(
      parent.email,
      `${parent.firstName} ${parent.lastName}`,
      studioLogoUrl ? null : studio.subaccountName,
      studioLogoUrl ? studioLogoUrl : null,
      studentEnrollments,
      Array.from(allPolicies),
    );
  }

  async handleNewParentRegistrationFlow(
    childTransactions: any[],
    stripe: Stripe,
    studio: any,
    studioDiscountDetails: any,
    discountAmount: any,
    isScheduled: boolean,
    parentPayload: CreateParentDto,
  ): Promise<void> {
    // Create the new parent
    const parent = (await this.parentsService.createWithLocationId(
      parentPayload,
    )) as any;
    console.log('Parent created: ', parent._id || parent.id);

    // Update all child transactions with the actual parent ID
    const parentId = parent._id || parent.id;
    for (const childTransaction of childTransactions) {
      await this.paymentTransactionService.update(
        childTransaction._id.toString(),
        {
          parentId: parentId.toString(),
        },
      );
    }

    // Get students created for this parent
    const students = await this.studentsService.getStudentsByParentId(
      parentId.toString(),
    );

    // Update child transactions with actual student IDs
    for (const student of students) {
      // Create tempStudentId to match the format used during checkout
      const tempStudentId =
        student.firstName +
        '-' +
        student.lastName +
        '-' +
        student.dob +
        '-' +
        student.gender;

      // Find and update child transactions for this student
      for (const transaction of childTransactions) {
        if (transaction.studentId === tempStudentId) {
          await this.paymentTransactionService.update(
            transaction._id.toString(),
            {
              studentId: student._id.toString(),
            },
          );
        }
      }
    }

    // Process each student enrollment using the existing flow
    await this.handleExistingParentFlow(
      childTransactions,
      stripe,
      studio,
      studioDiscountDetails,
      discountAmount,
      isScheduled,
    );
  }

  private async processStudentEnrollment(
    childTransaction: any,
    checkoutEntity: any,
    checkoutEntityType: PaymentTransactionEntityType,
    student: any,
    parent: any,
    studio: any,
    couponId: any,
    discountAmount: any,
    effectiveApplyDiscountTo: 'first' | 'all',
    isScheduled: boolean,
  ): Promise<void> {
    let subscriptionId = '';

    // Get next billing date from proration data's billing cycle anchor
    const subStartDate = calculateSubscriptionStartDate(checkoutEntity);

    const billingCycle =
      childTransaction.type === PaymentTransactionType.EVENT &&
      checkoutEntity.oneTime === true
        ? 'one-time'
        : checkoutEntity.tuitionBillingCycle;

    if (
      (childTransaction.type === PaymentTransactionType.ENROLLMENT &&
        checkoutEntity.tuitionBillingCycle.toLowerCase() !== 'one-time') ||
      (childTransaction.type === PaymentTransactionType.EVENT &&
        checkoutEntity.oneTime === false &&
        checkoutEntity.tuitionBillingCycle !== 'one-time')
    ) {
      // Create subscription
      const subscription = await this.subscriptionService.create({
        studioId: Types.ObjectId.createFromHexString(studio._id.toString()),
        parentId: Types.ObjectId.createFromHexString(parent._id.toString()),
        studentId: Types.ObjectId.createFromHexString(student._id.toString()),
        entityId: Types.ObjectId.createFromHexString(
          checkoutEntity._id.toString(),
        ),
        entityType:
          checkoutEntity.type === 'event'
            ? 'event'
            : checkoutEntity.type === 'class'
              ? 'class'
              : 'class',
        startDate: subStartDate,
        endDate: new Date(checkoutEntity.endDate),
        billingCycle: billingCycle,
        baseAmount: checkoutEntity.tuitionFee,
        finalAmount:
          checkoutEntity.tuitionFee -
          (discountAmount[student._id.toString()] || 0),
        status: SubscriptionStatus.ACTIVE,
        nextPaymentDate: subStartDate,
        appliedCouponId: couponId
          ? Types.ObjectId.createFromHexString(couponId._id.toString())
          : undefined,
        metadata: {
          transactionId: childTransaction._id.toString(),
          appliedDiscount: discountAmount[student._id.toString()] || 0,
          paymentMethod: childTransaction.paymentMethod as PaymentMethod,
        },
      });

      subscriptionId = subscription._id.toString();

      if (childTransaction.amount > 0) {
        await this.subscriptionInvoiceService.generateSubscriptionInvoices({
          subscription,
          childTransaction,
          discountAmount: discountAmount[student._id.toString()] || 0,
          enrollment: checkoutEntity,
          transactionId: childTransaction._id.toString(),
          transactionCodeId: checkoutEntity.transactionCodeId,
          applyDiscountTo: effectiveApplyDiscountTo,
          discountCategory: 'discount',
          paymentProcessingMethod: studio.paymentProcessingMethod,
        });
      } else {
        await this.subscriptionInvoiceService.create({
          studioId: Types.ObjectId.createFromHexString(studio._id.toString()),
          subscriptionId: Types.ObjectId.createFromHexString(
            subscription._id.toString(),
          ),
          parentId: Types.ObjectId.createFromHexString(parent._id.toString()),
          studentId: Types.ObjectId.createFromHexString(student._id.toString()),
          entityId: Types.ObjectId.createFromHexString(
            checkoutEntity._id.toString(),
          ),
          entityType: checkoutEntityType,
          baseAmount: childTransaction.amount,
          status:
            childTransaction.amount > 0
              ? childTransaction.status == PaymentTransactionStatus.SCHEDULED
                ? InvoiceStatus.SCHEDULED
                : InvoiceStatus.PENDING
              : InvoiceStatus.FREE,
          paymentProvider: childTransaction.paymentProvider as PaymentProvider,
          paymentMethod: childTransaction.paymentMethod as PaymentMethod,
          line_items: childTransaction.metadata.line_items.map((item) => ({
            name: item.price_data.product_data.name,
            amount: item.price_data.unit_amount / 100,
            quantity: item.quantity,
            type: item.price_data.product_data.description,
            total: (item.price_data.unit_amount * item.quantity) / 100,
          })),
          type: InvoiceType.ONE_TIME,
          dueDate: new Date(childTransaction.metadata.billingDate),
          appliedCouponId: couponId
            ? Types.ObjectId.createFromHexString(couponId._id.toString())
            : undefined,
          finalAmount:
            childTransaction.amount -
            (discountAmount[student._id.toString()] || 0),
          startDate: null,
          endDate: null,
          metadata: {
            attemptCount: 0,
            internalTransactionId: childTransaction._id.toString(),
            appliedDiscount: discountAmount[student._id.toString()] || 0,
            applyDiscountTo: effectiveApplyDiscountTo,
            discountCategory: 'discount',
          },
          transactionCodeId: checkoutEntity.transactionCodeId,
        });
      }
    } else {
      // Create subscription
      const subscription = await this.subscriptionService.create({
        studioId: Types.ObjectId.createFromHexString(studio._id.toString()),
        parentId: Types.ObjectId.createFromHexString(parent._id.toString()),
        studentId: Types.ObjectId.createFromHexString(student._id.toString()),
        entityId: Types.ObjectId.createFromHexString(
          checkoutEntity._id.toString(),
        ),
        entityType: checkoutEntityType,
        startDate: subStartDate,
        endDate: new Date(checkoutEntity.endDate),
        billingCycle: billingCycle,
        baseAmount: checkoutEntity.tuitionFee,
        finalAmount:
          checkoutEntity.tuitionFee -
          (discountAmount[student._id.toString()] || 0),
        status: SubscriptionStatus.ACTIVE,
        nextPaymentDate: null,
        appliedCouponId: couponId
          ? Types.ObjectId.createFromHexString(couponId._id.toString())
          : undefined,
        metadata: {
          transactionId: childTransaction._id.toString(),
          appliedDiscount: discountAmount[student._id.toString()] || 0,
          paymentMethod: childTransaction.paymentMethod as PaymentMethod,
        },
      });

      subscriptionId = subscription._id.toString();

      await this.subscriptionInvoiceService.create({
        studioId: Types.ObjectId.createFromHexString(studio._id.toString()),
        subscriptionId: Types.ObjectId.createFromHexString(
          subscription._id.toString(),
        ),
        parentId: Types.ObjectId.createFromHexString(parent._id.toString()),
        studentId: Types.ObjectId.createFromHexString(student._id.toString()),
        entityId: Types.ObjectId.createFromHexString(
          checkoutEntity._id.toString(),
        ),
        entityType: checkoutEntityType,
        baseAmount: childTransaction.amount,
        status:
          childTransaction.amount > 0
            ? childTransaction.status == PaymentTransactionStatus.SCHEDULED
              ? InvoiceStatus.SCHEDULED
              : InvoiceStatus.PENDING
            : InvoiceStatus.FREE,
        paymentProvider: childTransaction.paymentProvider as PaymentProvider,
        paymentMethod: childTransaction.paymentMethod as PaymentMethod,
        line_items: childTransaction.metadata.line_items.map((item) => ({
          name: item.price_data.product_data.name,
          amount: item.price_data.unit_amount / 100,
          quantity: item.quantity,
          type: item.price_data.product_data.description,
          total: (item.price_data.unit_amount * item.quantity) / 100,
        })),
        type: InvoiceType.ONE_TIME,
        dueDate: new Date(childTransaction.metadata.billingDate),
        appliedCouponId: couponId
          ? Types.ObjectId.createFromHexString(couponId._id.toString())
          : undefined,
        finalAmount:
          childTransaction.amount -
          (discountAmount[student._id.toString()] || 0),
        startDate: null,
        endDate: null,
        metadata: {
          attemptCount: 0,
          internalTransactionId: childTransaction._id.toString(),
          appliedDiscount: discountAmount[student._id.toString()] || 0,
          applyDiscountTo: effectiveApplyDiscountTo,
          discountCategory: 'discount',
        },
        transactionCodeId: checkoutEntity.transactionCodeId,
      });
    }

    // Update student record
    if (childTransaction.type === PaymentTransactionType.ENROLLMENT) {
      student.enrollments.push({
        enrollmentId: checkoutEntity._id,
        subscriptionId: subscriptionId,
        enrolledDate: new Date(),
        subscriptionStatus: isScheduled
          ? SubscriptionStatus.SCHEDULED
          : SubscriptionStatus.ACTIVE,
      });
    } else {
      student.events.push({
        eventId: checkoutEntity._id,
        eventDate: new Date(),
        subscriptionId: subscriptionId,
        subscriptionStatus: isScheduled
          ? SubscriptionStatus.SCHEDULED
          : SubscriptionStatus.ACTIVE,
      });
    }

    await student.save();

    // Send GHL trigger
    const triggerDataClassEnrollement = {
      triggerKey: process.env.CLASS_ENROL_TRIGGER_KEY,
      data: {
        studentFirstName: student.firstName,
        studentLastName: student.lastName,
        parentFirstName: parent.firstName,
        parentLastName: parent.lastName,
        parentEmail: parent.email,
        parentPhone: parent.primaryPhone,
        classEnrolled: checkoutEntity.title,
        classStartDate: checkoutEntity.startDate,
        classEndDate: checkoutEntity.endDate,
        classPrice: checkoutEntity.tuitionFee,
      },
      locationId: studio.locationId,
    };

    try {
      await this.triggersService.sendToGhl(triggerDataClassEnrollement);
    } catch (error) {
      this.logger.error(
        `Error sending trigger to GHL for class enrollement ${checkoutEntity._id}: ${error} `,
      );
    }
  }
}
