import { Injectable, Logger } from '@nestjs/common';
import { StripeEventHandler } from '../../../webhooks/interfaces/stripe-event-handler.interface';
import Stripe from 'stripe';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  PaymentTransaction,
  PaymentTransactionDocument,
} from 'src/database/schema/paymentTransaction';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import {
  PaymentMethod,
  PaymentTransactionStatus,
  PaymentTransactionType,
  InvoiceStatus,
} from 'src/stripe/type';
import { ReasonType } from 'src/database/schema/walletTransaction';
import { ParentsService } from 'src/parents/parents.service';
import { StudiosService } from 'src/studios/studios.service';
import { DiscountService } from 'src/discount/discount.service';
import { CreateParentDto } from 'src/parents/dto/create-parent.dto';
import { CheckoutSessionCompletedHandler } from './checkout-session-completed.handler';
import { StripeCommonService } from '../stripe-common.service';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';

@Injectable()
export class PaymentIntentSucceededHandler
  implements StripeEventHandler<Stripe.PaymentIntent>
{
  private readonly logger = new Logger(PaymentIntentSucceededHandler.name);

  constructor(
    private readonly parentsService: ParentsService,
    @InjectQueue('invoice-status-update') private invoiceStatusQueue: Queue,
    @InjectModel(PaymentTransaction.name)
    private readonly paymentTransactionModel: Model<PaymentTransaction>,
    @InjectModel(SubscriptionInvoice.name)
    private readonly subscriptionInvoiceModel: Model<SubscriptionInvoice>,
    private readonly studiosService: StudiosService,
    private readonly discountService: DiscountService,
    private readonly checkoutSessionCompletedHandler: CheckoutSessionCompletedHandler,

    //stripe common service
    private readonly stripeCommonService: StripeCommonService,
  ) {}

  /**
   * Processes a Stripe payment_intent.succeeded webhook event
   * Updates all transactions with the groupId to PAID status and updates their subscription invoices
   */
  async handleEvent(
    paymentIntent: Stripe.PaymentIntent,
    stripe: Stripe,
  ): Promise<void> {
    this.logger.log(`Payment intent succeeded: ${paymentIntent.id}`);

    const groupId = paymentIntent.metadata.groupId;
    const isScheduledPayment =
      paymentIntent.metadata.isScheduledPayment === 'true';
    const fromStudioPortal = paymentIntent.metadata.fromStudioPortal === 'true';
    const processInvoiceUsingPaymentIntent =
      paymentIntent.metadata.processInvoiceUsingPaymentIntent === 'true';
    // Get payment method type
    let paymentMethodType = 'unknown';

    if (paymentIntent.payment_method) {
      try {
        const paymentMethod = await stripe.paymentMethods.retrieve(
          paymentIntent.payment_method as string,
        );
        paymentMethodType = paymentMethod.type;
      } catch (error) {
        this.logger.error('Error retrieving payment method:', error);
      }
    }

    if (!groupId && !isScheduledPayment) {
      if (processInvoiceUsingPaymentIntent) {
        await this.queueInvoiceProcessing(paymentIntent.id, paymentMethodType);
      }
      this.logger.warn(
        `No groupId found in payment intent metadata: ${paymentIntent.id}`,
      );
      return;
    }

    // Process all transactions with this groupId
    await this.processTransactions(
      groupId,
      paymentMethodType,
      paymentIntent.id,
      fromStudioPortal,
      isScheduledPayment,
    );
  }

  /**
   * Process all transactions for this payment intent and update their subscription invoices
   */
  private async processTransactions(
    groupId: string,
    paymentMethodType: string,
    paymentIntentId: string,
    fromStudioPortal: boolean,
    isScheduledPayment: boolean,
  ): Promise<void> {
    try {
      // Find all PENDING transactions with this groupId
      const transactions = await this.paymentTransactionModel.find({
        groupId: groupId,
        status: PaymentTransactionStatus.PENDING,
      });

      // Get shared information from the first child transaction
      const firstTransaction = transactions[0];
      const sharedMetadata = firstTransaction.metadata;
      const studioId = firstTransaction.studioId;

      // Extract shared discount information
      let discountAmount = {};
      if (sharedMetadata.discountSplit) {
        discountAmount = sharedMetadata.discountSplit;
      }

      const studio = await this.studiosService.findByLocationId(studioId);
      const studioDiscountDetails = await this.discountService.findOne(
        studio._id.toString(),
      );

      const { stripe } = await this.stripeCommonService.initializeStripe(
        studioId.toString(),
      );

      if (!fromStudioPortal && !isScheduledPayment) {
        // Check if this is a new parent registration or existing parent purchase
        const isNewParentRegistration = !!sharedMetadata.createParentPayload;

        if (!isNewParentRegistration) {
          // Existing parent product purchase flow
          try {
            await this.checkoutSessionCompletedHandler.handleExistingParentFlow(
              transactions,
              stripe,
              studio,
              studioDiscountDetails,
              discountAmount,
              false,
            );
          } catch (error) {
            this.logger.error(`Error handling existing parent flow: ${error}`);
          }
        } else {
          // New parent registration flow
          for (const transaction of transactions) {
            await this.checkoutSessionCompletedHandler.handleNewParentRegistrationFlow(
              [transaction],
              stripe,
              studio,
              studioDiscountDetails,
              discountAmount,
              false,
              transaction.metadata.createParentPayload as CreateParentDto,
            );
          }
        }
      }

      if (transactions.length === 0) {
        this.logger.log(
          `No pending transactions found for groupId: ${groupId}`,
        );
        return;
      }

      this.logger.log(
        `Found ${transactions.length} transactions to process for groupId: ${groupId}`,
      );

      // Process each transaction
      for (const transaction of transactions) {
        try {
          await this.processSuccessfulTransaction(
            transaction,
            paymentMethodType,
            paymentIntentId,
            discountAmount,
          );
        } catch (error) {
          this.logger.error(
            `Failed to process transaction ${transaction._id}: ${error.message}`,
            error.stack,
          );
          // Continue processing other transactions even if one fails
        }
      }

      this.logger.log(
        `Successfully processed ${transactions.length} transaction(s) with groupId: ${groupId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to process transactions for groupId ${groupId}: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Process successful transaction and update associated subscription invoice
   */
  private async processSuccessfulTransaction(
    transaction: PaymentTransactionDocument,
    paymentMethodType: string,
    paymentIntentId: string,
    discountAmount: Record<string, number>,
  ): Promise<void> {
    // Update transaction status to PAID
    const updateResult = await this.paymentTransactionModel.updateOne(
      {
        _id: transaction._id,
        status: PaymentTransactionStatus.PENDING,
      },
      {
        $set: {
          status: PaymentTransactionStatus.PAID,
          paymentMethod: paymentMethodType,
          'metadata.paymentIntentId': paymentIntentId,
        },
      },
    );

    if (updateResult.modifiedCount === 0) {
      this.logger.log(
        `Transaction ${transaction._id} already processed (idempotency protection)`,
      );
      return;
    }

    this.logger.log(`Transaction ${transaction._id} updated to PAID status`);

    // Handle wallet deduction if wallet was used
    if (transaction.metadata?.walletAmountUsed > 0) {
      await this.parentsService.removeWalletBalance({
        parentId: transaction.parentId.toString(),
        amount: transaction.metadata.walletAmountUsed,
        reason: ReasonType.CLASS_BUY,
        paymentMethod: PaymentMethod.WALLET,
        studioId: transaction.studioId.toString(),
        studentId: transaction.studentId?.toString(),
      });

      this.logger.log(
        `Wallet deducted: ${transaction.metadata.walletAmountUsed} for transaction: ${transaction._id}`,
      );
    }

    // Handle wallet load if applicable
    if (transaction.type === PaymentTransactionType.WALLET_LOAD) {
      await this.parentsService.addWalletBalance({
        studioId: transaction.studioId.toString(),
        parentId: transaction.parentId.toString(),
        studentId: transaction.studentId?.toString(),
        amount: transaction.amount,
        reason: ReasonType.WALLET_LOAD,
        paymentMethod: paymentMethodType as PaymentMethod,
        metadata: {
          transactionId: transaction._id.toString(),
        },
      });

      this.logger.log(
        `Wallet loaded: ${transaction.amount} for parent: ${transaction.parentId}`,
      );
    }

    // Handle session fee marking
    const sessionId = transaction.metadata?.sessionId;
    if (sessionId) {
      const parent = await this.parentsService.findOne(
        transaction.parentId.toString(),
      );

      if (parent) {
        const isSessionFeePaid = { ...(parent.isSessionFeePaid || {}) };
        isSessionFeePaid[sessionId] = true;

        await this.parentsService.updateByProperty('_id', parent._id, {
          isSessionFeePaid: isSessionFeePaid,
        });
      }
    }

    // Update associated subscription invoice to PAID
    await this.updateSubscriptionInvoice(
      transaction._id.toString(),
      paymentMethodType,
      paymentIntentId,
      transaction,
      discountAmount,
    );
  }

  /**
   * Update subscription invoice to PAID status
   */
  private async updateSubscriptionInvoice(
    transactionId: string,
    paymentMethodType: string,
    paymentIntentId: string,
    transaction: PaymentTransactionDocument,
    discountAmount: Record<string, number>,
  ): Promise<void> {
    try {
      // Find the subscription invoice linked to this transaction
      const invoice = await this.subscriptionInvoiceModel.findOne({
        'metadata.internalTransactionId': transactionId,
      });

      if (!invoice) {
        this.logger.warn(
          `No subscription invoice found for transaction ${transactionId}`,
        );
        return;
      }

      const lineItemAmount =
        transaction.metadata.line_items.reduce(
          (acc, item) => acc + item.price_data.unit_amount,
          0,
        ) / 100;

      let discountAmount = 0;
      if (transaction.metadata.discountSplit) {
        discountAmount =
          transaction.metadata.discountSplit[transaction.studentId.toString()];
      }

      // Prepare payment record
      const payment = {
        method: paymentMethodType as PaymentMethod,
        amount: lineItemAmount - discountAmount,
        date: new Date(),
        paymentIntentId: paymentIntentId,
      };

      // Handle wallet payment if wallet was used
      const payments = [payment];
      if (transaction.metadata?.walletAmountUsed > 0) {
        payments.push({
          method: PaymentMethod.WALLET,
          amount: transaction.metadata.walletAmountUsed,
          date: new Date(),
          paymentIntentId: null,
        });
      }

      // Update invoice to PAID status
      await this.subscriptionInvoiceModel.updateOne(
        { _id: invoice._id },
        {
          $set: {
            status: InvoiceStatus.PAID,
            paymentDate: new Date(),
            'metadata.paymentIntentId': paymentIntentId,
          },
          $push: {
            payments: { $each: payments },
          },
        },
      );

      this.logger.log(
        `Subscription invoice ${invoice._id} updated to PAID status for transaction ${transactionId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to update subscription invoice for transaction ${transactionId}: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Queue invoice processing for direct invoice payments
   */
  private async queueInvoiceProcessing(
    paymentIntentId: string,
    paymentMethodType: string,
  ): Promise<void> {
    try {
      await this.invoiceStatusQueue.add(
        'update-invoice-status',
        {
          paymentIntentId: paymentIntentId,
          paymentMethodType: paymentMethodType,
          status: InvoiceStatus.PAID,
        },
        {
          attempts: 10, // More retries for invoice processing
          backoff: {
            type: 'exponential',
            delay: 1000,
          },
          removeOnComplete: 100,
          removeOnFail: 50,
        },
      );

      this.logger.log(
        `Invoice processing job queued for payment intent: ${paymentIntentId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to queue invoice processing for payment intent ${paymentIntentId}: ${error.message}`,
        error.stack,
      );
      // Don't throw - transaction processing might have already succeeded
    }
  }
}
