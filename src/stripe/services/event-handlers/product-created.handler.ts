import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Enrollment } from 'src/database/schema/enrollment';
import { Event } from 'src/database/schema/event';
import { StripeEventHandler } from 'src/webhooks/handlers/stripe/stripe-event-handler.interface';

@Injectable()
export class ProductCreatedHandler
  implements StripeEventHandler<[string, string]>
{
  private readonly logger = new Logger(ProductCreatedHandler.name);

  constructor(
    @InjectModel(Enrollment.name) private enrollmentModel: Model<Enrollment>,
    @InjectModel(Event.name) private eventModel: Model<Event>,
  ) {} // This would depend on what updateProductStripeId does in your original code // Inject necessary services here

  /**
   * Processes a Stripe product.created webhook event
   *
   * This method handles the product creation flow including:
   * - Linking Stripe product IDs to internal enrollment or event records
   * - Updating database records with Stripe product identifiers
   * - Implementing retry logic for eventual consistency
   * - Handling cases where the product might not be immediately available
   * - Logging detailed information about the product linking process
   *
   * @param ghlProductId - The internal product ID from GHL
   * @param stripeProductId - The Stripe product ID to link
   * @throws Error if product linking fails after maximum retry attempts
   */
  async handleEvent([ghlProductId, stripeProductId]: [
    string,
    string,
  ]): Promise<void> {
    try {
      //TODO: Add this to redis queue
      const maxAttempts = 10;
      let attempt = 1;

      while (attempt <= maxAttempts) {
        try {
          let entity;

          entity = await this.enrollmentModel.findOneAndUpdate(
            { productId_ghl: ghlProductId },
            { productId_stripe: stripeProductId },
            { new: true },
          );

          if (!entity) {
            entity = await this.eventModel.findOneAndUpdate(
              { productId_ghl: ghlProductId },
              { productId_stripe: stripeProductId },
              { new: true },
            );
          }
          if (entity) {
            console.log(
              `Successfully updated stripe ID for product ${ghlProductId}`,
            );
            break;
          }
          console.log(
            `Attempt ${attempt}: No enrollment found for product ${ghlProductId}`,
          );
          await new Promise((resolve) => setTimeout(resolve, 5000));
          attempt++;
        } catch (error) {
          console.error(`Attempt ${attempt} failed:`, error);
          if (attempt >= maxAttempts) {
            throw error;
          }
          attempt++;
          await new Promise((resolve) => setTimeout(resolve, 5000));
        }
      }
    } catch (error) {
      console.error('Error queuing stripe ID update:', error);
      throw error;
    }
  }
}
