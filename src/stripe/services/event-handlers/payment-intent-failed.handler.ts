import { Injectable, Logger } from '@nestjs/common';
import { StripeEventHandler } from '../../../webhooks/interfaces/stripe-event-handler.interface';
import Stripe from 'stripe';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { PaymentTransactionService } from 'src/payment-transaction/payment-transaction.service';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { PaymentTransaction } from 'src/database/schema/paymentTransaction';
import {
  InvoiceStatus,
  PaymentTransactionStatus,
  PaymentTransactionType,
} from 'src/stripe/type';

@Injectable()
export class PaymentIntentFailedHandler
  implements StripeEventHandler<Stripe.PaymentIntent>
{
  private readonly logger = new Logger(PaymentIntentFailedHandler.name);

  constructor(
    @InjectQueue('invoice-status-update') private invoiceStatusQueue: Queue,
    private readonly paymentTransactionService: PaymentTransactionService,
    @InjectModel(PaymentTransaction.name)
    private readonly paymentTransactionModel: Model<PaymentTransaction>,
  ) {}

  /**
   * Processes a Stripe payment_intent.failed webhook event
   * Handles transactions directly and queues invoice processing
   */
  async handleEvent(
    paymentIntent: Stripe.PaymentIntent,
    stripe: Stripe,
  ): Promise<void> {
    this.logger.log(`Payment intent failed: ${paymentIntent.id}`);

    let paymentMethodType = 'unknown';

    // Get payment method type
    if (paymentIntent.payment_method) {
      try {
        const paymentMethod = await stripe.paymentMethods.retrieve(
          paymentIntent.payment_method as string,
        );
        paymentMethodType = paymentMethod.type;
      } catch (error) {
        this.logger.error('Error retrieving payment method:', error);
      }
    }

    // Get failure reason
    const failureReason =
      paymentIntent.last_payment_error?.message || 'Payment failed';

    // Process transactions directly (synchronous)
    await this.processTransactions(
      paymentIntent.id,
      paymentMethodType,
      failureReason,
      stripe,
    );

    // Process invoices through queue (asynchronous with retry)
    await this.queueInvoiceProcessing(
      paymentIntent.id,
      paymentMethodType,
      failureReason,
    );
  }

  /**
   * Process all transactions for this payment intent directly
   */
  private async processTransactions(
    paymentIntentId: string,
    paymentMethodType: string,
    failureReason: string,
    stripe: Stripe,
  ): Promise<boolean> {
    try {
      // Find transactions by payment intent ID
      let transactions = await this.paymentTransactionModel.find({
        'metadata.paymentIntentId': paymentIntentId,
      });

      // If no transactions found by payment intent ID, try session-based lookup
      if (transactions.length === 0) {
        const sessionTransaction = await this.findTransactionBySession(
          paymentIntentId,
          stripe,
        );
        if (sessionTransaction) {
          transactions = sessionTransaction as any;
        }
      }

      if (transactions.length === 0) {
        this.logger.log(
          `No transactions found for payment intent: ${paymentIntentId}`,
        );
        return false;
      }

      // Process each transaction
      for (const transaction of transactions) {
        try {
          await this.processFailedTransaction(
            transaction,
            paymentMethodType,
            paymentIntentId,
            failureReason,
          );
          this.logger.log(
            `✅ Successfully processed failed transaction ${transaction._id}`,
          );
        } catch (error) {
          this.logger.error(
            `❌ Failed to process failed transaction ${transaction._id}: ${error.message}`,
            error.stack,
          );
          // Continue processing other transactions even if this one fails
        }
      }

      this.logger.log(
        `Successfully processed ${transactions.length} failed transaction(s) for payment intent: ${paymentIntentId}`,
      );

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to process failed transactions for payment intent ${paymentIntentId}: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Find transaction by session (backward compatibility)
   */
  private async findTransactionBySession(
    paymentIntentId: string,
    stripe: Stripe,
  ) {
    try {
      const sessions = await stripe.checkout.sessions.list({
        payment_intent: paymentIntentId,
        limit: 1,
      });

      if (sessions.data.length > 0) {
        const session = sessions.data[0];
        this.logger.log(
          `Found session ${session.id} for payment intent ${paymentIntentId}`,
        );

        return await this.paymentTransactionService.findChildTransactionByStripeSessionId(
          session.id,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error finding transaction by session for payment intent ${paymentIntentId}:`,
        error,
      );
    }

    return null;
  }

  /**
   * Process failed transaction
   */
  private async processFailedTransaction(
    transaction: any,
    paymentMethodType: string,
    paymentIntentId: string,
    failureReason: string,
  ): Promise<void> {
    // Update payment intent ID in metadata if not present
    if (!transaction.metadata?.paymentIntentId) {
      await this.paymentTransactionModel.updateOne(
        { _id: transaction._id },
        {
          $set: {
            'metadata.paymentIntentId': paymentIntentId,
          },
        },
      );
    }

    // Atomic update: Only update if status is not already FAILED
    // This prevents race conditions between concurrent webhook handlers
    const updateResult = await this.paymentTransactionModel.updateOne(
      {
        _id: transaction._id,
        status: { $ne: PaymentTransactionStatus.FAILED },
      },
      {
        $set: {
          status: PaymentTransactionStatus.FAILED,
          paymentMethod: paymentMethodType,
          'metadata.failureReason': failureReason,
          'metadata.lastAttemptDate': new Date(),
        },
      },
    );

    // Only proceed with additional operations if we successfully updated the status
    // This ensures only one handler instance processes the transaction
    if (updateResult.modifiedCount === 0) {
      this.logger.log(
        `Transaction ${transaction._id} already processed by another handler instance (idempotency protection)`,
      );
      return;
    }

    this.logger.log(
      `Successfully claimed failed transaction ${transaction._id} for processing`,
    );

    // Log wallet load failure if applicable
    if (transaction.type === PaymentTransactionType.WALLET_LOAD) {
      this.logger.log(
        `Wallet load failed for parent: ${transaction.parentId} - Reason: ${failureReason}`,
      );
    }

    // Update grouped transactions if this transaction has a groupId
    if (transaction.groupId) {
      const groupedTransactions =
        await this.paymentTransactionService.findChildTransactions(
          transaction.groupId,
        );

      for (const groupedTransaction of groupedTransactions) {
        if (groupedTransaction._id.toString() !== transaction._id.toString()) {
          await this.paymentTransactionService.update(
            groupedTransaction._id.toString(),
            {
              status: PaymentTransactionStatus.FAILED,
            },
          );
        }
      }

      this.logger.log(
        `Updated ${groupedTransactions.length} grouped transactions for groupId: ${transaction.groupId}`,
      );
    }

    // Queue invoice status update for this transaction (legacy support)
    try {
      await this.invoiceStatusQueue.add(
        'update-invoice-status',
        {
          transactionId: transaction._id.toString(),
          status: InvoiceStatus.FAILED,
          paymentIntentId: paymentIntentId,
          paymentMethodType: paymentMethodType,
          failureReason: failureReason,
          payments: [
            {
              method: paymentMethodType,
              amount: transaction.amount,
              date: new Date(),
              paymentIntentId: paymentIntentId,
            },
          ],
        },
        {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 1000,
          },
        },
      );
    } catch (error) {
      this.logger.error(
        `Failed to queue invoice update for failed transaction ${transaction._id}: ${error.message}`,
      );
    }

    this.logger.log(
      `Transaction ${transaction._id} marked as failed - Reason: ${failureReason}`,
    );
  }

  /**
   * Queue invoice processing for direct invoice payment failures
   */
  private async queueInvoiceProcessing(
    paymentIntentId: string,
    paymentMethodType: string,
    failureReason: string,
  ): Promise<void> {
    try {
      await this.invoiceStatusQueue.add(
        'update-invoice-status',
        {
          paymentIntentId: paymentIntentId,
          status: InvoiceStatus.FAILED,
          paymentMethodType: paymentMethodType,
          failureReason: failureReason || 'Payment failed',
        },
        {
          attempts: 10, // More retries for invoice processing
          backoff: {
            type: 'exponential',
            delay: 1000,
          },
          removeOnComplete: 100,
          removeOnFail: 50,
        },
      );

      this.logger.log(
        `Invoice failure processing job queued for payment intent: ${paymentIntentId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to queue invoice failure processing for payment intent ${paymentIntentId}: ${error.message}`,
        error.stack,
      );
      // Don't throw - transaction processing might have already succeeded
    }
  }
}
