import { Injectable, Logger } from '@nestjs/common';
import { StripeEventHandler } from '../../../webhooks/interfaces/stripe-event-handler.interface';
import { TransactionService } from '../../../transaction/transaction.service';
import Stripe from 'stripe';
import { PaymentTransactionService } from 'src/payment-transaction/payment-transaction.service';
import { InvoiceStatus, PaymentTransactionStatus } from 'src/stripe/type';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';

@Injectable()
export class ChargeRefundedHandler
  implements StripeEventHandler<Stripe.Charge>
{
  private readonly logger = new Logger(ChargeRefundedHandler.name);

  constructor(
    @InjectModel(SubscriptionInvoice.name)
    private readonly subscriptionInvoiceModel: Model<SubscriptionInvoice>,
  ) {}

  /**
   * Processes a Stripe charge.refunded webhook event
   *
   * @param charge - The Stripe charge object from the webhook
   * @param stripe - The initialized Stripe client instance
   * @throws Error if transaction processing fails
   */
  async handleEvent(charge: Stripe.Charge, stripe: Stripe): Promise<void> {
    this.logger.log(`Charge refunded: ${charge.id}`);

    let amount = charge.amount / 100;
    let refundId = charge.id;
    const invoice = await this.subscriptionInvoiceModel.findOne({
      'metadata.refundId': refundId,
    });

    if (invoice) {
      const refundLineItem = invoice.line_items.find(
        (item) => item.name === 'Refund',
      );
      invoice.status =
        charge.status === 'succeeded'
          ? refundLineItem.type === InvoiceStatus.FULL_REFUND
            ? InvoiceStatus.FULL_REFUND
            : InvoiceStatus.PARTIAL_REFUND
          : InvoiceStatus.REFUND_FAILED;
      invoice.finalAmount = invoice.finalAmount - amount;
      await invoice.save();
    }
  }
}
