import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import Stripe from 'stripe';
import { Credential } from 'src/database/schema/stripeCredential';

@Injectable()
export class StripeCustomerService {
  private readonly logger = new Logger(StripeCustomerService.name);

  constructor(
    @InjectModel(Credential.name) private credentialModel: Model<Credential>,
  ) {}

  async createCustomer(studioId: string, email: string, firstName, lastName) {
    const credential = await this.credentialModel.findOne({
      studioId: studioId,
    });

    const stripe = new Stripe(credential.apiSecret, {
      apiVersion: '2025-02-24.acacia',
    });

    const customer = await stripe.customers.create({
      email: email,
      name: `${firstName} ${lastName}`,
    });
    return customer;
  }

  async createOrFetchCustomer(
    studioId: string,
    email: string,
    firstName: string,
    lastName: string,
  ) {
    const credential = await this.credentialModel.findOne({
      studioId: studioId,
    });

    const stripe = new Stripe(credential.apiSecret, {
      apiVersion: '2025-02-24.acacia',
    });

    // Search for existing customer
    const existingCustomers = await stripe.customers.list({
      email: email,
      limit: 1,
    });

    if (existingCustomers.data.length > 0) {
      return existingCustomers.data[0];
    } else {
      // Create new customer with currency
      const customer = await stripe.customers.create({
        email: email,
        name: `${firstName} ${lastName}`,
      });
      return customer;
    }
  }
}
