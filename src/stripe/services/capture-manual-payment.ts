import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { Types } from 'mongoose';

import { ParentsService } from 'src/parents/parents.service';

import { CurrencyService } from 'src/currency/currency.service';

import { StripeCommonService } from './stripe-common.service';

import { SubscriptionInvoiceService } from 'src/subscription-invoice/subscription-invoice.service';
import { PaymentMethod } from '../type';
import { PaymentTransactionService } from 'src/payment-transaction/payment-transaction.service';
import { getGcpIpAddress } from 'src/utils/helperFunction';
import { InvoiceStatus, PaymentTransactionStatus } from 'src/stripe/type/index';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import { PaymentTransaction } from 'src/database/schema/paymentTransaction';

@Injectable()
export class StripeCaptureManualPaymentService {
  private readonly logger = new Logger(StripeCaptureManualPaymentService.name);
  constructor(
    @Inject(forwardRef(() => ParentsService))
    private readonly parentsService: ParentsService,

    @Inject(forwardRef(() => CurrencyService))
    private readonly currencyService: CurrencyService,

    @Inject(forwardRef(() => StripeCommonService))
    private readonly stripeCommonService: StripeCommonService,

    @Inject(forwardRef(() => SubscriptionInvoiceService))
    private readonly subscriptionInvoiceService: SubscriptionInvoiceService,

    @Inject(forwardRef(() => PaymentTransactionService))
    private readonly paymentTransactionService: PaymentTransactionService,
  ) {}

  /**
   * Helper method to update invoice metadata and status when payment fails
   */
  private async updateInvoiceOnFailure(
    invoiceId: string,
    invoice: SubscriptionInvoice,
    error: any,
    context: string,
  ): Promise<void> {
    try {
      if (!invoice) {
        this.logger.error(
          `invoiceId: ${invoiceId} || Invoice not found in ${context}`,
        );
        return;
      }

      await this.subscriptionInvoiceService.update(invoiceId, {
        metadata: {
          ...invoice.metadata,
          failureReason: error.message || `Payment failed - ${context}`,
          attemptCount: (invoice.metadata?.attemptCount || 0) + 1,
          lastAttemptDate: new Date(),
        },
        status: InvoiceStatus.FAILED,
      });
    } catch (updateError) {
      this.logger.error(
        `invoiceId: ${invoiceId} || Failed to update invoice with failure reason in ${context}:`,
        updateError,
      );
    }
  }

  /**
   * Helper method to update transaction metadata and status when payment fails
   */
  private async updateTransactionOnFailure(
    transactionId: string,
    transaction: PaymentTransaction,
    error: any,
    context: string,
  ): Promise<void> {
    try {
      if (!transaction) {
        this.logger.error(
          `transactionId: ${transactionId} || Transaction not found in ${context}`,
        );
        return;
      }

      await this.paymentTransactionService.update(transactionId, {
        metadata: {
          ...transaction.metadata,
          failureReason: error.message || `Payment failed - ${context}`,
          attemptCount: (transaction.metadata?.attemptCount || 0) + 1,
          lastAttemptDate: new Date(),
        },
        status: PaymentTransactionStatus.FAILED,
      });
    } catch (updateError) {
      this.logger.error(
        `transactionId: ${transactionId} || Failed to update transaction with failure reason in ${context}:`,
        updateError,
      );
    }
  }

  /**
   * Captures a manual payment for an invoice
   *
   * This method handles the complete payment flow including:
   * - Retrieving invoice, parent, and studio information
   * - Creating a Stripe Payment Intent
   * - Processing payments through Stripe Payment Intent
   *
   * @param body - Invoice data containing invoiceId, studioId
   * @returns Object with status information and payment details
   * @throws HttpException if payment processing fails or payment method is missing
   */

  async captureManualPayment(body: { invoiceId: string; studioId: string }) {
    const { invoiceId, studioId } = body;
    try {
      const { stripe } =
        await this.stripeCommonService.initializeStripe(studioId);

      const currency = await this.currencyService.findByStudioId(
        Types.ObjectId.createFromHexString(studioId),
      );

      const invoice =
        await this.subscriptionInvoiceService.findOnePopulated(invoiceId);

      if (!invoice) {
        throw new Error(`Invoice not found: ${invoiceId}`);
      }

      const parent = await this.parentsService.findOne(
        invoice.parentId.toString(),
      );

      // Calculate remaining amount for partially paid invoices
      const totalPaymentsMade =
        invoice.payments?.reduce((sum, payment) => sum + payment.amount, 0) ||
        0;

      const remainingAmount = invoice.finalAmount - totalPaymentsMade;

      // If invoice is already fully paid, return early
      if (remainingAmount <= 0) {
        this.logger.log(`Invoice ${invoiceId} is already fully paid`);
        return null;
      }

      let paymentMethod =
        (invoice.paymentMethod as string) === 'unknown' ||
        !invoice.paymentMethod
          ? (invoice.subscriptionId as any)?.metadata?.paymentMethod
          : invoice.paymentMethod;

      const paymentMethodsParams: any = {
        customer: parent.stripeCustomerId,
      };

      if (paymentMethod) {
        paymentMethodsParams.type = paymentMethod;
      }

      const paymentMethods =
        await stripe.paymentMethods.list(paymentMethodsParams);

      const productName = invoice.line_items.find(
        (item) => item.type === 'Tuition Fee',
      )?.name;
      let paymentMethodId;
      if (paymentMethods.data.length > 0) {
        paymentMethodId = paymentMethods.data[0].id;
        paymentMethod = paymentMethods.data[0].type;
      }

      let paymentIntent;
      try {
        if (paymentMethod === PaymentMethod.US_BANK_ACCOUNT) {
          // First create without confirming
          paymentIntent = await stripe.paymentIntents.create({
            amount: Math.floor(remainingAmount * 100), // Use remaining amount instead of finalAmount
            currency: currency.name,
            customer: parent.stripeCustomerId,
            payment_method: paymentMethodId,
            off_session: false,
            confirm: false,
            payment_method_types: [paymentMethod],
            description: `Payment for ${productName || 'Enrollio'}`,
            metadata: {
              processInvoiceUsingPaymentIntent: 'true',
            },
          });

          // Then confirm with mandate data
          const serverIp = await getGcpIpAddress();
          paymentIntent = await stripe.paymentIntents.confirm(
            paymentIntent.id,
            {
              payment_method: paymentMethodId,
              mandate_data: {
                customer_acceptance: {
                  type: 'online',
                  online: {
                    ip_address: serverIp || '127.0.0.1',
                    user_agent: 'Enrollio Server Process',
                  },
                },
              },
            },
          );
        } else if (
          paymentMethod === PaymentMethod.CARD ||
          paymentMethod === PaymentMethod.LINK
        ) {
          const paymentMethodTypes = [paymentMethod];
          if (paymentMethod === PaymentMethod.LINK) {
            paymentMethodTypes.push(PaymentMethod.CARD);
          }
          paymentIntent = await stripe.paymentIntents.create({
            amount: Math.floor(remainingAmount * 100), // Use remaining amount instead of finalAmount
            currency: currency.name,
            customer: parent.stripeCustomerId,
            payment_method: paymentMethodId,
            off_session: true,
            confirm: true,
            payment_method_types: paymentMethodTypes,
            description: `Payment for ${productName || 'Enrollio'}`,
            metadata: {
              processInvoiceUsingPaymentIntent: 'true',
            },
          });
        }

        await this.subscriptionInvoiceService.update(invoiceId, {
          status: InvoiceStatus.PENDING,
          paymentMethod: paymentMethod,
          metadata: {
            ...invoice.metadata,
            paymentIntentId: paymentIntent.id,
          },
        });

        return paymentIntent.id;
      } catch (error) {
        this.logger.error(`invoiceId: ${invoiceId} || error: ${error}`);

        // Save failure reason to invoice metadata
        await this.updateInvoiceOnFailure(
          invoiceId,
          invoice,
          error,
          'payment intent creation',
        );

        throw error;
      }
    } catch (error) {
      this.logger.error(`invoiceId: ${invoiceId} || error: ${error}`);

      // Save failure reason to invoice metadata for outer catch block
      try {
        const invoice =
          await this.subscriptionInvoiceService.findOnePopulated(invoiceId);

        await this.updateInvoiceOnFailure(
          invoiceId,
          invoice,
          error,
          'manual payment capture',
        );
      } catch (updateError) {
        this.logger.error(
          `invoiceId: ${invoiceId} || Failed to update invoice in outer catch block:`,
          updateError,
        );
      }

      throw error;
    }
  }

  async captureManualTransaction(body: {
    transactionId: string;
    studioId: string;
    metadata: any;
  }) {
    const { transactionId, studioId, metadata } = body;
    try {
      const { stripe } =
        await this.stripeCommonService.initializeStripe(studioId);

      const currency = await this.currencyService.findByStudioId(
        Types.ObjectId.createFromHexString(studioId),
      );

      const transaction =
        await this.paymentTransactionService.findOne(transactionId);

      if (!transaction) {
        throw new Error(`Transaction not found: ${transactionId}`);
      }

      const parent = await this.parentsService.findOne(
        transaction.parentId.toString(),
      );
      const paymentMethods = await stripe.paymentMethods.list({
        customer: parent.stripeCustomerId,
        type:
          transaction.paymentMethod === PaymentMethod.CARD
            ? PaymentMethod.CARD
            : transaction.paymentMethod === PaymentMethod.LINK
              ? PaymentMethod.LINK
              : PaymentMethod.US_BANK_ACCOUNT,
      });

      let paymentMethodId;
      if (paymentMethods.data.length > 0) {
        paymentMethodId = paymentMethods.data[0].id;
      }

      let paymentIntent;
      try {
        if (transaction.paymentMethod === PaymentMethod.US_BANK_ACCOUNT) {
          // First create without confirming
          paymentIntent = await stripe.paymentIntents.create({
            amount: Math.floor(transaction.amount * 100),
            currency: currency.name,
            customer: parent.stripeCustomerId,
            payment_method: paymentMethodId,
            off_session: false,
            confirm: false,
            payment_method_types: [transaction.paymentMethod],
            description: `Payment for ${transaction.metadata.description || 'Enrollio'}`,
            metadata: {
              ...metadata,
            },
          });

          // Then confirm with mandate data
          const serverIp = await getGcpIpAddress();
          paymentIntent = await stripe.paymentIntents.confirm(
            paymentIntent.id,
            {
              payment_method: paymentMethodId,
              mandate_data: {
                customer_acceptance: {
                  type: 'online',
                  online: {
                    ip_address: serverIp || '127.0.0.1',
                    user_agent: 'Enrollio Server Process',
                  },
                },
              },
            },
          );
        } else if (
          transaction.paymentMethod === PaymentMethod.CARD ||
          transaction.paymentMethod === PaymentMethod.LINK
        ) {
          const paymentMethodTypes = [transaction.paymentMethod];
          if (transaction.paymentMethod === PaymentMethod.LINK) {
            paymentMethodTypes.push(PaymentMethod.CARD);
          }
          paymentIntent = await stripe.paymentIntents.create({
            amount: Math.floor(transaction.amount * 100),
            currency: currency.name,
            customer: parent.stripeCustomerId,
            payment_method: paymentMethodId,
            off_session: true,
            confirm: true,
            payment_method_types: paymentMethodTypes,
            description: `Payment for ${transaction.metadata.description || 'Enrollio'}`,
            metadata: {
              ...metadata,
            },
          });
        }

        return paymentIntent.id;
      } catch (error) {
        this.logger.error(`transactionId: ${transactionId} || error: ${error}`);

        // Save failure reason to transaction metadata
        await this.updateTransactionOnFailure(
          transactionId,
          transaction,
          error,
          'payment intent creation',
        );

        throw error;
      }
    } catch (error) {
      this.logger.error(`transactionId: ${transactionId} || error: ${error}`);

      // Save failure reason to transaction metadata for outer catch block
      try {
        const transaction =
          await this.paymentTransactionService.findOne(transactionId);

        await this.updateTransactionOnFailure(
          transactionId,
          transaction,
          error,
          'manual transaction capture',
        );
      } catch (updateError) {
        this.logger.error(
          `transactionId: ${transactionId} || Failed to update transaction in outer catch block:`,
          updateError,
        );
      }

      throw error;
    }
  }
}
