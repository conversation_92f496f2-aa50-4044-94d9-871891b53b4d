import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import Stripe from 'stripe';
import { Credential } from 'src/database/schema/stripeCredential';
import { StudiosService } from 'src/studios/studios.service';
import { EnrollmentService } from 'src/enrollment/enrollment.service';
import { ParentsService } from 'src/parents/parents.service';
import { StudentsService } from 'src/students/students.service';
import { CurrencyService } from 'src/currency/currency.service';
import { DiscountService } from 'src/discount/discount.service';
import {
  sendDropClassEmail,
  sendTransactionEmail,
} from 'src/emailProvider/email';
import { GohighlevelService } from 'src/gohighlevel/gohighlevel.service';
import { Transaction } from 'src/database/schema/transaction';
import { GcpStorageService } from 'src/gcp-storage/gcp-storage.service';
import { WebhookErrorLogsService } from 'src/webhook-error-logs/webhook-error-logs.service';
import { TriggersService } from 'src/triggers/triggers.service';
import { StripeCouponService } from './stripe-coupon.service';
import { StripeHistoryService } from './remove-and-create-history.service';
import {
  InvoiceStatus,
  PaymentTransactionEntityType,
  PaymentTransactionStatus,
  SubscriptionStatus,
} from '../type';
import { ClassHistory } from 'src/database/schema/classHistory';
import { ClassHistoryService } from 'src/class-history/class-history.service';
import { SubscriptionStatus as StudentSubscriptionStatus } from 'src/database/schema/student';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import { Subscription } from 'src/database/schema/subscription';
import { SubscriptionService } from 'src/subscription/subscription.service';
import { DiscountCouponService } from 'src/discount-coupon/discount-coupon.service';
import { PaymentTransactionService } from 'src/payment-transaction/payment-transaction.service';
import { PaymentTransaction } from 'src/database/schema/paymentTransaction';

@Injectable()
export class StripeCancelSubscriptionByStudentService {
  private readonly logger = new Logger(
    StripeCancelSubscriptionByStudentService.name,
  );
  private SubscriptionStatus = {
    ACTIVE: 'active',
    TERM_ENDED: 'term-ended', // end date of a subscription
    DROPPED: 'dropped', // subscription is cancelled
    CLASS_TRANSFERRED: 'class-transferred', // student is transferred to a different class
  };
  constructor(
    @InjectModel(Credential.name) private credentialModel: Model<Credential>,
    @Inject(forwardRef(() => ParentsService))
    private readonly parentsService: ParentsService,
    @Inject(forwardRef(() => StudentsService))
    private readonly studentsService: StudentsService,
    @Inject(forwardRef(() => EnrollmentService))
    private readonly enrollmentService: EnrollmentService,
    @Inject(forwardRef(() => StudiosService))
    private readonly studioService: StudiosService,
    @Inject(forwardRef(() => CurrencyService))
    private readonly currencyService: CurrencyService,
    @Inject(forwardRef(() => DiscountService))
    private readonly discountService: DiscountService,
    @Inject(forwardRef(() => GohighlevelService))
    private readonly gohighlevelService: GohighlevelService,
    @Inject(forwardRef(() => GcpStorageService))
    private readonly gcpStorageService: GcpStorageService,
    @Inject(forwardRef(() => WebhookErrorLogsService))
    private readonly triggersService: TriggersService,
    @Inject(forwardRef(() => StripeCouponService))
    private readonly stripeCouponService: StripeCouponService,
    @Inject(forwardRef(() => StripeHistoryService))
    private readonly StripeHistoryService: StripeHistoryService,
    @InjectModel(ClassHistory.name)
    private classHistoryModel: Model<ClassHistory>,
    private readonly classhistoryService: ClassHistoryService,
    @InjectModel(Subscription.name)
    private subscriptionModel: Model<Subscription>,
    @InjectModel(SubscriptionInvoice.name)
    private subscriptionInvoiceModel: Model<SubscriptionInvoice>,
    @Inject(forwardRef(() => SubscriptionService))
    private readonly subscriptionService: SubscriptionService,
    @Inject(forwardRef(() => DiscountCouponService))
    private readonly discountCouponService: DiscountCouponService,
    @Inject(forwardRef(() => PaymentTransactionService))
    private readonly paymentTransactionService: PaymentTransactionService,

    //import the model for payment transaction
    @InjectModel(PaymentTransaction.name)
    private paymentTransactionModel: Model<PaymentTransaction>,
  ) {}

  /**
   * Cancels a student's subscription to a class
   *
   * This method handles the complete cancellation process including:
   * - Retrieving Stripe credentials and subscription information
   * - Canceling the subscription in Stripe
   * - Recalculating discounts for remaining students/classes
   * - Creating new coupons for adjusted discounts
   * - Updating subscription schedules with new discount amounts
   * - Marking scheduled transactions as cancelled
   * - Updating student enrollment records
   * - Removing GHL tags from parent contact
   * - Sending cancellation emails to parents
   * - Triggering class drop notifications
   *
   * @param classId - ID of the class to cancel
   * @param locationId - ID of the studio location
   * @param studentId - ID of the student to remove from class
   * @param skipEmail - Whether to skip sending confirmation emails
   * @returns Void
   * @throws Error if subscription cancellation fails
   */
  async cancelSubscriptionByStudent(
    classId: string,
    locationId: string,
    studentId: string,
    skipEmail: boolean = false,
  ) {
    const student = await this.studentsService.findOne(studentId);
    const enrollment = student.enrollments.find(
      (e) => e.enrollmentId._id.toString() === classId,
    );

    // Get enrollment data to access session
    const enrollmentData = await this.enrollmentService.findOne(classId);
    const sessionId = enrollmentData.session?._id.toString();

    // Get all students under the same parent
    const parent = await this.parentsService.findOne(
      student.parentId.toString(),
    );

    // Find all scheduled transactions for this parent/session
    const scheduledTxs =
      await this.paymentTransactionService.findTransactionsByQuery({
        parentId: student.parentId,
        studentId: studentId,
        status: PaymentTransactionStatus.SCHEDULED,
        'metadata.sessionId': sessionId,
      });

    // If parent has already paid for this session, remove session reg fee from all scheduled transactions
    if (parent.isSessionFeePaid?.[sessionId] && scheduledTxs.length !== 0) {
      for (const tx of scheduledTxs) {
        if (tx.metadata?.line_items) {
          const newLineItems = tx.metadata.line_items.filter(
            (item) =>
              item.price_data?.product_data?.description !==
              'Session Registration Fee',
          );
          if (newLineItems.length !== tx.metadata.line_items.length) {
            tx.metadata.line_items = newLineItems;
            tx.amount = newLineItems.reduce(
              (sum, item) =>
                sum + (item.price_data.unit_amount * item.quantity) / 100,
              0,
            );
            await tx.save();
          }
        }
      }
    }

    // Find the cancelled student's scheduled transaction for this session
    if (scheduledTxs.length !== 0) {
      const cancelledTx = scheduledTxs.find(
        (tx) => tx.studentId.toString() === student._id.toString(),
      );
      if (cancelledTx) {
        // Check if the cancelled student's transaction has the session reg fee
        let sessionFeeLineItem = null;
        if (cancelledTx.metadata?.line_items) {
          sessionFeeLineItem = cancelledTx.metadata.line_items.find(
            (item) =>
              item.price_data?.product_data?.description ===
              'Session Registration Fee',
          );
        }

        if (sessionFeeLineItem) {
          // Remove session fee from cancelled student's transaction
          cancelledTx.metadata.line_items =
            cancelledTx.metadata.line_items.filter(
              (item) =>
                item.price_data?.product_data?.description !==
                'Session Registration Fee',
            );
          cancelledTx.amount = cancelledTx.metadata.line_items.reduce(
            (sum, item) =>
              sum + (item.price_data.unit_amount * item.quantity) / 100,
            0,
          );
          await cancelledTx.save();

          // Find the oldest other scheduled transaction
          const otherTxs = scheduledTxs.filter(
            (tx) => tx._id.toString() !== cancelledTx._id.toString(),
          );
          if (otherTxs.length > 0) {
            const oldest = otherTxs.sort(
              (a, b) =>
                // @ts-expect-error createdAt is present via mongoose timestamps
                new Date(a.createdAt).getTime() -
                // @ts-expect-error createdAt is present via mongoose timestamps
                new Date(b.createdAt).getTime(),
            )[0];
            // Add session fee to the oldest if not already present
            const alreadyHasSessionFee = oldest.metadata?.line_items?.some(
              (item) =>
                item.price_data?.product_data?.description ===
                'Session Registration Fee',
            );
            if (!alreadyHasSessionFee) {
              oldest.metadata.line_items = oldest.metadata.line_items || [];
              oldest.metadata.line_items.push(sessionFeeLineItem);
              oldest.amount = oldest.metadata.line_items.reduce(
                (sum, item) =>
                  sum + (item.price_data.unit_amount * item.quantity) / 100,
                0,
              );
              await oldest.save();
            }
          }
        } else {
          // If the cancelled student's transaction does not have the session fee, and parent hasn't paid, add it to the oldest scheduled transaction
          const otherTxs = scheduledTxs.filter(
            (tx) => tx._id.toString() !== cancelledTx._id.toString(),
          );
          if (otherTxs.length > 0) {
            const oldest = otherTxs.sort(
              (a, b) =>
                // @ts-expect-error createdAt is present via mongoose timestamps
                new Date(a.createdAt).getTime() -
                // @ts-expect-error createdAt is present via mongoose timestamps
                new Date(b.createdAt).getTime(),
            )[0];
            const alreadyHasSessionFee = oldest.metadata?.line_items?.some(
              (item) =>
                item.price_data?.product_data?.description ===
                'Session Registration Fee',
            );
            if (!alreadyHasSessionFee) {
              // Recreate the session fee line item using the format from product-checkout.service.ts
              const currency =
                oldest.metadata.line_items?.[0]?.price_data?.currency || 'usd';
              const sessionFeeLineItemToAdd = {
                price_data: {
                  currency: currency,
                  product_data: {
                    name: enrollmentData.session.name,
                    description: 'Session Registration Fee',
                  },
                  unit_amount:
                    enrollmentData.session.registrationFeeAmount * 100,
                },
                quantity: 1,
              };
              oldest.metadata.line_items = oldest.metadata.line_items || [];
              oldest.metadata.line_items.push(sessionFeeLineItemToAdd);
              oldest.amount = oldest.metadata.line_items.reduce(
                (sum, item) =>
                  sum + (item.price_data.unit_amount * item.quantity) / 100,
                0,
              );
              await oldest.save();
            }
          }
        }
      }
    }

    const allStudents = await this.studentsService.getStudentsByParentId(
      parent._id.toString(),
    );

    // Get and sort all active students by their earliest enrollment date
    const activeStudents = allStudents
      .filter((s) => {
        const activeEnrollments = s.enrollments.filter(
          (e) =>
            e.subscriptionStatus === 'active' &&
            e.enrollmentId._id !== Types.ObjectId.createFromHexString(classId),
        );
        return activeEnrollments.length > 0;
      })
      .sort((a, b) => {
        const aDate = Math.min(
          ...a.enrollments
            .filter(
              (e) =>
                e.subscriptionStatus === 'active' &&
                e.enrollmentId._id.toString() !== classId,
            )
            .map((e) => new Date(e.enrolledDate).getTime()),
        );
        const bDate = Math.min(
          ...b.enrollments
            .filter(
              (e) =>
                e.subscriptionStatus === 'active' &&
                e.enrollmentId._id.toString() !== classId,
            )
            .map((e) => new Date(e.enrolledDate).getTime()),
        );
        return aDate - bDate;
      });

    // Process each student
    for (let i = 0; i < activeStudents.length; i++) {
      const currentStudent = activeStudents[i];
      const studentPosition = i + 1; // 1-based index for student position

      // Get and sort active enrollments for current student
      const activeEnrollments = currentStudent.enrollments
        .filter(
          (e) =>
            // Only include active subscriptions AND exclude the specific enrollment being cancelled
            e.subscriptionStatus === 'active' &&
            !(
              currentStudent._id ===
              Types.ObjectId.createFromHexString(student._id.toString())
            ),
        )
        .sort(
          (a, b) =>
            new Date(a.enrolledDate).getTime() -
            new Date(b.enrolledDate).getTime(),
        );

      // Process each enrollment for the current student
      for (let j = 0; j < activeEnrollments.length; j++) {
        const enrollment = activeEnrollments[j];
        const classPosition = j + 1; // 1-based index for class position

        // Calculate positions and call calculateDiscount
        const multiClassDiscount = await this.discountService.calculateDiscount(
          {
            studioId: locationId,
            student: {
              firstName: currentStudent.firstName,
              lastName: currentStudent.lastName,
              classPosition,
              studentPosition: 0, // Not needed for multi-class
              tuitionFee: (enrollment.enrollmentId as any).tuitionFee,
              enrollmentId: enrollment.enrollmentId.toString(),
            },
            category: 'multi-class',
          },
        );

        const multiStudentDiscount =
          await this.discountService.calculateDiscount({
            studioId: locationId,
            student: {
              firstName: currentStudent.firstName,
              lastName: currentStudent.lastName,
              classPosition: 0, // Not needed for multi-student
              studentPosition,
              tuitionFee: (enrollment.enrollmentId as any).tuitionFee,
              enrollmentId: enrollment.enrollmentId.toString(),
            },
            category: 'multi-student',
          });

        const totalDiscount =
          multiClassDiscount.totalDiscount + multiStudentDiscount.totalDiscount;

        if (totalDiscount > 0) {
          const coupon = await this.discountCouponService.createCoupon({
            type: 'fixed',
            value: totalDiscount,
            name: `Fixed Discount of ${totalDiscount}`,
            studioId: Types.ObjectId.createFromHexString(locationId.toString()),
            category: 'enrollment',
          });

          if (
            (enrollment.enrollmentId as any).tuitionBillingCycle !== 'one-time'
          ) {
            // Update subscription with new coupon ID
            try {
              await this.subscriptionService.update(enrollment.subscriptionId, {
                appliedCouponId: Types.ObjectId.createFromHexString(
                  coupon._id.toString(),
                ),
              });
            } catch (error) {
              console.error(
                'Error updating subscription with new coupon:',
                error,
              );
              throw new Error('Error updating subscription with new coupon');
            }
          }
        }
      }
    }

    try {
      //cancel the transaction if it exists
      await this.paymentTransactionModel.updateOne(
        {
          studentId: Types.ObjectId.createFromHexString(student._id.toString()),
          typeId: Types.ObjectId.createFromHexString(classId),
          status: PaymentTransactionStatus.SCHEDULED,
        },
        { status: PaymentTransactionStatus.CANCELLED },
      );

      //cancel the subscription
      await this.subscriptionModel.updateOne(
        { _id: enrollment.subscriptionId.toString() },
        { status: SubscriptionStatus.CANCELLED },
      );

      if ((enrollment.enrollmentId as any).tuitionBillingCycle !== 'one-time') {
        // Find and update any upcoming invoices to cancelled
        await this.subscriptionInvoiceModel.updateMany(
          {
            studentId: Types.ObjectId.createFromHexString(
              student._id.toString(),
            ),
            subscriptionId: Types.ObjectId.createFromHexString(
              enrollment.subscriptionId.toString(),
            ),
            status: {
              $in: [InvoiceStatus.UPCOMING, InvoiceStatus.SCHEDULED],
            },
          },
          { status: InvoiceStatus.CANCELLED },
        );
      } else {
        // Find and update any upcoming invoices to cancelled
        await this.subscriptionInvoiceModel.updateMany(
          {
            studentId: Types.ObjectId.createFromHexString(
              student._id.toString(),
            ),
            subscriptionId: Types.ObjectId.createFromHexString(
              enrollment.subscriptionId.toString(),
            ),
            status: {
              $in: [InvoiceStatus.UPCOMING, InvoiceStatus.SCHEDULED],
            },
          },
          { status: InvoiceStatus.CANCELLED },
        );
      }
    } catch (error) {
      console.error('Error canceling subscription:', error);
    }

    // Update student model to remove the cancelled enrollment
    await this.studentsService.findByIdAndUpdate(student._id.toString(), {
      $pull: {
        enrollments: {
          enrollmentId: Types.ObjectId.createFromHexString(classId),
        },
      },
    });

    const studio = await this.studioService.findByLocationId(locationId);
    const currency = await this.currencyService.findByStudioId(
      Types.ObjectId.createFromHexString(studio._id.toString()),
    );

    // ghl tags update remove tags
    const tagsArray = enrollmentData.tags.map((tag) => tag.fieldName);
    try {
      const tagsRemoved =
        await this.gohighlevelService.removeTagsFromContactByEmail(
          studio._id.toString(),
          parent.email,
          tagsArray,
        );
      if (!tagsRemoved) {
        this.logger.warn(
          `Failed to remove tags - contact not found for email: ${parent.email}`,
        );
      }
    } catch (error) {
      this.logger.error(`Error removing tags from contact ${error}`);
    }

    let studioLogoUrl = null;
    try {
      studioLogoUrl = await this.gcpStorageService.getPublicImage(
        studio._id.toString(),
        'studio-logo',
      );
    } catch (error) {
      this.logger.error(`Error getting studio logo for transaction ${error}`);
    }

    const triggerDataClassDrop = {
      triggerKey: process.env.CLASS_DROP_TRIGGER_KEY,
      data: {
        studentFirstName: student.firstName,
        studentLastName: student.lastName,
        parentFirstName: parent.firstName,
        parentLastName: parent.lastName,
        parentEmail: parent.email,
        parentPhone: parent.primaryPhone,
      },
      locationId: studio.locationId,
    };

    try {
      await this.triggersService.sendToGhl(triggerDataClassDrop);
    } catch (error) {
      this.logger.error(
        `Error sending trigger to GHL for class drop with classId ${classId} Error: ${error} `,
      );
    }

    await sendDropClassEmail(
      parent.email,
      parent.name,
      studioLogoUrl ? null : studio.subaccountName,
      studioLogoUrl ? studioLogoUrl : null,
      {
        className: enrollmentData.title,
        session: enrollmentData.session ? enrollmentData.session.name : '',
        startDate: new Date(enrollmentData.startDate).toLocaleDateString(
          'en-US',
          {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
          },
        ),
        endDate: new Date(enrollmentData.endDate).toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        }),
        tuitionFee: enrollmentData.tuitionFee,
        location: enrollmentData.location
          ? enrollmentData.location.fieldName
          : '',
        instructor: enrollmentData.instructor
          ? enrollmentData.instructor
              .map((instructor) => instructor.name)
              .join(', ')
          : '',
        currency: currency.name,
      },
    );

    await this.StripeHistoryService.removeEnrollmentAndCreateHistory(
      student._id as Types.ObjectId,
      student.parentId,
      classId,
      locationId,
      this.SubscriptionStatus.DROPPED,
    );

    // class history
    // if skipEmail - transfer class
    // else - drop class
    try {
      if (skipEmail) {
        await this.classhistoryService.upsertClassHistoryStudentRecord({
          enrollmentId: enrollment.enrollmentId?._id.toString(),
          students: [
            {
              status: StudentSubscriptionStatus.CLASS_TRANSFERRED,
              studentId: student._id as string,
            },
          ],
          studioId: studio._id?.toString(),
        });
      } else {
        await this.classhistoryService.upsertClassHistoryStudentRecord({
          enrollmentId: enrollment.enrollmentId?._id.toString(),
          students: [
            {
              status: StudentSubscriptionStatus.DROPPED,
              studentId: student._id as string,
            },
          ],
          studioId: studio._id?.toString(),
        });
      }
    } catch (error) {
      this.logger.error(
        `Error upserting class history for transaction ${error}`,
      );
    }

    if (!skipEmail) {
      await sendTransactionEmail(
        parent.email,
        [],
        currency.name || 'USD',
        `${parent.firstName} ${parent.lastName}`,
        studio.subaccountName,
        studioLogoUrl ? studioLogoUrl : null,
      );
    }
  }
}
