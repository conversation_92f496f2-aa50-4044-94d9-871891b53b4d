import {
  forwardRef,
  Inject,
  Injectable,
  HttpStatus,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { Types } from 'mongoose';
import {
  calculateSubscriptionStartDate,
  getPaymentMethodTypes,
  supportsUSBankAccount,
} from 'src/utils/helperFunction';
import {
  sendPoliciesEmail,
  sendTransactionEmail,
} from 'src/emailProvider/email';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Credential } from 'src/database/schema/stripeCredential';
import { StudiosService } from 'src/studios/studios.service';
import { EnrollmentService } from 'src/enrollment/enrollment.service';
import { TransactionService } from 'src/transaction/transaction.service';
import { ParentsService } from 'src/parents/parents.service';
import { StudentsService } from 'src/students/students.service';
import { Proration } from 'src/database/schema/prorations';
import { CurrencyService } from 'src/currency/currency.service';
import { DiscountService } from 'src/discount/discount.service';
import { EventsService } from 'src/events/events.service';
import { GohighlevelService } from 'src/gohighlevel/gohighlevel.service';

import { GcpStorageService } from 'src/gcp-storage/gcp-storage.service';

import { TriggersService } from 'src/triggers/triggers.service';
import { StripeCouponService } from './stripe-coupon.service';
import { StripeCommonService } from './stripe-common.service';
import { PaymentTransactionService } from 'src/payment-transaction/payment-transaction.service';
import { SubscriptionInvoiceService } from 'src/subscription-invoice/subscription-invoice.service';
import { SubscriptionService } from 'src/subscription/subscription.service';
import {
  PaymentProvider,
  PaymentTransactionEntityType,
  PaymentMethod,
  PaymentTransactionSource,
  PaymentTransactionStatus,
  PaymentTransactionType,
  InvoiceStatus,
  InvoiceType,
} from '../type';
import { SubscriptionStatus } from 'src/database/schema/subscription';
import { DiscountCouponService } from 'src/discount-coupon/discount-coupon.service';
import { ClassHistory } from 'src/database/schema/classHistory';
import { ClassHistoryService } from 'src/class-history/class-history.service';
import { SubscriptionStatus as StudentSubscriptionStatus } from 'src/database/schema/student';
import { generateId } from 'src/utils/helperFunction';
import {
  calculateProratedAmountFromEntity,
  isScheduledEntity,
} from 'src/utils/helpers/billing-proration';
import * as dayjs from 'dayjs';
import { getGcpIpAddress } from 'src/utils/helperFunction';
import { ReasonType } from 'src/database/schema/walletTransaction';
import { formatTime } from 'src/utils/datetime';
import { Policy } from 'src/database/schema/policy';

@Injectable()
export class StripeAddStudentToEntityService {
  private readonly logger = new Logger(StripeAddStudentToEntityService.name);
  constructor(
    @InjectModel(Credential.name) private credentialModel: Model<Credential>,
    @Inject(forwardRef(() => ParentsService))
    private readonly parentsService: ParentsService,
    @Inject(forwardRef(() => StudentsService))
    private readonly studentsService: StudentsService,
    @Inject(forwardRef(() => TransactionService))
    private readonly transactionService: TransactionService,
    @Inject(forwardRef(() => EnrollmentService))
    private readonly enrollmentService: EnrollmentService,
    @Inject(forwardRef(() => EventsService))
    private readonly eventService: EventsService,
    @Inject(forwardRef(() => StudiosService))
    private readonly studioService: StudiosService,
    @InjectModel(Proration.name) private prorationModel: Model<Proration>,
    @Inject(forwardRef(() => CurrencyService))
    private readonly currencyService: CurrencyService,
    @Inject(forwardRef(() => DiscountService))
    private readonly discountService: DiscountService,
    @Inject(forwardRef(() => GohighlevelService))
    private readonly gohighlevelService: GohighlevelService,
    @Inject(forwardRef(() => GcpStorageService))
    private readonly gcpStorageService: GcpStorageService,
    @Inject(forwardRef(() => TriggersService))
    private readonly triggersService: TriggersService,
    @Inject(forwardRef(() => StripeCouponService))
    private readonly stripeCouponService: StripeCouponService,
    @Inject(forwardRef(() => StripeCommonService))
    private readonly stripeCommonService: StripeCommonService,

    @Inject(forwardRef(() => PaymentTransactionService))
    private readonly paymentTransactionService: PaymentTransactionService,
    @Inject(forwardRef(() => SubscriptionService))
    private readonly subscriptionService: SubscriptionService,
    @Inject(forwardRef(() => SubscriptionInvoiceService))
    private readonly subscriptionInvoiceService: SubscriptionInvoiceService,
    @Inject(forwardRef(() => DiscountCouponService))
    private readonly discountCouponService: DiscountCouponService,
    @InjectModel(ClassHistory.name)
    private classHistoryModel: Model<ClassHistory>,
    private readonly classhistoryService: ClassHistoryService,
  ) {}

  /**
   * Helper method to safely create ObjectId from hex string
   */
  private createObjectIdSafe(
    hexString: string,
    fieldName: string,
  ): Types.ObjectId {
    if (!hexString) {
      throw new BadRequestException(`${fieldName} is required`);
    }

    if (!/^[0-9a-fA-F]{24}$/.test(hexString)) {
      throw new BadRequestException(
        `Invalid ${fieldName} format: ${hexString}. It must be a 24-character hexadecimal string.`,
      );
    }

    if (!Types.ObjectId.isValid(hexString)) {
      throw new BadRequestException(
        `Invalid ${fieldName} format: ${hexString}`,
      );
    }

    return Types.ObjectId.createFromHexString(hexString);
  }

  /**
   * Adds a student to a class or event and processes payment
   *
   * This method handles the complete enrollment flow including:
   * - Retrieving studio, parent, and student information
   * - Calculating prorated tuition fees based on billing cycles
   * - Applying available credits and discounts
   * - Processing payments through Stripe Payment Intent
   * - Creating transactions and invoices
   * - Setting up subscription schedules for recurring payments
   * - Updating student enrollment records
   * - Sending confirmation emails
   *
   * @param body - Enrollment data containing entityId, parentId, studentId, studioId, entityType, creditToApply, and paymentMethod
   * @returns Object with status information and payment details
   * @throws HttpException if payment processing fails or payment method is missing
   */

  /*
replaces old -> new
transaction -> paymentTransaction
stripe subscription lib -> subscription
invoices -> subscriptionInvoice
  */
  async addStudentToEntity(body: any) {
    const {
      entityId,
      studentIds,
      studioId,
      entityType,
      discountCategory,
      discountType,
      discountAmount,
      discountPercentage,
      applyDiscountTo,
      sessionId,
      collectUpfront,
      customCreditAmount,
      skipPaymentMethod,
    } = body;
    let paymentMethod = body.paymentMethod;

    this.logger.log('=== STARTING addStudentToEntity ===');
    this.logger.log(
      `Input parameters: ${JSON.stringify({
        entityId,
        studentIds,
        studioId,
        entityType,
        paymentMethod,
        discountCategory,
        discountType,
        discountAmount,
        discountPercentage,
        applyDiscountTo,
        sessionId,
        collectUpfront,
        customCreditAmount,
        skipPaymentMethod,
      })}`,
    );

    try {
      this.logger.log('Step 1: Validating required IDs');

      // Validate required IDs first
      if (!studioId || !Types.ObjectId.isValid(studioId)) {
        this.logger.error(`Invalid studioId: ${studioId}`);
        throw new BadRequestException(`Invalid studioId format: ${studioId}`);
      }

      if (!entityId || !Types.ObjectId.isValid(entityId)) {
        this.logger.error(`Invalid entityId: ${entityId}`);
        throw new BadRequestException(`Invalid entityId format: ${entityId}`);
      }

      if (!Array.isArray(studentIds) || studentIds.length === 0) {
        this.logger.error(`Invalid studentIds: ${JSON.stringify(studentIds)}`);
        throw new BadRequestException('studentIds must be a non-empty array');
      }

      // Validate all student IDs
      for (const studentId of studentIds) {
        if (!studentId || !Types.ObjectId.isValid(studentId)) {
          this.logger.error(`Invalid studentId: ${studentId}`);
          throw new BadRequestException(
            `Invalid studentId format: ${studentId}`,
          );
        }
      }

      this.logger.log(
        `Validation passed. Processing ${studentIds.length} students`,
      );

      for (const studentId of studentIds) {
        this.logger.log(`\n=== Processing student: ${studentId} ===`);

        this.logger.log('Step 2: Initializing Stripe');
        const { stripe, credential } =
          await this.stripeCommonService.initializeStripe(studioId);
        this.logger.log('Stripe initialized successfully');

        this.logger.log('Step 3: Fetching studio data');
        const studio = await this.studioService.findOne(studioId);
        this.logger.log(
          `Studio found: ${studio.subaccountName} (${studio._id})`,
        );

        this.logger.log('Step 4: Fetching student data');
        const student = await this.studentsService.findById(studentId);
        this.logger.log(
          `Student found: ${student.firstName} ${student.lastName} (${student._id})`,
        );

        this.logger.log('Step 5: Fetching parent data');
        const parent = await this.parentsService.findOne(
          student.parentId.toString(),
        );

        if (!paymentMethod && parent.stripeCustomerId) {
          const defaultPaymentMethod =
            await this.stripeCommonService.getCustomerDefaultPaymentMethod(
              parent.stripeCustomerId,
              stripe,
            );
          paymentMethod =
            defaultPaymentMethod && defaultPaymentMethod.type
              ? defaultPaymentMethod.type
              : null;
        }

        const studioDiscountDetails =
          await this.discountService.findOne(studioId);
        this.logger.log(
          `Discount details: ${JSON.stringify(studioDiscountDetails?.discountRules || 'none')}`,
        );

        this.logger.log('Step 7: Fetching entity (class/event) data');
        let entity;
        let invoices = [];
        if (entityType === PaymentTransactionEntityType.CLASS) {
          entity = await this.enrollmentService.findOne(entityId);
          this.logger.log(`Class found: ${entity.title} (${entity._id})`);
        } else if (entityType === PaymentTransactionEntityType.EVENT) {
          entity = await this.eventService.findOne(entityId);
          this.logger.log(`Event found: ${entity.title} (${entity._id})`);
        }

        const billingCycle =
          entityType === PaymentTransactionEntityType.EVENT &&
          entity.oneTime === true
            ? 'one-time'
            : entity.tuitionBillingCycle;

        if (!entity) {
          this.logger.error(
            `Entity not found for entityId: ${entityId}, entityType: ${entityType}`,
          );
          throw new BadRequestException(`Entity not found`);
        }

        this.logger.log('Step 8: Getting currency and calculating amounts');

        const customer_id = parent.stripeCustomerId;
        const studioCurrency = await this.currencyService.findByStudioId(
          studio._id as Types.ObjectId,
        );

        const currency = studioCurrency.name.toLowerCase();

        // Validate US bank account payment method for USD only
        if (
          paymentMethod === 'us_bank_account' &&
          !supportsUSBankAccount(currency.toUpperCase())
        ) {
          this.logger.error(
            `US bank account payment not supported for currency: ${currency}`,
          );
          return {
            status: HttpStatus.BAD_REQUEST,
            message:
              'US bank account payments are only available for USD transactions',
          };
        }

        let proratedAmount = calculateProratedAmountFromEntity(entity);

        const totalPayments =
          await this.enrollmentService.calculateTotalPayments(
            entity._id.toString(),
          );
        // Store original line items before any wallet balance deduction
        const originalLineItems = [];
        if (proratedAmount > 0) {
          if (collectUpfront) {
            proratedAmount = proratedAmount * totalPayments.totalPayments;
          }
          originalLineItems.push({
            price_data: {
              currency: currency,
              product_data: {
                name: entity.title,
                description: 'Tuition Fee',
              },
              unit_amount: Math.floor(proratedAmount * 100),
            },
            quantity: 1,
          });
        }

        let discountSplit = {};
        let totalDiscount = 0;
        let effectiveApplyDiscountTo: 'first' | 'all' | undefined =
          applyDiscountTo; // Start with manual parameter

        // Check if discount parameters are provided in the request body
        if (
          discountCategory &&
          (discountAmount > 0 || discountPercentage > 0)
        ) {
          // Use discount from request body parameters
          if (discountType === 'amount') {
            totalDiscount = discountAmount;
          } else if (discountType === 'percentage') {
            totalDiscount = (proratedAmount * discountPercentage) / 100;
          }

          // If no manual applyDiscountTo is specified, derive it from discountRules
          if (
            !effectiveApplyDiscountTo &&
            studioDiscountDetails?.discountRules
          ) {
            effectiveApplyDiscountTo =
              studioDiscountDetails.discountRules === 'first-month'
                ? 'first'
                : 'all';
          }

          // Default to 'all' if still not specified
          if (!effectiveApplyDiscountTo) {
            effectiveApplyDiscountTo = 'all';
          }

          // Set discount split based on applyDiscountTo parameter
          if (
            effectiveApplyDiscountTo === 'all' ||
            effectiveApplyDiscountTo === 'first'
          ) {
            discountSplit[student._id.toString()] = totalDiscount;
          }

          this.logger.log(
            `Applied manual discount: ${totalDiscount} for student ${student._id.toString()} with applyDiscountTo: ${effectiveApplyDiscountTo} (derived from discountRules: ${studioDiscountDetails?.discountRules})`,
          );
        } else {
          // Use existing automatic discount calculation logic
          if (
            studioDiscountDetails?.discountRules &&
            studioDiscountDetails?.discountRules === 'all-months'
          ) {
            if (entityType === PaymentTransactionEntityType.CLASS) {
              // Get existing students count for discount calculation
              const existingStudents =
                await this.studentsService.getActiveStudentCountByParentId(
                  parent._id.toString(),
                );

              const {
                totalDiscount: updatedTotalDiscount,
                discountSplit: updatedDiscountSplit,
              } = await this.stripeCommonService.calculateDiscounts({
                students: [
                  {
                    studentId: student._id.toString(),
                    firstName: student.firstName,
                    lastName: student.lastName,
                    tuitionFee: proratedAmount || entity.tuitionFee,
                    enrollmentId: entity._id.toString(),
                    existingEnrollments: student.enrollments
                      .filter(
                        (e) =>
                          e.subscriptionStatus === 'active' ||
                          e.subscriptionStatus === 'scheduled',
                      )
                      .map((e) => ({
                        enrollmentId: e.enrollmentId.toString(),
                        subscriptionStatus: e.subscriptionStatus,
                      })),
                  },
                ],
                existingStudentIds: existingStudents.studentIds,
                studioId,
              });
              totalDiscount = updatedTotalDiscount;
              discountSplit = updatedDiscountSplit;

              // Set effectiveApplyDiscountTo based on discountRules for automatic discounts
              effectiveApplyDiscountTo = 'all';
            }
          } else if (studioDiscountDetails?.discountRules === 'first-month') {
            // Handle first-month discount rule even when no automatic discount is calculated
            effectiveApplyDiscountTo = 'first';
          }

          // Default to 'all' if still not specified
          if (!effectiveApplyDiscountTo) {
            effectiveApplyDiscountTo = 'all';
          }
        }

        this.logger.log(
          'Step 9: Calculating final amounts and getting payment methods',
        );
        // Calculate total amount before wallet balance
        const amountBeforeWallet = Math.max(0, proratedAmount - totalDiscount);
        this.logger.log(
          `Amount before wallet: ${amountBeforeWallet}, Total discount: ${totalDiscount}`,
        );

        // Get appropriate payment method types based on studio currency
        const supportedPaymentMethods = getPaymentMethodTypes(
          currency.toUpperCase(),
          true,
        );
        this.logger.log(
          `Supported payment methods for currency ${currency}: ${supportedPaymentMethods.join(', ')}`,
        );

        if (!supportedPaymentMethods.includes(paymentMethod)) {
          paymentMethod = null;
          this.logger.log(
            `Converting ${paymentMethod} payment method to null for validation`,
          );
        }

        // Validate requested payment method is supported for this currency
        if (
          !skipPaymentMethod &&
          paymentMethod &&
          !supportedPaymentMethods.includes(paymentMethod)
        ) {
          this.logger.error(
            `Payment method ${paymentMethod} not supported for currency ${currency}`,
          );
          if (paymentMethod === 'us_bank_account') {
            return {
              status: HttpStatus.BAD_REQUEST,
              message:
                'US bank account payments are only available for USD transactions',
            };
          }
          return {
            status: HttpStatus.BAD_REQUEST,
            message: `Payment method ${paymentMethod} is not supported for ${currency.toUpperCase()} transactions`,
          };
        }

        // Get payment methods
        const paymentMethods = await stripe.paymentMethods.list({
          customer: customer_id,
          type: paymentMethod ? paymentMethod : undefined,
        });
        this.logger.log(
          `Found ${paymentMethods.data.length} payment methods for customer ${customer_id}`,
        );

        const isScheduled = isScheduledEntity(entity);
        this.logger.log(`Is scheduled entity: ${isScheduled}`);

        // Use sessionId from request body if provided, otherwise use entity's session ID
        const effectiveSessionId = sessionId || entity.session?._id?.toString();

        const sessionFee =
          effectiveSessionId &&
          entity.session?.isRegistrationFee &&
          !parent.isSessionFeePaid?.[effectiveSessionId]
            ? entity.session.registrationFeeAmount
            : 0;

        //entity.session.registrationFeeAmount paid once per session
        //entity.registrationFeeAmount is paid everytime while purchasing the class
        const totalOneTimeFees =
          (entity.registrationFeeAmount || 0) +
          (entity.costumeFee || 0) +
          (sessionFee || 0);

        // Calculate total amount including one-time fees
        const totalAmount = amountBeforeWallet + totalOneTimeFees;
        this.logger.log(
          `Total amount: ${totalAmount} (amountBeforeWallet: ${amountBeforeWallet} + totalOneTimeFees: ${totalOneTimeFees})`,
        );

        // Initialize wallet variables - will only be used for immediate payments
        const walletBalance = parent.walletBalance || 0;
        let walletAmountUsed = 0;
        let amountToPay = totalAmount; // Default to full amount
        this.logger.log(
          `Wallet balance available: ${walletBalance}, Initial amount to pay: ${amountToPay}`,
        );

        //subscription
        let subscriptionId: string | null = null;
        // Create coupon if discount exists
        let couponId = null;
        if (
          (entityType === PaymentTransactionEntityType.CLASS &&
            entity.tuitionBillingCycle.toLowerCase() !== 'one-time' &&
            totalAmount > 0) ||
          (entityType === PaymentTransactionEntityType.EVENT &&
            entity.oneTime === false &&
            entity.tuitionBillingCycle !== 'one-time' &&
            totalAmount > 0)
        ) {
          // Check for discount parameters first, then fall back to studio discount rules
          const discountRules =
            body?.discountRules || studioDiscountDetails?.discountRules;

          if (discountRules && discountRules !== 'first-month') {
            if (totalDiscount > 0) {
              couponId = await this.discountCouponService.createCoupon({
                type: 'fixed',
                value: totalDiscount,
                name: `Fixed Discount of ${totalDiscount}`,
                studioId: this.createObjectIdSafe(
                  studio._id.toString(),
                  'studio._id',
                ),
                category:
                  discountCategory === 'scholarship'
                    ? 'scholarship'
                    : 'enrollment',
              });
            }
          }
        }

        let paymentIntent;
        let childTransaction = null;
        let paymentIntentSent = false;
        // Generate groupId for immediate payments instead of creating root transaction
        const groupId = generateId();

        if (sessionFee > 0) {
          originalLineItems.push({
            price_data: {
              currency: currency,
              product_data: {
                name: entity.session.name,
                description: 'Session Registration Fee',
              },
              unit_amount: Math.round(sessionFee * 100),
            },
            quantity: 1,
          });
        }
        if (entity.registrationFeeAmount > 0) {
          originalLineItems.push({
            price_data: {
              currency: currency,
              product_data: {
                name: entity.title,
                description: 'Registration Fee',
              },
              unit_amount: Math.round(entity.registrationFeeAmount * 100),
            },
            quantity: 1,
          });
        }
        if (entity.costumeFee > 0) {
          originalLineItems.push({
            price_data: {
              currency: currency,
              product_data: {
                name: entity.title,
                description: 'Costume Fee',
              },
              unit_amount: Math.round(entity.costumeFee * 100),
            },
            quantity: 1,
          });
        }

        //! 1st payment
        if (amountToPay > 0 && !collectUpfront) {
          // If no payment methods exist, generate and return the card update URL
          if (paymentMethods.data.length === 0) {
            if (!skipPaymentMethod) {
              const setupIntent = await stripe.setupIntents.create({
                payment_method_types: supportedPaymentMethods,
                customer: customer_id,
                usage: 'off_session',
              });

              return {
                status: HttpStatus.OK,
                message: 'Payment method not found',
                checkoutUrl: null,
                clientSecret: setupIntent.client_secret,
                stripePublicApiKey: credential.apiKey,
              };
            } else {
              const commonTxMetadata = {
                entityType: entityType,
                entityId: entity._id.toString(),
                sessionId: effectiveSessionId,
                billingDate:
                  entity.session?.billingDate || new Date().toISOString(),
                discountSplit: discountSplit,
                couponId: couponId,
                line_items: originalLineItems,
                walletAmountUsed: walletAmountUsed,
                originalAmount: totalAmount,
              };

              const childTxData = {
                studioId: this.createObjectIdSafe(
                  studio._id.toString(),
                  'studio._id',
                ),
                parentId: this.createObjectIdSafe(
                  parent._id.toString(),
                  'parent._id',
                ),
                studentId: this.createObjectIdSafe(
                  student._id.toString(),
                  'student._id',
                ),
                groupId: groupId,
                paymentSource:
                  PaymentTransactionSource.STUDIO_PORTAL_PRODUCT_BUY,
                type:
                  entityType === PaymentTransactionEntityType.CLASS
                    ? PaymentTransactionType.ENROLLMENT
                    : PaymentTransactionType.EVENT,
                typeId: this.createObjectIdSafe(
                  entity._id.toString(),
                  'entity._id',
                ),
                amount: amountToPay,
                status: PaymentTransactionStatus.FAILED,
                paymentProvider: PaymentProvider.STRIPE,
                paymentMethod: null,
                metadata: {
                  ...commonTxMetadata,
                  description: `Student enrollment with no payment method for ${entityType}: ${entity.title}`,
                },
              };
              this.logger.log(
                'Creating payment transaction for no payment method scenario',
              );
              childTransaction =
                await this.paymentTransactionService.create(childTxData);
              this.logger.log(
                `Transaction created with ID: ${childTransaction._id}`,
              );
            }
          } else {
            this.logger.log('Payment methods available, processing payment');
            if (!isScheduled) {
              this.logger.log('Processing immediate payment (not scheduled)');
              // Apply wallet balance for immediate payments only
              if (walletBalance > 0 && totalAmount > 0) {
                walletAmountUsed = Math.min(walletBalance, totalAmount);
                amountToPay = totalAmount - walletAmountUsed;
                this.logger.log(
                  `Applied wallet balance: ${walletAmountUsed}, New amount to pay: ${amountToPay}`,
                );
              }

              const paymentMethodId = paymentMethods.data[0].id;
              this.logger.log(`Using payment method: ${paymentMethodId}`);

              if (!skipPaymentMethod) {
                try {
                  this.logger.log(
                    `Creating payment intent for amount: ${amountToPay} with method: ${paymentMethod}`,
                  );

                  // For US bank accounts, we need to handle mandates
                  if (paymentMethod === PaymentMethod.US_BANK_ACCOUNT) {
                    this.logger.log('Processing US bank account payment');

                    try {
                      // First create without confirming
                      paymentIntent = await stripe.paymentIntents.create({
                        amount: Math.floor(amountToPay * 100),
                        currency: currency,
                        customer: customer_id,
                        payment_method: paymentMethodId,
                        off_session: false,
                        payment_method_types: [paymentMethod],
                        confirm: false,
                        description: `Payment for ${entity.name || 'Enrollio'} - ${student.firstName} ${student.lastName}`,
                        metadata: {
                          groupId: groupId,
                          fromStudioPortal: 'true',
                        },
                      });

                      // Then confirm with mandate data
                      const serverIp = await getGcpIpAddress();
                      paymentIntent = await stripe.paymentIntents.confirm(
                        paymentIntent.id,
                        {
                          payment_method: paymentMethodId,
                          mandate_data: {
                            customer_acceptance: {
                              type: 'online',
                              online: {
                                ip_address: serverIp || '127.0.0.1',
                                user_agent: 'Enrollio Server Process',
                              },
                            },
                          },
                        },
                      );
                      paymentIntentSent = true;
                      this.logger.log(
                        `US bank payment intent created successfully: ${paymentIntent.id}`,
                      );
                    } catch (usBankError) {
                      this.logger.error(
                        `US bank account payment failed: ${usBankError.message}`,
                      );

                      // Check if it's the specific error about us_bank_account not being enabled
                      if (usBankError.message?.includes('us_bank_account')) {
                        return {
                          status: HttpStatus.BAD_REQUEST,
                          message: 'Studio doesnt have ACH payment enabled',
                        };
                      }

                      // For other errors, fall back to card payment if available
                      if (supportedPaymentMethods.includes('card')) {
                        this.logger.log('Falling back to card payment');
                        paymentMethod = PaymentMethod.CARD;
                        // Continue to card payment logic below
                      } else {
                        throw usBankError;
                      }
                    }
                  }

                  if (
                    paymentMethod === PaymentMethod.CARD ||
                    paymentMethod === PaymentMethod.LINK
                  ) {
                    this.logger.log(`Processing ${paymentMethod} payment`);
                    const paymentMethodTypes = [paymentMethod];
                    if (paymentMethod === PaymentMethod.LINK) {
                      paymentMethodTypes.push(PaymentMethod.CARD);
                    }
                    // For cards and link, create and confirm in one step
                    paymentIntent = await stripe.paymentIntents.create({
                      amount: Math.floor(amountToPay * 100),
                      currency: currency,
                      customer: customer_id,
                      payment_method: paymentMethodId,
                      off_session: true,
                      confirm: true,
                      payment_method_types: paymentMethodTypes,
                      description: `Payment for ${entity.name || 'Enrollio'} - ${student.firstName} ${student.lastName}`,
                      metadata: {
                        groupId: groupId,
                        fromStudioPortal: 'true',
                      },
                    });
                    paymentIntentSent = true;
                    this.logger.log(
                      `${paymentMethod} payment intent created successfully: ${paymentIntent.id}`,
                    );
                  }
                } catch (error) {
                  this.logger.error(
                    `Error creating payment intent for transaction: ${error.message}`,
                    error.stack,
                  );
                  paymentIntentSent = false;
                }
              }

              this.logger.log(
                `Payment intent status: ${paymentIntentSent ? 'SUCCESS' : 'FAILED'}`,
              );
              const commonTxMetadata = {
                entityType: entityType,
                entityId: entity._id.toString(),
                sessionId: effectiveSessionId,
                paymentIntentId: paymentIntentSent ? paymentIntent.id : null,
                billingDate:
                  entity.session?.billingDate || new Date().toISOString(),
                discountSplit: discountSplit,
                couponId: couponId,
                line_items: originalLineItems,
                walletAmountUsed: walletAmountUsed,
                originalAmount: totalAmount,
              };

              const childTxData = {
                studioId: this.createObjectIdSafe(
                  studio._id.toString(),
                  'studio._id',
                ),
                parentId: this.createObjectIdSafe(
                  parent._id.toString(),
                  'parent._id',
                ),
                studentId: this.createObjectIdSafe(
                  student._id.toString(),
                  'student._id',
                ),
                groupId: groupId,
                paymentSource:
                  PaymentTransactionSource.STUDIO_PORTAL_PRODUCT_BUY,
                type:
                  entityType === PaymentTransactionEntityType.CLASS
                    ? PaymentTransactionType.ENROLLMENT
                    : PaymentTransactionType.EVENT,
                typeId: this.createObjectIdSafe(
                  entity._id.toString(),
                  'entity._id',
                ),
                amount: amountToPay,
                status: paymentIntentSent
                  ? PaymentTransactionStatus.PENDING
                  : PaymentTransactionStatus.FAILED,
                paymentProvider: PaymentProvider.STRIPE,
                paymentMethod: paymentMethod,
                metadata: {
                  ...commonTxMetadata,
                  description: `Student enrollment with combined payment for ${entityType}: ${entity.title}`,
                },
              };
              this.logger.log(
                'Creating payment transaction for immediate payment',
              );
              childTransaction =
                await this.paymentTransactionService.create(childTxData);
              this.logger.log(
                `Payment transaction created with ID: ${childTransaction._id}`,
              );
            } else {
              this.logger.log('Processing scheduled payments');
              if (totalAmount > 0) {
                this.logger.log('Creating scheduled payment transaction');

                const childScheduledTuitionData = {
                  studioId: this.createObjectIdSafe(
                    studio._id.toString(),
                    'studio._id',
                  ),
                  parentId: this.createObjectIdSafe(
                    parent._id.toString(),
                    'parent._id',
                  ),
                  studentId: this.createObjectIdSafe(
                    student._id.toString(),
                    'student._id',
                  ),
                  groupId: groupId,
                  paymentSource:
                    PaymentTransactionSource.STUDIO_PORTAL_PRODUCT_BUY,
                  type:
                    entityType === PaymentTransactionEntityType.CLASS
                      ? PaymentTransactionType.ENROLLMENT
                      : PaymentTransactionType.EVENT,
                  typeId: this.createObjectIdSafe(
                    entity._id.toString(),
                    'entity._id',
                  ),

                  amount: amountToPay,
                  status: collectUpfront
                    ? PaymentTransactionStatus.PENDING
                    : PaymentTransactionStatus.SCHEDULED,
                  paymentProvider: PaymentProvider.STRIPE,
                  paymentMethod: paymentMethod,
                  metadata: {
                    entityType: entityType,
                    entityId: entity._id.toString(),
                    sessionId: effectiveSessionId,
                    description: `Student Scheduled ${entityType} Tuition Fee`,
                    line_items: originalLineItems,
                    walletAmountUsed: walletAmountUsed, // No wallet balance used for scheduled payments
                    originalAmount: totalAmount,
                    billingDate:
                      entity.session?.billingDate || new Date().toISOString(),
                    discountSplit: discountSplit,
                    couponId: couponId,
                  },
                };
                childTransaction = await this.paymentTransactionService.create(
                  childScheduledTuitionData,
                );
                this.logger.log(
                  `Scheduled payment transaction created with ID: ${childTransaction._id}`,
                );
              }
            }
          }
        } else {
          this.logger.log('Amount is 0 or negative, creating free transaction');
          const commonTxMetadata = {
            entityType: entityType,
            entityId: entity._id.toString(),
            sessionId: effectiveSessionId,
            billingDate:
              entity.session?.billingDate || new Date().toISOString(),
            discountSplit: discountSplit,
            couponId: couponId,
            line_items: originalLineItems,
            walletAmountUsed: walletAmountUsed,
            originalAmount: totalAmount,
          };

          const childTxData = {
            studioId: this.createObjectIdSafe(
              studio._id.toString(),
              'studio._id',
            ),
            parentId: this.createObjectIdSafe(
              parent._id.toString(),
              'parent._id',
            ),
            studentId: this.createObjectIdSafe(
              student._id.toString(),
              'student._id',
            ),
            groupId: groupId,
            paymentSource: PaymentTransactionSource.STUDIO_PORTAL_PRODUCT_BUY,
            type:
              entityType === PaymentTransactionEntityType.CLASS
                ? PaymentTransactionType.ENROLLMENT
                : PaymentTransactionType.EVENT,
            typeId: this.createObjectIdSafe(
              entity._id.toString(),
              'entity._id',
            ),
            amount: amountToPay,
            status: PaymentTransactionStatus.FREE,
            paymentProvider: PaymentProvider.STRIPE,
            paymentMethod: null,
            metadata: {
              ...commonTxMetadata,
              description: `Student enrollment with no payment method for ${entityType}: ${entity.title}`,
            },
          };
          childTransaction =
            await this.paymentTransactionService.create(childTxData);
          this.logger.log(
            `Free transaction created with ID: ${childTransaction._id}`,
          );
        }

        this.logger.log('Step 11: Processing subscription and billing logic');
        // Get next billing date from proration data's billing cycle anchor
        const subStartDate = calculateSubscriptionStartDate(entity);
        this.logger.log(`Subscription start date calculated: ${subStartDate}`);

        let finalUpfrontAmount = 0;
        let upfrontTotalAmount = 0;

        if (collectUpfront) {
          // Calculate total payments for the entire enrollment period
          if (customCreditAmount) {
            // Apply discount to custom credit amount
            const customCreditDiscount = totalDiscount > 0 ? totalDiscount : 0;
            finalUpfrontAmount = Math.max(
              0,
              customCreditAmount - customCreditDiscount,
            );

            originalLineItems.push({
              price_data: {
                currency: currency,
                product_data: {
                  name: 'Custom Credit with Discount',
                  description: 'custom-credit',
                },
                unit_amount: Math.round(finalUpfrontAmount * 100),
              },
              quantity: 1,
            });
          } else {
            const totalPayments =
              await this.enrollmentService.calculateTotalPayments(
                entity._id.toString(),
              );

            // Calculate total amount based on tuition fee and total payments
            upfrontTotalAmount =
              totalPayments.totalPayments * entity.tuitionFee +
              totalOneTimeFees;

            // Apply discount to upfront payment
            const upfrontDiscount =
              effectiveApplyDiscountTo === 'all'
                ? totalDiscount * totalPayments.totalPayments
                : totalDiscount;

            // Calculate final amount after discount
            finalUpfrontAmount = Math.max(
              0,
              upfrontTotalAmount - upfrontDiscount,
            );
          }

          const paymentMethodId = paymentMethods.data[0].id;

          try {
            if (paymentMethod === PaymentMethod.US_BANK_ACCOUNT) {
              try {
                // First create without confirming
                paymentIntent = await stripe.paymentIntents.create({
                  amount: Math.floor(finalUpfrontAmount * 100),
                  currency: currency,
                  customer: customer_id,
                  payment_method: paymentMethodId,
                  off_session: false,
                  payment_method_types: [paymentMethod],
                  confirm: false,
                  description: `Upfront Payment for ${entity.name || 'Enrollio'} - ${student.firstName} ${student.lastName}`,
                  metadata: {
                    groupId: groupId,
                    fromStudioPortal: 'true',
                  },
                });

                // Then confirm with mandate data
                const serverIp = await getGcpIpAddress();
                paymentIntent = await stripe.paymentIntents.confirm(
                  paymentIntent.id,
                  {
                    payment_method: paymentMethodId,
                    mandate_data: {
                      customer_acceptance: {
                        type: 'online',
                        online: {
                          ip_address: serverIp || '127.0.0.1',
                          user_agent: 'Enrollio Server Process',
                        },
                      },
                    },
                  },
                );
                paymentIntentSent = true;
              } catch (usBankError) {
                this.logger.error(
                  `US bank account upfront payment failed: ${usBankError.message}`,
                );

                // Check if it's the specific error about us_bank_account not being enabled
                if (usBankError.message?.includes('us_bank_account')) {
                  return {
                    status: HttpStatus.BAD_REQUEST,
                    message: 'Studio doesnt have ACH payment enabled',
                  };
                }

                // For other errors, fall back to card payment if available
                if (supportedPaymentMethods.includes('card')) {
                  this.logger.log(
                    'Falling back to card payment for upfront payment',
                  );
                  paymentMethod = PaymentMethod.CARD;
                  // Continue to card payment logic below
                } else {
                  throw usBankError;
                }
              }
            }

            if (
              paymentMethod === PaymentMethod.CARD ||
              paymentMethod === PaymentMethod.LINK
            ) {
              const paymentMethodTypes = [paymentMethod];
              if (paymentMethod === PaymentMethod.LINK) {
                paymentMethodTypes.push(PaymentMethod.CARD);
              }
              paymentIntent = await stripe.paymentIntents.create({
                amount: Math.floor(finalUpfrontAmount * 100),
                currency: currency,
                customer: customer_id,
                payment_method: paymentMethodId,
                off_session: true,
                payment_method_types: paymentMethodTypes,
                confirm: true,
                description: `Upfront Payment for ${entity.name || 'Enrollio'} - ${student.firstName} ${student.lastName}`,
                metadata: {
                  groupId: groupId,
                  fromStudioPortal: 'true',
                },
              });
              paymentIntentSent = true;
            }

            // Create subscription invoice for upfront payment
            const appliedDiscount = customCreditAmount
              ? totalDiscount > 0
                ? totalDiscount
                : 0
              : effectiveApplyDiscountTo === 'all'
                ? totalDiscount *
                  (upfrontTotalAmount > 0
                    ? (upfrontTotalAmount - totalOneTimeFees) /
                      entity.tuitionFee
                    : 1)
                : totalDiscount;

            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const invoice = await this.subscriptionInvoiceService.create({
              studioId: this.createObjectIdSafe(
                studio._id.toString(),
                'studio._id',
              ),
              parentId: this.createObjectIdSafe(
                parent._id.toString(),
                'parent._id',
              ),
              studentId: this.createObjectIdSafe(
                student._id.toString(),
                'student._id',
              ),
              entityId: this.createObjectIdSafe(
                entity._id.toString(),
                'entity._id',
              ),
              entityType: entityType,
              baseAmount: customCreditAmount || upfrontTotalAmount,
              finalAmount: finalUpfrontAmount,
              status: InvoiceStatus.PENDING,
              type: InvoiceType.UPFRONT,
              paymentProvider: PaymentProvider.STRIPE,
              paymentMethod: paymentMethod,
              line_items: originalLineItems.map((item) => ({
                name: item.price_data.product_data.name,
                amount: item.price_data.unit_amount / 100,
                quantity: item.quantity,
                type: item.price_data.product_data.description,
                total: (item.price_data.unit_amount * item.quantity) / 100,
              })),
              dueDate: null,
              appliedCouponId: couponId,
              startDate: new Date(subStartDate),
              endDate: new Date(entity.endDate),
              metadata: {
                internalTransactionId: childTransaction
                  ? childTransaction._id.toString()
                  : null,
                appliedDiscount: appliedDiscount,
                applyDiscountTo: effectiveApplyDiscountTo || 'all',
                paymentIntentId: paymentIntentSent ? paymentIntent.id : null,
              },
            });
          } catch (error) {
            this.logger.error(
              `Error creating payment intent for upfront payment ${error}`,
            );
            paymentIntentSent = false;
          }
        } else {
          this.logger.log('Processing regular subscription creation');
          if (entity.tuitionBillingCycle !== 'one-time') {
            this.logger.log('Creating recurring subscription');
            // Create subscription
            const subscription = await this.subscriptionService.create({
              studioId: this.createObjectIdSafe(
                studio._id.toString(),
                'studio._id',
              ),
              parentId: this.createObjectIdSafe(
                parent._id.toString(),
                'parent._id',
              ),
              studentId: this.createObjectIdSafe(
                student._id.toString(),
                'student._id',
              ),
              entityId: this.createObjectIdSafe(
                entity._id.toString(),
                'entity._id',
              ),
              entityType: entityType,
              startDate: subStartDate,
              endDate: new Date(entity.endDate),
              billingCycle: billingCycle,
              baseAmount: entity.tuitionFee,
              finalAmount: entity.tuitionFee - (totalDiscount || 0),
              status: SubscriptionStatus.ACTIVE,
              nextPaymentDate: subStartDate,
              appliedCouponId: couponId
                ? this.createObjectIdSafe(
                    typeof couponId === 'string'
                      ? couponId
                      : couponId._id?.toString(),
                    'couponId',
                  )
                : undefined,
              metadata: {
                transactionId:
                  amountToPay > 0 ? childTransaction._id.toString() : null,
                appliedDiscount: totalDiscount || 0,
                discountCategory: discountCategory || 'discount',
                paymentMethod: paymentMethod,
              },
            });
            this.logger.log(
              `Recurring subscription created with ID: ${subscription._id}`,
            );

            subscriptionId = subscription._id.toString();

            if (amountToPay > 0) {
              // eslint-disable-next-line @typescript-eslint/no-unused-vars
              invoices =
                await this.subscriptionInvoiceService.generateSubscriptionInvoices(
                  {
                    subscription,
                    childTransaction: childTransaction,
                    discountAmount: totalDiscount || 0,
                    enrollment: entity,
                    transactionId: childTransaction._id.toString(),
                    transactionCodeId: entity.transactionCodeId,
                    applyDiscountTo: effectiveApplyDiscountTo || 'all',
                    noPaymentMethod: skipPaymentMethod,
                    paymentIntentSent: paymentIntentSent,
                    discountCategory: discountCategory || 'discount',
                    paymentProcessingMethod: studio.paymentProcessingMethod,
                  },
                );
            } else {
              // eslint-disable-next-line @typescript-eslint/no-unused-vars
              invoices = [];
            }
          } else {
            this.logger.log('Processing one-time billing cycle');
            if (amountToPay > 0) {
              this.logger.log('Creating one-time subscription');
              //creating subscription for one time fees
              const subscription = await this.subscriptionService.create({
                studioId: this.createObjectIdSafe(
                  studio._id.toString(),
                  'studio._id',
                ),
                parentId: this.createObjectIdSafe(
                  parent._id.toString(),
                  'parent._id',
                ),
                studentId: this.createObjectIdSafe(
                  student._id.toString(),
                  'student._id',
                ),
                entityId: this.createObjectIdSafe(
                  entity._id.toString(),
                  'entity._id',
                ),
                entityType: entityType,
                startDate: subStartDate,
                endDate: new Date(entity.endDate),
                billingCycle: billingCycle,
                baseAmount: amountToPay,
                finalAmount: amountToPay - (totalDiscount || 0),
                status: SubscriptionStatus.ACTIVE,
                nextPaymentDate: null,
                appliedCouponId: couponId
                  ? this.createObjectIdSafe(
                      typeof couponId === 'string'
                        ? couponId
                        : couponId._id?.toString(),
                      'couponId',
                    )
                  : undefined,
                metadata: {
                  appliedDiscount: totalDiscount || 0,
                  discountCategory: discountCategory || 'discount',
                  paymentMethod: paymentMethod,
                },
              });

              subscriptionId = subscription._id.toString();

              //creating single invoice for one time fees
              // eslint-disable-next-line @typescript-eslint/no-unused-vars
              const invoice = await this.subscriptionInvoiceService.create({
                studioId: this.createObjectIdSafe(
                  studio._id.toString(),
                  'studio._id',
                ),
                subscriptionId: this.createObjectIdSafe(
                  subscription._id.toString(),
                  'subscription._id',
                ),
                parentId: this.createObjectIdSafe(
                  parent._id.toString(),
                  'parent._id',
                ),
                studentId: this.createObjectIdSafe(
                  student._id.toString(),
                  'student._id',
                ),
                entityId: this.createObjectIdSafe(
                  entity._id.toString(),
                  'entity._id',
                ),
                entityType: entityType,
                baseAmount: amountToPay,
                status:
                  paymentMethods.data.length === 0
                    ? InvoiceStatus.FAILED
                    : childTransaction.status ==
                        PaymentTransactionStatus.SCHEDULED
                      ? InvoiceStatus.SCHEDULED
                      : InvoiceStatus.PENDING,
                paymentProvider: PaymentProvider.STRIPE,
                paymentMethod: paymentMethod,
                line_items: originalLineItems.map((item) => ({
                  name: item.price_data.product_data.name,
                  amount: item.price_data.unit_amount / 100,
                  quantity: item.quantity,
                  type: item.price_data.product_data.description,
                  total: (item.price_data.unit_amount * item.quantity) / 100,
                })),
                type: InvoiceType.ONE_TIME,
                dueDate: dayjs(entity.session.billingDate).toDate(),
                appliedCouponId: couponId
                  ? this.createObjectIdSafe(
                      typeof couponId === 'string'
                        ? couponId
                        : couponId._id?.toString(),
                      'couponId',
                    )
                  : undefined,
                finalAmount:
                  amountToPay - (discountSplit[student._id.toString()] || 0),
                startDate: null,
                endDate: null,
                metadata: {
                  attemptCount: 0,
                  appliedDiscount: discountSplit[student._id.toString()] || 0,
                  applyDiscountTo: effectiveApplyDiscountTo,
                  paymentIntentId: paymentIntentSent ? paymentIntent.id : null,
                  discountCategory: discountCategory || 'discount',
                },
                transactionCodeId: entity.transactionCodeId,
              });
            }
          }
        }

        if (amountToPay <= 0) {
          this.logger.log('Creating free/zero amount subscription');
          //creating subscription for free enrollment or zero amount enrollment
          const subscription = await this.subscriptionService.create({
            studioId: this.createObjectIdSafe(
              studio._id.toString(),
              'studio._id',
            ),
            parentId: this.createObjectIdSafe(
              parent._id.toString(),
              'parent._id',
            ),
            studentId: this.createObjectIdSafe(
              student._id.toString(),
              'student._id',
            ),
            entityId: this.createObjectIdSafe(
              entity._id.toString(),
              'entity._id',
            ),
            entityType: entityType,
            startDate: subStartDate,
            endDate: new Date(entity.endDate),
            billingCycle: billingCycle,
            baseAmount: entity.tuitionFee,
            finalAmount: amountToPay,
            status: SubscriptionStatus.ACTIVE,
            nextPaymentDate: null,
            appliedCouponId: couponId
              ? this.createObjectIdSafe(
                  typeof couponId === 'string'
                    ? couponId
                    : couponId._id?.toString(),
                  'couponId',
                )
              : undefined,
            metadata: {
              appliedDiscount: totalDiscount || 0,
              paymentMethod: paymentMethod,
            },
          });

          subscriptionId = subscription._id.toString();
          this.logger.log(
            `Free subscription created with ID: ${subscriptionId}`,
          );
        }

        this.logger.log('Step 12: Adding enrollment to student record');
        this.logger.log(
          `Entity type: ${entityType}, Subscription ID: ${subscriptionId}`,
        );

        if (entityType === PaymentTransactionEntityType.CLASS) {
          this.logger.log('Adding class enrollment to student');
          student.enrollments.push({
            enrollmentId: this.createObjectIdSafe(
              entity._id.toString(),
              'entity._id',
            ),
            subscriptionId: subscriptionId,
            enrolledDate: new Date(),
            subscriptionStatus: isScheduled
              ? SubscriptionStatus.SCHEDULED
              : SubscriptionStatus.ACTIVE,
          });
          this.logger.log(`Class enrollment added to student: ${entity._id}`);
        } else {
          this.logger.log('Adding event enrollment to student');
          student.events.push({
            eventId: this.createObjectIdSafe(
              entity._id.toString(),
              'entity._id',
            ),
            eventDate: new Date(),
            subscriptionId: subscriptionId,
            subscriptionStatus: isScheduled
              ? SubscriptionStatus.SCHEDULED
              : SubscriptionStatus.ACTIVE,
          });
          this.logger.log(`Event enrollment added to student: ${entity._id}`);
        }

        const triggerDataClassEnrollement = {
          triggerKey: process.env.CLASS_ENROL_TRIGGER_KEY,
          data: {
            studentFirstName: student.firstName,
            studentLastName: student.lastName,
            parentFirstName: parent.firstName,
            parentLastName: parent.lastName,
            parentEmail: parent.email,
            parentPhone: parent.primaryPhone,
            classEnrolled: entity.title,
            classStartDate: entity.startDate,
            classEndDate: entity.endDate,
            classPrice: entity.tuitionFee,
          },
          locationId: studio.locationId,
        };

        try {
          this.logger.log('Sending GHL trigger');
          await this.triggersService.sendToGhl(triggerDataClassEnrollement);
          this.logger.log('GHL trigger sent successfully');
        } catch (error) {
          this.logger.error(
            `Error sending trigger to GHL for class enrollement ${entity._id}: ${error.message}`,
            error.stack,
          );
        }

        this.logger.log('Step 14: Saving student record to database');
        this.logger.log(
          `Student data before save: enrollments count = ${student.enrollments.length}, events count = ${student.events.length}`,
        );

        try {
          const updatedStudent = await student.save();
          this.logger.log(
            `Successfully saved student enrollment for ${student.firstName} ${student.lastName} (ID: ${updatedStudent._id})`,
          );
        } catch (error) {
          this.logger.error(
            `CRITICAL ERROR - Failed to save student for transaction: ${error.message}`,
            error.stack,
          );
          throw new BadRequestException(
            `Failed to save student enrollment: ${error.message}`,
          );
        }

        // ghl tags update  add new tags
        const tagsArray = entity.tags.map((tag) => tag.fieldName);
        for (const tag of tagsArray) {
          try {
            const tagAdded =
              await this.gohighlevelService.addTagToContactByEmail(
                studio._id.toString(),
                parent.email,
                tag,
              );
            if (!tagAdded) {
              this.logger.warn(
                `Failed to add tag "${tag}" - contact not found for email: ${parent.email}`,
              );
            }
          } catch (error) {
            this.logger.error(
              `Error adding tag "${tag}" to contact: ${error.message}`,
            );
          }
        }

        try {
          if (
            dayjs().isSame(dayjs(entity.session.billingDate), 'day') ||
            dayjs().isAfter(dayjs(entity.session.billingDate), 'day')
          ) {
            await this.classhistoryService.upsertClassHistoryStudentRecord({
              enrollmentId: entity._id,
              students: [
                {
                  status: StudentSubscriptionStatus.SCHEDULED,
                  studentId: student._id as string,
                },
              ],
              studioId: studio._id.toString(),
            });
          } else {
            await this.classhistoryService.upsertClassHistoryStudentRecord({
              enrollmentId: entity._id,
              students: [
                {
                  status: StudentSubscriptionStatus.ACTIVE,
                  studentId: student._id as string,
                },
              ],
              studioId: studio._id.toString(),
            });
          }
        } catch (error) {
          this.logger.error(
            `Error upserting class history for transaction ${error}`,
          );
        }

        let studioLogoUrl = null;
        try {
          studioLogoUrl = await this.gcpStorageService.getPublicImage(
            studio._id.toString(),
            'studio-logo',
          );
        } catch (error) {
          this.logger.error(
            `Error getting studio logo for transaction ${error}`,
          );
        }

        if (
          !isScheduled &&
          amountToPay > 0 &&
          childTransaction &&
          childTransaction.metadata.line_items &&
          childTransaction.metadata.line_items.length > 0
        ) {
          const transactions = childTransaction.metadata.line_items.map(
            (item) => ({
              type: item.price_data.product_data.description,
              name: item.price_data.product_data.name,
              product: item.price_data.product_data.description,
              amount: (item.price_data.unit_amount * item.quantity) / 100,
              status: 'paid',
              transactionId: childTransaction.metadata.paymentIntentId,
            }),
          );

          await sendTransactionEmail(
            parent.email,
            transactions,
            childTransaction.metadata.line_items[0].price_data.currency.toUpperCase(),
            `${parent.firstName} ${parent.lastName}`,
            studio.subaccountName,
            studioLogoUrl,
          );
        }

        await sendPoliciesEmail(
          parent.email,
          `${parent.firstName} ${parent.lastName}`,
          studioLogoUrl ? null : studio.subaccountName,
          studioLogoUrl ? studioLogoUrl : null,
          [
            {
              studentName: `${student.firstName} ${student.lastName}`,
              classes: [
                {
                  name: entity.title,
                  startDate: new Date(entity.startDate).toLocaleDateString(),
                  endDate: new Date(entity.endDate).toLocaleDateString(),
                  days: entity.days.join(', '),
                  time: `${formatTime(entity.startTime)}-${formatTime(entity.endTime)}`,
                  location: entity.location?.fieldName || '',
                },
              ],
            },
          ],
          entity.policyGroup.length > 0
            ? Array.from(entity.policyGroup).map((policy: Policy) => ({
                name: policy.name.trim(),
                description: policy.description.trim(),
                applicableClasses: entity.title,
              }))
            : [],
        );

        this.logger.log(
          `=== Successfully completed processing for all ${studentIds.length} students ===`,
        );
      }
      return {
        status: HttpStatus.OK,
        message: 'Payment processed successfully',
      };
    } catch (error) {
      this.logger.error('=== CRITICAL ERROR in addStudentToEntity ===');
      this.logger.error(`Error message: ${error.message}`);
      this.logger.error(`Error stack: ${error.stack}`);
      this.logger.error(`Error type: ${error.constructor.name}`);

      // If it's already a BadRequestException, re-throw it
      if (error instanceof BadRequestException) {
        this.logger.error('Re-throwing BadRequestException');
        throw error;
      }

      this.logger.error('Handling payment method error');
      return this.stripeCommonService.handlePaymentMethodError(error);
    }
  }
}
