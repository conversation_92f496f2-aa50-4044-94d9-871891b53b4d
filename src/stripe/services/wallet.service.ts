import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { Model, Types } from 'mongoose';

import { ParentsService } from 'src/parents/parents.service';

import { CurrencyService } from 'src/currency/currency.service';

import { StripeCommonService } from './stripe-common.service';

import { SubscriptionInvoiceService } from 'src/subscription-invoice/subscription-invoice.service';
import { PaymentMethod } from 'src/stripe/type';
import { InvoiceStatus, PaymentProvider } from 'src/stripe/type';
import { StudentsService } from 'src/students/students.service';
import { StudiosService } from 'src/studios/studios.service';
import {
  PaymentTransactionSource,
  PaymentTransactionStatus,
  PaymentTransactionType,
} from '../type';
import { PaymentTransactionService } from 'src/payment-transaction/payment-transaction.service';
import { InjectModel } from '@nestjs/mongoose';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import { ReasonType } from 'src/database/schema/walletTransaction';
import { getGcpIpAddress } from 'src/utils/helperFunction';
import Stripe from 'stripe';

@Injectable()
export class StripeWalletService {
  private readonly logger = new Logger(StripeWalletService.name);
  constructor(
    @InjectModel(SubscriptionInvoice.name)
    private readonly subscriptionInvoiceModel: Model<SubscriptionInvoice>,

    @Inject(forwardRef(() => ParentsService))
    private readonly parentsService: ParentsService,

    @Inject(forwardRef(() => CurrencyService))
    private readonly currencyService: CurrencyService,

    @Inject(forwardRef(() => StripeCommonService))
    private readonly stripeCommonService: StripeCommonService,

    @Inject(forwardRef(() => SubscriptionInvoiceService))
    private readonly subscriptionInvoiceService: SubscriptionInvoiceService,

    @Inject(forwardRef(() => StudentsService))
    private readonly studentsService: StudentsService,

    @Inject(forwardRef(() => StudiosService))
    private readonly studiosService: StudiosService,

    @Inject(forwardRef(() => PaymentTransactionService))
    private readonly paymentTransactionService: PaymentTransactionService,
  ) {}

  /**
   * Captures a manual payment for an invoice
   *
   * This method handles the complete payment flow including:
   * - Retrieving invoice, parent, and studio information
   * - Creating a Stripe Payment Intent
   * - Processing payments through Stripe Payment Intent
   *
   * @param body - Invoice data containing invoiceId, studioId
   * @returns Object with status information and payment details
   * @throws HttpException if payment processing fails or payment method is missing
   */

  async createWalletLoad(body: {
    studioId: string;
    parentId: string;
    amount: number;
    paymentMethod: PaymentMethod;
  }) {
    const { studioId, parentId, amount, paymentMethod } = body;
    try {
      const { stripe } =
        await this.stripeCommonService.initializeStripe(studioId);
      const currency = await this.currencyService.findByStudioId(
        Types.ObjectId.createFromHexString(studioId),
      );

      const parent = await this.parentsService.findOne(parentId);

      const paymentMethods = await stripe.paymentMethods.list({
        customer: parent.stripeCustomerId,
        type: paymentMethod as Stripe.PaymentMethodListParams.Type,
      });

      let paymentMethodId;
      if (paymentMethods.data.length > 0) {
        paymentMethodId = paymentMethods.data[0].id;
        // Set the first payment method as the default
        await stripe.customers.update(parent.stripeCustomerId, {
          invoice_settings: {
            default_payment_method: paymentMethodId,
          },
        });
      }

      let paymentIntent;
      try {
        if (paymentMethod === PaymentMethod.US_BANK_ACCOUNT) {
          // First create without confirming
          paymentIntent = await stripe.paymentIntents.create({
            amount: Math.floor(amount * 100),
            currency: currency.name,
            customer: parent.stripeCustomerId,
            payment_method: paymentMethodId,
            off_session: false,
            confirm: false,
            description: `Wallet load for ${parent.firstName || 'Enrollio'} ${parent.lastName || ''}`,
          });

          // Then confirm with mandate data
          const serverIp = await getGcpIpAddress();
          paymentIntent = await stripe.paymentIntents.confirm(
            paymentIntent.id,
            {
              payment_method: paymentMethodId,
              mandate_data: {
                customer_acceptance: {
                  type: 'online',
                  online: {
                    ip_address: serverIp || '127.0.0.1',
                    user_agent: 'Enrollio Server Process',
                  },
                },
              },
            },
          );
        } else if (
          paymentMethod === PaymentMethod.CARD ||
          paymentMethod === PaymentMethod.LINK
        ) {
          const paymentMethodTypes = [paymentMethod];
          if (paymentMethod === PaymentMethod.LINK) {
            paymentMethodTypes.push(PaymentMethod.CARD);
          }
          paymentIntent = await stripe.paymentIntents.create({
            amount: Math.floor(amount * 100),
            currency: currency.name,
            customer: parent.stripeCustomerId,
            payment_method: paymentMethodId,
            off_session: true,
            confirm: true,
            payment_method_types: paymentMethodTypes,
            description: `Wallet load for ${parent.firstName || 'Enrollio'} ${parent.lastName || ''}`,
          });
        }

        const transactionObj = {
          studioId: Types.ObjectId.createFromHexString(studioId),
          parentId: Types.ObjectId.createFromHexString(parentId),
          studentId: null,
          groupId: null,
          paymentSource: PaymentTransactionSource.WALLET_LOAD,
          type: PaymentTransactionType.WALLET_LOAD,
          amount: amount,
          status: PaymentTransactionStatus.PENDING,
          paymentProvider: PaymentProvider.STRIPE,
          paymentMethod: paymentMethod,
          metadata: {
            paymentIntentId: paymentIntent.id,
          },
        };
        const transaction =
          await this.paymentTransactionService.create(transactionObj);

        return {
          status: 'success',
          message: 'Wallet load created successfully',
          data: transaction,
        };
      } catch (error) {
        this.logger.error(error);
        throw new Error('Failed to create wallet load');
      }
    } catch (error) {
      this.logger.error(error);
      throw new Error('Failed to initialize Stripe');
    }
  }

  async processStudentInvoicesWithWallet(body: {
    parentId: string;
    studentId: string;
    studioId: string;
  }) {
    const { parentId, studentId, studioId } = body;

    const parent = await this.parentsService.findOne(parentId);

    // Check if student is in exclusion list
    if (parent.walletStudentExclusions?.includes(studentId)) {
      return {
        status: 'fail',
        message: 'Student is excluded from using wallet balance',
      };
    }

    // Get current wallet balance
    const walletBalance = parent.walletBalance || 0;
    if (walletBalance <= 0) {
      return {
        status: 'fail',
        message: 'Insufficient wallet balance',
      };
    }

    const invoices = await this.subscriptionInvoiceModel
      .find({
        studioId: Types.ObjectId.createFromHexString(studioId),
        parentId: Types.ObjectId.createFromHexString(parentId),
        studentId: Types.ObjectId.createFromHexString(studentId),
        status: { $in: [InvoiceStatus.PARTIALLY_PAID, InvoiceStatus.FAILED] },
      })
      .sort({ dueDate: 1 }); // Process oldest invoices first

    let remainingBalance = walletBalance;
    const processedInvoices = [];

    // Process each invoice while we have balance
    for (const invoice of invoices) {
      const amountDue =
        invoice.finalAmount -
        (invoice.payments?.reduce((acc, payment) => acc + payment.amount, 0) ||
          0);

      if (remainingBalance >= amountDue) {
        // Can pay full invoice
        const payment = {
          method: PaymentMethod.WALLET,
          amount: amountDue,
          date: new Date(),
        };

        invoice.payments = [...(invoice.payments || []), payment];
        invoice.status = InvoiceStatus.PAID;
        await invoice.save();

        remainingBalance -= amountDue;
        processedInvoices.push({
          invoiceId: invoice._id,
          amountPaid: amountDue,
        });
        await this.parentsService.removeWalletBalance({
          parentId,
          amount: amountDue,
          reason: ReasonType.INVOICE_PAYMENT,
          paymentMethod: PaymentMethod.WALLET,
          studioId,
          studentId,
        });
      } else if (remainingBalance > 0) {
        // Can only partially pay
        const amountToPay = remainingBalance;

        const payment = {
          method: PaymentMethod.WALLET,
          amount: amountToPay,
          date: new Date(),
        };

        invoice.payments = [...(invoice.payments || []), payment];
        invoice.status = InvoiceStatus.PARTIALLY_PAID;
        await invoice.save();

        processedInvoices.push({
          invoiceId: invoice._id,
          amountPaid: amountToPay,
        });

        remainingBalance = 0;

        await this.parentsService.removeWalletBalance({
          parentId,
          amount: amountToPay,
          reason: ReasonType.INVOICE_PAYMENT,
          paymentMethod: PaymentMethod.WALLET,
          studioId,
          studentId,
        });
        break;
      }
    }

    await this.parentsService.updateByProperty('_id', parentId, {
      walletBalance: remainingBalance,
    });

    return {
      status: 'success',
      message: 'Invoices processed successfully',
      data: {
        processedInvoices,
        remainingBalance,
      },
    };
  }

  /**
   * Calculate how much wallet balance can be applied to an invoice without actually deducting
   * Used by cron jobs to determine payment intent amounts
   */
  async calculateWalletUsageForInvoice(body: {
    parentId: string;
    invoiceAmount: number;
    studioId: string;
    studentId?: string;
  }): Promise<{
    walletAmountUsed: number;
    paymentIntentAmount: number;
    canPayFully: boolean;
    remainingWalletBalance: number;
  }> {
    const { parentId, invoiceAmount, studioId, studentId } = body;

    const parent = await this.parentsService.findOne(parentId);

    // Check if student is in exclusion list (if studentId provided)
    if (studentId && parent.walletStudentExclusions?.includes(studentId)) {
      return {
        walletAmountUsed: 0,
        paymentIntentAmount: invoiceAmount,
        canPayFully: false,
        remainingWalletBalance: parent.walletBalance || 0,
      };
    }

    // Get current wallet balance
    const walletBalance = parent.walletBalance || 0;

    if (walletBalance <= 0) {
      return {
        walletAmountUsed: 0,
        paymentIntentAmount: invoiceAmount,
        canPayFully: false,
        remainingWalletBalance: 0,
      };
    }

    const walletAmountUsed = Math.min(walletBalance, invoiceAmount);
    const paymentIntentAmount = invoiceAmount - walletAmountUsed;
    const canPayFully = walletAmountUsed >= invoiceAmount;
    const remainingWalletBalance = walletBalance - walletAmountUsed;

    return {
      walletAmountUsed,
      paymentIntentAmount,
      canPayFully,
      remainingWalletBalance,
    };
  }

  /**
   * Calculate wallet usage for a transaction amount
   * Used by scheduled payments cron
   */
  async calculateWalletUsageForTransaction(body: {
    parentId: string;
    transactionAmount: number;
    studioId: string;
    studentId?: string;
  }): Promise<{
    walletAmountUsed: number;
    paymentIntentAmount: number;
    canPayFully: boolean;
    remainingWalletBalance: number;
  }> {
    // Use the same logic as invoice calculation
    return await this.calculateWalletUsageForInvoice({
      parentId: body.parentId,
      invoiceAmount: body.transactionAmount,
      studioId: body.studioId,
      studentId: body.studentId,
    });
  }
}
