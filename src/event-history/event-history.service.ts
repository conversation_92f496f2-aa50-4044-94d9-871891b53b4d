import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CreateEventHistoryDto } from './dto/create-event-history.dto';
import { UpdateEventHistoryDto } from './dto/update-event-history.dto';
import { EventHistory } from 'src/database/schema/eventHistory';

@Injectable()
export class EventHistoryService {
  constructor(
    @InjectModel(EventHistory.name)
    private eventHistoryModel: Model<EventHistory>,
  ) {}

  async create(
    createEventHistoryDto: CreateEventHistoryDto,
    studioIdString: string,
  ) {
    try {
      if (!studioIdString) {
        throw new Error('Studio ID is required');
      }

      const studioId = Types.ObjectId.createFromHexString(studioIdString);
      let studentId;

      if (createEventHistoryDto.studentId) {
        try {
          studentId = Types.ObjectId.createFromHexString(
            createEventHistoryDto.studentId,
          );
        } catch (error) {
          throw new Error('Invalid student ID format');
        }
      }

      const convertedData = {
        ...(studentId && { studentId }),
        studioId,
        ...(createEventHistoryDto.events && {
          events: createEventHistoryDto.events,
        }),
      };

      const newEventHistory = new this.eventHistoryModel(convertedData);
      const savedHistory = await newEventHistory.save();

      if (!savedHistory) {
        throw new Error('Failed to create event history');
      }
      return savedHistory;
    } catch (error) {
      if (error.message.includes('hex string')) {
        throw new Error('Invalid ID format provided');
      }
      throw error;
    }
  }

  async findAll(
    locationId: string,
    studentId: string,
    page: number = 1,
    limit: number = 10,
  ) {
    const skip = (page - 1) * limit;

    const [items, total] = await Promise.all([
      this.eventHistoryModel
        .find({
          studioId: Types.ObjectId.createFromHexString(locationId),
          studentId: Types.ObjectId.createFromHexString(studentId),
        })
        .skip(skip)
        .limit(limit)
        .exec(),

      this.eventHistoryModel.countDocuments({
        studioId: Types.ObjectId.createFromHexString(locationId),
        studentId: Types.ObjectId.createFromHexString(studentId),
      }),
    ]);

    return {
      items,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string, locationId: string) {
    return await this.eventHistoryModel
      .findOne({
        _id: Types.ObjectId.createFromHexString(id),
        studioId: Types.ObjectId.createFromHexString(locationId),
      })
      .exec();
  }

  async update(
    id: string,
    updateEventHistoryDto: UpdateEventHistoryDto,
    locationId: string,
  ) {
    const updateQuery: any = {
      $push: {
        events: {
          $each: updateEventHistoryDto.events || [],
        },
      },
    };

    if (updateEventHistoryDto.studentId) {
      updateQuery.studentId = Types.ObjectId.createFromHexString(
        updateEventHistoryDto.studentId,
      );
    }

    return await this.eventHistoryModel
      .findOneAndUpdate(
        {
          _id: Types.ObjectId.createFromHexString(id),
          studioId: Types.ObjectId.createFromHexString(locationId),
        },
        updateQuery,
        { new: true },
      )
      .exec();
  }

  async remove(id: string, locationId: string) {
    return await this.eventHistoryModel
      .findOneAndDelete({
        _id: Types.ObjectId.createFromHexString(id),
        studioId: Types.ObjectId.createFromHexString(locationId),
      })
      .exec();
  }
}
