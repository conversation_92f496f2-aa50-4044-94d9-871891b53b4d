import { Module } from '@nestjs/common';
import { EventHistoryService } from './event-history.service';
import { EventHistoryController } from './event-history.controller';
import { MongooseModule } from '@nestjs/mongoose';
import {
  EventHistory,
  EventHistorySchema,
} from 'src/database/schema/eventHistory';
import { JwtModule } from '@nestjs/jwt';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: EventHistory.name, schema: EventHistorySchema },
    ]),
    JwtModule,
  ],
  controllers: [EventHistoryController],
  providers: [EventHistoryService],
  exports: [EventHistoryService],
})
export class EventHistoryModule {}
