import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { Request } from 'express';
import { EventHistoryService } from './event-history.service';
import { CreateEventHistoryDto } from './dto/create-event-history.dto';
import { UpdateEventHistoryDto } from './dto/update-event-history.dto';

@Controller('event-history')
@UseGuards(JwtAuthGuard)
export class EventHistoryController {
  constructor(private readonly eventHistoryService: EventHistoryService) {}

  @Post()
  create(
    @Body() createEventHistoryDto: CreateEventHistoryDto,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return this.eventHistoryService.create(createEventHistoryDto, locationId);
  }

  @Get('student/:studentId')
  findAll(
    @Req() request: Request,
    @Param('studentId') studentId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    const locationId = request['locationId'];
    return this.eventHistoryService.findAll(locationId, studentId, page, limit);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @Req() request: Request) {
    const locationId = request['locationId'];
    return this.eventHistoryService.findOne(id, locationId);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateEventHistoryDto: UpdateEventHistoryDto,
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return this.eventHistoryService.update(
      id,
      updateEventHistoryDto,
      locationId,
    );
  }

  @Delete(':id')
  remove(@Param('id') id: string, @Req() request: Request) {
    const locationId = request['locationId'];
    return this.eventHistoryService.remove(id, locationId);
  }
}
