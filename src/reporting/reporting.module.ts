import { Module } from '@nestjs/common';
import { ReportingService } from './reporting.service';
import { ReportingController } from './reporting.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Parent, ParentSchema } from 'src/database/schema/parent';
import {
  SubscriptionInvoice,
  SubscriptionInvoiceSchema,
} from 'src/database/schema/subscriptionInvoice';
import { AuthModule } from 'src/auth/auth.module';
import { Studio, StudioSchema } from 'src/database/schema/studio';
import {
  Subscription,
  SubscriptionSchema,
} from 'src/database/schema/subscription';
import { Student, StudentSchema } from 'src/database/schema/student';
import { Enrollment, EnrollmentSchema } from 'src/database/schema/enrollment';
import { Session, SessionSchema } from 'src/database/schema/session';
import { StudentsModule } from 'src/students/students.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Parent.name, schema: ParentSchema },
      { name: SubscriptionInvoice.name, schema: SubscriptionInvoiceSchema },
      { name: Studio.name, schema: StudioSchema },
      { name: Subscription.name, schema: SubscriptionSchema },
      { name: Student.name, schema: StudentSchema },
      { name: Enrollment.name, schema: EnrollmentSchema },
      { name: Session.name, schema: SessionSchema },
    ]),
    AuthModule,
    StudentsModule,
  ],
  controllers: [ReportingController],
  providers: [ReportingService],
})
export class ReportingModule {}
