import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Parent } from 'src/database/schema/parent';
import { Studio } from 'src/database/schema/studio';
import { Subscription } from 'src/database/schema/subscription';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import { Student } from 'src/database/schema/student';
import { Enrollment } from 'src/database/schema/enrollment';
import { InvoiceStatus } from 'src/stripe/type';
import { Session } from 'src/database/schema/session';
import { SubscriptionStatus } from 'src/database/schema/subscription';
import { PipelineStage } from 'mongoose';
import { StudentsService } from 'src/students/students.service';

@Injectable()
export class ReportingService {
  constructor(
    @InjectModel(Parent.name)
    private readonly parentModel: Model<Parent>,
    @InjectModel(SubscriptionInvoice.name)
    private readonly subscriptionInvoiceModel: Model<SubscriptionInvoice>,
    @InjectModel(Studio.name)
    private readonly studioModel: Model<Studio>,
    @InjectModel(Subscription.name)
    private readonly subscriptionModel: Model<Subscription>,
    @InjectModel(Student.name)
    private readonly studentModel: Model<Student>,
    @InjectModel(Enrollment.name)
    private readonly enrollmentModel: Model<Enrollment>,
    @InjectModel(Session.name)
    private readonly sessionModel: Model<Session>,
    private readonly studentsService: StudentsService,
  ) {}

  async getParentOutstandingBalances({
    studioId,
    parentId,
  }: {
    studioId: string;
    parentId?: string;
  }) {
    const studio = await this.studioModel.findById(studioId).lean().exec();

    if (!studio) {
      throw new NotFoundException('Studio not found');
    }

    const studioIdObjectId = new Types.ObjectId(studioId);

    if (parentId) {
      const parent = await this.parentModel.findById(parentId).lean().exec();
      if (!parent) {
        throw new NotFoundException('Parent not found');
      }
      const parentIdObjectId = new Types.ObjectId(parentId);
      const outstandingBalances = await this.subscriptionInvoiceModel
        .find({
          parentId: parentIdObjectId,
          studioId: studioIdObjectId,
          status: {
            $in: [
              InvoiceStatus.PENDING,
              InvoiceStatus.FAILED,
              InvoiceStatus.SCHEDULED,
            ],
          },
        })
        .lean()
        .exec();

      const totalOutstanding = outstandingBalances.reduce((acc, invoice) => {
        return acc + invoice.finalAmount;
      }, 0);

      const totalOutstandingFormatted = totalOutstanding.toFixed(2);

      return {
        studioName: studio.subaccountName,
        parentFirstName: parent.firstName,
        parentLastName: parent.lastName,
        parentEmail: parent.email,
        totalOutstanding: totalOutstandingFormatted,
        outstandingBalances,
        isSingleParent: true,
      };
    } else {
      const parents = await this.parentModel
        .find({ studioId: studioIdObjectId })
        .lean()
        .exec();
      if (!parents || parents.length === 0) {
        return {
          studioName: studio.subaccountName,
          parentsOutstandingBalances: [],
          isSingleParent: false,
        };
      }

      const allParentsBalances = await Promise.all(
        parents.map(async (parent) => {
          const parentIdObjectId = new Types.ObjectId(parent._id);
          const outstandingBalances = await this.subscriptionInvoiceModel
            .find({
              parentId: parentIdObjectId,
              studioId: studioIdObjectId,
              status: {
                $in: [
                  InvoiceStatus.PENDING,
                  InvoiceStatus.FAILED,
                  InvoiceStatus.SCHEDULED,
                ],
              },
            })
            .lean()
            .exec();

          const totalOutstanding = outstandingBalances.reduce(
            (acc, invoice) => {
              return acc + invoice.finalAmount;
            },
            0,
          );

          return {
            parentId: parent._id.toString(),
            parentFirstName: parent.firstName,
            parentLastName: parent.lastName,
            parentEmail: parent.email,
            totalOutstanding: totalOutstanding.toFixed(2),
            outstandingBalancesCount: outstandingBalances.length,
          };
        }),
      );

      const parentsWithOutstandingBalances = allParentsBalances.filter(
        (p) => parseFloat(p.totalOutstanding) > 0,
      );

      return {
        studioName: studio.subaccountName,
        parentsOutstandingBalances: parentsWithOutstandingBalances,
        isSingleParent: false,
      };
    }
  }

  async getPaymentByClass({
    studioId,
    classId,
  }: {
    studioId: string;
    classId: string;
  }) {
    const studio = await this.studioModel.findById(studioId).lean().exec();

    if (!studio) {
      throw new NotFoundException('Studio not found');
    }

    const entityId = new Types.ObjectId(classId);
    const subscription = await this.subscriptionModel
      .findOne({ entityId })
      .lean()
      .exec();

    const enrollment = await this.enrollmentModel
      .findById(entityId)
      .lean()
      .exec();

    const className = enrollment.title;

    if (!subscription) {
      throw new NotFoundException('Subscription not found');
    }

    const subscriptionObjectId = new Types.ObjectId(
      subscription._id.toString(),
    );

    //use the subsciptionId to get all the subscription invoices, status is paid
    const subscriptionInvoices = await this.subscriptionInvoiceModel
      .find({
        subscriptionId: subscriptionObjectId,
        status: {
          $in: [InvoiceStatus.PAID, InvoiceStatus.PARTIALLY_PAID],
        },
      })
      .lean()
      .exec();

    if (!subscriptionInvoices) {
      throw new NotFoundException('Subscription invoices not found');
    }

    // Fetch student details for each invoice
    const populatedInvoices = await Promise.all(
      subscriptionInvoices.map(async (invoice) => {
        const student = await this.studentModel
          .findById(invoice.studentId)
          .lean()
          .exec();
        return {
          ...invoice,
          studentFirstName: student ? student.firstName : 'N/A',
          studentLastName: student ? student.lastName : 'N/A',
        };
      }),
    );

    //get the total amount paid
    const totalAmountPaid = populatedInvoices.reduce((acc, invoice) => {
      return acc + invoice.finalAmount;
    }, 0);

    return {
      studioName: studio.subaccountName,
      className,
      totalAmountPaid,
      subscriptionInvoices: populatedInvoices,
    };
  }

  async getPaymentsBySession({
    studioId,
    sessionId,
  }: {
    studioId: string;
    sessionId: string;
  }) {
    const studio = await this.studioModel.findById(studioId).lean().exec();

    if (!studio) {
      throw new NotFoundException('Studio not found');
    }

    const sessionObjectId = new Types.ObjectId(sessionId);

    //get the session details
    const session = await this.sessionModel
      .findById(sessionObjectId)
      .lean()
      .exec();

    if (!session) {
      throw new NotFoundException('Session not found');
    }

    //get all the enrollments for the session
    const enrollments = await this.enrollmentModel
      .find({ session: sessionObjectId })
      .lean()
      .exec();

    if (!enrollments || enrollments.length === 0) {
      throw new NotFoundException('Enrollments not found for this session');
    }

    //capture all the enrollmentIds for the session
    //these are the classes that are being taught in the session for which i need to get the payments
    const enrollmentIds = enrollments.map(
      (enrollment) => new Types.ObjectId(enrollment._id.toString()),
    );

    const enrollmentMap = new Map(
      enrollments.map((e) => [e._id.toString(), e.title]),
    );

    //now i need to get all the subscriptions for the enrollmentIds
    const subscriptions = await this.subscriptionModel
      .find({ entityId: { $in: enrollmentIds } })
      .lean()
      .exec();

    if (!subscriptions || subscriptions.length === 0) {
      // It's possible a session has enrollments (classes) but no one has subscribed yet.
      // Return with 0 payments rather than an error.
      return {
        studioName: studio.subaccountName,
        sessionName: session.name,
        totalAmountPaid: 0,
        paymentsByClass: [],
        subscriptionInvoices: [], // keep this for consistency if JSON response is used.
      };
    }

    const subscriptionIdToEnrollmentIdMap = new Map(
      subscriptions.map((sub) => [sub._id.toString(), sub.entityId.toString()]),
    );

    //get all the subscription invoices for the enrollmentIds, status is paid
    const subscriptionInvoices = await this.subscriptionInvoiceModel
      .find({
        subscriptionId: {
          $in: subscriptions.map(
            (subscription) => new Types.ObjectId(subscription._id.toString()),
          ),
        },
        status: {
          $in: [InvoiceStatus.PAID, InvoiceStatus.PARTIALLY_PAID],
        },
      })
      .lean()
      .exec();

    // if (!subscriptionInvoices) { // An empty array is a valid result if no paid invoices
    //   throw new NotFoundException('Subscription invoices not found');
    // }

    //get the total amount paid
    const totalAmountPaid = subscriptionInvoices.reduce((acc, invoice) => {
      return acc + invoice.finalAmount;
    }, 0);

    const paymentsByClass = {};
    for (const invoice of subscriptionInvoices) {
      const subscriptionId = invoice.subscriptionId.toString();
      const enrollmentId = subscriptionIdToEnrollmentIdMap.get(subscriptionId);
      if (enrollmentId) {
        const className = enrollmentMap.get(enrollmentId) || 'Unknown Class';
        paymentsByClass[className] =
          (paymentsByClass[className] || 0) + invoice.finalAmount;
      }
    }

    const paymentsByClassArray = Object.entries(paymentsByClass).map(
      ([className, amount]) => ({
        className,
        amount: parseFloat((amount as number).toFixed(2)),
      }),
    );

    return {
      studioName: studio.subaccountName,
      sessionName: session.name, // Added session name
      totalAmountPaid: parseFloat(totalAmountPaid.toFixed(2)),
      paymentsByClass: paymentsByClassArray, // Added payments by class
      subscriptionInvoices, // Still returning this for potential JSON use, though CSV focuses on summary
    };
  }

  async getRevenueOverTimePeriod({
    studioId,
    startDate,
    endDate,
  }: {
    studioId: string;
    startDate: string;
    endDate: string;
  }) {
    const studio = await this.studioModel.findById(studioId).lean().exec();

    if (!studio) {
      throw new NotFoundException('Studio not found');
    }

    //convert studioId to objectId
    const studioObjectId = new Types.ObjectId(studioId);

    //get all the subscription invoices for the studio, status is paid, createdAt is between startDate and endDate
    const subscriptionInvoices = await this.subscriptionInvoiceModel
      .find({
        studioId: studioObjectId,
        status: { $in: [InvoiceStatus.PAID, InvoiceStatus.PARTIALLY_PAID] },
        createdAt: { $gte: new Date(startDate), $lte: new Date(endDate) },
      })
      .lean()
      .exec();

    // if (!subscriptionInvoices) { // An empty array is a valid result if no paid invoices
    //   throw new NotFoundException('Subscription invoices not found');
    // }

    //get the total amount paid
    let totalAmountPaid = 0;
    const paymentsByProduct = {};

    subscriptionInvoices.forEach((invoice) => {
      totalAmountPaid += invoice.finalAmount;
      if (invoice.line_items && Array.isArray(invoice.line_items)) {
        invoice.line_items.forEach((item) => {
          const productName = item.name || 'Unnamed Product';
          paymentsByProduct[productName] =
            (paymentsByProduct[productName] || 0) + (Number(item.total) || 0);
        });
      }
    });

    const paymentsByProductArray = Object.entries(paymentsByProduct).map(
      ([productName, amount]) => ({
        productName,
        amount: parseFloat((amount as number).toFixed(2)),
      }),
    );

    return {
      studioName: studio.subaccountName,
      totalAmountPaid: parseFloat(totalAmountPaid.toFixed(2)),
      paymentsByProduct: paymentsByProductArray,
      subscriptionInvoices, // Keep for JSON response if needed, though CSV focuses on summary
    };
  }

  async getTopSpendingParents({ studioId }: { studioId: string }) {
    const studioObjectId = new Types.ObjectId(studioId);

    const topSpendingParentsData =
      await this.subscriptionInvoiceModel.aggregate([
        {
          $match: {
            studioId: studioObjectId,
            status: {
              $in: [InvoiceStatus.PAID, InvoiceStatus.PARTIALLY_PAID],
            },
          },
        },
        {
          $group: {
            _id: '$parentId',
            totalSpent: { $sum: '$finalAmount' },
          },
        },
        {
          $lookup: {
            from: 'parents', // Collection name for the Parent model
            localField: '_id',
            foreignField: '_id',
            as: 'parentInfo',
          },
        },
        {
          $unwind: '$parentInfo',
        },
        {
          $project: {
            _id: 0,
            parentId: '$_id',
            parentFirstName: '$parentInfo.firstName',
            parentLastName: '$parentInfo.lastName',
            parentEmail: '$parentInfo.email',
            totalSpent: 1,
          },
        },
        {
          $sort: { totalSpent: -1 },
        },
      ]);

    return topSpendingParentsData.map((parentData) => ({
      ...parentData,
      // Ensure totalSpent is a number with two decimal places
      totalSpent: parseFloat(Number(parentData.totalSpent).toFixed(2)),
    }));
  }

  async getScheduledUpcomingPayments({
    studioId,
    startDate,
    endDate,
  }: {
    studioId: string;
    startDate: string;
    endDate: string;
  }) {
    const studioObjectId = new Types.ObjectId(studioId);
    const studio = await this.studioModel
      .findById(studioObjectId)
      .lean()
      .exec();

    if (!studio) {
      throw new NotFoundException('Studio not found');
    }

    const invoices = await this.subscriptionInvoiceModel
      .find({
        studioId: studioObjectId,
        status: { $in: [InvoiceStatus.SCHEDULED, InvoiceStatus.UPCOMING] },
        createdAt: { $gte: startDate, $lte: endDate },
      })
      .lean()
      .exec();

    let totalUpcomingAmount = 0;
    const paymentsByProduct = {};

    invoices.forEach((invoice) => {
      totalUpcomingAmount += invoice.finalAmount;
      if (invoice.line_items && Array.isArray(invoice.line_items)) {
        invoice.line_items.forEach((item) => {
          const productName = item.name || 'Unnamed Product';
          paymentsByProduct[productName] =
            (paymentsByProduct[productName] || 0) + (Number(item.total) || 0);
        });
      }
    });

    const paymentsByProductArray = Object.entries(paymentsByProduct).map(
      ([productName, amount]) => ({
        productName,
        amount: parseFloat((amount as number).toFixed(2)),
      }),
    );

    return {
      studioName: studio.subaccountName,
      totalUpcomingAmount: parseFloat(totalUpcomingAmount.toFixed(2)),
      paymentsByProduct: paymentsByProductArray,
      // We no longer return individual invoices for this summarized report
    };
  }

  async getIssuedRefunds({
    studioId,
    startDate,
    endDate,
  }: {
    studioId: string;
    startDate?: string;
    endDate?: string;
  }) {
    const studioObjectId = new Types.ObjectId(studioId);
    const studio = await this.studioModel
      .findById(studioObjectId)
      .lean()
      .exec();

    if (!studio) {
      throw new NotFoundException('Studio not found');
    }

    const matchQuery: any = {
      studioId: studioObjectId,
      status: {
        $in: [InvoiceStatus.FULL_REFUND, InvoiceStatus.PARTIAL_REFUND],
      },
    };

    if (startDate && endDate) {
      matchQuery.updatedAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      };
    }

    const aggregationPipeline: any = [
      {
        $match: matchQuery,
      },
      {
        $lookup: {
          from: 'parents',
          localField: 'parentId',
          foreignField: '_id',
          as: 'parentInfo',
        },
      },
      {
        $unwind: { path: '$parentInfo', preserveNullAndEmptyArrays: true },
      },
      {
        $lookup: {
          from: 'students',
          localField: 'studentId',
          foreignField: '_id',
          as: 'studentInfo',
        },
      },
      {
        $unwind: { path: '$studentInfo', preserveNullAndEmptyArrays: true },
      },
      {
        $group: {
          _id: '$_id',
          doc: { $first: '$$ROOT' },
          parentInfo: { $first: '$parentInfo' },
          studentInfo: { $first: '$studentInfo' },
        },
      },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: [
              '$doc',
              { parentInfo: '$parentInfo' },
              { studentInfo: '$studentInfo' },
            ],
          },
        },
      },
      {
        $group: {
          _id: null,
          totalAmountRefunded: { $sum: '$finalAmount' },
          refundedInvoices: { $push: '$$ROOT' },
        },
      },
      {
        $project: {
          _id: 0,
          totalAmountRefunded: 1,
          refundDetails: {
            $reduce: {
              input: '$refundedInvoices',
              initialValue: [],
              in: {
                $concatArrays: [
                  '$$value',
                  {
                    $map: {
                      input: '$$this.line_items',
                      as: 'item',
                      in: {
                        refundDate: '$$this.updatedAt',
                        parentName: {
                          $concat: [
                            '$$this.parentInfo.firstName',
                            ' ',
                            '$$this.parentInfo.lastName',
                          ],
                        },
                        parentEmail: '$$this.parentInfo.email',
                        studentName: {
                          $concat: [
                            '$$this.studentInfo.firstName',
                            ' ',
                            '$$this.studentInfo.lastName',
                          ],
                        },
                        productName: '$$item.name',
                        amountRefunded: '$$item.total',
                      },
                    },
                  },
                ],
              },
            },
          },
        },
      },
    ];

    const results = await this.subscriptionInvoiceModel
      .aggregate(aggregationPipeline)
      .exec();
    const reportDataFromAggregation = results[0];

    if (!reportDataFromAggregation) {
      return {
        studioName: studio.subaccountName,
        totalRefundedAmount: 0,
        refundDetails: [],
      };
    }

    return {
      studioName: studio.subaccountName,
      totalRefundedAmount: parseFloat(
        Number(reportDataFromAggregation.totalAmountRefunded || 0).toFixed(2),
      ),
      refundDetails: (reportDataFromAggregation.refundDetails || []).map(
        (detail) => ({
          ...detail,
          amountRefunded: parseFloat(
            Number(detail.amountRefunded || 0).toFixed(2),
          ),
        }),
      ),
    };
  }

  async getFailedPayments({
    studioId,
    startDate,
    endDate,
  }: {
    studioId: string;
    startDate?: string;
    endDate?: string;
  }) {
    const studioObjectId = new Types.ObjectId(studioId);
    const studio = await this.studioModel
      .findById(studioObjectId)
      .lean()
      .exec();

    if (!studio) {
      throw new NotFoundException('Studio not found');
    }

    const matchQuery: any = {
      studioId: studioObjectId,
      status: InvoiceStatus.FAILED,
    };

    if (startDate && endDate) {
      // Using createdAt for date range filtering as per user's last update
      matchQuery.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      };
    }

    const aggregationPipeline: any = [
      {
        $match: matchQuery,
      },
      {
        $lookup: {
          from: 'parents',
          localField: 'parentId',
          foreignField: '_id',
          as: 'parentInfo',
        },
      },
      {
        $unwind: { path: '$parentInfo', preserveNullAndEmptyArrays: true },
      },
      {
        $lookup: {
          from: 'students',
          localField: 'studentId',
          foreignField: '_id',
          as: 'studentInfo',
        },
      },
      {
        $unwind: { path: '$studentInfo', preserveNullAndEmptyArrays: true },
      },
      {
        $unwind: '$line_items', // Unwind line_items to process each item
      },
      {
        $project: {
          _id: 0,
          failedDate: '$updatedAt', // Or createdAt if that's more appropriate for "Date" column
          parentName: {
            $concat: ['$parentInfo.firstName', ' ', '$parentInfo.lastName'],
          },
          parentEmail: '$parentInfo.email',
          studentName: {
            $concat: ['$studentInfo.firstName', ' ', '$studentInfo.lastName'],
          },
          productName: '$line_items.name',
          failedAmount: '$finalAmount',
        },
      },
      {
        $group: {
          // Group back to calculate total failed amount and keep distinct line items
          _id: null, // Group all documents together for a grand total
          lineItems: { $push: '$$ROOT' }, // Push all projected line items into an array
          totalFailedAmount: { $sum: '$failedAmount' }, // Sum of all line_item.total
        },
      },
      {
        $project: {
          // Final projection
          _id: 0,
          lineItems: 1,
          totalFailedAmount: 1,
        },
      },
    ];

    const results = await this.subscriptionInvoiceModel
      .aggregate(aggregationPipeline)
      .exec();

    const reportDataFromAggregation = results[0];

    if (!reportDataFromAggregation) {
      return {
        studioName: studio.subaccountName,
        totalFailedAmount: 0,
        failedPaymentDetails: [],
      };
    }

    const actualTotalFailedAmount =
      reportDataFromAggregation.totalFailedAmount || 0;
    const lineItemsFromAggregation = reportDataFromAggregation.lineItems || [];

    return {
      studioName: studio.subaccountName,
      totalFailedAmount: parseFloat(Number(actualTotalFailedAmount).toFixed(2)),
      failedPaymentDetails: lineItemsFromAggregation.map((item) => ({
        ...item,
        // Ensure failedAmount from item is also treated as a number and defaults to 0 if necessary
        failedAmount: parseFloat(Number(item.failedAmount || 0).toFixed(2)),
      })),
    };
  }

  async getDaysPastDueForOverduePayments({
    studioId,
    dueDays,
  }: {
    studioId: string;
    dueDays: string;
  }) {
    const studioObjectId = new Types.ObjectId(studioId);
    const studio = await this.studioModel
      .findById(studioObjectId)
      .lean()
      .exec();
    if (!studio) {
      throw new NotFoundException('Studio not found');
    }

    const numDueDays = parseInt(dueDays, 10);
    if (isNaN(numDueDays) || numDueDays < 0) {
      return {
        studioName: studio.subaccountName,
        inputDueDays: dueDays,
        totalAmountDue: 0,
        overduePaymentDetails: [],
      };
    }

    const currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0); // Normalize current date to start of day for accurate comparison

    const overdueInvoicesRaw = await this.subscriptionInvoiceModel
      .find({
        studioId: studioObjectId,
        status: { $in: [InvoiceStatus.PENDING, InvoiceStatus.FAILED] },
        dueDate: { $exists: true, $ne: null, $lt: currentDate },
      })
      .lean()
      .exec();

    if (!overdueInvoicesRaw || overdueInvoicesRaw.length === 0) {
      return {
        studioName: studio.subaccountName,
        inputDueDays: dueDays,
        totalAmountDue: 0,
        overduePaymentDetails: [],
      };
    }

    let totalAmountDue = 0;
    const overduePaymentDetails = [];

    for (const invoice of overdueInvoicesRaw) {
      const dueDate = new Date(invoice.dueDate);
      dueDate.setHours(0, 0, 0, 0); // Normalize due date

      const timeDiff = currentDate.getTime() - dueDate.getTime();
      const daysPastDue = Math.floor(timeDiff / (1000 * 60 * 60 * 24));

      if (daysPastDue >= numDueDays) {
        const parent = invoice.parentId
          ? await this.parentModel.findById(invoice.parentId).lean().exec()
          : null;
        const student = invoice.studentId
          ? await this.studentModel.findById(invoice.studentId).lean().exec()
          : null;

        const parentName = parent
          ? `${parent.firstName || ''} ${parent.lastName || ''}`.trim()
          : '';
        const parentEmail = parent ? parent.email || '' : '';
        const studentName = student
          ? `${student.firstName || ''} ${student.lastName || ''}`.trim()
          : '';

        if (invoice.line_items && Array.isArray(invoice.line_items)) {
          for (const item of invoice.line_items) {
            const dueAmount = Number(item.total) || 0;
            totalAmountDue += dueAmount;
            overduePaymentDetails.push({
              invoiceDueDate: invoice.dueDate,
              parentName,
              parentEmail,
              studentName,
              productName: item.name || 'N/A',
              dueAmount: parseFloat(dueAmount.toFixed(2)),
              actualDaysPastDue: daysPastDue,
            });
          }
        } else {
          // Handle cases where invoice might not have line_items but still represents a due amount
          // This part depends on how invoices without line_items should be treated for their 'finalAmount'
          const dueAmount = Number(invoice.finalAmount) || 0;
          totalAmountDue += dueAmount;
          overduePaymentDetails.push({
            invoiceDueDate: invoice.dueDate,
            parentName,
            parentEmail,
            studentName,
            productName: 'N/A', // Or a generic description
            dueAmount: parseFloat(dueAmount.toFixed(2)),
            actualDaysPastDue: daysPastDue,
          });
        }
      }
    }

    if (overduePaymentDetails.length === 0) {
      return {
        studioName: studio.subaccountName,
        inputDueDays: dueDays,
        totalAmountDue: 0,
        overduePaymentDetails: [],
      };
    }

    return {
      studioName: studio.subaccountName,
      inputDueDays: dueDays,
      totalAmountDue: parseFloat(totalAmountDue.toFixed(2)),
      overduePaymentDetails,
    };
  }

  async getActiveStudentsAndClassCount({ studioId }: { studioId: string }) {
    // Validate studio
    const studioObjectId = new Types.ObjectId(studioId);
    const studio = await this.studioModel
      .findById(studioObjectId)
      .lean()
      .exec();
    if (!studio) {
      throw new NotFoundException('Studio not found');
    }

    // Fetch students that belong to the studio and have at least one ACTIVE enrollment
    const students = await this.studentModel
      .find({
        studioId: studioObjectId,
        'enrollments.subscriptionStatus': { $in: ['active', 'scheduled'] },
      })
      .lean()
      .exec();

    if (!students || students.length === 0) {
      return {
        studioName: studio.subaccountName,
        totalActiveStudents: 0,
        activeStudentDetails: [],
      };
    }

    // Collect enrollmentIds, parentIds, and sessionIds for batch queries
    const enrollmentIdsSet = new Set<string>();
    const parentIdsSet = new Set<string>();

    students.forEach((student) => {
      if (student.parentId) {
        parentIdsSet.add(student.parentId.toString());
      }
      if (Array.isArray(student.enrollments)) {
        student.enrollments.forEach((enrollmentInfo) => {
          if (
            (enrollmentInfo.subscriptionStatus === 'active' ||
              enrollmentInfo.subscriptionStatus === 'scheduled') &&
            enrollmentInfo.enrollmentId
          ) {
            enrollmentIdsSet.add(enrollmentInfo.enrollmentId.toString());
          }
        });
      }
    });

    // Fetch enrollments titles and session ids
    const enrollmentDocs = await this.enrollmentModel
      .find({
        _id: {
          $in: Array.from(enrollmentIdsSet).map((id) => new Types.ObjectId(id)),
        },
      })
      .select({ _id: 1, title: 1, session: 1 })
      .lean()
      .exec();
    const enrollmentIdToData = new Map<
      string,
      { title: string; sessionId?: string }
    >();
    const sessionIdsSet = new Set<string>();
    enrollmentDocs.forEach((e) => {
      enrollmentIdToData.set(e._id.toString(), {
        title: e.title,
        sessionId: e.session?.toString(),
      });
      if (e.session) sessionIdsSet.add(e.session.toString());
    });

    // Fetch sessions
    const sessionDocs = await this.sessionModel
      .find({
        _id: {
          $in: Array.from(sessionIdsSet).map((id) => new Types.ObjectId(id)),
        },
      })
      .select({ _id: 1, name: 1 })
      .lean()
      .exec();
    const sessionIdToName = new Map<string, string>();
    sessionDocs.forEach((s) => {
      sessionIdToName.set(s._id.toString(), s.name);
    });

    // Fetch parent info
    const parentDocs = await this.parentModel
      .find({
        _id: {
          $in: Array.from(parentIdsSet).map((id) => new Types.ObjectId(id)),
        },
      })
      .select({ _id: 1, firstName: 1, lastName: 1, email: 1, primaryPhone: 1 })
      .lean()
      .exec();
    const parentIdToInfo = new Map<string, any>();
    parentDocs.forEach((p) => {
      parentIdToInfo.set(p._id.toString(), p);
    });

    const activeStudentDetails = [] as any[];

    students.forEach((student) => {
      const parentInfo =
        parentIdToInfo.get(student.parentId?.toString() || '') || {};

      const activeEnrollments = (student.enrollments || []).filter(
        (e) =>
          (e.subscriptionStatus === 'active' ||
            e.subscriptionStatus === 'scheduled') &&
          e.enrollmentId,
      );

      const classesActive = activeEnrollments.length;

      activeEnrollments.forEach((enrollmentInfo) => {
        const enrollmentData = enrollmentIdToData.get(
          enrollmentInfo.enrollmentId.toString(),
        );
        const className = enrollmentData?.title || 'Unknown Class';
        const sessionName = enrollmentData?.sessionId
          ? sessionIdToName.get(enrollmentData.sessionId) || ''
          : '';

        activeStudentDetails.push({
          studentFirstName: student.firstName,
          studentLastName: student.lastName,
          parentFirstName: parentInfo.firstName || '',
          parentLastName: parentInfo.lastName || '',
          parentPhone: parentInfo.primaryPhone || '',
          parentEmail: parentInfo.email || '',
          classesActive,
          className,
          sessionName,
          enrolledDate: enrollmentInfo.enrolledDate,
        });
      });
    });

    const totalActiveStudents = students.length;

    return {
      studioName: studio.subaccountName,
      totalActiveStudents,
      activeStudentDetails,
    };
  }

  async getDropsByClass({
    studioId,
    startDate,
    endDate,
  }: {
    studioId: string;
    startDate: string;
    endDate: string;
  }) {
    const studioObjectId = new Types.ObjectId(studioId);
    const studio = await this.studioModel
      .findById(studioObjectId)
      .lean()
      .exec();
    if (!studio) {
      throw new NotFoundException('Studio not found');
    }

    const matchConditions: any = {
      studioId: studioObjectId,
      entityType: 'class',
      status: {
        $in: [
          SubscriptionStatus.CANCELLED,
          SubscriptionStatus.ENDED,
          SubscriptionStatus.SUSPENDED,
        ],
      },
    };

    if (startDate && endDate) {
      matchConditions.updatedAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate),
      };
    }

    const pipeline: PipelineStage[] = [
      { $match: matchConditions },
      {
        $lookup: {
          from: 'students',
          localField: 'studentId',
          foreignField: '_id',
          as: 'student',
        },
      },
      { $unwind: { path: '$student', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'parents',
          localField: 'student.parentId',
          foreignField: '_id',
          as: 'parent',
        },
      },
      { $unwind: { path: '$parent', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'enrollments',
          localField: 'entityId',
          foreignField: '_id',
          as: 'enrollment',
        },
      },
      { $unwind: { path: '$enrollment', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'sessions',
          localField: 'enrollment.session',
          foreignField: '_id',
          as: 'session',
        },
      },
      { $unwind: { path: '$session', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'customforms',
          localField: 'enrollment.instructor',
          foreignField: '_id',
          as: 'instructors',
        },
      },
      {
        $addFields: {
          instructorNameAggregated: {
            $cond: [
              { $gt: [{ $size: '$instructors' }, 0] },
              {
                $reduce: {
                  input: '$instructors.fieldName',
                  initialValue: '',
                  in: {
                    $cond: [
                      { $eq: ['$$value', ''] },
                      '$$this',
                      { $concat: ['$$value', ', ', '$$this'] },
                    ],
                  },
                },
              },
              '$enrollment.mainTeacher',
            ],
          },
        },
      },
      {
        $project: {
          _id: 0,
          dropDate: '$updatedAt',
          sessionName: '$session.name',
          className: '$enrollment.title',
          instructorName: '$instructorNameAggregated',
          studentName: {
            $concat: ['$student.firstName', ' ', '$student.lastName'],
          },
          parentName: {
            $concat: ['$parent.firstName', ' ', '$parent.lastName'],
          },
          parentEmail: '$parent.email',
          parentPhone: '$parent.primaryPhone',
        },
      },
    ];

    const results = await this.subscriptionModel.aggregate(pipeline).exec();

    return {
      studioName: studio.subaccountName,
      totalDrops: results.length,
      drops: results,
      dateRange: startDate && endDate ? { startDate, endDate } : undefined,
    };
  }

  async getEnrollsByClass({
    studioId,
    startDate,
    endDate,
  }: {
    studioId: string;
    startDate: string;
    endDate: string;
  }) {
    const studioObjectId = new Types.ObjectId(studioId);
    const studio = await this.studioModel
      .findById(studioObjectId)
      .lean()
      .exec();
    if (!studio) {
      throw new NotFoundException('Studio not found');
    }

    const dateFilter: any = {};
    if (startDate) dateFilter.$gte = new Date(startDate);
    if (endDate) dateFilter.$lte = new Date(endDate);

    const pipeline: PipelineStage[] = [
      { $match: { studioId: studioObjectId } },
      { $unwind: '$enrollments' },
      {
        $match: {
          'enrollments.subscriptionStatus': {
            $in: ['active', 'scheduled'],
          },
          ...(startDate || endDate
            ? { 'enrollments.enrolledDate': dateFilter }
            : {}),
        },
      },
      {
        $lookup: {
          from: 'enrollments',
          localField: 'enrollments.enrollmentId',
          foreignField: '_id',
          as: 'enrollmentDoc',
        },
      },
      { $unwind: { path: '$enrollmentDoc', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'sessions',
          localField: 'enrollmentDoc.session',
          foreignField: '_id',
          as: 'session',
        },
      },
      { $unwind: { path: '$session', preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'customforms',
          localField: 'enrollmentDoc.instructor',
          foreignField: '_id',
          as: 'instructors',
        },
      },
      {
        $lookup: {
          from: 'parents',
          localField: 'parentId',
          foreignField: '_id',
          as: 'parent',
        },
      },
      { $unwind: { path: '$parent', preserveNullAndEmptyArrays: true } },
      {
        $addFields: {
          instructorNameAggregated: {
            $cond: [
              { $gt: [{ $size: '$instructors' }, 0] },
              {
                $reduce: {
                  input: '$instructors.fieldName',
                  initialValue: '',
                  in: {
                    $cond: [
                      { $eq: ['$$value', ''] },
                      '$$this',
                      { $concat: ['$$value', ', ', '$$this'] },
                    ],
                  },
                },
              },
              '$enrollmentDoc.mainTeacher',
            ],
          },
        },
      },
      {
        $project: {
          _id: 0,
          enrolledDate: '$enrollments.enrolledDate',
          sessionName: '$session.name',
          className: '$enrollmentDoc.title',
          instructorName: '$instructorNameAggregated',
          studentName: {
            $concat: ['$firstName', ' ', '$lastName'],
          },
          parentName: {
            $concat: ['$parent.firstName', ' ', '$parent.lastName'],
          },
          parentEmail: '$parent.email',
          parentPhone: '$parent.primaryPhone',
        },
      },
    ];

    const results = await this.studentModel.aggregate(pipeline).exec();

    return {
      studioName: studio.subaccountName,
      totalEnrolls: results.length,
      enrolls: results,
      dateRange: startDate && endDate ? { startDate, endDate } : undefined,
    };
  }

  async getTransfersByClass({
    studioId,
    startDate,
    endDate,
  }: {
    studioId: string;
    startDate: string;
    endDate: string;
  }) {
    // Placeholder implementation – will return empty list until transfer tracking is available
    const studioObjectId = new Types.ObjectId(studioId);
    const studio = await this.studioModel
      .findById(studioObjectId)
      .lean()
      .exec();

    return {
      studioName: studio ? studio.subaccountName : '',
      totalTransfers: 0,
      transfers: [],
      dateRange: { startDate, endDate },
    };
  }

  async getSessionEnrollmentReport({
    studioId,
    sessionId,
  }: {
    studioId: string;
    sessionId?: string;
  }) {
    const studioObjectId = new Types.ObjectId(studioId);
    const studio = await this.studioModel
      .findById(studioObjectId)
      .lean()
      .exec();

    if (!studio) {
      throw new NotFoundException('Studio not found');
    }

    const sessionFilter: any = { studioId: studioObjectId };
    if (sessionId) {
      sessionFilter._id = new Types.ObjectId(sessionId);
    }

    const sessions = await this.sessionModel.find(sessionFilter).lean().exec();

    if (!sessions || sessions.length === 0) {
      return {
        studioName: studio.subaccountName,
        sessionName: sessionId ? 'Session not found' : 'All Sessions',
        sessionData: [],
      };
    }

    const sessionData = await Promise.all(
      sessions.map(async (session) => {
        // Get all enrollments for this session
        const enrollments = await this.enrollmentModel
          .find({ session: session._id })
          .lean()
          .exec();

        const classesData = await Promise.all(
          enrollments.map(async (enrollment) => {
            // Get active students for this enrollment using the same logic as enrollment service
            const studentsWithEnrollment =
              await this.studentsService.findAllByEnrollmentId(
                enrollment._id.toString(),
              );

            const activeStudentCount = studentsWithEnrollment.filter(
              (student) =>
                student.enrollments.some(
                  (e) =>
                    e.enrollmentId.toString() === enrollment._id.toString() &&
                    (e.subscriptionStatus === 'active' ||
                      e.subscriptionStatus === 'scheduled'),
                ),
            ).length;

            const totalSeats = enrollment.maxSize || 0;
            const seatsLeft = Math.max(0, totalSeats - activeStudentCount);

            return {
              classId: enrollment._id.toString(),
              className: enrollment.title || 'Unnamed Class',
              tuitionFee: enrollment.tuitionFee || 0,
              billingCycle: enrollment.tuitionBillingCycle || 'N/A',
              totalSeats,
              totalStudentsEnrolled: activeStudentCount,
              seatsLeft,
            };
          }),
        );

        return {
          sessionId: session._id.toString(),
          sessionName: session.name || 'Unnamed Session',
          classes: classesData,
        };
      }),
    );

    return {
      studioName: studio.subaccountName,
      sessionName:
        sessionId && sessions.length === 1
          ? sessions[0].name
          : 'Multiple Sessions',
      sessionData,
    };
  }

  async getTransactionReport({
    studioId,
    startDate,
    endDate,
  }: {
    studioId: string;
    startDate: string;
    endDate: string;
  }) {
    const studioObjectId = new Types.ObjectId(studioId);
    const studio = await this.studioModel
      .findById(studioObjectId)
      .lean()
      .exec();

    if (!studio) {
      throw new NotFoundException('Studio not found');
    }

    // Parse date range - supports ISO 8601 format (2025-02-17T21:31:05.522+00:00) and simple dates (2025-02-17)
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Validate dates
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      throw new Error(
        'Invalid date format. Please use ISO 8601 format (e.g., 2025-02-17T21:31:05.522+00:00) or simple date format (e.g., 2025-02-17)',
      );
    }

    // If endDate is just a date (no time), include the entire end date
    if (!endDate.includes('T')) {
      end.setHours(23, 59, 59, 999);
    }

    // Aggregation pipeline to get transaction data
    const aggregationPipeline: PipelineStage[] = [
      {
        $match: {
          studioId: studioObjectId,
          status: {
            $in: [InvoiceStatus.PAID, InvoiceStatus.PARTIALLY_PAID],
          },
          paymentDate: {
            $gte: start,
            $lte: end,
          },
        },
      },
      {
        $lookup: {
          from: 'parents',
          localField: 'parentId',
          foreignField: '_id',
          as: 'parent',
        },
      },
      {
        $lookup: {
          from: 'students',
          localField: 'studentId',
          foreignField: '_id',
          as: 'student',
        },
      },
      {
        $unwind: {
          path: '$parent',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $unwind: {
          path: '$student',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $project: {
          paymentDate: 1,
          status: 1,
          studentName: {
            $cond: {
              if: '$student',
              then: {
                $concat: [
                  { $ifNull: ['$student.firstName', ''] },
                  ' ',
                  { $ifNull: ['$student.lastName', ''] },
                ],
              },
              else: 'N/A',
            },
          },
          parentName: {
            $cond: {
              if: '$parent',
              then: {
                $concat: [
                  { $ifNull: ['$parent.firstName', ''] },
                  ' ',
                  { $ifNull: ['$parent.lastName', ''] },
                ],
              },
              else: 'N/A',
            },
          },
          parentEmail: {
            $ifNull: ['$parent.email', 'N/A'],
          },
          chargeName: {
            $cond: {
              if: { $gt: [{ $size: '$line_items' }, 0] },
              then: {
                $reduce: {
                  input: '$line_items',
                  initialValue: '',
                  in: {
                    $cond: {
                      if: { $eq: ['$$value', ''] },
                      then: '$$this.name',
                      else: { $concat: ['$$value', ', ', '$$this.name'] },
                    },
                  },
                },
              },
              else: 'N/A',
            },
          },
          amount: '$finalAmount',
        },
      },
      {
        $sort: {
          paymentDate: -1,
        },
      },
    ];

    const transactions = await this.subscriptionInvoiceModel
      .aggregate(aggregationPipeline)
      .exec();

    return {
      studioName: studio.subaccountName,
      transactionDate: `${startDate} to ${endDate}`,
      transactions,
      totalTransactions: transactions.length,
      totalAmount: transactions.reduce(
        (sum, transaction) => sum + (transaction.amount || 0),
        0,
      ),
    };
  }
}
