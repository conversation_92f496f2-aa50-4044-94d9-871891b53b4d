import {
  Body,
  Controller,
  Get,
  Query,
  Req,
  Res,
  UseGuards,
} from '@nestjs/common';
import { ReportingService } from './reporting.service';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { Response, Request } from 'express';
const PDFDocument = require('pdfkit');

@Controller('reporting')
export class ReportingController {
  constructor(private readonly reportingService: ReportingService) {}

  //Parent Outstanding Balances Report
  @Get('parent-outstanding-balances')
  @UseGuards(JwtAuthGuard)
  async getParentOutstandingBalances(
    @Req() request: Request,
    @Query('parentId') parentId: string,
    @Res() response: Response,
    @Query('format') format?: string,
  ) {
    const studioId = request['locationId'];

    const reportData = await this.reportingService.getParentOutstandingBalances(
      {
        studioId,
        parentId,
      },
    );

    if (format === 'csv') {
      const EOL = '\r\n';
      let csvString = '';

      // Title
      csvString += 'Parent Outstanding Balances' + EOL;
      csvString += EOL; // Blank line after title

      // Studio Name
      csvString += 'Studio Name: ' + reportData.studioName + EOL;

      // Generated On
      const generatedOn = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      csvString += `Generated On - ${generatedOn}` + EOL;
      csvString += EOL; // Blank line

      // CSV Headers
      csvString += 'Parent Name,Parent Email,Outstanding Balance' + EOL;

      // CSV Data
      if (reportData.isSingleParent) {
        if (reportData.parentFirstName) {
          const parentFullName =
            `${reportData.parentFirstName || ''} ${reportData.parentLastName || ''}`.trim();
          const parentEmail = reportData.parentEmail || '';
          const totalOutstanding =
            reportData.totalOutstanding != null
              ? Number(reportData.totalOutstanding).toFixed(2)
              : '0.00';
          csvString += `"${parentFullName}","${parentEmail}",${totalOutstanding}${EOL}`;
        }
      } else {
        if (
          reportData.parentsOutstandingBalances &&
          reportData.parentsOutstandingBalances.length > 0
        ) {
          reportData.parentsOutstandingBalances.forEach((parentData) => {
            const parentFullName =
              `${parentData.parentFirstName || ''} ${parentData.parentLastName || ''}`.trim();
            const parentEmail = parentData.parentEmail || '';
            const totalOutstanding =
              parentData.totalOutstanding != null
                ? Number(parentData.totalOutstanding).toFixed(2)
                : '0.00';
            csvString += `"${parentFullName}","${parentEmail}",${totalOutstanding}${EOL}`;
          });
        } else {
          csvString += 'No outstanding balances found.' + EOL;
        }
      }

      response.setHeader('Content-Type', 'text/csv');
      response.setHeader(
        'Content-Disposition',
        'attachment; filename="parent-outstanding-balances.csv"',
      );
      response.status(200).send(csvString);
    } else {
      // Default to JSON
      response.status(200).json(reportData);
    }
  }

  //payment by class
  @Get('payments-by-class')
  @UseGuards(JwtAuthGuard)
  async getPaymentByClass(
    @Req() request: Request,
    @Query('classId') classId: string,
    @Res() response: Response,
    @Query('format') format?: string,
  ) {
    const studioId = request['locationId'];
    const reportData = await this.reportingService.getPaymentByClass({
      studioId,
      classId,
    });

    if (format === 'csv') {
      const EOL = '\r\n';
      let csvString = '';

      // Title
      csvString += 'Payments By Class' + EOL;
      csvString += EOL; // Blank line after title

      // Studio Name
      csvString += 'Studio Name: ' + reportData.studioName + EOL;

      // Class Name
      csvString += 'Class Name: ' + reportData.className + EOL;

      // Generated On
      const generatedOn = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      csvString += `Generated On - ${generatedOn}` + EOL;
      csvString += EOL; // Blank line

      // Total Payments
      csvString +=
        `Total Payments - $${reportData.totalAmountPaid.toFixed(2)}` + EOL;
      csvString += EOL; // Blank line

      // CSV Headers
      csvString += 'Student Name,Amount' + EOL;

      // CSV Data
      if (reportData && reportData.subscriptionInvoices) {
        reportData.subscriptionInvoices.forEach((invoice) => {
          const studentFullName =
            `${invoice.studentFirstName || ''} ${invoice.studentLastName || ''}`.trim();
          const amount =
            invoice.finalAmount != null
              ? Number(invoice.finalAmount).toFixed(2)
              : '0.00';
          csvString += `"${studentFullName}",${amount}${EOL}`;
        });
      }

      response.setHeader('Content-Type', 'text/csv');
      response.setHeader(
        'Content-Disposition',
        'attachment; filename="payment-by-class.csv"',
      );
      response.status(200).send(csvString);
    } else {
      // Default to JSON
      response.status(200).json(reportData);
    }
  }

  //Payments by Session
  @Get('payments-by-session')
  @UseGuards(JwtAuthGuard)
  async getPaymentsBySession(
    @Req() request: Request,
    @Query('sessionId') sessionId: string,
    @Res() response: Response,
    @Query('format') format?: string,
  ) {
    const studioId = request['locationId'];
    const reportData = await this.reportingService.getPaymentsBySession({
      studioId,
      sessionId,
    });

    if (format === 'csv') {
      const EOL = '\r\n';
      let csvString = '';

      // Title
      csvString += 'Payments By Session' + EOL;
      csvString += EOL; // Blank line after title

      // Studio Name
      csvString += 'Studio Name: ' + reportData.studioName + EOL;

      // Session Name
      csvString += 'Session Name: ' + reportData.sessionName + EOL;

      // Generated On
      const generatedOn = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      csvString += `Generated On - ${generatedOn}` + EOL;
      csvString += EOL; // Blank line

      // Total Payments
      csvString +=
        `Total Payments - ${reportData.totalAmountPaid.toFixed(2)}` + EOL;
      csvString += EOL; // Blank line

      // Payments by Class
      csvString += 'Class Name,Amount' + EOL;
      if (
        reportData &&
        reportData.paymentsByClass &&
        reportData.paymentsByClass.length > 0
      ) {
        reportData.paymentsByClass.forEach((classPayment) => {
          csvString +=
            `${classPayment.className} - $${classPayment.amount.toFixed(2)}` +
            EOL;
        });
      }
      csvString += EOL; // Blank line

      response.setHeader('Content-Type', 'text/csv');
      response.setHeader(
        'Content-Disposition',
        'attachment; filename="payments-by-session.csv"',
      );
      response.status(200).send(csvString);
    } else {
      // Default to JSON
      response.status(200).json(reportData);
    }
  }

  //Revenue Over a Selected Time Period
  @Get('revenue-over-time-period')
  @UseGuards(JwtAuthGuard)
  async getRevenueOverTimePeriod(
    @Req() request: Request,
    @Query() query: { startDate: string; endDate: string },
    @Res() response: Response,
    @Query('format') format?: string,
  ) {
    const studioId = request['locationId'];
    const startDate = query.startDate;
    const endDate = query.endDate;
    const reportData = await this.reportingService.getRevenueOverTimePeriod({
      studioId,
      startDate,
      endDate,
    });

    if (format === 'csv') {
      const EOL = '\r\n';
      let csvString = '';

      // Title
      csvString += 'Revenue Over Time Period' + EOL;
      csvString += EOL; // Blank line after title

      // Studio Name
      csvString += 'Studio Name: ' + reportData.studioName + EOL;

      // Generated On
      const generatedOn = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      csvString += `Generated On - ${generatedOn}` + EOL;

      // Date Range
      const formattedStartDate = new Date(startDate)
        .toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          year: 'numeric',
        })
        .toUpperCase();
      const formattedEndDate = new Date(endDate)
        .toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          year: 'numeric',
        })
        .toUpperCase();
      csvString +=
        `Date Range - ${formattedStartDate} to ${formattedEndDate}` + EOL;
      csvString += EOL; // Blank line

      // Total Payments
      csvString +=
        `Total Payments - ${reportData.totalAmountPaid.toFixed(2)}` + EOL;
      csvString += EOL; // Blank line

      // Payments by Product
      csvString += 'Product Name,Amount' + EOL;
      if (
        reportData &&
        reportData.paymentsByProduct &&
        reportData.paymentsByProduct.length > 0
      ) {
        reportData.paymentsByProduct.forEach((productPayment) => {
          csvString +=
            `${productPayment.productName} - ${productPayment.amount.toFixed(2)}` +
            EOL;
        });
      }
      csvString += EOL; // Blank line

      response.setHeader('Content-Type', 'text/csv');
      response.setHeader(
        'Content-Disposition',
        'attachment; filename="revenue-over-time-period.csv"',
      );
      response.status(200).send(csvString);
    } else {
      // Default to JSON
      response.status(200).json(reportData);
    }
  }

  //Top Spending Parents
  @Get('top-spending-parents')
  @UseGuards(JwtAuthGuard)
  async getTopSpendingParents(
    @Req() request: Request,
    @Res() response: Response,
    @Query('format') format?: string,
  ) {
    const studioId = request['locationId'];
    const reportData = await this.reportingService.getTopSpendingParents({
      studioId,
    });

    if (format === 'csv') {
      const EOL = '\r\n';
      let csvString = '';
      csvString += 'Top Spending Parents (by Amount)' + EOL;
      csvString += EOL;
      const generatedOn = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      csvString += `Generated On - ${generatedOn}` + EOL;
      csvString += EOL;
      csvString += 'Parent Name,Parent Email,Total Amount Spent' + EOL;
      reportData.forEach((parent) => {
        csvString += `"${parent.parentFirstName} ${parent.parentLastName}",${parent.parentEmail},${parent.totalSpent.toFixed(2)}${EOL}`;
      });
      response.setHeader('Content-Type', 'text/csv');
      response.setHeader(
        'Content-Disposition',
        'attachment; filename="top-spending-parents.csv"',
      );
      response.status(200).send(csvString);
    } else {
      response.status(200).json(reportData);
    }
  }

  //Scheduled Upcoming Payments
  @Get('scheduled-upcoming-payments')
  @UseGuards(JwtAuthGuard)
  async getScheduledUpcomingPayments(
    @Req() request: Request,
    @Query() query: { startDate: string; endDate: string },
    @Res() response: Response,
    @Query('format') format?: string,
  ) {
    const studioId = request['locationId'];
    const { startDate, endDate } = query;

    if (!startDate || !endDate) {
      response.status(400).json({
        message: 'startDate and endDate are required in the request body.',
      });
      return;
    }

    const reportData = await this.reportingService.getScheduledUpcomingPayments(
      { studioId, startDate, endDate },
    );

    if (format === 'csv') {
      const EOL = '\r\n';
      let csvString = '';

      // Title
      csvString += 'Scheduled Upcoming Payments' + EOL;
      csvString += EOL; // Blank line after title

      // Studio Name
      csvString += 'Studio Name: ' + reportData.studioName + EOL;

      // Generated On
      const generatedOn = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      csvString += `Generated On - ${generatedOn}` + EOL;

      // Date Range
      const formattedStartDate = new Date(startDate)
        .toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          year: 'numeric',
        })
        .toUpperCase();
      const formattedEndDate = new Date(endDate)
        .toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
          year: 'numeric',
        })
        .toUpperCase();
      csvString +=
        `Date Range - ${formattedStartDate} to ${formattedEndDate}` + EOL;
      csvString += EOL; // Blank line

      // Total Upcoming Payments
      csvString +=
        `Total Upcoming Payments - ${reportData.totalUpcomingAmount.toFixed(2)}` +
        EOL;
      csvString += EOL; // Blank line

      // Payments by Product Header
      csvString += 'Product Name,Amount' + EOL;
      // Payments by Product Data
      if (
        reportData.paymentsByProduct &&
        reportData.paymentsByProduct.length > 0
      ) {
        reportData.paymentsByProduct.forEach((productPayment) => {
          csvString += `${productPayment.productName},${productPayment.amount.toFixed(2)}${EOL}`;
        });
      }
      csvString += EOL; // Blank line

      response.setHeader('Content-Type', 'text/csv');
      response.setHeader(
        'Content-Disposition',
        'attachment; filename="scheduled-upcoming-payments.csv"',
      );
      response.status(200).send(csvString);
    } else {
      response.status(200).json(reportData);
    }
  }

  //Issued Refunds
  @Get('issued-refunds')
  @UseGuards(JwtAuthGuard)
  async getIssuedRefunds(
    @Req() request: Request,
    @Query() query: { startDate?: string; endDate?: string },
    @Res() response: Response,
    @Query('format') format?: string,
  ) {
    const studioId = request['locationId'];
    const { startDate, endDate } = query;

    const reportData = await this.reportingService.getIssuedRefunds({
      studioId,
      startDate,
      endDate,
    });

    if (format === 'csv') {
      const EOL = '\r\n';
      let csvString = '';

      // Title
      csvString += 'Issued Refunds Report' + EOL;
      csvString += EOL;

      // Studio Name
      csvString += `Studio Name: ${reportData.studioName}` + EOL;

      // Generated On
      const generatedOn = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      csvString += `Generated On - ${generatedOn}` + EOL;

      // Date Range (only if provided)
      if (startDate && endDate) {
        const formattedStartDate = new Date(startDate)
          .toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
          })
          .toUpperCase();
        const formattedEndDate = new Date(endDate)
          .toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
          })
          .toUpperCase();
        csvString +=
          `Date Range - ${formattedStartDate} to ${formattedEndDate}` + EOL;
      }
      csvString += EOL;

      // Total Refunded Payments
      csvString +=
        `Total Refunded Payments - ${reportData.totalRefundedAmount.toFixed(2)}` +
        EOL;
      csvString += EOL;

      // CSV Headers
      csvString +=
        'Date,Parent Name,Parent Email,Student Name,Product,Amount Refunded' +
        EOL;

      // CSV Data rows
      if (reportData.refundDetails && reportData.refundDetails.length > 0) {
        reportData.refundDetails.forEach((detail) => {
          const refundDate = detail.refundDate
            ? new Date(detail.refundDate).toLocaleDateString('en-US')
            : 'N/A';
          const parentName = detail.parentName || 'N/A';
          const parentEmail = detail.parentEmail || 'N/A';
          const studentName = detail.studentName || 'N/A';
          const productName = detail.productName || 'N/A';
          csvString += `${refundDate},"${parentName}","${parentEmail}","${studentName}","${productName}",${detail.amountRefunded.toFixed(2)}${EOL}`;
        });
      }

      response.setHeader('Content-Type', 'text/csv');
      response.setHeader(
        'Content-Disposition',
        'attachment; filename="issued-refunds.csv"',
      );
      response.status(200).send(csvString);
    } else {
      response.status(200).json(reportData);
    }
  }

  //Failed Payments
  @Get('failed-payments')
  @UseGuards(JwtAuthGuard)
  async getFailedPayments(
    @Req() request: Request,
    @Query() query: { startDate?: string; endDate?: string },
    @Res() response: Response,
    @Query('format') format?: string,
  ) {
    const studioId = request['locationId'];
    const { startDate, endDate } = query;

    const reportData = await this.reportingService.getFailedPayments({
      studioId,
      startDate,
      endDate,
    });

    if (format === 'csv') {
      const EOL = '\r\n';
      let csvString = '';

      // Title - No specific title mentioned, using default
      csvString += 'Failed Payments Report' + EOL;
      csvString += EOL;

      // Studio Name
      csvString += `Studio Name: ${reportData.studioName}` + EOL;

      // Generated On
      const generatedOn = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      csvString += `Generated On - ${generatedOn}` + EOL;

      // Date Range (only if provided)
      if (startDate && endDate) {
        const formattedStartDate = new Date(startDate)
          .toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
          })
          .toUpperCase();
        const formattedEndDate = new Date(endDate)
          .toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric',
          })
          .toUpperCase();
        csvString +=
          `Date Range - ${formattedStartDate} to ${formattedEndDate}` + EOL;
      }
      csvString += EOL;

      // Total Failed Payments
      csvString +=
        `Total Failed Payments - ${reportData.totalFailedAmount.toFixed(2)}` +
        EOL;
      csvString += EOL;

      // CSV Headers
      csvString +=
        'Date,Parent Name,Parent Email,Student Name,Product,Failed Amount' +
        EOL;

      // CSV Data rows from failedPaymentDetails
      if (
        reportData.failedPaymentDetails &&
        reportData.failedPaymentDetails.length > 0
      ) {
        reportData.failedPaymentDetails.forEach((detail) => {
          const failedDate = detail.failedDate
            ? new Date(detail.failedDate).toLocaleDateString('en-US')
            : 'N/A';
          const parentName = detail.parentName || 'N/A';
          const parentEmail = detail.parentEmail || 'N/A';
          const studentName = detail.studentName || 'N/A';
          const productName = detail.productName || 'N/A';
          csvString += `${failedDate},"${parentName}","${parentEmail}","${studentName}","${productName}",${detail.failedAmount.toFixed(2)}${EOL}`;
        });
      }

      response.setHeader('Content-Type', 'text/csv');
      response.setHeader(
        'Content-Disposition',
        'attachment; filename="failed-payments.csv"',
      );
      response.status(200).send(csvString);
    } else {
      // For JSON, return the structured data from the service
      response.status(200).json(reportData);
    }
  }

  //Days Past Due for Overdue Payments
  @Get('days-past-due-overdue-payments')
  @UseGuards(JwtAuthGuard)
  async getDaysPastDueForOverduePayments(
    @Req() request: Request,
    @Query('dueDays') dueDays: string,
    @Res() response: Response,
    @Query('format') format?: string,
  ) {
    const studioId = request['locationId'];

    if (!dueDays || isNaN(parseInt(dueDays, 10)) || parseInt(dueDays, 10) < 0) {
      response.status(400).json({
        message: 'A valid non-negative number for dueDays is required.',
      });
      return;
    }

    const reportData =
      await this.reportingService.getDaysPastDueForOverduePayments({
        studioId,
        dueDays,
      });

    if (format === 'csv') {
      const EOL = '\r\n';
      let csvString = '';

      // Report Title (Optional, can be removed if not desired)
      // csvString += 'Overdue Payments Report' + EOL;
      // csvString += EOL;

      // Studio Name
      csvString += `Studio Name: ${reportData.studioName}` + EOL;

      // Generated On
      const generatedOn = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      csvString += `Generated On - ${generatedOn}` + EOL;

      // Number of days past due criteria
      csvString += `Number of days past due - ${reportData.inputDueDays}` + EOL;
      csvString += EOL; // Blank line after summary headers

      // Total Amount Due
      csvString +=
        `Total Amount Due - ${reportData.totalAmountDue.toFixed(2)}` + EOL;
      csvString += EOL; // Blank line after total

      // CSV Headers for itemized data
      csvString +=
        'Date,Parent Name,Parent Email,Student Name,Product,Due Amount,Due Days' +
        EOL;

      // CSV Data rows
      if (
        reportData.overduePaymentDetails &&
        reportData.overduePaymentDetails.length > 0
      ) {
        reportData.overduePaymentDetails.forEach((detail) => {
          const invoiceDueDate = detail.invoiceDueDate
            ? new Date(detail.invoiceDueDate).toLocaleDateString('en-US')
            : 'N/A';
          const parentName = detail.parentName || ''; // Default to empty string if null/undefined
          const parentEmail = detail.parentEmail || '';
          const studentName = detail.studentName || '';
          const productName = detail.productName || 'N/A';
          csvString += `${invoiceDueDate},"${parentName}","${parentEmail}","${studentName}","${productName}",${detail.dueAmount.toFixed(2)},${detail.actualDaysPastDue}${EOL}`;
        });
      }

      response.setHeader('Content-Type', 'text/csv');
      response.setHeader(
        'Content-Disposition',
        'attachment; filename="days-past-due-overdue-payments.csv"',
      );
      response.status(200).send(csvString);
    } else {
      // JSON response
      response.status(200).json(reportData);
    }
  }

  //Active Students & Class Count
  @Get('active-students-class-count')
  @UseGuards(JwtAuthGuard)
  async getActiveStudentsClassCount(
    @Req() request: Request,
    @Res() response: Response,
    @Query('format') format?: string,
  ) {
    const studioId = request['locationId'];

    const reportData =
      await this.reportingService.getActiveStudentsAndClassCount({
        studioId,
      });

    if (format === 'csv') {
      const EOL = '\r\n';
      let csvString = '';

      // Title
      csvString += 'Active Students & Class Count' + EOL;
      csvString += EOL;

      // Studio Name
      csvString += 'Studio Name: ' + reportData.studioName + EOL;

      // Generated On
      const generatedOn = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      csvString += `Generated On - ${generatedOn}` + EOL;
      csvString += EOL;

      // Total Active Students
      csvString +=
        `Total Active Students - ${reportData.totalActiveStudents}` + EOL;
      csvString += EOL;

      // CSV Headers
      csvString +=
        'Student Name,Number of Enrolled Classes,Enrolled Date,Session Name,Class Name,Parent Name,Parent Phone,Parent Email' +
        EOL;

      // CSV Data
      if (
        reportData.activeStudentDetails &&
        reportData.activeStudentDetails.length > 0
      ) {
        reportData.activeStudentDetails.forEach((detail) => {
          const studentFullName =
            `${detail.studentFirstName || ''} ${detail.studentLastName || ''}`.trim();
          const enrolledDate = detail.enrolledDate
            ? new Date(detail.enrolledDate).toLocaleDateString('en-US')
            : '';
          const parentFullName =
            `${detail.parentFirstName || ''} ${detail.parentLastName || ''}`.trim();
          const parentPhone = detail.parentPhone || '';
          const parentEmail = detail.parentEmail || '';
          const classesActive = detail.classesActive;
          const className = detail.className || '';
          const sessionName = detail.sessionName || '';
          csvString += `"${studentFullName}",${classesActive},${enrolledDate},"${sessionName}","${className}","${parentFullName}","${parentPhone}","${parentEmail}"${EOL}`;
        });
      }

      response.setHeader('Content-Type', 'text/csv');
      response.setHeader(
        'Content-Disposition',
        'attachment; filename="active-students-class-count.csv"',
      );
      response.status(200).send(csvString);
    } else {
      // Default to JSON
      response.status(200).json(reportData);
    }
  }

  //Drops by Class (by Date Range)
  @Get('drops-by-class')
  @UseGuards(JwtAuthGuard)
  async getDropsByClass(
    @Req() request: Request,
    @Query() query: { startDate: string; endDate: string },
    @Res() response: Response,
    @Query('format') format?: string,
  ) {
    const studioId = request['locationId'];
    const { startDate, endDate } = query;

    const reportData = await this.reportingService.getDropsByClass({
      studioId,
      startDate,
      endDate,
    });

    if (format === 'csv') {
      const EOL = '\r\n';
      let csvString = '';

      // Title & headers
      csvString += 'Drops by Class' + EOL;
      csvString += EOL;
      csvString += `Studio Name: ${reportData.studioName}` + EOL;
      const generatedOn = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      csvString += `Generated On - ${generatedOn}` + EOL;
      if (startDate && endDate) {
        const formattedStartDate = new Date(startDate).toLocaleDateString(
          'en-US',
        );
        const formattedEndDate = new Date(endDate).toLocaleDateString('en-US');
        csvString +=
          `Date Range - ${formattedStartDate} to ${formattedEndDate}` + EOL;
      }
      csvString += EOL;

      // CSV header line
      csvString +=
        'Session Name,Class Name,Instructor Name,Student Name,Parent Name,Parent Email,Parent Phone,Dropped On Date' +
        EOL;

      if (reportData.drops && reportData.drops.length > 0) {
        reportData.drops.forEach((d) => {
          const studentName = d.studentName || '';
          const parentName = d.parentName || '';
          const dropDate = d.dropDate
            ? new Date(d.dropDate).toLocaleDateString('en-US')
            : '';
          csvString += `"${d.sessionName || ''}","${d.className || ''}","${d.instructorName || ''}","${studentName}","${parentName}","${d.parentEmail || ''}","${d.parentPhone || ''}",${dropDate}${EOL}`;
        });
      }

      response.setHeader('Content-Type', 'text/csv');
      response.setHeader(
        'Content-Disposition',
        'attachment; filename="drops-by-class.csv"',
      );
      response.status(200).send(csvString);
    } else {
      response.status(200).json(reportData);
    }
  }

  //Enrolls by Class with Student Details (by Date Range)
  @Get('enrolls-by-class')
  @UseGuards(JwtAuthGuard)
  async getEnrollsByClass(
    @Req() request: Request,
    @Query() query: { startDate: string; endDate: string },
    @Res() response: Response,
    @Query('format') format?: string,
  ) {
    const studioId = request['locationId'];
    const { startDate, endDate } = query;

    const reportData = await this.reportingService.getEnrollsByClass({
      studioId,
      startDate,
      endDate,
    });

    if (format === 'csv') {
      const EOL = '\r\n';
      let csvString = '';

      csvString += 'Enrolls by Class' + EOL;
      csvString += EOL;
      csvString += `Studio Name: ${reportData.studioName}` + EOL;
      const generatedOn = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      csvString += `Generated On - ${generatedOn}` + EOL;
      if (startDate || endDate) {
        const formattedStartDate = startDate
          ? new Date(startDate).toLocaleDateString('en-US')
          : '';
        const formattedEndDate = endDate
          ? new Date(endDate).toLocaleDateString('en-US')
          : '';
        const rangeLabel =
          formattedStartDate && formattedEndDate
            ? `${formattedStartDate} to ${formattedEndDate}`
            : formattedStartDate || formattedEndDate;
        csvString += `Date Range - ${rangeLabel}` + EOL;
      }
      csvString += EOL;

      csvString +=
        'Session Name,Class Name,Instructor Name,Student Name,Parent Name,Parent Email,Parent Phone,Enrolled On Date' +
        EOL;

      if (reportData.enrolls && reportData.enrolls.length > 0) {
        reportData.enrolls.forEach((e) => {
          const studentName = e.studentName || '';
          const parentName = e.parentName || '';
          const enrolledDate = e.enrolledDate
            ? new Date(e.enrolledDate).toLocaleDateString('en-US')
            : '';
          csvString += `"${e.sessionName || ''}","${e.className || ''}","${e.instructorName || ''}","${studentName}","${parentName}","${e.parentEmail || ''}","${e.parentPhone || ''}",${enrolledDate}${EOL}`;
        });
      }

      response.setHeader('Content-Type', 'text/csv');
      response.setHeader(
        'Content-Disposition',
        'attachment; filename="enrolls-by-class.csv"',
      );
      response.status(200).send(csvString);
    } else {
      response.status(200).json(reportData);
    }
  }

  //Transfers by date range (placeholder)
  @Get('transfers-by-class')
  @UseGuards(JwtAuthGuard)
  async getTransfersByClass(
    @Req() request: Request,
    @Query() query: { startDate: string; endDate: string },
    @Res() response: Response,
    @Query('format') format?: string,
  ) {
    const studioId = request['locationId'];
    const { startDate, endDate } = query;

    const reportData = await this.reportingService.getTransfersByClass({
      studioId,
      startDate,
      endDate,
    });

    // For now, empty dataset, but structure matches others
    if (format === 'csv') {
      const EOL = '\r\n';
      let csvString = '';

      csvString += 'Transfers by Class' + EOL;
      csvString += EOL;
      csvString += `Studio Name: ${reportData.studioName}` + EOL;
      const generatedOn = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      csvString += `Generated On - ${generatedOn}` + EOL;
      if (startDate && endDate) {
        const formattedStartDate = new Date(startDate).toLocaleDateString(
          'en-US',
        );
        const formattedEndDate = new Date(endDate).toLocaleDateString('en-US');
        csvString +=
          `Date Range - ${formattedStartDate} to ${formattedEndDate}` + EOL;
      }
      csvString += EOL;
      csvString +=
        'Transferred From Class,Transferred To Class,Instructor of Previous Class,Instructor of New Class,Student Name,Parent Name,Parent Email,Parent Phone,Transferred On Date' +
        EOL;
      // No data rows yet
      response.setHeader('Content-Type', 'text/csv');
      response.setHeader(
        'Content-Disposition',
        'attachment; filename="transfers-by-class.csv"',
      );
      response.status(200).send(csvString);
    } else {
      response.status(200).json(reportData);
    }
  }

  //Session Enrollment Report
  @Get('session-enrollment-report')
  @UseGuards(JwtAuthGuard)
  async getSessionEnrollmentReport(
    @Req() request: Request,
    @Res() response: Response,
    @Query('sessionId') sessionId?: string,
    @Query('format') format?: string,
  ) {
    const studioId = request['locationId'];

    const reportData = await this.reportingService.getSessionEnrollmentReport({
      studioId,
      sessionId,
    });

    if (format === 'csv') {
      const EOL = '\r\n';
      let csvString = '';

      // Title
      csvString += 'Session Enrollment Report' + EOL;
      csvString += EOL; // Blank line after title

      // Studio Name
      csvString += 'Studio Name: ' + reportData.studioName + EOL;

      // Session Selected (if specific session)
      if (sessionId) {
        csvString += 'Session Selected: ' + reportData.sessionName + EOL;
      }

      // Generated On
      const generatedOn = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      });
      csvString += `Date Generated: ${generatedOn}` + EOL;
      csvString += EOL; // Blank line

      // CSV Headers
      csvString +=
        'Session Name,Class Name,Class Tuition Fee,Class Billing Cycle,Total Seats,Total Students Enrolled,Seats Left' +
        EOL;

      // CSV Data
      if (reportData.sessionData && reportData.sessionData.length > 0) {
        reportData.sessionData.forEach((session) => {
          if (session.classes && session.classes.length > 0) {
            session.classes.forEach((classItem) => {
              const sessionName = session.sessionName || 'N/A';
              const className = classItem.className || 'N/A';
              const tuitionFee =
                classItem.tuitionFee != null
                  ? classItem.tuitionFee.toFixed(2)
                  : '0.00';
              const billingCycle = classItem.billingCycle || 'N/A';
              const totalSeats = classItem.totalSeats || 0;
              const totalEnrolled = classItem.totalStudentsEnrolled || 0;
              const seatsLeft = classItem.seatsLeft || 0;

              csvString += `"${sessionName}","${className}",${tuitionFee},"${billingCycle}",${totalSeats},${totalEnrolled},${seatsLeft}${EOL}`;
            });
          } else {
            // Session with no classes
            const sessionName = session.sessionName || 'N/A';
            csvString += `"${sessionName}","No Classes","0.00","N/A",0,0,0${EOL}`;
          }
        });
      } else {
        csvString += 'No session data found.' + EOL;
      }

      response.setHeader('Content-Type', 'text/csv');
      response.setHeader(
        'Content-Disposition',
        'attachment; filename="session-enrollment-report.csv"',
      );
      response.status(200).send(csvString);
    } else {
      // Default to JSON
      response.status(200).json(reportData);
    }
  }

  //Transaction Report
  @Get('transaction-report')
  @UseGuards(JwtAuthGuard)
  async getTransactionReport(
    @Req() request: Request,
    @Query() query: { startDate: string; endDate: string },
    @Res() response: Response,
    @Query('format') format?: string,
  ) {
    const studioId = request['locationId'];
    const { startDate, endDate } = query;

    if (!startDate || !endDate) {
      response.status(400).json({
        message: 'startDate and endDate are required.',
      });
      return;
    }

    try {
      const reportData = await this.reportingService.getTransactionReport({
        studioId,
        startDate,
        endDate,
      });

      if (format === 'csv') {
        const EOL = '\r\n';
        let csvString = '';

        // Title
        csvString += 'Transaction Report' + EOL;
        csvString += EOL; // Blank line after title

        // Studio Name
        csvString += 'Studio Name: ' + reportData.studioName + EOL;

        // Transaction Date Range
        csvString += 'Transaction Date: ' + reportData.transactionDate + EOL;

        // Generated On
        const generatedOn = new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        });
        csvString += `Generated On: ${generatedOn}` + EOL;
        csvString += EOL; // Blank line

        // CSV Headers
        csvString +=
          'Date of Payment,Student Name,Parent Name,Parent Email,Charge Name,Status,Amount' +
          EOL;

        // CSV Data
        if (reportData.transactions && reportData.transactions.length > 0) {
          reportData.transactions.forEach((transaction) => {
            const paymentDate = transaction.paymentDate
              ? new Date(transaction.paymentDate).toLocaleDateString('en-US')
              : 'N/A';
            const studentName = transaction.studentName || 'N/A';
            const parentName = transaction.parentName || 'N/A';
            const parentEmail = transaction.parentEmail || 'N/A';
            const chargeName = transaction.chargeName || 'N/A';
            const status = transaction.status || 'N/A';
            const amount =
              transaction.amount != null
                ? Number(transaction.amount).toFixed(2)
                : '0.00';

            csvString += `${paymentDate},"${studentName}","${parentName}","${parentEmail}","${chargeName}","${status}",${amount}${EOL}`;
          });
        } else {
          csvString +=
            'No transactions found for the specified date range.' + EOL;
        }

        response.setHeader('Content-Type', 'text/csv');
        response.setHeader(
          'Content-Disposition',
          'attachment; filename="transaction-report.csv"',
        );
        response.status(200).send(csvString);
      } else if (format === 'pdf') {
        // Generate PDF
        const pdfBuffer = await this.generateTransactionReportPdf(reportData);

        response.setHeader('Content-Type', 'application/pdf');
        response.setHeader(
          'Content-Disposition',
          'attachment; filename="transaction-report.pdf"',
        );
        response.status(200).send(pdfBuffer);
      } else {
        // Default to JSON
        response.status(200).json(reportData);
      }
    } catch (error) {
      response.status(400).json({
        message: error.message || 'Error generating transaction report',
      });
    }
  }

  private async generateTransactionReportPdf(reportData: any): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      try {
        const doc = new PDFDocument({ margin: 50, size: 'A4' });
        const chunks: Buffer[] = [];

        doc.on('data', (chunk) => chunks.push(chunk));
        doc.on('end', () => resolve(Buffer.concat(chunks)));

        // Header
        doc.fontSize(20).text('Transaction Report', { align: 'center' });
        doc.moveDown();

        // Studio Name
        doc.fontSize(14).text(`Studio Name: ${reportData.studioName}`);
        doc.moveDown(0.5);

        // Transaction Date Range
        doc
          .fontSize(12)
          .text(`Transaction Date: ${reportData.transactionDate}`);
        doc.moveDown(0.5);

        // Generated On
        const generatedOn = new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        });
        doc.text(`Generated On: ${generatedOn}`);
        doc.moveDown();

        // Table setup
        const tableTop = doc.y;
        const tableLeft = 50;
        const colWidths = [70, 90, 90, 110, 90, 70, 50]; // Column widths
        const rowHeight = 20;

        // Table headers
        doc.fontSize(10).font('Helvetica-Bold');
        let currentX = tableLeft;
        const headers = [
          'Date of Payment',
          'Student Name',
          'Parent Name',
          'Parent Email',
          'Charge Name',
          'Status',
          'Amount',
        ];

        headers.forEach((header, i) => {
          doc.text(header, currentX, tableTop, {
            width: colWidths[i],
            align: 'left',
          });
          currentX += colWidths[i];
        });

        // Draw header line
        doc
          .moveTo(tableLeft, tableTop + 15)
          .lineTo(
            tableLeft + colWidths.reduce((a, b) => a + b, 0),
            tableTop + 15,
          )
          .stroke();

        // Table data
        doc.font('Helvetica');
        let currentY = tableTop + rowHeight;

        if (reportData.transactions && reportData.transactions.length > 0) {
          reportData.transactions.forEach((transaction, index) => {
            // Check if we need a new page
            if (currentY > doc.page.height - 100) {
              doc.addPage();
              currentY = 50;
            }

            const paymentDate = transaction.paymentDate
              ? new Date(transaction.paymentDate).toLocaleDateString('en-US')
              : 'N/A';
            const studentName = transaction.studentName || 'N/A';
            const parentName = transaction.parentName || 'N/A';
            const parentEmail = transaction.parentEmail || 'N/A';
            const chargeName = transaction.chargeName || 'N/A';
            const status = transaction.status || 'N/A';
            const amount =
              transaction.amount != null
                ? `$${Number(transaction.amount).toFixed(2)}`
                : '$0.00';

            const rowData = [
              paymentDate,
              studentName,
              parentName,
              parentEmail,
              chargeName,
              status,
              amount,
            ];

            currentX = tableLeft;
            rowData.forEach((data, i) => {
              doc.text(data, currentX, currentY, {
                width: colWidths[i],
                align: i === 6 ? 'right' : 'left', // Amount column (index 6) is right-aligned
                height: rowHeight,
              });
              currentX += colWidths[i];
            });

            currentY += rowHeight;

            // Draw row separator line for every few rows
            if ((index + 1) % 5 === 0) {
              doc
                .moveTo(tableLeft, currentY - 2)
                .lineTo(
                  tableLeft + colWidths.reduce((a, b) => a + b, 0),
                  currentY - 2,
                )
                .stroke();
            }
          });
        } else {
          doc.text(
            'No transactions found for the specified date range.',
            tableLeft,
            currentY,
          );
        }

        // Summary
        if (reportData.transactions && reportData.transactions.length > 0) {
          doc.moveDown(2);
          doc.fontSize(12).font('Helvetica-Bold');
          doc.text(`Total Transactions: ${reportData.totalTransactions}`);
          doc.text(
            `Total Amount: $${Number(reportData.totalAmount).toFixed(2)}`,
          );
        }

        doc.end();
      } catch (error) {
        reject(error);
      }
    });
  }
}
