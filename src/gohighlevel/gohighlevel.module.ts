import { Modu<PERSON> } from '@nestjs/common';
import { GohighlevelService } from './gohighlevel.service';
import { HttpModule } from '@nestjs/axios';
import { MongooseModule } from '@nestjs/mongoose';
import { Studio, StudioSchema } from 'src/database/schema/studio';
import { GohighlevelController } from './gohighlevel.controller';
import { JwtModule } from '@nestjs/jwt';
import { ConfigModule, ConfigService } from '@nestjs/config';

@Module({
  imports: [
    HttpModule,
    MongooseModule.forFeature([{ name: Studio.name, schema: StudioSchema }]),
    JwtModule,
    ConfigModule,
  ],
  controllers: [GohighlevelController],
  providers: [GohighlevelService],
  exports: [GohighlevelService],
})
export class GohighlevelModule {}
