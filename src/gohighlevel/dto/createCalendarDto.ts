import {
  IsBoolean,
  IsString,
  <PERSON><PERSON><PERSON>y,
  IsOptional,
  IsN<PERSON>ber,
  IsEnum,
  ValidateNested,
  IsNotEmpty,
} from 'class-validator';
import { Type } from 'class-transformer';

export enum RecurrenceFrequency {
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
}

export enum CalendarDayArrayIndexMap {
  Sun = 0,
  Mon = 1,
  Tue = 2,
  Wed = 3,
  Thu = 4,
  Fri = 5,
  Sat = 6,
}

export enum RecurrenceBookingOption {
  SKIP = 'skip',
  CONTINUE = 'continue',
  BOOK_NEXT = 'book_next',
}

export class RecurringDto {
  @IsEnum(RecurrenceFrequency)
  @IsOptional()
  freq: RecurrenceFrequency;

  @IsEnum(RecurrenceBookingOption)
  @IsOptional()
  bookingOption?: RecurrenceBookingOption;

  @IsNumber()
  @IsOptional()
  count?: number; // Number of occurrences
}

// Enums for allowed values
export enum MeetingLocationType {
  CUSTOM = 'custom',
  ZOOM = 'zoom',
  GMEET = 'gmeet',
  PHONE = 'phone',
  ADDRESS = 'address',
}

export enum EventType {
  ROUND_ROBIN_OPTIMIZE_FOR_AVAILABILITY = 'RoundRobin_OptimizeForAvailability',
  ROUND_ROBIN_OPTIMIZE_FOR_EQUAL_DISTRIBUTION = 'RoundRobin_OptimizeForEqualDistribution',
}

export enum CalendarType {
  ROUND_ROBIN = 'round_robin',
  EVENT = 'event',
  CLASS_BOOKING = 'class_booking',
  COLLECTIVE = 'collective',
  SERVICE_BOOKING = 'service_booking',
  PERSONAL = 'personal',
}

export enum WidgetType {
  DEFAULT = 'default',
  CLASSIC = 'classic',
}

// DTO for the Calendar Notification
class CalendarNotificationDto {
  @IsString()
  @IsOptional()
  type: string;

  @IsBoolean()
  @IsOptional()
  shouldSendToContact: boolean;

  @IsBoolean()
  @IsOptional()
  shouldSendToGuest: boolean;

  @IsBoolean()
  @IsOptional()
  shouldSendToUser: boolean;

  @IsBoolean()
  @IsOptional()
  shouldSendToSelectedUsers: boolean;

  @IsString()
  @IsOptional()
  selectedUsers: string; // Comma-separated emails
}

// DTO for Team Member
class TeamMemberDto {
  @IsString()
  @IsOptional()
  userId: string; // Required user ID

  @IsString()
  @IsOptional()
  meetingLocation?: string;

  @IsString()
  @IsOptional()
  meetingLocationType?: string;

  @IsBoolean()
  @IsOptional()
  isPrimary?: boolean; // Marks a user as primary
}

// DTO for Open Hours - Time Slot
class OpenHourTimeSlotDto {
  @IsNumber()
  @IsOptional()
  openHour: number; // Format: HH:mm

  @IsNumber()
  @IsOptional()
  openMinute: number; // Format: HH:mm

  @IsNumber()
  @IsOptional()
  closeHour: number; // Format: HH:mm

  @IsNumber()
  @IsOptional()
  closeMinute: number;
}

// DTO for Open Hours
class OpenHoursDto {
  @IsArray()
  @IsNumber({}, { each: true })
  @IsOptional()
  daysOfTheWeek: number[]; // Array of numbers representing days (e.g., 0 for Sunday, 1 for Monday, etc.)

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OpenHourTimeSlotDto)
  @IsOptional()
  hours: OpenHourTimeSlotDto[]; // Array of time slots for each day
}

// Main DTO for Calendar configuration
export class CalendarDto {
  @IsBoolean()
  @IsOptional()
  isActive: boolean;

  @IsNumber()
  @IsOptional()
  appoinmentPerDay?: number;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CalendarNotificationDto)
  @IsOptional()
  notifications: CalendarNotificationDto[];

  @IsString()
  @IsOptional()
  locationId: string;

  @IsString()
  @IsOptional()
  groupId: string;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => TeamMemberDto)
  @IsOptional()
  teamMembers: TeamMemberDto[];

  @Type(() => RecurringDto)
  @IsOptional()
  recurring: RecurringDto;

  @IsNumber()
  @IsOptional()
  priority: number;

  @IsEnum(MeetingLocationType)
  @IsOptional()
  meetingLocationType: MeetingLocationType;

  @IsEnum(EventType)
  @IsOptional()
  eventType: EventType;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsOptional()
  description: string;

  @IsString()
  @IsOptional()
  slug: string;

  @IsString()
  @IsOptional()
  widgetSlug: string;

  @IsEnum(CalendarType)
  @IsOptional()
  calendarType: CalendarType;

  @IsEnum(WidgetType)
  @IsOptional()
  widgetType: WidgetType;

  @IsString()
  @IsOptional()
  eventTitle: string;

  @IsString()
  @IsOptional()
  eventColor: string;

  @IsNumber()
  @IsOptional()
  slotDuration: number;

  @IsString()
  @IsOptional()
  slotDurationUnit: string;

  @IsNumber()
  @IsOptional()
  slotInterval: number;

  @IsString()
  @IsOptional()
  slotIntervalUnit: string;

  @IsNumber()
  @IsOptional()
  appoinmentPerSlot: number;

  @IsBoolean()
  @IsOptional()
  allowMultipleBookings: boolean;

  @IsBoolean()
  @IsOptional()
  enableRecurring: boolean;

  @IsString()
  @IsOptional()
  timeZone: string;

  @IsString()
  @IsOptional()
  startDate: string; // Format: YYYY-MM-DD

  @IsString()
  @IsOptional()
  endDate: string; // Format: YYYY-MM-DD

  @IsString()
  @IsOptional()
  startTime: string; // Format: HH:mm

  @IsString()
  @IsOptional()
  endTime: string; // Format: HH:mm

  @IsBoolean()
  @IsOptional()
  allowReschedule: boolean;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => OpenHoursDto)
  @IsOptional()
  openHours: OpenHoursDto[]; // Array of open hours objects
}
