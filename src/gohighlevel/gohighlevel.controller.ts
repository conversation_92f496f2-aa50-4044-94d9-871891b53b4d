import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Req,
  UseGuards,
  Query,
  Body,
  Param,
} from '@nestjs/common';
import { GohighlevelService } from './gohighlevel.service';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { CreateEnrollmentDto } from 'src/enrollment/dto/create-enrollment.dto';
import { UpdateEnrollmentDto } from 'src/enrollment/dto/update-enrollment.dto';
import { CalendarDto } from './dto/createCalendarDto';

@Controller('ghl_auth')
@UseGuards(JwtAuthGuard)
export class GohighlevelController {
  constructor(private readonly gohighlevelService: GohighlevelService) {}

  // Contact Management
  @Post('contacts')
  async createContact(
    @Req() request: Request,
    @Body() contactData: any,
    @Query('updateContact') updateContact?: boolean,
  ) {
    const locationId = request['locationId'];
    return this.gohighlevelService.createContact(
      contactData,
      locationId,
      updateContact || false,
    );
  }

  @Get('contacts')
  async getAllContacts(
    @Req() request: Request,
    @Query('firstName') firstName?: string,
    @Query('lastName') lastName?: string,
    @Query('email') email?: string,
  ) {
    const locationId = request['locationId'];
    return this.gohighlevelService.getAllContacts(
      locationId,
      firstName,
      lastName,
      email,
    );
  }

  @Get('contacts/:contactId')
  async fetchContactDetails(
    @Req() request: Request,
    @Param('contactId') contactId: string,
  ) {
    const locationId = request['locationId'];
    return this.gohighlevelService.fetchContactDetails(locationId, contactId);
  }

  @Put('contacts/:contactId')
  async updateContact(
    @Req() request: Request,
    @Param('contactId') contactId: string,
    @Body() updateData: any,
  ) {
    const locationId = request['locationId'];
    return this.gohighlevelService.updateContact(
      locationId,
      contactId,
      updateData,
    );
  }

  // Contact Tags
  @Post('contacts/:contactId/tags')
  async addTagToContact(
    @Req() request: Request,
    @Param('contactId') contactId: string,
    @Body('tag') tag: string,
  ) {
    const studioId = request['studioId'];
    return this.gohighlevelService.addTagToContact(studioId, contactId, tag);
  }

  @Delete('contacts/:contactId/tags')
  async removeTagsFromContact(
    @Req() request: Request,
    @Param('contactId') contactId: string,
    @Body('tags') tags: string[],
  ) {
    const studioId = request['studioId'];
    return this.gohighlevelService.removeTagsFromContact(
      studioId,
      contactId,
      tags,
    );
  }

  // Contact Notes
  @Post('contacts/:contactId/notes')
  async addNotesToContact(
    @Req() request: Request,
    @Param('contactId') contactId: string,
    @Body('note') note: string,
  ) {
    const studioId = request['studioId'];
    return this.gohighlevelService.addNotesToContact(studioId, contactId, note);
  }

  // Product Management
  @Post('products')
  async createProduct(
    @Req() request: Request,
    @Body('name') name: string,
    @Body('description') description: string,
    @Body('metadata') metadata?: Record<string, string>,
  ) {
    const locationId = request['locationId'];
    return this.gohighlevelService.createProduct(
      name,
      locationId,
      description,
      metadata,
    );
  }

  @Get('products/:productId')
  async getProductById(
    @Req() request: Request,
    @Param('productId') productId: string,
  ) {
    const locationId = request['locationId'];
    return this.gohighlevelService.getProductById(locationId, productId);
  }

  @Post('products/:productId/price')
  async createPriceForAProduct(
    @Req() request: Request,
    @Param('productId') productId: string,
    @Body() priceBody: any,
  ) {
    const locationId = request['locationId'];
    return this.gohighlevelService.createPriceForAProduct(
      locationId,
      productId,
      priceBody,
    );
  }

  // Calendar Management
  @Post('calendars')
  async createCalendar(
    @Req() request: Request,
    @Body('name') name: string,
    @Body() createCalendarDetails: Partial<CalendarDto>,
  ) {
    const locationId = request['locationId'];
    return this.gohighlevelService.createCalendar(
      name,
      locationId,
      createCalendarDetails,
    );
  }

  @Get('calendars/:calendarId')
  async fetchCalendarDetails(
    @Req() request: Request,
    @Param('calendarId') calendarId: string,
  ) {
    const locationId = request['locationId'];
    return this.gohighlevelService.fetchCalendarDetails(locationId, calendarId);
  }

  @Put('calendars/:calendarId')
  async updateCalendar(
    @Req() request: Request,
    @Param('calendarId') calendarId: string,
    @Body() updateEnrollmentDto: UpdateEnrollmentDto,
  ) {
    const locationId = request['locationId'];
    return this.gohighlevelService.updateCalendar(
      calendarId,
      locationId,
      updateEnrollmentDto,
    );
  }

  @Post('calendars/:calendarId/block-slots')
  async blockCalendarSlots(
    @Req() request: Request,
    @Param('calendarId') calendarId: string,
    @Body() dto: CreateEnrollmentDto,
    @Body('start') start: string,
    @Body('end') end: string,
  ) {
    const locationId = request['locationId'];
    return this.gohighlevelService.blockCalendarSlots(
      locationId,
      calendarId,
      dto,
      start,
      end,
    );
  }

  // Appointment Management
  @Put('appointments/:eventId')
  async updateAppointment(
    @Req() request: Request,
    @Param('eventId') eventId: string,
    @Body() updateData: any,
  ) {
    const locationId = request['locationId'];
    return this.gohighlevelService.updateAppointment(
      locationId,
      eventId,
      updateData,
    );
  }

  // Location Tags
  @Get('location/tags')
  async getLocationTags(@Req() request: Request) {
    const studioId = request['studioId'];
    return this.gohighlevelService.getLocationTags(studioId);
  }

  @Post('location/tags')
  async addTagToLocation(@Req() request: Request, @Body('tag') tag: string) {
    const studioId = request['studioId'];
    return this.gohighlevelService.addTagToLocation(studioId, tag);
  }

  @Put('location/tags/:tagId')
  async updateLocationTag(
    @Req() request: Request,
    @Param('tagId') tagId: string,
    @Body('newTagName') newTagName: string,
  ) {
    const studioId = request['studioId'];
    return this.gohighlevelService.updateLocationTag(
      studioId,
      tagId,
      newTagName,
    );
  }

  @Delete('location/tags/:tagId')
  async deleteLocationTag(
    @Req() request: Request,
    @Param('tagId') tagId: string,
  ) {
    const studioId = request['studioId'];
    return this.gohighlevelService.deleteLocationTag(studioId, tagId);
  }

  // Location Management
  @Get('location/details')
  async getLocationDetails(@Req() request: Request) {
    const locationId = request['locationId'];
    return this.gohighlevelService.getLocationDetails(locationId);
  }

  @Get('location/custom-fields')
  async fetchCustomFields(@Req() request: Request) {
    const locationId = request['locationId'];
    return this.gohighlevelService.fetchCustomFields(locationId);
  }
}
