import {
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
  OnModuleDestroy,
  Inject,
} from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { Studio, StudioDocument } from 'src/database/schema/studio';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CalendarDayArrayIndexMap, CalendarDto } from './dto/createCalendarDto';
import { CreateEnrollmentDto } from 'src/enrollment/dto/create-enrollment.dto';
import {
  convertTo24HourTime,
  calculateSlotDuration,
} from 'src/utils/helperFunction';
import { UpdateEnrollmentDto } from '../enrollment/dto/update-enrollment.dto';
import type { Redis } from 'ioredis';

@Injectable()
export class GohighlevelService implements OnModuleDestroy {
  private readonly logger = new Logger(GohighlevelService.name);
  private tokenUrl: string;
  private clientId: string;
  private clientSecret: string;
  // Map to store refresh promises by locationId to prevent concurrent refreshes
  private refreshPromises: Map<string, Promise<string>> = new Map();

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    @InjectModel(Studio.name) private studioModel: Model<StudioDocument>,
    @Inject('REDIS_CLIENT') private readonly redis: Redis,
  ) {
    this.tokenUrl = this.configService.get<string>('OAUTH_TOKEN_URL');
    this.clientId = this.configService.get<string>('CLIENT_ID');
    this.clientSecret = this.configService.get<string>('CLIENT_SECRET');

    this.logger.log('Redis client injected for token refresh locking');
  }

  /**
   * Helper method to make HTTP requests with automatic token refresh on 401/403 errors
   */
  public async makeHttpRequestWithRetry(
    method: 'get' | 'post' | 'put' | 'delete',
    url: string,
    locationId: string,
    data?: any,
    customHeaders?: any,
  ): Promise<any> {
    const studio = await this.studioModel.findOne({ locationId }).exec();
    if (!studio) {
      throw new HttpException('Studio not found', HttpStatus.NOT_FOUND);
    }

    let access_token = studio.access_token;
    let headers = {
      Accept: 'application/json',
      Authorization: `Bearer ${access_token}`,
      Version: '2021-07-28',
      ...customHeaders,
    };

    try {
      const response = await this.executeHttpRequest(
        method,
        url,
        headers,
        data,
      );
      return response.data;
    } catch (error) {
      // Handle 401 codes by refreshing token and retrying
      if (error.response?.status === 401) {
        try {
          access_token = await this.refereshToken(locationId);
          headers = {
            Accept: 'application/json',
            Authorization: `Bearer ${access_token}`,
            Version: '2021-07-28',
            ...customHeaders,
          };

          const retryResponse = await this.executeHttpRequest(
            method,
            url,
            headers,
            data,
          );
          return retryResponse.data;
        } catch (retryError) {
          throw retryError;
        }
      }
      throw error;
    }
  }

  /**
   * Helper method for requests that use studioId instead of locationId
   */
  private async makeHttpRequestWithRetryByStudioId(
    method: 'get' | 'post' | 'put' | 'delete',
    url: string,
    studioId: string,
    data?: any,
    customHeaders?: any,
  ): Promise<any> {
    const studio = await this.studioModel.findById({ _id: studioId }).exec();
    if (!studio) {
      throw new HttpException('Studio not found', HttpStatus.NOT_FOUND);
    }

    return this.makeHttpRequestWithRetry(
      method,
      url,
      studio.locationId,
      data,
      customHeaders,
    );
  }

  /**
   * Execute HTTP request based on method
   */
  private async executeHttpRequest(
    method: 'get' | 'post' | 'put' | 'delete',
    url: string,
    headers: any,
    data?: any,
  ): Promise<any> {
    switch (method) {
      case 'get':
        return await firstValueFrom(this.httpService.get(url, { headers }));
      case 'post':
        return await firstValueFrom(
          this.httpService.post(url, data, { headers }),
        );
      case 'put':
        return await firstValueFrom(
          this.httpService.put(url, data, { headers }),
        );
      case 'delete':
        return await firstValueFrom(
          this.httpService.delete(url, { headers, data }),
        );
      default:
        throw new Error(`Unsupported HTTP method: ${method}`);
    }
  }

  async refereshToken(locationId: string): Promise<any> {
    const lockKey = `ghl:token_refresh_lock:${locationId}`;
    const lockTTL = 30000; // 30 seconds
    const lockValue = `${Date.now()}_${Math.random()}`; // Unique value for this instance
    const maxRetries = 50; // Max 5 seconds of waiting (50 * 100ms)
    let retries = 0;

    // Try to acquire lock with retries
    while (retries < maxRetries) {
      try {
        // Try to set lock only if it doesn't exist (NX) with expiration (PX)
        const lockAcquired = await this.redis.set(
          lockKey,
          lockValue,
          'PX',
          lockTTL,
          'NX',
        );

        if (lockAcquired === 'OK') {
          // Lock acquired, perform refresh
          try {
            this.logger.log(
              `Acquired refresh lock for location: ${locationId}`,
            );
            return await this.performTokenRefresh(locationId);
          } finally {
            // Release lock only if we still own it (to prevent releasing someone else's lock)
            const currentValue = await this.redis.get(lockKey);
            if (currentValue === lockValue) {
              await this.redis.del(lockKey);
              this.logger.log(
                `Released refresh lock for location: ${locationId}`,
              );
            }
          }
        }

        // Lock not acquired, another instance is refreshing
        this.logger.log(
          `Waiting for existing refresh to complete for location: ${locationId} (retry ${retries + 1})`,
        );

        // Wait a bit before retrying
        await new Promise((resolve) => setTimeout(resolve, 100));
        retries++;

        // Check if the refresh might have completed
        if (retries % 10 === 0) {
          // Every 1 second, check if we have fresh tokens
          const studio = await this.studioModel.findOne({ locationId }).exec();
          if (studio) {
            // Check if token was recently updated (within last 5 seconds)
            const studioDoc = studio as any; // Access document properties
            if (studioDoc.updatedAt) {
              const tokenAge =
                Date.now() - new Date(studioDoc.updatedAt).getTime();
              if (tokenAge < 5000) {
                this.logger.log(
                  `Found recently refreshed token for location: ${locationId}`,
                );
                return studio.access_token;
              }
            }
          }
        }
      } catch (error) {
        this.logger.error(
          `Redis error during token refresh lock: ${error.message}`,
        );
        // Fall back to in-memory locking if Redis fails
        return this.refereshTokenWithInMemoryLock(locationId);
      }
    }

    // If we couldn't acquire lock after all retries, try to get the current token
    const studio = await this.studioModel.findOne({ locationId }).exec();
    if (studio && studio.access_token) {
      this.logger.warn(
        `Using existing token after lock timeout for location: ${locationId}`,
      );
      return studio.access_token;
    }

    throw new HttpException(
      'Unable to refresh token: lock timeout',
      HttpStatus.SERVICE_UNAVAILABLE,
    );
  }

  // Fallback method using in-memory lock
  private async refereshTokenWithInMemoryLock(
    locationId: string,
  ): Promise<any> {
    // Check if there's already a refresh in progress for this location
    const existingRefresh = this.refreshPromises.get(locationId);
    if (existingRefresh) {
      this.logger.log(
        `Using existing refresh promise (in-memory) for location: ${locationId}`,
      );
      return existingRefresh;
    }

    // Create a new refresh promise
    const refreshPromise = this.performTokenRefresh(locationId).finally(() => {
      // Clean up the promise after it completes
      this.refreshPromises.delete(locationId);
    });

    // Store the promise to prevent concurrent refreshes
    this.refreshPromises.set(locationId, refreshPromise);

    return refreshPromise;
  }

  private async performTokenRefresh(locationId: string): Promise<string> {
    const studio = await this.studioModel.findOne({ locationId }).exec();
    if (!studio) {
      throw new HttpException('Studio not found', HttpStatus.NOT_FOUND);
    }

    const refreshToken = studio.refresh_token;
    const payload = {
      client_id: this.clientId,
      client_secret: this.clientSecret,
      grant_type: 'refresh_token',
      refresh_token: refreshToken,
    };

    try {
      const response = await firstValueFrom(
        this.httpService.post(this.tokenUrl, payload, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }),
      );

      this.logger.log(
        `Successfully refreshed token for location: ${locationId}`,
      );

      // Use findOneAndUpdate to atomically update the tokens
      // This prevents race conditions when saving
      await this.studioModel
        .findOneAndUpdate(
          { locationId },
          {
            $set: {
              access_token: response.data.access_token,
              refresh_token: response.data.refresh_token,
            },
          },
          { new: true },
        )
        .exec();

      return response.data.access_token;
    } catch (error) {
      this.logger.error(
        `Failed to refresh token for location ${locationId}:`,
        error.response?.data || error.message,
      );

      if (error.response?.status === 400) {
        await this.sendEnhancedSlackNotification({
          type: 'TOKEN_REFRESH_FAILED',
          locationId,
          studio,
          error,
          method: 'performTokenRefresh',
          url: this.tokenUrl,
          payload: { ...payload, refresh_token: '***HIDDEN***' }, // Hide sensitive data
          timestamp: new Date().toISOString(),
        });
      }
      throw new Error(
        `Failed to refresh token: ${error.response?.data?.error || 'Unknown error'}`,
      );
    }
  }

  async refreshToken_ghl(locationId): Promise<any> {
    const lockKey = `ghl:token_refresh_lock:${locationId}`;
    const lockTTL = 30000; // 30 seconds
    const lockValue = `${Date.now()}_${Math.random()}_ghl`; // Unique value for this instance
    const maxRetries = 50; // Max 5 seconds of waiting (50 * 100ms)
    let retries = 0;

    // Try to acquire lock with retries
    while (retries < maxRetries) {
      try {
        // Try to set lock only if it doesn't exist (NX) with expiration (PX)
        const lockAcquired = await this.redis.set(
          lockKey,
          lockValue,
          'PX',
          lockTTL,
          'NX',
        );

        if (lockAcquired === 'OK') {
          // Lock acquired, perform refresh
          try {
            this.logger.log(
              `Acquired refresh lock for location (ghl): ${locationId}`,
            );
            await this.performTokenRefreshGhl(locationId);

            // Fetch updated studio data
            const studio = await this.studioModel
              .findOne({ locationId })
              .exec();
            const subaccountName = await this.getSubaccountName(
              studio.locationId,
              studio.access_token,
            );
            return {
              ...studio.toObject(),
              subaccountName,
            };
          } finally {
            // Release lock only if we still own it
            const currentValue = await this.redis.get(lockKey);
            if (currentValue === lockValue) {
              await this.redis.del(lockKey);
              this.logger.log(
                `Released refresh lock for location (ghl): ${locationId}`,
              );
            }
          }
        }

        // Lock not acquired, another instance is refreshing
        this.logger.log(
          `Waiting for existing refresh to complete for location (ghl): ${locationId} (retry ${retries + 1})`,
        );

        // Wait a bit before retrying
        await new Promise((resolve) => setTimeout(resolve, 100));
        retries++;

        // Check if the refresh might have completed
        if (retries % 10 === 0) {
          // Every 1 second, check if we have fresh tokens
          const studio = await this.studioModel.findOne({ locationId }).exec();
          if (studio) {
            // Check if token was recently updated (within last 5 seconds)
            const studioDoc = studio as any;
            if (studioDoc.updatedAt) {
              const tokenAge =
                Date.now() - new Date(studioDoc.updatedAt).getTime();
              if (tokenAge < 5000) {
                this.logger.log(
                  `Found recently refreshed token for location (ghl): ${locationId}`,
                );
                const subaccountName = await this.getSubaccountName(
                  studio.locationId,
                  studio.access_token,
                );
                return {
                  ...studio.toObject(),
                  subaccountName,
                };
              }
            }
          }
        }
      } catch (error) {
        this.logger.error(
          `Redis error during token refresh lock (ghl): ${error.message}`,
        );
        // Fall back to in-memory locking if Redis fails
        return this.refreshTokenGhlWithInMemoryLock(locationId);
      }
    }

    // If we couldn't acquire lock after all retries, try to get the current token
    const studio = await this.studioModel.findOne({ locationId }).exec();
    if (studio && studio.access_token) {
      this.logger.warn(
        `Using existing token after lock timeout for location (ghl): ${locationId}`,
      );
      const subaccountName = await this.getSubaccountName(
        studio.locationId,
        studio.access_token,
      );
      return {
        ...studio.toObject(),
        subaccountName,
      };
    }

    throw new HttpException(
      'Unable to refresh token: lock timeout',
      HttpStatus.SERVICE_UNAVAILABLE,
    );
  }

  // Fallback method using in-memory lock for refreshToken_ghl
  private async refreshTokenGhlWithInMemoryLock(
    locationId: string,
  ): Promise<any> {
    // Check if there's already a refresh in progress for this location
    const existingRefresh = this.refreshPromises.get(locationId);
    if (existingRefresh) {
      this.logger.log(
        `Using existing refresh promise for location (ghl): ${locationId}`,
      );
      // Wait for the existing refresh to complete, then fetch the updated studio data
      await existingRefresh;
      const studio = await this.studioModel.findOne({ locationId }).exec();
      const subaccountName = await this.getSubaccountName(
        studio.locationId,
        studio.access_token,
      );
      return {
        ...studio.toObject(),
        subaccountName,
      };
    }

    // Create a new refresh promise
    const refreshPromise = this.performTokenRefreshGhl(locationId).finally(
      () => {
        // Clean up the promise after it completes
        this.refreshPromises.delete(locationId);
      },
    );

    // Store the promise to prevent concurrent refreshes
    this.refreshPromises.set(locationId, refreshPromise);

    try {
      await refreshPromise;
      const studio = await this.studioModel.findOne({ locationId }).exec();
      const subaccountName = await this.getSubaccountName(
        studio.locationId,
        studio.access_token,
      );
      return {
        ...studio.toObject(),
        subaccountName,
      };
    } catch (error) {
      throw error;
    }
  }

  private async performTokenRefreshGhl(locationId: string): Promise<string> {
    const studio = await this.studioModel.findOne({ locationId }).exec();
    if (!studio) {
      throw new HttpException('Studio not found', HttpStatus.NOT_FOUND);
    }

    const refreshToken = studio.refresh_token;
    const payload = {
      client_id: this.clientId,
      client_secret: this.clientSecret,
      grant_type: 'refresh_token',
      refresh_token: refreshToken,
    };

    try {
      const response = await firstValueFrom(
        this.httpService.post(this.tokenUrl, payload, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }),
      );

      this.logger.log(
        `Successfully refreshed token for location (ghl): ${locationId}`,
      );

      // Use findOneAndUpdate to atomically update the tokens
      await this.studioModel
        .findOneAndUpdate(
          { locationId },
          {
            $set: {
              access_token: response.data.access_token,
              refresh_token: response.data.refresh_token,
            },
          },
          { new: true },
        )
        .exec();

      return response.data.access_token;
    } catch (error) {
      this.logger.error(
        `Failed to refresh token for location ${locationId} (ghl):`,
        error.response?.data || error.message,
      );

      if (error.response?.status === 400) {
        await this.sendEnhancedSlackNotification({
          type: 'TOKEN_REFRESH_FAILED',
          locationId,
          studio,
          error,
          method: 'performTokenRefreshGhl',
          url: this.tokenUrl,
          payload: { ...payload, refresh_token: refreshToken },
          timestamp: new Date().toISOString(),
        });
      }
      throw new Error(
        `Failed to refresh token: ${error.response?.data?.error || 'Unknown error'}`,
      );
    }
  }

  async getSubaccountName(locationId, accessToken) {
    const url = `https://services.leadconnectorhq.com/locations/${locationId}`;

    const headers = {
      Authorization: `Bearer ${accessToken}`,
      Version: '2021-07-28',
      Accept: 'application/json',
    };

    try {
      const response = await firstValueFrom(
        this.httpService.get(url, { headers }),
      );
      console.log(response.data.location.name);
      return response.data.location.name;
    } catch (error) {
      console.error('Error:', error);
      throw error;
    }
  }

  async exchangeCodeForToken(code: string): Promise<any> {
    const redisKey = `ghl:auth_code:${code}`;
    // Check if tokens for this code already exist in Redis
    const cachedTokens = await this.redis.get(redisKey);
    if (cachedTokens) {
      try {
        const parsed = JSON.parse(cachedTokens);
        // On cache hit, do NOT make any further API calls (including getSubaccountName),
        // simply return the cached value which includes subaccountName and tokens.
        this.logger.log(`Returning tokens from Redis for code: ${code}`);
        return parsed;
      } catch (e) {
        this.logger.warn(`Failed to parse cached tokens for code: ${code}`);
        // If parsing fails, continue to fetch new tokens
      }
    }

    const payload = {
      client_id: this.clientId || '67e92e3443252fde19e6cbd2-mb97z7n5',
      client_secret:
        this.clientSecret || '8f023df8-402d-4a4d-8208-1f539dc4d426',
      grant_type: 'authorization_code',
      code,
    };

    this.logger.log('ghl auth payload: ', payload);

    try {
      const response = await firstValueFrom(
        this.httpService.post(this.tokenUrl, payload, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }),
      );
      this.logger.log('Response from api call: ', response.data);
      const subaccountName = await this.getSubaccountName(
        response.data.locationId,
        response.data.access_token,
      );
      const studioDetails = {
        ...response.data,
        subaccountName,
      };
      this.logger.log('subaccount: ', studioDetails);
      // Store in Redis for future requests (set TTL to 1 day = 86400 seconds)
      await this.redis.set(
        redisKey,
        JSON.stringify(studioDetails),
        'EX',
        86400,
      );
      return studioDetails; // Optionally, you can save this data later
    } catch (error) {
      throw new Error(
        `Failed to exchange code for token: ${error.response?.data?.error?.error_description || error.message}`,
      );
    }
  }

  async createContact(
    contactData: any,
    locationId: string,
    updateContact: boolean = false,
  ) {
    try {
      const url = 'https://services.leadconnectorhq.com/contacts';
      const payload = {
        ...contactData,
        locationId,
      };

      try {
        return await this.makeHttpRequestWithRetry(
          'post',
          url,
          locationId,
          payload,
        );
      } catch (error) {
        if (error.response?.data?.message?.includes('duplicate')) {
          if (updateContact) {
            const updateUrl = `https://services.leadconnectorhq.com/contacts/${error.response?.data?.meta?.contactId}`;
            try {
              return await this.makeHttpRequestWithRetry(
                'put',
                updateUrl,
                locationId,
                payload,
              );
            } catch (updateError) {
              const encodedEmail = encodeURIComponent(contactData.email);
              const encodedPhone = encodeURIComponent(contactData.phone);
              const searchUrl = `https://services.leadconnectorhq.com/contacts/search/duplicate?locationId=${locationId}&email=${encodedEmail}&number=${encodedPhone}`;

              const duplicateResponse = await this.makeHttpRequestWithRetry(
                'get',
                searchUrl,
                locationId,
              );
              if (duplicateResponse.contact) {
                return duplicateResponse.contact;
              }
            }
          }

          const encodedEmail = encodeURIComponent(contactData.email);
          const encodedPhone = encodeURIComponent(contactData.phone);
          const searchUrl = `https://services.leadconnectorhq.com/contacts/search/duplicate?locationId=${locationId}&email=${encodedEmail}&number=${encodedPhone}`;

          const duplicateResponse = await this.makeHttpRequestWithRetry(
            'get',
            searchUrl,
            locationId,
          );
          if (duplicateResponse.contact) {
            return duplicateResponse.contact;
          }
        }

        this.logger.error(`Error creating contact: ${error.message}`);
        throw new HttpException(
          'error creating contact in ghl',
          error.response?.data.message || HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    } catch (error) {
      throw new HttpException(
        'error creating contact in ghl',
        error.response?.data.message || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createProduct(
    name: string,
    locationId: string,
    description: string,
    metadata?: Record<string, string>,
  ) {
    try {
      const url = 'https://services.leadconnectorhq.com/products/';
      const body = {
        name: name,
        locationId: locationId,
        productType: 'SERVICE',
        description,
      };

      return await this.makeHttpRequestWithRetry('post', url, locationId, body);
    } catch (error) {
      const errorMessage = error.response?.data || error.message;
      throw new HttpException(
        'error creating product in ghl',
        error,
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async createCalendar(
    name: string,
    locationId: string,
    createCalendarDetails: Partial<CalendarDto>,
  ) {
    try {
      const url = 'https://services.leadconnectorhq.com/calendars/';
      const body = {
        ...createCalendarDetails,
        name: name,
        locationId: locationId,
      };

      const response = await this.makeHttpRequestWithRetry(
        'post',
        url,
        locationId,
        body,
      );
      return response.calendar.id;
    } catch (error) {
      const errorMessage = error.response?.data || error.message;
      this.logger.error(
        `Error: ${errorMessage}`,
        error.response ? error.response.data : error.message,
      );
    }
  }

  async createPriceForAProduct(locationId, productId: string, priceBody) {
    try {
      const url = `https://services.leadconnectorhq.com/products/${productId}/price`;
      return await this.makeHttpRequestWithRetry(
        'post',
        url,
        locationId,
        priceBody,
      );
    } catch (error) {
      const errorMessage = error.response?.data || error.message;
      throw new HttpException(
        'error creating price for a product in ghl',
        error,
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async blockCalendarSlots(
    locationId,
    calendarId,
    dto: CreateEnrollmentDto,
    start,
    end,
  ): Promise<any> {
    const calendarBlockBody = {
      calendarId: calendarId,
      locationId: locationId,
      startTime: start,
      endTime: end,
      title: dto.title,
    };

    try {
      const url =
        'https://services.leadconnectorhq.com/calendars/events/block-slots';
      return await this.makeHttpRequestWithRetry(
        'post',
        url,
        locationId,
        calendarBlockBody,
      );
    } catch (error) {
      this.logger.error(
        'Error:',
        error.response ? error.response.data : error.message,
      );
      // throw new Error('Failed to block calendar slots');
    }
  }

  //add tag to contact
  async addTagToContact(studioId: string, contactId: string, tag: string) {
    try {
      const url = `https://services.leadconnectorhq.com/contacts/${contactId}/tags`;
      const body = {
        tags: [tag],
      };

      return await this.makeHttpRequestWithRetryByStudioId(
        'post',
        url,
        studioId,
        body,
      );
    } catch (error) {
      this.logger.error(
        `Error adding tag "${tag}" to contact ${contactId}`,
        error.response ? error.response.data : error.message,
      );
      // throw new Error('Failed to add tag to contact');
    }
  }

  //add notes to contact
  async addNotesToContact(studioId, contactId, note) {
    try {
      const studio = await this.studioModel.findById(studioId).exec();
      if (!studio) {
        throw new HttpException('Studio not found', HttpStatus.NOT_FOUND);
      }
      const access_token = await this.refereshToken(studio.locationId);
      const url = `https://services.leadconnectorhq.com/contacts/${contactId}/notes`;
      const body = {
        body: note,
      };

      return await this.makeHttpRequestWithRetryByStudioId(
        'post',
        url,
        studioId,
        body,
      );
    } catch (error) {
      console.error(
        'Error:',
        error.response ? error.response.data : error.message,
      );
      throw new Error('Failed to add notes to contact');
    }
  }

  async getNoteForContact(studioId: string, contactId: string, noteId: string) {
    try {
      const studio = await this.studioModel.findById(studioId).exec();
      if (!studio) {
        throw new HttpException('Studio not found', HttpStatus.NOT_FOUND);
      }
      const access_token = await this.refereshToken(studio.locationId);
      const url = `https://services.leadconnectorhq.com/contacts/${contactId}/notes/${noteId}`;
      const headers = {
        Accept: 'application/json',
        Authorization: `Bearer ${access_token}`,
        Version: '2021-07-28',
      };
      const response = await firstValueFrom(
        this.httpService.get(url, { headers }),
      );
      return response.data;
    } catch (error) {
      this.logger.error(
        `Error fetching note ${noteId} for contact ${contactId}`,
        error.response ? error.response.data : error.message,
      );
      throw new HttpException(
        'Failed to fetch note for contact',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateNoteForContact(
    studioId: string,
    contactId: string,
    noteId: string,
    noteBody: string,
  ) {
    try {
      const studio = await this.studioModel.findById(studioId).exec();
      if (!studio) {
        throw new HttpException('Studio not found', HttpStatus.NOT_FOUND);
      }
      const access_token = await this.refereshToken(studio.locationId);
      const url = `https://services.leadconnectorhq.com/contacts/${contactId}/notes/${noteId}`;
      const headers = {
        Accept: 'application/json',
        Authorization: `Bearer ${access_token}`,
        'Content-Type': 'application/json',
        Version: '2021-07-28',
      };
      const body = {
        body: noteBody,
      };
      const response = await firstValueFrom(
        this.httpService.put(url, body, { headers }),
      );
      return response.data;
    } catch (error) {
      this.logger.error(
        `Error updating note ${noteId} for contact ${contactId}`,
        error.response ? error.response.data : error.message,
      );
      throw new HttpException(
        'Failed to update note for contact',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async deleteNoteForContact(
    studioId: string,
    contactId: string,
    noteId: string,
  ) {
    try {
      const studio = await this.studioModel.findById(studioId).exec();
      if (!studio) {
        throw new HttpException('Studio not found', HttpStatus.NOT_FOUND);
      }
      const access_token = await this.refereshToken(studio.locationId);
      const url = `https://services.leadconnectorhq.com/contacts/${contactId}/notes/${noteId}`;
      const headers = {
        Accept: 'application/json',
        Authorization: `Bearer ${access_token}`,
        Version: '2021-07-28',
      };
      const response = await firstValueFrom(
        this.httpService.delete(url, { headers }),
      );
      return response.data;
    } catch (error) {
      this.logger.error(
        `Error deleting note ${noteId} for contact ${contactId}`,
        error.response ? error.response.data : error.message,
      );
      throw new HttpException(
        'Failed to delete note for contact',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAllNotesForContact(studioId: string, contactId: string) {
    try {
      const studio = await this.studioModel.findById(studioId).exec();
      if (!studio) {
        throw new HttpException('Studio not found', HttpStatus.NOT_FOUND);
      }
      const access_token = await this.refereshToken(studio.locationId);
      const url = `https://services.leadconnectorhq.com/contacts/${contactId}/notes`;
      const headers = {
        Accept: 'application/json',
        Authorization: `Bearer ${access_token}`,
        Version: '2021-07-28',
      };
      const response = await firstValueFrom(
        this.httpService.get(url, { headers }),
      );
      return response.data;
    } catch (error) {
      this.logger.error(
        `Error fetching notes for contact ${contactId}`,
        error.response ? error.response.data : error.message,
      );
      throw new HttpException(
        'Failed to fetch notes for contact',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getLocationTags(studioId: string) {
    try {
      const studio = await this.studioModel.findById(studioId).exec();
      if (!studio) {
        throw new HttpException('Studio not found', HttpStatus.NOT_FOUND);
      }

      const url = `https://services.leadconnectorhq.com/locations/${studio.locationId}/tags`;
      return await this.makeHttpRequestWithRetry('get', url, studio.locationId);
    } catch (error) {
      console.error(
        'Error:',
        error.response ? error.response.data : error.message,
      );
      throw new HttpException(
        'Failed to get location tags',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  // add tag to location
  async addTagToLocation(studioId, tag) {
    try {
      const studio = await this.studioModel.findById(studioId).exec();
      if (!studio) {
        throw new HttpException('Studio not found', HttpStatus.NOT_FOUND);
      }

      const url = `https://services.leadconnectorhq.com/locations/${studio.locationId}/tags`;
      const body = {
        name: tag,
      };

      return await this.makeHttpRequestWithRetry(
        'post',
        url,
        studio.locationId,
        body,
      );
    } catch (error) {
      this.logger.error(
        `Error adding tag ${tag} to location ${studioId}`,
        error.response ? error.response.data : error.message,
      );

      // Check for specific 400 error about duplicate tag
      if (
        error.response?.status === 400 &&
        error.response?.data?.message === 'The tag name is already exist.'
      ) {
        const tags = await this.getLocationTags(studioId);
        const existingTag = tags.tags.find(
          (t) => t.name.toLowerCase() === tag.toLowerCase(),
        );
        return existingTag;
      }
    }
  }

  async getAllContacts(
    locationId: string,
    firstName?: string,
    lastName?: string,
    email?: string,
  ) {
    try {
      const locationId_object = Types.ObjectId.createFromHexString(locationId);
      const studio = await this.studioModel
        .findOne({ _id: locationId_object })
        .exec();
      if (!studio) {
        throw new HttpException('Studio not found', HttpStatus.NOT_FOUND);
      }

      const url = 'https://services.leadconnectorhq.com/contacts/search';

      const searchBody: any = {
        locationId: studio.locationId,
        page: 1,
        pageLimit: 50,
      };

      // Add search filters if firstName or lastName is provided
      if (firstName || lastName || email) {
        searchBody.filters = [];

        if (firstName) {
          searchBody.filters.push({
            field: 'firstName',
            operator: 'contains',
            value: firstName,
          });
        }

        if (lastName) {
          searchBody.filters.push({
            field: 'lastName',
            operator: 'contains',
            value: lastName,
          });
        }
      }

      if (email) {
        searchBody.filters.push({
          field: 'email',
          operator: 'contains',
          value: email,
        });
      }

      return await this.makeHttpRequestWithRetry(
        'post',
        url,
        studio.locationId,
        searchBody,
      );
    } catch (error) {
      throw new HttpException(
        'error searching contacts in ghl',
        error.response?.data.message || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async searchContactByEmail(
    studioId: string,
    email: string,
  ): Promise<string | null> {
    try {
      this.logger.log(
        `Searching for contact with email: ${email} in studio: ${studioId}`,
      );

      const studio = await this.studioModel.findById(studioId).exec();
      if (!studio) {
        throw new HttpException('Studio not found', HttpStatus.NOT_FOUND);
      }

      const url = 'https://services.leadconnectorhq.com/contacts/search';

      const searchBody = {
        locationId: studio.locationId,
        page: 1,
        pageLimit: 1, // We only need the first match
        filters: [
          {
            field: 'email',
            operator: 'eq', // Use exact match for email
            value: email,
          },
        ],
      };

      const response = await this.makeHttpRequestWithRetry(
        'post',
        url,
        studio.locationId,
        searchBody,
      );

      if (response.contacts && response.contacts.length > 0) {
        const contactId = response.contacts[0].id;
        this.logger.log(
          `Found contact with ID: ${contactId} for email: ${email}`,
        );
        return contactId;
      }

      this.logger.warn(`No contact found for email: ${email}`);
      return null;
    } catch (error) {
      this.logger.error(`Error searching contact by email: ${email}`, error);
      throw new HttpException(
        'Error searching contact by email in GHL',
        error.response?.data.message || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async addTagToContactByEmail(
    studioId: string,
    email: string,
    tag: string,
  ): Promise<boolean> {
    try {
      this.logger.log(`Adding tag "${tag}" to contact with email: ${email}`);

      const contactId = await this.searchContactByEmail(studioId, email);

      if (!contactId) {
        this.logger.warn(
          `Cannot add tag "${tag}" - no contact found for email: ${email}`,
        );
        return false;
      }

      await this.addTagToContact(studioId, contactId, tag);
      this.logger.log(
        `Successfully added tag "${tag}" to contact with email: ${email}`,
      );
      return true;
    } catch (error) {
      this.logger.error(
        `Failed to add tag "${tag}" to contact with email: ${email}`,
        error,
      );
      return false;
    }
  }

  async updateContact(locationId: string, contactId: string, updateData: any) {
    try {
      const url = `https://services.leadconnectorhq.com/contacts/${contactId}`;
      return await this.makeHttpRequestWithRetry(
        'put',
        url,
        locationId,
        updateData,
      );
    } catch (error) {
      throw new HttpException(
        'Error updating contact in GHL',
        error.response?.data.message || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private async sendEnhancedSlackNotification(notificationData: {
    type: string;
    locationId: string;
    studio: any;
    error: any;
    method: string;
    url: string;
    payload?: any;
    timestamp: string;
  }) {
    const slackWebhookUrl =
      '*********************************************************************************';

    const { type, locationId, studio, error, method, url, payload, timestamp } =
      notificationData;

    const locationName = studio?.subaccountName || 'Unknown';

    const errorDetails = {
      status: error.response?.status || 'unknown',
      statusText: error.response?.statusText || 'unknown',
      errorCode: error.response?.data?.error || 'unknown',
      errorDescription:
        error.response?.data?.error_description || 'No description',
      message: error.message || 'No message',
    };

    try {
      const slackPayload = {
        text: `🚨 ${type}: ${locationName} - Token Refresh Failed`,
        blocks: [
          {
            type: 'header',
            text: {
              type: 'plain_text',
              text: `🚨 ${type} - Action Required`,
              emoji: true,
            },
          },
          {
            type: 'section',
            fields: [
              {
                type: 'mrkdwn',
                text: `*Studio:*\n${locationName}`,
              },
              {
                type: 'mrkdwn',
                text: `*Location ID:*\n${locationId}`,
              },
              {
                type: 'mrkdwn',
                text: `*Error:*\n${errorDetails.status} ${errorDetails.statusText}`,
              },
              {
                type: 'mrkdwn',
                text: `*Time:*\n${timestamp}`,
              },
            ],
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `*Error Details:*\n${errorDetails.errorDescription}`,
            },
          },
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: '*Action Required:*\nStudio needs to re-authenticate GoHighLevel connection.',
            },
          },
        ],
      };

      const slackResponse = await firstValueFrom(
        this.httpService.post(slackWebhookUrl, slackPayload),
      );

      this.logger.log(
        `Enhanced Slack notification sent successfully for ${locationId}`,
      );
    } catch (slackError) {
      this.logger.error(
        'Failed to send enhanced Slack notification:',
        slackError.message,
      );
    }
  }

  async updateCalendar(
    calendarId: string,
    locationId: string,
    updateEnrollmentDto: UpdateEnrollmentDto,
  ) {
    try {
      const studioObjectId = Types.ObjectId.createFromHexString(locationId);
      const studio = await this.studioModel
        .findOne({ _id: studioObjectId })
        .exec();
      if (!studio) {
        throw new HttpException('Studio not found', HttpStatus.NOT_FOUND);
      }

      // Map availability to GHL openHours format
      const openHours = updateEnrollmentDto.availability.map((day) => {
        const { hour: openHour, minute: openMinute } = convertTo24HourTime(
          day.startTime,
        );
        const { hour: closeHour, minute: closeMinute } = convertTo24HourTime(
          day.endTime,
        );
        return {
          daysOfTheWeek: [
            CalendarDayArrayIndexMap[
              day.day as keyof typeof CalendarDayArrayIndexMap
            ],
          ],
          hours: [
            {
              openHour,
              openMinute,
              closeHour,
              closeMinute,
            },
          ],
        };
      });

      const slotDuration = calculateSlotDuration(
        updateEnrollmentDto.availability[0].startTime,
        updateEnrollmentDto.availability[0].endTime,
      );

      const url = `https://services.leadconnectorhq.com/calendars/${calendarId}`;
      const body = {
        name: updateEnrollmentDto.title,
        openHours,
        slotDuration,
        appoinmentPerSlot: updateEnrollmentDto.maxSize,
      };

      return await this.makeHttpRequestWithRetry(
        'put',
        url,
        studio.locationId,
        body,
      );
    } catch (error) {
      const errorMessage = error.response?.data?.message || error.message;
      throw new HttpException(
        `Error updating calendar open hours: ${errorMessage}`,
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateLocationTag(studioId: string, tagId: string, newTagName: string) {
    try {
      const studio = await this.studioModel.findById({ _id: studioId }).exec();
      if (!studio) {
        throw new HttpException('Studio not found', HttpStatus.NOT_FOUND);
      }

      const url = `https://services.leadconnectorhq.com/locations/${studio.locationId}/tags/${tagId}`;
      const body = {
        name: newTagName,
      };

      return await this.makeHttpRequestWithRetry(
        'put',
        url,
        studio.locationId,
        body,
      );
    } catch (error) {
      console.error(
        'Error:',
        error.response ? error.response.data : error.message,
      );
      throw new HttpException(
        'Failed to update location tag',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async deleteLocationTag(studioId: string, tagId: string) {
    try {
      const studio = await this.studioModel.findById({ _id: studioId }).exec();
      if (!studio) {
        throw new HttpException('Studio not found', HttpStatus.NOT_FOUND);
      }

      const url = `https://services.leadconnectorhq.com/locations/${studio.locationId}/tags/${tagId}`;
      return await this.makeHttpRequestWithRetry(
        'delete',
        url,
        studio.locationId,
      );
    } catch (error) {
      this.logger.error(
        `Failed to remove tags ${tagId} from ${studioId}: ${error.response?.data || error.message}`,
      );
    }
  }

  async removeTagsFromContact(
    studioId: string,
    contactId: string,
    tags: string[],
  ) {
    try {
      this.logger.debug(
        `Attempting to remove tags ${tags.join(', ')} from contact ${contactId}`,
      );

      const url = `https://services.leadconnectorhq.com/contacts/${contactId}/tags`;
      const body = {
        tags: tags,
      };

      await this.makeHttpRequestWithRetryByStudioId(
        'delete',
        url,
        studioId,
        body,
      );
      this.logger.log(`Successfully removed tags from contact ${contactId}`);
    } catch (error) {
      this.logger.error(
        `Failed to remove tags ${tags.join(', ')} from contact ${contactId}: ${error.response?.data || error.message}`,
      );
    }
  }

  async getProductById(locationId: string, productId: string) {
    try {
      const url = `https://services.leadconnectorhq.com/products/${productId}?locationId=${locationId}`;
      return await this.makeHttpRequestWithRetry('get', url, locationId);
    } catch (error) {
      this.logger.error(
        `Error getting product by ID: ${error.response?.data || error.message}`,
      );
      throw new HttpException(
        'Error getting product from GHL',
        error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async fetchCustomFields(locationId) {
    try {
      const url = `https://services.leadconnectorhq.com/locations/${locationId}/customFields`;
      return await this.makeHttpRequestWithRetry('get', url, locationId);
    } catch (error) {
      console.error(
        'Error:',
        error.response ? error.response.data : error.message,
      );
      throw new Error('Failed to fetch custom fields');
    }
  }

  async fetchContactDetails(locationId, contactId) {
    try {
      const url = `https://services.leadconnectorhq.com/contacts/${contactId}`;
      return await this.makeHttpRequestWithRetry('get', url, locationId);
    } catch (error) {
      console.error(
        'Error:',
        error.response ? error.response.data : error.message,
      );
      throw new Error('Failed to fetch contact details');
    }
  }

  async fetchCalendarDetails(locationId, calendarId) {
    try {
      const url = `https://services.leadconnectorhq.com/calendars/${calendarId}`;
      return await this.makeHttpRequestWithRetry('get', url, locationId);
    } catch (error) {
      console.error(
        'Error:',
        error.response ? error.response.data : error.message,
      );
      throw new Error('Failed to fetch calendar details');
    }
  }

  async getLocationDetails(locationId) {
    try {
      const url = `https://services.leadconnectorhq.com/locations/${locationId}`;
      return await this.makeHttpRequestWithRetry('get', url, locationId);
    } catch (error) {
      console.error(
        'Error:',
        error.response ? error.response.data : error.message,
      );
      throw new Error('Failed to fetch location details');
    }
  }

  async updateAppointment(locationId, eventId, updateData) {
    try {
      const url = `https://services.leadconnectorhq.com/calendars/events/appointments/${eventId}`;
      return await this.makeHttpRequestWithRetry(
        'put',
        url,
        locationId,
        updateData,
      );
    } catch (error) {
      console.error('Error:', error.response ? error.response : error.message);
      throw new Error('Failed to update appointment');
    }
  }

  async onModuleDestroy() {
    // Clean up Redis connection
    if (this.redis) {
      this.logger.log('Closing Redis connection...');
      await this.redis.quit();
    }
  }

  async removeTagsFromContactByEmail(
    studioId: string,
    email: string,
    tags: string[],
  ): Promise<boolean> {
    try {
      this.logger.log(
        `Removing tags [${tags.join(', ')}] from contact with email: ${email}`,
      );

      const contactId = await this.searchContactByEmail(studioId, email);

      if (!contactId) {
        this.logger.warn(
          `Cannot remove tags - no contact found for email: ${email}`,
        );
        return false;
      }

      await this.removeTagsFromContact(studioId, contactId, tags);
      this.logger.log(
        `Successfully removed tags [${tags.join(', ')}] from contact with email: ${email}`,
      );
      return true;
    } catch (error) {
      this.logger.error(
        `Failed to remove tags [${tags.join(', ')}] from contact with email: ${email}`,
        error,
      );
      return false;
    }
  }
}
