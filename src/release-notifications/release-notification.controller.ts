import {
  <PERSON>,
  Get,
  Post,
  Body,
  Headers,
  HttpStatus,
  HttpException,
  Logger,
  Param,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ReleaseNotificationService } from './release-notification.service';
import { createHmac } from 'crypto';

interface RepoReleases {
  [key: string]: {
    releases: Array<{
      tag: string;
      title: string;
      description: string;
      createdAt: string;
      htmlUrl: string;
    }>;
  };
}

interface GitHubReleasePayload {
  action: string;
  repository: {
    name: string;
  };
  release: {
    tag_name: string;
    name: string;
    body: string;
    html_url: string;
    created_at: string;
  };
}

@Controller('release-notification')
export class ReleaseNotificationController {
  private readonly logger = new Logger(ReleaseNotificationController.name);

  constructor(
    private readonly releaseService: ReleaseNotificationService,
    private readonly configService: ConfigService,
  ) {}

  @Post('webhook')
  async handleWebhook(
    @Headers('x-hub-signature-256') signature: string,
    @Headers('x-github-event') event: string,
    @Body() payload: GitHubReleasePayload,
  ) {
    try {
      if (payload.action === 'created' || payload.action === 'edited') {
        // Verify webhook signature
        const webhookSecret = this.configService.get<string>(
          'GITHUB_WEBHOOK_SECRET',
        );
        const computedSignature = `sha256=${createHmac('sha256', webhookSecret)
          .update(JSON.stringify(payload))
          .digest('hex')}`;

        if (signature !== computedSignature) {
          this.logger.warn('Invalid webhook signature received');
          throw new HttpException('Invalid signature', HttpStatus.UNAUTHORIZED);
        }

        // Handle only release events
        if (event !== 'release') {
          this.logger.debug(`Ignoring non-release event: ${event}`);
          return { message: 'Event ignored' };
        }

        const release = {
          repository: payload.repository.name,
          tag: payload.release.tag_name,
          title: payload.release.name,
          description: payload.release.body,
          htmlUrl: payload.release.html_url,
          createdAt: new Date(payload.release.created_at),
          seen: false,
        };

        await this.releaseService.saveRelease(release);

        this.logger.log(
          `Successfully saved release for ${payload.repository.name}`,
        );
        return {
          message: 'Release saved successfully',
        };
      } else if (payload.action === 'deleted') {
        await this.releaseService.deleteRelease(payload.release.name);
        this.logger.log(
          `Successfully deleted release for ${payload.repository.name}`,
        );
        return {
          message: 'Release deleted successfully',
        };
      } else {
        this.logger.log(`Ignoring release event: ${payload.action}`);
        return {
          message: 'Release ignored',
        };
      }
    } catch (error) {
      this.logger.error('Error handling webhook', error);
      throw new HttpException(
        'Error handling webhook',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':locationId')
  async getRepositoryReleasesForStudio(
    @Param('locationId') locationId: string,
  ): Promise<RepoReleases> {
    try {
      const releases = await this.releaseService.getReleases(locationId);
      return releases;
    } catch (error) {
      this.logger.error('Error fetching releases', error);
      throw new HttpException(
        'Error fetching releases',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post(':locationId/seen/:releaseId')
  async markReleaseAsSeen(
    @Param('locationId') locationId: string,
    @Param('releaseId') releaseId: string,
  ) {
    try {
      return await this.releaseService.markAsSeen(releaseId, locationId);
    } catch (error) {
      this.logger.error('Error marking release as seen', error);
      throw new HttpException(
        'Error marking release as seen',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
