import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ReleaseNotificationSeen } from 'src/database/schema/releaseNotificationSeen';
import { ReleaseNotification } from 'src/database/schema/releaseNotification';
import { Studio } from 'src/database/schema/studio';

@Injectable()
export class ReleaseNotificationService {
  private readonly logger = new Logger(ReleaseNotificationService.name);

  constructor(
    @InjectModel(ReleaseNotification.name)
    private releaseNotificationModel: Model<ReleaseNotification>,
    @InjectModel(ReleaseNotificationSeen.name)
    private releaseNotificationSeenModel: Model<ReleaseNotificationSeen>,
    @InjectModel(Studio.name)
    private studioModel: Model<Studio>,
  ) {}

  async getReleases(locationId?: string) {
    try {
      const studio = await this.studioModel.findOne({ locationId });
      // Get all releases
      const releases = await this.releaseNotificationModel
        .find()
        .sort({ createdAt: -1 });

      // If studioId provided, get seen status
      let seenStatus = {};
      if (studio._id) {
        const seenRecords = await this.releaseNotificationSeenModel
          .find({ studioId: studio._id })
          .exec();

        seenStatus = seenRecords.reduce((acc, record) => {
          acc[record.releaseId] = record.seen;
          return acc;
        }, {});
      }

      // Group releases by repository with seen status
      const groupedReleases = releases.reduce(
        (acc, release) => {
          const repo = release.repository;
          if (!acc[repo]) {
            acc[repo] = { releases: [] };
          }

          acc[repo].releases.push({
            id: release._id,
            tag: release.tag,
            title: release.title,
            description: release.description,
            createdAt: release.createdAt.toISOString(),
            htmlUrl: release.htmlUrl,
            seen: studio._id ? !!seenStatus[release._id.toString()] : undefined,
          });

          return acc;
        },
        {} as Record<string, { releases: Array<any> }>,
      );

      return groupedReleases;
    } catch (error) {
      this.logger.error('Error fetching releases', error);
      return {};
    }
  }

  async markAsSeen(releaseId: string, locationId: string) {
    try {
      const studio = await this.studioModel.findOne({ locationId });
      await this.releaseNotificationSeenModel.findOneAndUpdate(
        {
          releaseId,
          studioId: studio._id,
        },
        {
          $set: {
            seen: true,
            seenAt: new Date(),
          },
        },
        {
          upsert: true,
          new: true,
        },
      );

      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error marking release ${releaseId} as seen for studio ${locationId}`,
        error,
      );
      throw error;
    }
  }

  async saveRelease(releaseData: Partial<ReleaseNotification>) {
    try {
      const filter = {
        repository: releaseData.repository,
        tag: releaseData.tag,
      };

      const update = {
        $set: {
          repository: releaseData.repository,
          tag: releaseData.tag,
          title: releaseData.title,
          description: releaseData.description,
          htmlUrl: releaseData.htmlUrl,
          createdAt: releaseData.createdAt,
        },
      };

      const options = {
        new: true, // Return the modified document
        upsert: true, // Create if it doesn't exist
        setDefaultsOnInsert: true, // Apply schema defaults on insert
      };

      const savedRelease = await this.releaseNotificationModel.findOneAndUpdate(
        filter,
        update,
        options,
      );

      this.logger.debug(
        `Release ${savedRelease._id} saved/updated for ${releaseData.repository}:${releaseData.tag}`,
      );

      return savedRelease;
    } catch (error) {
      this.logger.error(
        `Error saving/updating release for ${releaseData.repository}:${releaseData.tag}`,
        error,
      );
      throw error;
    }
  }

  async deleteRelease(releaseName: string) {
    await this.releaseNotificationModel.findOneAndDelete({
      title: releaseName,
    });
  }
}
