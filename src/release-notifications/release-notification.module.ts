import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ReleaseNotificationController } from './release-notification.controller';
import { ReleaseNotificationService } from './release-notification.service';
import {
  ReleaseNotification,
  ReleaseNotificationSchema,
} from '../database/schema/releaseNotification';
import { ConfigModule } from '@nestjs/config';
import {
  ReleaseNotificationSeen,
  ReleaseNotificationSeenSchema,
} from 'src/database/schema/releaseNotificationSeen';
import { Studio, StudioSchema } from 'src/database/schema/studio';

@Module({
  imports: [
    ConfigModule,
    MongooseModule.forFeature([
      { name: ReleaseNotification.name, schema: ReleaseNotificationSchema },
      {
        name: ReleaseNotificationSeen.name,
        schema: ReleaseNotificationSeenSchema,
      },
      { name: Studio.name, schema: StudioSchema },
    ]),
  ],
  controllers: [ReleaseNotificationController],
  providers: [ReleaseNotificationService],
})
export class ReleaseNotificationModule {}
