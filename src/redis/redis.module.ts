import { Module, Global, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';

@Global()
@Module({
  providers: [
    {
      provide: 'REDIS_CLIENT',
      useFactory: (configService: ConfigService) => {
        const logger = new Logger('RedisModule');
        const host = configService.get<string>('REDIS_HOST') || 'localhost';
        const port = parseInt(
          configService.get<string>('REDIS_PORT') || '6379',
        );

        logger.log(`Initializing Redis connection to ${host}:${port}`);

        const redis = new Redis({
          host,
          port,
          password: configService.get<string>('REDIS_PASSWORD'),
          username: configService.get<string>('REDIS_USERNAME'),
          enableReadyCheck: true,
          maxRetriesPerRequest: null,
          retryStrategy: (times) => {
            logger.warn(`Redis connection retry attempt ${times}`);
            if (times > 3) {
              logger.error('Redis connection failed after 3 retries');
              return null;
            }
            return Math.min(times * 100, 2000);
          },
          lazyConnect: true,
        });

        // Connection success events
        redis.on('connect', () => {
          logger.log(`✅ Redis connected to ${host}:${port}`);
        });

        redis.on('ready', () => {
          logger.log(`🚀 Redis ready and operational at ${host}:${port}`);
        });

        // Connection failure events
        redis.on('error', (err) => {
          logger.error(`❌ Redis connection error: ${err.message}`);
        });

        redis.on('close', () => {
          logger.warn(`🔌 Redis connection closed to ${host}:${port}`);
        });

        redis.on('reconnecting', (delayMs) => {
          logger.log(
            `🔄 Redis reconnecting in ${delayMs}ms to ${host}:${port}`,
          );
        });

        redis.on('end', () => {
          logger.warn(`🛑 Redis connection ended to ${host}:${port}`);
        });

        // BullMQ specific logging
        redis.on('select', (db) => {
          logger.debug(`Redis selected database ${db}`);
        });

        // Log initial connection attempt
        redis.connect().catch((err) => {
          logger.error(
            `Failed to establish initial Redis connection: ${err.message}`,
          );
        });

        return redis;
      },
      inject: [ConfigService],
    },
  ],
  exports: ['REDIS_CLIENT'],
})
export class RedisModule {}
