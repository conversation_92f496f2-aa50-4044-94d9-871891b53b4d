import { Injectable, Logger } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ClassHistory } from 'src/database/schema/classHistory';
import { SubscriptionStatus } from 'src/database/schema/student';

@Injectable()
export class ClassHistoryService {
  private readonly logger = new Logger(ClassHistoryService.name);

  constructor(
    @InjectModel(ClassHistory.name)
    private classHistoryModel: Model<ClassHistory>,
  ) {}

  async upsertClassHistoryStudentRecord({
    enrollmentId,
    students,
    studioId,
  }: {
    enrollmentId: string;
    students: { studentId: string; status: SubscriptionStatus }[];
    studioId?: string;
  }) {
    const existingClassHistory = await this.classHistoryModel.findOne({
      classId: enrollmentId,
    });
    if (existingClassHistory) {
      // Push multiple student records to the students array, avoiding duplicates by studentId
      const existingStudentIds = existingClassHistory.students.map(
        (s: any) => s.studentId,
      );
      const newStudents = students.filter(
        (s) => !existingStudentIds.includes(s.studentId),
      );
      if (newStudents.length > 0) {
        await this.classHistoryModel.updateOne(
          { classId: enrollmentId },
          {
            $push: {
              students: { $each: newStudents },
            },
          },
        );
      }
    } else {
      // If not found, create a new document with all students in the students array
      await this.classHistoryModel.create({
        classId: enrollmentId,
        studioId: studioId,
        students: students,
      });
    }
  }
}
