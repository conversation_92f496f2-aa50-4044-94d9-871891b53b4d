import { Module } from '@nestjs/common';
import { ClassHistoryService } from './class-history.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  ClassHistory,
  ClassHistorySchema,
} from 'src/database/schema/classHistory';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: ClassHistory.name, schema: ClassHistorySchema },
    ]),
  ],
  controllers: [],
  providers: [ClassHistoryService],
  exports: [ClassHistoryService],
})
export class ClassHistoryModule {}
