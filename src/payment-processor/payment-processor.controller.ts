import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Req,
  Res,
  Query,
  UseGuards,
  Delete,
} from '@nestjs/common';
import { request, Request, Response } from 'express';
import { PaymentProcessorService } from './payment-processor.service';
import { CreateParentDto } from 'src/parents/dto/create-parent.dto';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { BulkChargeStudentsDto, RecordPaymentDto } from './dto';

@Controller('payment-processor')
@UseGuards(JwtAuthGuard)
export class PaymentProcessorController {
  constructor(private paymentProcessorService: PaymentProcessorService) {}

  // from studio portal: drop a class
  @Post('/subscription/cancel')
  async cancelSubscriptionByStudent(@Body() body) {
    return await this.paymentProcessorService.cancelSubscriptionByStudent(
      body.classId,
      body.locationId,
      body.studentId,
    );
  }

  // from studio portal: transfer a class
  @UseGuards(JwtAuthGuard)
  @Post('/subscription/transfer')
  async transferSubscription(
    @Body()
    body: {
      currentClassId: string;
      newClassId: string;
      studentId: string;
      newCreditAmount: number;
      noCreditRequired: boolean;
    },
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return await this.paymentProcessorService.transferSubscription(
      body.currentClassId,
      body.newClassId,
      body.studentId,
      locationId,
      body.noCreditRequired,
      body.newCreditAmount,
    );
  }

  @UseGuards(JwtAuthGuard)
  @Post('/subscription/get-remaining-credit')
  async getRemainingCredit(
    @Body()
    body: {
      currentClassId: string;
      newClassId: string;
      studentId: string;
    },
    @Req() request: Request,
  ) {
    const locationId = request['locationId'];
    return await this.paymentProcessorService.getRemainingCredit(
      body.currentClassId,
      body.newClassId,
      body.studentId,
      locationId,
    );
  }

  @Get('/card-details')
  async getCardDetails(
    @Query('email') email: string,
    @Query('locationId') locationId: string,
  ) {
    return await this.paymentProcessorService.getCardsByEmail(
      email,
      locationId,
    );
  }

  @Post('/card-details')
  async generateCardDetails(@Body() body) {
    return await this.paymentProcessorService.generateUpdateCardLink(
      body.email,
      body.studioId,
    );
  }

  // from parent portal registration form
  @Post('/parent-checkout-session')
  async createParentCheckoutSession(@Body() createParentDto: CreateParentDto) {
    return this.paymentProcessorService.createParentCheckoutSession(
      createParentDto,
    );
  }
  // from parent portal
  @Post('/product-checkout-session')
  async createProductCheckoutSession(@Body() body) {
    return this.paymentProcessorService.createProductCheckoutSession(body);
  }

  // from studio portal
  @Post('/add-student-to-entity')
  async addStudentToEntity(@Body() body) {
    return this.paymentProcessorService.addStudentToEntity(body);
  }

  @Post('/create')
  async create(
    @Body('publicKey') publicKey: string,
    @Body('secretKey') secretKey: string,
    @Body('studioId') studioId: string,
  ) {
    return await this.paymentProcessorService.create(
      publicKey,
      secretKey,
      studioId,
    );
  }

  @Get('/credential/studio/:studioId')
  async findOneByStudioId(@Param('studioId') studioId: string) {
    return this.paymentProcessorService.findOneByStudioId(studioId);
  }

  @Delete('/credential/studio/:studioId')
  async deleteByStudioId(@Param('studioId') studioId: string) {
    return this.paymentProcessorService.deleteByStudioId(studioId);
  }

  @Post('/bulk-charge-students')
  async bulkChargeStudents(
    @Body() body: BulkChargeStudentsDto,
    @Req() request: Request,
  ) {
    const studioId = request['locationId'];
    return await this.paymentProcessorService.bulkChargeStudents(
      body,
      studioId,
    );
  }

  @Post('/record-manual-payment')
  async recordManualPayment(
    @Body() body: RecordPaymentDto,
    @Req() request: Request,
  ) {
    const studioId = request['locationId'];
    return await this.paymentProcessorService.recordManualPayment(
      body,
      studioId,
    );
  }

  @Post('refund/:internalTransactionId')
  async refundInvoice(
    @Param('internalTransactionId') internalTransactionId: string,
    @Body() body: { amount: number; type: 'partial' | 'full' },
    @Req() request: Request,
  ) {
    const studioId = request['locationId'];
    return await this.paymentProcessorService.refundInvoice(
      internalTransactionId,
      body,
      studioId,
    );
  }

  @Post('/execute-charge-from-wallet')
  @UseGuards(JwtAuthGuard)
  async processStudentInvoicesWithWallet(
    @Body() body: { parentId: string; studentId: string },
    @Req() request: Request,
  ) {
    const studioId = request['locationId'];
    return await this.paymentProcessorService.processStudentInvoicesWithWallet({
      parentId: body.parentId,
      studentId: body.studentId,
      studioId,
    });
  }

  @Post('/check-payment-method')
  @UseGuards(JwtAuthGuard)
  async checkPaymentMethodOnFile(
    @Body() body: { parentId: string; paymentMethod: string },
    @Req() request: Request,
  ) {
    const studioId = request['locationId'];
    return await this.paymentProcessorService.checkPaymentMethodOnFile(
      studioId,
      body.parentId,
      body.paymentMethod,
    );
  }
}
