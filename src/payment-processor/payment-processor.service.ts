import {
  Inject,
  Injectable,
  HttpException,
  HttpStatus,
  forwardRef,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Studio } from 'src/database/schema/studio';
import { CreateParentDto } from 'src/parents/dto/create-parent.dto';
import { StripeService } from 'src/stripe/stripe.service';
import { Credential } from 'src/database/schema/stripeCredential';
import { BulkChargeStudentsDto, RecordPaymentDto } from './dto';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import { InvoiceStatus, PaymentMethod } from 'src/stripe/type';
import { ParentsService } from '../parents/parents.service';
import { Student } from 'src/database/schema/student';
import { ReasonType } from 'src/database/schema/walletTransaction';
import { PaymentTransaction } from 'src/database/schema/paymentTransaction';

@Injectable()
export class PaymentProcessorService {
  constructor(
    @InjectModel(Studio.name)
    private readonly studioModel: Model<Studio>,
    @InjectModel(Student.name)
    private readonly studentModel: Model<Student>,
    @Inject(forwardRef(() => StripeService))
    private readonly stripeService: StripeService,
    @InjectModel(Credential.name)
    private readonly credentialModel: Model<Credential>,
    @InjectModel(SubscriptionInvoice.name)
    private readonly subscriptionInvoiceModel: Model<SubscriptionInvoice>,
    @InjectModel(PaymentTransaction.name)
    private readonly paymentTransactionModel: Model<PaymentTransaction>,
    @Inject(forwardRef(() => ParentsService))
    private readonly parentsService: ParentsService,
  ) {}

  async create(
    publicKey: string,
    secretKey: string,
    studioId: string,
  ): Promise<Credential> {
    try {
      const studio = await this.studioModel.findById(studioId);
      if (!studio) {
        throw new Error('Studio not found');
      }
      if (studio.paymentProvider == 'stripe') {
        const { webhookId, webhookSecret } =
          await this.stripeService.createWebhook(secretKey, studioId);
        const studioIdRetrieved = studio._id.toString();
        const createdCredential = new this.credentialModel({
          studioId: studioIdRetrieved,
          apiKey: publicKey,
          apiSecret: secretKey,
          webhookId: webhookId,
          webhookSecret: webhookSecret,
        });
        return createdCredential.save();
      }
    } catch (error) {
      console.error('Error creating studio: ', error);
      throw new HttpException(
        {
          status: 'fail',
          message: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async cancelSubscriptionByStudent(
    classId: string,
    locationId: string,
    studentId: string,
  ) {
    const studio = await this.studioModel.findById(locationId);
    if (!studio) {
      throw new Error('Studio not found');
    }
    if (studio.paymentProvider === 'stripe') {
      return this.stripeService.cancelStudentSubscription(
        classId,
        locationId,
        studentId,
      );
    }
  }

  async transferSubscription(
    currentClassId: string,
    newClassId: string,
    studentId: string,
    locationId: string,
    noCreditRequired: boolean,
    newCreditAmount: number,
  ) {
    const studio = await this.studioModel.findById(locationId);
    if (!studio) {
      throw new Error('Studio not found');
    }
    if (studio.paymentProvider === 'stripe') {
      return this.stripeService.transferSubscription(
        currentClassId,
        newClassId,
        studentId,
        locationId,
        noCreditRequired,
        newCreditAmount,
      );
    }
  }

  async getRemainingCredit(
    currentClassId: string,
    newClassId: string,
    studentId: string,
    locationId: string,
  ) {
    const studio = await this.studioModel.findById(locationId);
    if (!studio) {
      throw new Error('Studio not found');
    }
    if (studio.paymentProvider === 'stripe') {
      return this.stripeService.getRemainingCredit(
        currentClassId,
        newClassId,
        studentId,
        locationId,
      );
    }
  }

  async getCardsByEmail(email: string, locationId: string) {
    const studio = await this.studioModel.findById(locationId);
    if (!studio) {
      throw new Error('Studio not found');
    }
    if (studio.paymentProvider === 'stripe') {
      return this.stripeService.getCardsByEmail(email, locationId);
    }
  }

  async generateUpdateCardLink(email: string, studioId: string) {
    const studio = await this.studioModel.findById(studioId);
    if (!studio) {
      throw new Error('Studio not found');
    }
    if (studio.paymentProvider === 'stripe') {
      return this.stripeService.generateUpdateCardLink(email, studioId);
    }
  }

  async createParentCheckoutSession(createParentDto: CreateParentDto) {
    const studio = await this.studioModel.findById(createParentDto.studioId);
    if (!studio) {
      throw new Error('Studio not found');
    }
    if (studio.paymentProvider === 'stripe') {
      return this.stripeService.createParentCheckoutSession(createParentDto);
    }
  }

  async createProductCheckoutSession(body: any) {
    const studio = await this.studioModel.findById(body.studioId);
    if (!studio) {
      throw new Error('Studio not found');
    }
    if (studio.paymentProvider === 'stripe') {
      return this.stripeService.createProductCheckoutSession(body);
    }
  }

  async addStudentToEntity(body: any) {
    const studio = await this.studioModel.findById(body.studioId);
    if (!studio) {
      throw new Error('Studio not found');
    }
    if (studio.paymentProvider === 'stripe') {
      return this.stripeService.addStudentToEntity(body);
    }
  }

  async findOne(credentialId: string) {
    return this.credentialModel.findById(credentialId);
  }

  async findOneByStudioId(studioId: string) {
    const credential = await this.credentialModel.findOne({ studioId });
    return credential ? true : false;
  }

  async deleteByStudioId(studioId: string) {
    try {
      const credential = await this.credentialModel
        .findOne({ studioId })
        .exec();
      if (!credential) {
        throw new Error('Credential not found');
      }

      await this.credentialModel
        .deleteOne({
          studioId,
        })
        .exec();

      await this.stripeService.deleteWebhook(credential);

      return {
        status: 'success',
        message: 'Credential deleted successfully',
      };
    } catch (error) {
      console.error('Error deleting credential:', error);
      return {
        status: 'fail',
        message: 'Failed to delete credential',
      };
    }
  }

  async captureManualPayment(studioId: string, invoiceId: string) {
    const studio = await this.studioModel.findById(studioId);
    if (!studio) {
      throw new Error('Studio not found');
    }
    if (studio.paymentProvider === 'stripe') {
      return this.stripeService.captureManualPayment({
        invoiceId,
        studioId,
      });
    }
  }

  async bulkChargeStudents(body: BulkChargeStudentsDto, studioId: string) {
    const studio = await this.studioModel.findById(studioId);
    if (!studio) {
      throw new Error('Studio not found');
    }
    if (studio.paymentProvider === 'stripe') {
      return this.stripeService.bulkChargeStudents(body, studioId);
    }
  }

  async recordManualPayment(body: RecordPaymentDto, studioId: string) {
    const {
      totalAmount,
      balanceAmount,
      invoicesSplit,
      paymentMethod,
      checkNumber,
      applyToWallet,
      studentId,
      transactionCodeId,
    } = body;

    if (
      Number.isNaN(totalAmount) ||
      Number.isNaN(balanceAmount) ||
      (!applyToWallet &&
        Number.isNaN(
          invoicesSplit.reduce((acc, invoice) => acc + invoice.amount, 0),
        ))
    ) {
      return {
        status: 'fail',
        message: 'Total amount and balance amount must be numbers',
      };
    }

    if (
      totalAmount < 0 ||
      balanceAmount < 0 ||
      (!applyToWallet && invoicesSplit.some((invoice) => invoice.amount < 0))
    ) {
      return {
        status: 'fail',
        message: 'Total amount and balance amount must be positive',
      };
    }

    if (
      !applyToWallet &&
      invoicesSplit.some((invoice) => !invoice.amount || invoice.amount === 0)
    ) {
      return {
        status: 'fail',
        message: 'All invoices must have valid non-zero amounts',
      };
    }

    if (
      !applyToWallet &&
      invoicesSplit.some((invoice) => !Number.isInteger(invoice.amount * 100))
    ) {
      return {
        status: 'fail',
        message: 'Invoice amounts must not have more than 2 decimal places',
      };
    }

    if (!Object.values(PaymentMethod).includes(paymentMethod)) {
      return {
        status: 'fail',
        message: 'Invalid payment method',
      };
    }

    const studio = await this.studioModel.findById(studioId);
    if (!studio) {
      return {
        status: 'fail',
        message: 'Studio not found',
      };
    }
    const invoicesTotalAmount = !applyToWallet
      ? invoicesSplit.reduce((acc, invoice) => acc + invoice.amount, 0)
      : 0;
    const totalRequestedAmount = balanceAmount + invoicesTotalAmount;

    if (!applyToWallet && totalRequestedAmount !== totalAmount) {
      return {
        status: 'fail',
        message: 'Total amount does not match balance amount',
      };
    }
    if (paymentMethod === PaymentMethod.CHECK) {
      if (!checkNumber) {
        return {
          status: 'fail',
          message: 'Check number is required',
        };
      }
    }
    const student = await this.studentModel.findById(studentId);

    if (!student) {
      return {
        status: 'fail',
        message: 'Student not found',
      };
    }

    if (!student.parentId) {
      return {
        status: 'fail',
        message: 'Student must have an associated parent',
      };
    }

    if (!applyToWallet && invoicesSplit.length > 0) {
      if (balanceAmount > 0) {
        await this.parentsService.addWalletBalance({
          studioId,
          parentId: student.parentId.toString(),
          studentId,
          amount: balanceAmount,
          reason: ReasonType.EXCESS_LOAD,
          paymentMethod: paymentMethod,
        });
      }

      for (const invoiceItem of invoicesSplit) {
        if (!invoiceItem.invoiceId) {
          return {
            status: 'fail',
            message: 'All invoices must have valid IDs',
          };
        }
      }

      invoicesSplit.forEach(async (invoiceItem) => {
        const invoice = await this.subscriptionInvoiceModel.findById(
          invoiceItem.invoiceId,
        );
        if (!invoice) {
          return {
            status: 'fail',
            message: 'Invoice not found',
          };
        }

        const payments: {
          method: PaymentMethod;
          amount: number;
          checkNumber?: string;
          date: Date;
        } = {
          method: paymentMethod,
          amount: invoiceItem.amount,
          date: new Date(),
        };
        if (paymentMethod === PaymentMethod.CHECK) {
          payments.checkNumber = checkNumber;
        }
        if (!invoice.payments) {
          invoice.payments = [];
        }

        const totalPayments = [...invoice.payments, payments];
        const totalAmountPaid = totalPayments.reduce(
          (acc, payment) => acc + payment.amount,
          0,
        );

        if (totalAmountPaid >= invoice.finalAmount) {
          invoice.status = InvoiceStatus.PAID;
        } else if (totalAmountPaid < invoice.finalAmount) {
          invoice.status = InvoiceStatus.PARTIALLY_PAID;
        }

        if (transactionCodeId) {
          invoice.transactionCodeId = transactionCodeId;
        }

        invoice.payments = totalPayments;
        await invoice.save();
      });
    } else {
      // apply to wallet
      if (totalAmount != balanceAmount) {
        return {
          status: 'fail',
          message: 'Total amount does not match balance amount',
        };
      }

      await this.parentsService.addWalletBalance({
        parentId: student.parentId.toString(),
        amount: balanceAmount,
        studioId,
        studentId,
        paymentMethod: paymentMethod,
        reason: ReasonType.WALLET_LOAD,
      });
    }
    return {
      status: 'success',
      message: 'Payment recorded successfully',
    };
  }

  async refundInvoice(
    transactionId: string,
    body: { amount: number; type: 'partial' | 'full' },
    studioId: string,
  ) {
    try {
      const studio = await this.studioModel.findById(studioId);
      if (!studio) {
        throw new Error('Studio not found');
      }
      const transaction =
        await this.paymentTransactionModel.findById(transactionId);
      if (!transaction) {
        throw new Error('Transaction not found');
      }

      if (body.amount > transaction.amount) {
        return {
          status: 'fail',
          message: 'Amount to refund is greater than the invoice amount',
        };
      }

      let paymentIntentId = transaction.metadata?.paymentIntentId;

      if (studio.paymentProvider === 'stripe') {
        if (
          transaction.paymentProvider === 'stripe' &&
          (transaction.paymentMethod === PaymentMethod.CARD ||
            transaction.paymentMethod === PaymentMethod.US_BANK_ACCOUNT)
        ) {
          const refundLineItem = {
            name: 'Refund',
            amount: body.amount,
            type:
              body.type === 'full'
                ? InvoiceStatus.FULL_REFUND
                : InvoiceStatus.PARTIAL_REFUND,
            total: body.amount,
            quantity: 1,
          };

          let invoice = await this.subscriptionInvoiceModel.findOne({
            'metadata.internalTransactionId': transaction._id.toString(),
          });

          if (!invoice) {
            throw new Error('Invoice not found');
          }

          const refund = await this.stripeService.refundPayment(
            paymentIntentId,
            body.amount,
            studioId,
          );

          invoice.line_items = [...invoice.line_items, refundLineItem];

          invoice.metadata = {
            ...invoice.metadata,
            refundId: refund.charge.toString(),
          };
          invoice.status = InvoiceStatus.PENDING_REFUND;
          await invoice.save();
        } else {
          throw new Error(
            'Refund is only supported for Stripe Card and US Bank Account payments',
          );
        }
      }
      return {
        message: 'Invoice refunded successfully',
      };
    } catch (error) {
      console.error('Error refunding invoice:', error);
      if (error.message === 'Refund is only supported for Stripe payments') {
        throw new HttpException(
          'Refund is only supported for Stripe Card and US Bank Account payments',
          HttpStatus.BAD_REQUEST,
        );
      }
      throw new HttpException(
        'Failed to refund invoice',
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  async checkPaymentMethodOnFile(
    studioId: string,
    parentId: string,
    paymentMethod: string,
  ) {
    const studio = await this.studioModel.findById(studioId);
    if (!studio) {
      throw new Error('Studio not found');
    }

    const parent = await this.parentsService.findOne(parentId);
    if (!parent) {
      throw new Error('Parent not found');
    }
    if (studio.paymentProvider === 'stripe') {
      return this.stripeService.checkPaymentMethodOnFile(
        studioId,
        parent.stripeCustomerId,
        paymentMethod,
      );
    }
  }

  async createWalletLoad(body: {
    studioId: string;
    parentId: string;
    amount: number;
    paymentMethod: PaymentMethod;
  }) {
    const studio = await this.studioModel.findById(body.studioId);
    if (!studio) {
      throw new Error('Studio not found');
    }
    if (studio.paymentProvider === 'stripe') {
      return this.stripeService.createWalletLoad(body);
    }
  }

  async processStudentInvoicesWithWallet(body: {
    parentId: string;
    studentId: string;
    studioId: string;
  }) {
    const { studioId } = body;

    const studio = await this.studioModel.findById(studioId);
    if (!studio) {
      throw new Error('Studio not found');
    }
    if (studio.paymentProvider === 'stripe') {
      return this.stripeService.processStudentInvoicesWithWallet(body);
    }
  }
}
