import { forwardRef, Module, Inject } from '@nestjs/common';
import { PaymentProcessorService } from './payment-processor.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  Credential,
  CredentialSchema,
} from 'src/database/schema/stripeCredential';
import { Proration, ProrationSchema } from 'src/database/schema/prorations';
import { Student, StudentSchema } from 'src/database/schema/student';
import { Enrollment, EnrollmentSchema } from 'src/database/schema/enrollment';
import {
  EnrollmentHistory,
  EnrollmentHistorySchema,
} from 'src/database/schema/enrollmentHistory';
import { Event, EventSchema } from 'src/database/schema/event';
import { Session, SessionSchema } from 'src/database/schema/session';
import { BullModule } from '@nestjs/bullmq';
import { ScheduleModule } from '@nestjs/schedule';
import { JwtModule } from '@nestjs/jwt';

// Import modules
import { StripeModule } from 'src/stripe/stripe.module';
import { StudiosModule } from 'src/studios/studios.module';
import { ParentsModule } from 'src/parents/parents.module';
import { EnrollmentModule } from 'src/enrollment/enrollment.module';
import { StudentsModule } from 'src/students/students.module';
import { CurrencyModule } from 'src/currency/currency.module';
import { CouponModule } from 'src/coupon/coupon.module';
import { DiscountModule } from 'src/discount/discount.module';
import { EnrollmentHistoryModule } from 'src/enrollment-history/enrollment-history.module';
import { EventsModule } from 'src/events/events.module';
import { EventHistoryModule } from 'src/event-history/event-history.module';
import { GohighlevelModule } from 'src/gohighlevel/gohighlevel.module';
import { PoliciesModule } from 'src/policies/policies.module';
import { GcpStorageModule } from 'src/gcp-storage/gcp-storage.module';
import { LeadsModule } from 'src/leads/leads.module';
import { WebhookErrorLogsModule } from 'src/webhook-error-logs/webhook-error-logs.module';
import { TriggersModule } from 'src/triggers/triggers.module';
import { TransactionModule } from 'src/transaction/transaction.module';
import { WebhooksModule } from 'src/webhooks/webhooks.module';
import { DiscountCouponModule } from 'src/discount-coupon/discount-coupon.module';
import { SubscriptionModule } from 'src/subscription/subscription.module';
import { SubscriptionInvoiceModule } from 'src/subscription-invoice/subscription-invoice.module';
import { Studio } from 'src/database/schema/studio';
import { StudioSchema } from 'src/database/schema/studio';
import { PaymentProcessorController } from './payment-processor.controller';
import { InvoiceStatusProcessor } from './queue-processors/invoice-status.processor';
import { PaymentTransactionService } from 'src/payment-transaction/payment-transaction.service';
import {
  PaymentTransaction,
  PaymentTransactionSchema,
} from 'src/database/schema/paymentTransaction';
import { SubscriptionInvoiceSchema } from 'src/database/schema/subscriptionInvoice';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import { StripeService } from 'src/stripe/stripe.service';
import { TransactionCodeSchema } from 'src/transaction-code/entities/transaction-code.entity';
import { TransactionCode } from 'src/transaction-code/entities/transaction-code.entity';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Credential.name, schema: CredentialSchema },
      { name: Proration.name, schema: ProrationSchema },
      { name: Student.name, schema: StudentSchema },
      { name: Enrollment.name, schema: EnrollmentSchema },
      { name: EnrollmentHistory.name, schema: EnrollmentHistorySchema },
      { name: Event.name, schema: EventSchema },
      { name: Session.name, schema: SessionSchema },
      { name: Studio.name, schema: StudioSchema },
      { name: PaymentTransaction.name, schema: PaymentTransactionSchema },
      {
        name: SubscriptionInvoice.name,
        schema: SubscriptionInvoiceSchema,
      },
      { name: TransactionCode.name, schema: TransactionCodeSchema },
    ]),
    BullModule.registerQueue(
      {
        name: 'parent-remove-tags',
      },
      {
        name: 'invoice-status-update',
      },
    ),
    forwardRef(() => StripeModule),
    forwardRef(() => ParentsModule),
    forwardRef(() => StudentsModule),
    forwardRef(() => TransactionModule),
    forwardRef(() => StudiosModule),
    forwardRef(() => EnrollmentModule),
    forwardRef(() => CurrencyModule),
    forwardRef(() => DiscountModule),
    forwardRef(() => CouponModule),
    forwardRef(() => EnrollmentHistoryModule),
    forwardRef(() => EventsModule),
    forwardRef(() => EventHistoryModule),
    forwardRef(() => GohighlevelModule),
    forwardRef(() => PoliciesModule),
    forwardRef(() => GcpStorageModule),
    forwardRef(() => LeadsModule),
    forwardRef(() => WebhookErrorLogsModule),
    forwardRef(() => WebhooksModule),
    forwardRef(() => DiscountCouponModule),
    forwardRef(() => SubscriptionModule),
    forwardRef(() => SubscriptionInvoiceModule),
    ScheduleModule.forRoot(),
    JwtModule,
    TriggersModule,
  ],
  controllers: [PaymentProcessorController],
  providers: [
    PaymentProcessorService,
    InvoiceStatusProcessor,
    PaymentTransactionService,
    StripeService,
  ],
  exports: [PaymentProcessorService, InvoiceStatusProcessor],
})
export class PaymentProcessorModule {}
