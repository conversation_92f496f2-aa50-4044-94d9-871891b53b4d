import { PaymentMethod } from 'src/stripe/type';

export class BulkChargeStudentsDto {
  studentId: string[];
  amount: number;
  chargeName: string;
  isSingleStudent: boolean = false;
}

export class RecordPaymentDto {
  totalAmount: number;
  balanceAmount: number;
  invoicesSplit: {
    invoiceId: string;
    amount: number;
  }[];
  paymentMethod: PaymentMethod;
  checkNumber?: string;
  applyToWallet: boolean;
  studentId: string;
  transactionCodeId?: string;
}
