import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Job } from 'bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { PaymentTransactionService } from 'src/payment-transaction/payment-transaction.service';
import { SubscriptionInvoiceService } from 'src/subscription-invoice/subscription-invoice.service';
import { ParentsService } from 'src/parents/parents.service';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { SubscriptionInvoice } from 'src/database/schema/subscriptionInvoice';
import {
  InvoiceStatus,
  PaymentMethod,
  PaymentTransactionStatus,
} from 'src/stripe/type';
import { ReasonType } from 'src/database/schema/walletTransaction';

interface InvoiceProcessingJob {
  // For transaction-based invoice updates (legacy)
  transactionId?: string;

  // For direct invoice processing (new)
  paymentIntentId?: string;
  paymentMethodType?: string;

  // Common fields
  status: InvoiceStatus;
  payments?: {
    method: PaymentMethod;
    amount: number;
    date: Date;
    paymentIntentId?: string;
  }[];
  failureReason?: string;
}

@Injectable()
@Processor('invoice-status-update')
export class InvoiceStatusProcessor extends WorkerHost {
  private readonly logger = new Logger(InvoiceStatusProcessor.name);

  constructor(
    private readonly paymentTransactionService: PaymentTransactionService,
    private readonly subscriptionInvoiceService: SubscriptionInvoiceService,
    private readonly parentsService: ParentsService,
    @InjectModel(SubscriptionInvoice.name)
    private readonly subscriptionInvoiceModel: Model<SubscriptionInvoice>,
  ) {
    super();
  }

  async process(job: Job<InvoiceProcessingJob>) {
    const {
      transactionId,
      paymentIntentId,
      status,
      payments,
      paymentMethodType,
      failureReason,
    } = job.data;

    this.logger.log(
      `Processing invoice update to '${status}' - ${transactionId ? `Transaction: ${transactionId}` : `PaymentIntent: ${paymentIntentId}`}`,
    );

    try {
      let invoice = null;

      // Handle transaction-based invoice update (legacy flow)
      if (transactionId && !paymentIntentId) {
        invoice =
          await this.paymentTransactionService.findOldestInvoiceByStatus(
            transactionId,
            PaymentTransactionStatus.PENDING,
          );

        if (!invoice) {
          this.logger.warn(
            `No pending invoice found for transaction ${transactionId}`,
          );
          return {
            updated: false,
            reason: 'No pending invoice found for transaction',
          };
        }
      }
      // Handle direct invoice processing by payment intent ID (new flow)
      else if (paymentIntentId) {
        invoice = await this.subscriptionInvoiceModel.findOne({
          'metadata.paymentIntentId': paymentIntentId,
        });

        if (!invoice) {
          throw new Error(
            `No invoice found for payment intent: ${paymentIntentId}`,
          );
        }
      } else {
        throw new Error(
          'Either transactionId or paymentIntentId must be provided',
        );
      }

      // Process the invoice based on status
      if (status === InvoiceStatus.PAID) {
        await this.processSuccessfulInvoicePayment(
          invoice,
          payments || [],
          paymentMethodType,
        );
      } else if (status === InvoiceStatus.FAILED) {
        await this.processFailedInvoicePayment(
          invoice,
          failureReason || 'Payment failed',
        );
      } else {
        // Handle other status updates
        await this.updateInvoiceStatus(invoice, status, payments);
      }

      this.logger.log(
        `Successfully updated invoice to '${status}' status - Invoice: ${invoice._id}`,
      );

      return { updated: true, invoiceId: invoice._id.toString() };
    } catch (error) {
      this.logger.error(
        `Failed to update invoice status: ${error.message}`,
        error.stack,
      );
      // Re-throw the error to trigger a retry if configured
      throw error;
    }
  }

  /**
   * Process successful invoice payment with wallet logic
   */
  private async processSuccessfulInvoicePayment(
    invoice: any,
    payments: {
      method: PaymentMethod;
      amount: number;
      date: Date;
      paymentIntentId?: string;
    }[],
    paymentMethodType?: string,
  ): Promise<void> {
    const finalPayments = [...payments];

    // Handle wallet deduction if wallet was used
    if (invoice.metadata?.walletAmountUsed > 0) {
      await this.parentsService.removeWalletBalance({
        parentId: invoice.parentId.toString(),
        amount: invoice.metadata.walletAmountUsed,
        reason: ReasonType.INVOICE_PAYMENT,
        paymentMethod: PaymentMethod.WALLET,
        studioId: invoice.studioId.toString(),
        studentId: invoice.studentId.toString(),
      });

      // Add wallet payment to payments array if not already included
      const hasWalletPayment = finalPayments.some(
        (p) => p.method === PaymentMethod.WALLET,
      );
      if (!hasWalletPayment) {
        finalPayments.push({
          method: PaymentMethod.WALLET,
          amount: invoice.metadata.walletAmountUsed,
          date: new Date(),
        });
      }

      this.logger.log(
        `Wallet deducted: ${invoice.metadata.walletAmountUsed} for invoice: ${invoice._id}`,
      );
    }

    // If no payments provided but we have payment method, create one
    if (finalPayments.length === 0 && paymentMethodType) {
      const stripeAmount =
        invoice.metadata?.paymentIntentAmount || invoice.finalAmount;
      finalPayments.push({
        method: paymentMethodType as PaymentMethod,
        amount: stripeAmount,
        date: new Date(),
        paymentIntentId: invoice.metadata?.paymentIntentId,
      });
    }

    // Update invoice status to paid
    await this.subscriptionInvoiceService.update(invoice._id.toString(), {
      status: InvoiceStatus.PAID,
      paymentDate: new Date(),

      metadata: {
        ...invoice.metadata,
        paymentIntentId: invoice.metadata?.paymentIntentId,
      },
      payments: [...(invoice.payments || []), ...finalPayments],
    });

    // Update metadata to mark wallet as deducted
    if (invoice.metadata?.walletAmountUsed > 0) {
      await this.subscriptionInvoiceModel.updateOne(
        { _id: invoice._id },
        {
          $set: {
            'metadata.deductedAt': new Date(),
          },
        },
      );
    }

    const totalAmount = finalPayments.reduce((sum, p) => sum + p.amount, 0);
    this.logger.log(
      `Invoice ${invoice._id} marked as paid - Total: $${totalAmount} (${finalPayments.map((p) => `${p.method}: $${p.amount}`).join(', ')})`,
    );
  }

  /**
   * Process failed invoice payment
   */
  private async processFailedInvoicePayment(
    invoice: any,
    failureReason: string,
  ): Promise<void> {
    // Update invoice status to failed
    await this.subscriptionInvoiceService.update(invoice._id.toString(), {
      status: InvoiceStatus.FAILED,
      paymentDate: new Date(),
    });

    // Update metadata to include failure info
    await this.subscriptionInvoiceModel.updateOne(
      { _id: invoice._id },
      {
        $set: {
          'metadata.failureReason': failureReason,
          'metadata.attemptCount': (invoice.metadata?.attemptCount || 0) + 1,
          'metadata.lastAttemptDate': new Date(),
        },
      },
    );

    this.logger.log(
      `Invoice ${invoice._id} marked as failed - Reason: ${failureReason}`,
    );
  }

  /**
   * Generic invoice status update
   */
  private async updateInvoiceStatus(
    invoice: any,
    status: InvoiceStatus,
    payments?: {
      method: PaymentMethod;
      amount: number;
      date: Date;
      paymentIntentId?: string;
    }[],
  ): Promise<void> {
    const updateData: any = {
      status,
      paymentDate: new Date(),
    };

    if (payments && payments.length > 0) {
      updateData.payments = [...(invoice.payments || []), ...payments];
    }

    await this.subscriptionInvoiceService.update(
      invoice._id.toString(),
      updateData,
    );
  }
}
