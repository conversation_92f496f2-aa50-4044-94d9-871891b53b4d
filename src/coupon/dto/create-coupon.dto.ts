import {
  IsString,
  IsEnum,
  IsN<PERSON>ber,
  IsBoolean,
  IsOptional,
} from 'class-validator';
import { Types } from 'mongoose';

export class CreateCouponDto {
  @IsString()
  name: string;

  @IsString()
  studioId: Types.ObjectId;

  @IsEnum(['flat', 'percent', 'amount'])
  type: 'flat' | 'percent' | 'amount';

  @IsNumber()
  value: number;

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsEnum(['all-months', 'first-month'])
  discountRules: 'all-months' | 'first-month';

  @IsEnum(['multi-student', 'multi-class'])
  category: 'multi-student' | 'multi-class';

  @IsString()
  stripeId: string;
}
