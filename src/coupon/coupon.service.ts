import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Coupon } from 'src/database/schema/coupon';
import { CreateCouponDto } from './dto/create-coupon.dto';
import { UpdateCouponDto } from './dto/update-coupon.dto';
@Injectable()
export class CouponService {
  constructor(@InjectModel(Coupon.name) private couponModel: Model<Coupon>) {}

  async findByAmount(params: {
    studioId: Types.ObjectId;
    amount: number;
    category: 'multi-student' | 'multi-class';
    discountRules: 'first-month' | 'all-months';
  }) {
    const { studioId, amount, category, discountRules } = params;

    return await this.couponModel.findOne({
      studioId,
      isActive: true,
      category,
      value: amount,
      discountRules,
    });
  }

  async create(createCouponDto: CreateCouponDto) {
    // Store in database
    const coupon = new this.couponModel({
      ...createCouponDto,
    });

    return await coupon.save();
  }

  // async update(id: string, updateCouponDto: UpdateCouponDto) {
  //   const coupon = await this.couponModel.findById(id);
  //   if (!coupon) {
  //     throw new NotFoundException('Coupon not found');
  //   }

  //   // Update Stripe coupon if needed
  //   if (updateCouponDto.value !== undefined || updateCouponDto.type) {
  //     await this.stripeService.updateStripeCoupon(coupon.stripeId, {
  //       type: updateCouponDto.type || coupon.type,
  //       value: updateCouponDto.value || coupon.value,
  //     });
  //   }

  //   return await this.couponModel.findByIdAndUpdate(id, updateCouponDto, {
  //     new: true,
  //   });
  // }

  // async toggleActive(id: string, isActive: boolean) {
  //   const coupon = await this.couponModel.findById(id);
  //   if (!coupon) {
  //     throw new NotFoundException('Coupon not found');
  //   }

  //   // Toggle in Stripe
  //   if (isActive) {
  //     await this.stripeService.enableStripeCoupon(coupon.stripeId);
  //   } else {
  //     await this.stripeService.disableStripeCoupon(coupon.stripeId);
  //   }

  //   return await this.couponModel.findByIdAndUpdate(
  //     id,
  //     { isActive },
  //     { new: true },
  //   );
  // }
}
