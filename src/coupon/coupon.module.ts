import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { CouponService } from './coupon.service';
import { Coupon, CouponSchema } from '../database/schema/coupon';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Coupon.name, schema: CouponSchema }]),
  ],
  controllers: [],
  providers: [CouponService],
  exports: [CouponService],
})
export class CouponModule {}
