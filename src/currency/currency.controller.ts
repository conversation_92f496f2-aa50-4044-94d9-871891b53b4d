import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
} from '@nestjs/common';
import { CurrencyService } from './currency.service';
import { CreateCurrencyDto } from './dto/create-currency.dto';
import { UpdateCurrencyDto } from './dto/update-currency.dto';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { Types } from 'mongoose';

@UseGuards(JwtAuthGuard)
@Controller('currency')
export class CurrencyController {
  constructor(private readonly currencyService: CurrencyService) {}

  @Post()
  create(
    @Body() createCurrencyDto: CreateCurrencyDto,
    @Req() request: Request,
  ) {
    const studioId_string = request['locationId'];
    const studioId = Types.ObjectId.createFromHexString(studioId_string);
    return this.currencyService.create(createCurrencyDto, studioId);
  }

  // @Get()
  // findAll(@Req() request: Request) {
  //   const studioId_string = request['locationId'];
  //   const studioId = Types.ObjectId.createFromHexString(studioId_string);
  //   return this.currencyService.findAll(studioId);
  // }

  @Get()
  findOne(@Param('id') id: string, @Req() request: Request) {
    const studioId_string = request['locationId'];
    const studioId = Types.ObjectId.createFromHexString(studioId_string);
    return this.currencyService.findOne(id, studioId);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateCurrencyDto: UpdateCurrencyDto,
    @Req() request: Request,
  ) {
    const studioId_string = request['locationId'];
    const studioId = Types.ObjectId.createFromHexString(studioId_string);
    return this.currencyService.update(id, updateCurrencyDto, studioId);
  }

  @Delete(':id')
  remove(@Param('id') id: string, @Req() request: Request) {
    const studioId_string = request['locationId'];
    const studioId = Types.ObjectId.createFromHexString(studioId_string);
    return this.currencyService.remove(id, studioId);
  }
}
