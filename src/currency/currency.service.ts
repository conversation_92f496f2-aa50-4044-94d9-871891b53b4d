import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CreateCurrencyDto } from './dto/create-currency.dto';
import { UpdateCurrencyDto } from './dto/update-currency.dto';
import { Currency, CurrencyDocument } from 'src/database/schema/currency';

@Injectable()
export class CurrencyService {
  constructor(
    @InjectModel(Currency.name) private currencyModel: Model<CurrencyDocument>,
  ) {}

  async create(
    createCurrencyDto: CreateCurrencyDto,
    studioId: Types.ObjectId,
  ): Promise<Currency> {
    const createdCurrency = new this.currencyModel({
      ...createCurrencyDto,
      studioId,
    });
    return createdCurrency.save();
  }

  async findAll(studioId: Types.ObjectId): Promise<Currency[]> {
    return this.currencyModel.find({ studioId }).exec();
  }

  async findOne(id: string, studioId: Types.ObjectId): Promise<Currency> {
    const currency = await this.currencyModel
      .findOne({
        studioId,
      })
      .exec();

    if (!currency) {
      throw new NotFoundException(`Currency with ID ${id} not found`);
    }
    return currency;
  }

  async findByStudioId(studioId: Types.ObjectId): Promise<Currency> {
    const currency = await this.currencyModel
      .findOne({
        studioId,
      })
      .exec();

    return currency;
  }

  async update(
    id: string,
    updateCurrencyDto: UpdateCurrencyDto,
    studioId: Types.ObjectId,
  ): Promise<Currency> {
    const updatedCurrency = await this.currencyModel
      .findOneAndUpdate({ _id: id, studioId }, updateCurrencyDto, { new: true })
      .exec();

    if (!updatedCurrency) {
      throw new NotFoundException(`Currency with ID ${id} not found`);
    }
    return updatedCurrency;
  }

  async remove(id: string, studioId: Types.ObjectId): Promise<Currency> {
    const deletedCurrency = await this.currencyModel
      .findOneAndDelete({ _id: id, studioId })
      .exec();

    if (!deletedCurrency) {
      throw new NotFoundException(`Currency with ID ${id} not found`);
    }
    return deletedCurrency;
  }
}
