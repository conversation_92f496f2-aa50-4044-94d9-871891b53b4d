import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export type TransactionCodeDocument = TransactionCode & Document;

@Schema({ timestamps: true })
export class TransactionCode {
  @Prop({ required: true })
  code: string;

  @Prop()
  description: string;

  @Prop({ required: false })
  sessionId: string;

  @Prop({ required: false })
  sessionName: string;

  @Prop({
    required: false,
    type: [
      {
        entityId: { type: String, required: true },
        entityName: { type: String, required: true },
      },
    ],
    default: [],
  })
  entities: Array<{
    entityId: string;
    entityName: string;
  }>;

  @Prop({ default: false })
  isDeleted: boolean;
}

export const TransactionCodeSchema =
  SchemaFactory.createForClass(TransactionCode);
