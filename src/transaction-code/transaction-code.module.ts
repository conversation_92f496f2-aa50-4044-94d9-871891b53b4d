import { Modu<PERSON> } from '@nestjs/common';
import { TransactionCodeService } from './transaction-code.service';
import { TransactionCodeController } from './transaction-code.controller';
import { MongooseModule } from '@nestjs/mongoose';
import {
  TransactionCode,
  TransactionCodeSchema,
} from './entities/transaction-code.entity';
import { Enrollment, EnrollmentSchema } from 'src/database/schema/enrollment';
import { Session, SessionSchema } from 'src/database/schema/session';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: TransactionCode.name, schema: TransactionCodeSchema },
      { name: Enrollment.name, schema: EnrollmentSchema },
      { name: Session.name, schema: SessionSchema },
    ]),
  ],
  controllers: [TransactionCodeController],
  providers: [TransactionCodeService],
})
export class TransactionCodeModule {}
