import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CreateTransactionCodeDto } from './dto/create-transaction-code.dto';
import { UpdateTransactionCodeDto } from './dto/update-transaction-code.dto';
import {
  TransactionCode,
  TransactionCodeDocument,
} from './entities/transaction-code.entity';
import { Enrollment } from 'src/database/schema/enrollment';
import { Session } from 'src/database/schema/session';

@Injectable()
export class TransactionCodeService {
  constructor(
    @InjectModel(TransactionCode.name)
    private transactionCodeModel: Model<TransactionCodeDocument>,
    @InjectModel(Session.name) private sessionModel: Model<Session>,
    @InjectModel(Enrollment.name) private enrollmentModel: Model<Enrollment>,
  ) {}

  async create(
    createTransactionCodeDto: CreateTransactionCodeDto,
  ): Promise<TransactionCode> {
    let session = null;
    let sessionName = '';
    let entities = createTransactionCodeDto.entities || [];

    // Check if sessionId is provided and valid
    if (createTransactionCodeDto.sessionId) {
      session = await this.sessionModel.findById(
        createTransactionCodeDto.sessionId,
      );
      if (!session) {
        throw new NotFoundException('Session not found');
      }
      sessionName = session.name;

      if (
        createTransactionCodeDto.isAllClassSelected &&
        entities.length === 0
      ) {
        // Get all enrollments for the session to populate entities
        const sessionEnrollments = await this.enrollmentModel
          .find({ session: session._id })
          .exec();

        if (sessionEnrollments.length === 0) {
          throw new NotFoundException('No enrollments found for this session');
        }

        // Populate entities with all enrollments from the session
        entities = sessionEnrollments.map((enrollment) => ({
          entityId: enrollment._id.toString(),
          entityName: enrollment.title,
        }));
      }
    }

    // Create the transaction code with updated data
    const transactionCodeData = {
      ...createTransactionCodeDto,
      entities,
      sessionName,
    };

    const createdTransactionCode = new this.transactionCodeModel(
      transactionCodeData,
    );

    // Update enrollments with the transaction code if needed
    if (
      session &&
      (createTransactionCodeDto.isAllClassSelected || entities.length > 0)
    ) {
      if (createTransactionCodeDto.isAllClassSelected) {
        // Update all enrollments for the session
        await this.enrollmentModel
          .updateMany(
            { session: session._id },
            { transactionCodeId: createdTransactionCode._id.toString() },
          )
          .exec();
      } else if (entities.length > 0) {
        // Handle specific entities case
        const entityIds = entities.map((entity) => entity.entityId);

        // Check if all enrollments exist in a single query
        const enrollments = await this.enrollmentModel
          .find({
            _id: { $in: entityIds },
          })
          .exec();

        // Validate that all enrollments were found
        if (enrollments.length !== entityIds.length) {
          const foundIds = enrollments.map((e) => e._id.toString());
          const missingIds = entityIds.filter((id) => !foundIds.includes(id));
          throw new NotFoundException(
            `Enrollments not found: ${missingIds.join(', ')}`,
          );
        }

        // Update all enrollments in a single bulk operation
        await this.enrollmentModel
          .updateMany(
            { _id: { $in: entityIds } },
            { transactionCodeId: createdTransactionCode._id.toString() },
          )
          .exec();
      }
    }

    return createdTransactionCode.save();
  }

  async findAll(): Promise<TransactionCode[]> {
    return this.transactionCodeModel.find().exec();
  }

  async findOne(id: string): Promise<TransactionCode> {
    return this.transactionCodeModel.findById(id).exec();
  }

  async update(
    id: string,
    updateTransactionCodeDto: UpdateTransactionCodeDto,
  ): Promise<TransactionCode> {
    return this.transactionCodeModel
      .findByIdAndUpdate(id, updateTransactionCodeDto, { new: true })
      .exec();
  }

  async remove(id: string): Promise<TransactionCode> {
    return this.transactionCodeModel
      .findByIdAndUpdate(id, { isDeleted: true }, { new: true })
      .exec();
  }
}
