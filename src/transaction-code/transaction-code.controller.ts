import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
} from '@nestjs/common';
import { TransactionCodeService } from './transaction-code.service';
import { CreateTransactionCodeDto } from './dto/create-transaction-code.dto';
import { UpdateTransactionCodeDto } from './dto/update-transaction-code.dto';

@Controller('transaction-code')
export class TransactionCodeController {
  constructor(
    private readonly transactionCodeService: TransactionCodeService,
  ) {}

  @Post()
  create(@Body() createTransactionCodeDto: CreateTransactionCodeDto) {
    return this.transactionCodeService.create(createTransactionCodeDto);
  }

  @Get()
  findAll() {
    return this.transactionCodeService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.transactionCodeService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateTransactionCodeDto: UpdateTransactionCodeDto,
  ) {
    return this.transactionCodeService.update(id, updateTransactionCodeDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.transactionCodeService.remove(id);
  }
}
