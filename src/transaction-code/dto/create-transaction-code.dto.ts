import {
  Is<PERSON><PERSON>,
  IsNot<PERSON>mpty,
  Is<PERSON>rray,
  IsBoolean,
  IsOptional,
} from 'class-validator';

export class CreateTransactionCodeDto {
  @IsString()
  @IsNotEmpty()
  code: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsOptional()
  @IsString()
  sessionId: string;

  @IsOptional()
  @IsBoolean()
  isAllClassSelected: boolean;

  @IsOptional()
  @IsArray()
  entities: Array<{
    entityId: string;
  }>;
}
