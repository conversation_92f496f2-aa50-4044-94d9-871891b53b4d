import {
  Injectable,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Discount } from '../database/schema/discount';
import { CreateDiscountDto } from './dto/create-discount.dto';
import {
  UpdateDiscountDto,
  UpdateDiscountRulesDto,
} from './dto/update-discount.dto';
import { Logger } from '@nestjs/common';
import { DiscountCoupon } from 'src/database/schema/discountCoupon';

@Injectable()
export class DiscountService {
  private readonly logger = new Logger(DiscountService.name);

  constructor(
    @InjectModel(Discount.name)
    private discountModel: Model<Discount>,
    @InjectModel(DiscountCoupon.name)
    private discountCouponModel: Model<DiscountCoupon>,
  ) {}

  async create(studioId: string, createDiscountDto: CreateDiscountDto) {
    const existingDiscount = await this.discountModel.findOne({
      studioId: Types.ObjectId.createFromHexString(studioId),
      category: createDiscountDto.category,
    });

    if (existingDiscount) {
      throw new ConflictException(
        `A discount with category '${createDiscountDto.category}' already exists for this studio`,
      );
    }

    const newDiscount = new this.discountModel({
      studioId: Types.ObjectId.createFromHexString(studioId),
      ...createDiscountDto,
      excludedClasses: createDiscountDto.excludedClasses?.map((id) =>
        Types.ObjectId.createFromHexString(id),
      ),
    });
    return await newDiscount.save();
  }

  async findAll(studioId: string) {
    return await this.discountModel
      .find({
        studioId: Types.ObjectId.createFromHexString(studioId),
      })
      .exec();
  }

  async findOne(studioId: string) {
    return await this.discountModel
      .findOne({
        studioId: Types.ObjectId.createFromHexString(studioId),
      })
      .exec();
  }

  async update(studioId: string, updateDiscountDto: UpdateDiscountDto) {
    const existingDiscount = await this.discountModel.findOne({
      studioId: Types.ObjectId.createFromHexString(studioId),
      category: updateDiscountDto.category,
    });

    if (!existingDiscount) {
      throw new NotFoundException(`Discount does not exist`);
    }

    const {
      excludedClasses,
      isActive,
      type,
      discounts,
      flat,
      amountByStudent,
      byStudent,
      discountRules,
    } = updateDiscountDto;

    const updateData: any = {};

    if (excludedClasses) {
      updateData.excludedClasses = excludedClasses.map((id) =>
        Types.ObjectId.createFromHexString(id),
      );
    }

    if (isActive !== undefined) updateData.isActive = isActive;
    if (type) updateData.type = type;
    if (updateDiscountDto.category === 'multi-class' && !discounts) {
      updateData.discounts = {};
    } else if (updateDiscountDto.category === 'multi-class' && discounts) {
      updateData.discounts = discounts;
    }

    if (updateDiscountDto.category === 'multi-student') {
      if (!byStudent) {
        updateData.byStudent = {};
      } else if (byStudent) {
        updateData.byStudent = byStudent;
      }

      if (!flat) {
        updateData.flat = 0;
      } else if (flat !== undefined) {
        updateData.flat = flat;
      }

      if (!amountByStudent) {
        updateData.amountByStudent = {};
      } else if (amountByStudent) {
        updateData.amountByStudent = amountByStudent;
      }
    }

    if (discountRules !== undefined) updateData.discountRules = discountRules;

    return this.discountModel
      .findOneAndUpdate(
        {
          studioId: Types.ObjectId.createFromHexString(studioId),
          category: updateDiscountDto.category,
        },
        { $set: updateData },
        { new: true },
      )
      .exec();
  }

  async updateDiscountRules(
    studioId: string,
    updateDiscountRulesDto: UpdateDiscountRulesDto,
  ) {
    // Update all discounts for this studio with the same rules
    return this.discountModel
      .updateMany(
        {
          studioId: Types.ObjectId.createFromHexString(studioId),
        },
        {
          $set: { discountRules: updateDiscountRulesDto.discountRules },
        },
        { new: true },
      )
      .exec();
  }

  async remove(studioId: string, id: string) {
    return await this.discountModel
      .findOneAndDelete({
        _id: id,
        studioId: Types.ObjectId.createFromHexString(studioId),
      })
      .exec();
  }

  async findByCategory(
    locationId: string,
    category: 'multi-class' | 'multi-student',
  ) {
    const discount = await this.discountModel.findOne({
      studioId: locationId,
      category: category,
    });
    // .populate({
    //   path: 'excludedClasses',
    //   select: 'title',
    //   model: 'Enrollment',
    //   populate: {
    //     path: 'session',
    //     model: 'CustomForm',
    //     select: 'fieldName'
    //   }
    // });

    return discount || {};
  }

  async calculateDiscount(params: {
    studioId: string;
    student: {
      firstName: string;
      lastName: string;
      classPosition: number; // Position in student's classes
      studentPosition: number; // Position among siblings
      tuitionFee: number;
      enrollmentId: string;
    };
    category: 'multi-student' | 'multi-class' | 'all';
  }) {
    const { studioId, student, category } = params;
    let totalDiscount = 0;

    const query = {
      studioId:
        typeof studioId === 'string'
          ? Types.ObjectId.createFromHexString(studioId)
          : studioId,
      isActive: true,
    };

    // Add category filter if not 'all'
    if (category !== 'all') {
      query['category'] = category;
    }

    const discounts = await this.discountModel.find(query).lean().exec();

    // Handle null or empty response
    if (!discounts || !Array.isArray(discounts)) {
      return { totalDiscount: 0 };
    }

    for (const discount of discounts) {
      let itemDiscount = 0;

      // Skip inactive discounts (safety check)
      if (!discount.isActive) {
        continue;
      }

      // Skip discounts for zero dollar classes - business rule
      if (student.tuitionFee <= 0) {
        continue;
      }

      if (
        discount.excludedClasses?.some(
          (id) => id.toString() === student.enrollmentId.toString(),
        )
      ) {
        continue;
      }

      if (discount.type === 'flat') {
        itemDiscount = (student.tuitionFee * (discount.flat || 0)) / 100;
      } else if (discount.category === 'multi-class') {
        switch (discount.type) {
          case 'percent':
            itemDiscount =
              (student.tuitionFee *
                (discount.discounts?.[student.classPosition] || 0)) /
              100;
            break;
          case 'dollars':
            itemDiscount = discount.discounts?.[student.classPosition] || 0;
            break;
        }
      } else {
        // multi-student
        switch (discount.type) {
          case 'percent':
            itemDiscount =
              (student.tuitionFee *
                (discount.discounts?.[student.studentPosition] || 0)) /
              100;
            break;
          case 'dollars':
            itemDiscount = discount.discounts?.[student.studentPosition] || 0;
            break;
          case 'by-student':
            itemDiscount =
              (student.tuitionFee *
                (discount.byStudent?.[student.studentPosition] || 0)) /
              100;
            break;
          case 'amount-by-student':
            itemDiscount =
              discount.amountByStudent?.[student.studentPosition] || 0;
            break;
        }
      }

      totalDiscount += itemDiscount;
    }

    return {
      totalDiscount: Math.round(totalDiscount * 100) / 100,
    };
  }

  async recalculateDiscountForInvoice(
    appliedCouponId: Types.ObjectId,
    newBaseAmount: number,
    newTuitionFee: number,
  ): Promise<number> {
    try {
      const discountCoupon =
        await this.discountCouponModel.findById(appliedCouponId);
      if (!discountCoupon || !discountCoupon.isActive) {
        this.logger.warn(
          `Discount coupon ${appliedCouponId} not found or inactive`,
        );
        return 0;
      }

      let discountAmount = 0;

      if (discountCoupon.type === 'percentage') {
        discountAmount = (newTuitionFee * discountCoupon.value) / 100;
      } else if (discountCoupon.type === 'fixed') {
        discountAmount = Math.min(discountCoupon.value, newBaseAmount);
      } else {
        this.logger.warn(
          `Unknown discount type: ${discountCoupon.type} for coupon ${appliedCouponId}`,
        );
        return 0;
      }

      discountAmount = Math.max(0, Math.min(discountAmount, newBaseAmount));
      return discountAmount;
    } catch (error) {
      this.logger.error(
        `Error recalculating discount for coupon ${appliedCouponId}`,
        {
          error: error.message,
          couponId: appliedCouponId.toString(),
        },
      );
      return 0;
    }
  }
}
