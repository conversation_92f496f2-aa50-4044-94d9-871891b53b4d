import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
} from '@nestjs/common';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { DiscountService } from './discount.service';
import { CreateDiscountDto } from './dto/create-discount.dto';
import {
  UpdateDiscountDto,
  UpdateDiscountRulesDto,
} from './dto/update-discount.dto';

@Controller('discount')
@UseGuards(JwtAuthGuard)
export class DiscountController {
  constructor(private readonly discountService: DiscountService) {}

  @Post()
  create(@Body() createDiscountDto: CreateDiscountDto, @Req() request: any) {
    const locationId = request['locationId'];
    return this.discountService.create(locationId, createDiscountDto);
  }

  @Get()
  findAll(@Req() request: any) {
    const locationId = request['locationId'];
    return this.discountService.findAll(locationId);
  }

  @Get('studio')
  findOne(@Req() request: any) {
    const locationId = request['locationId'];
    return this.discountService.findOne(locationId);
  }

  @Patch('')
  update(@Body() updateDiscountDto: UpdateDiscountDto, @Req() request: any) {
    const locationId = request['locationId'];
    return this.discountService.update(locationId, updateDiscountDto);
  }

  @Patch('rules')
  updateDiscountRules(
    @Body() UpdateDiscountRulesDto: UpdateDiscountRulesDto,
    @Req() request: any,
  ) {
    const locationId = request['locationId'];
    return this.discountService.updateDiscountRules(
      locationId,
      UpdateDiscountRulesDto,
    );
  }

  @Delete(':id')
  remove(@Param('id') id: string, @Req() request: any) {
    const locationId = request['locationId'];
    return this.discountService.remove(locationId, id);
  }

  @Get('multi-class')
  findMultiClassDiscounts(@Req() request: any) {
    const locationId = request['locationId'];
    return this.discountService.findByCategory(locationId, 'multi-class');
  }

  @Get('multi-student')
  findMultiStudentDiscounts(@Req() request: any) {
    const locationId = request['locationId'];
    return this.discountService.findByCategory(locationId, 'multi-student');
  }
}
