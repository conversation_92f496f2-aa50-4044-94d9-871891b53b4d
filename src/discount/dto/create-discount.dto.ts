import {
  IsMongoId,
  IsOptional,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsArray,
  ValidateNested,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';

export class DiscountsDto {
  @IsNumber()
  @IsOptional()
  firstClass?: number;

  @IsNumber()
  @IsOptional()
  secondClass?: number;

  @IsNumber()
  @IsOptional()
  thirdClass?: number;

  @IsNumber()
  @IsOptional()
  fourthClass?: number;
}

export class CreateDiscountDto {
  @IsMongoId()
  @IsOptional()
  studioId?: string;

  @IsArray()
  @IsMongoId({ each: true })
  @IsOptional()
  excludedClasses?: string[];

  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @IsEnum(['percent', 'dollars', 'byStudent', 'flat', 'amountByStudent'])
  @IsOptional()
  type?: 'percent' | 'dollars' | 'byStudent' | 'flat' | 'amountByStudent';

  @IsObject()
  @IsOptional()
  discounts?: {
    [key: string]: number;
  };

  @IsObject()
  @IsOptional()
  byStudent?: {
    [key: string]: number;
  };

  @IsNumber()
  @IsOptional()
  flat?: number;

  @IsObject()
  @IsOptional()
  amountByStudent?: {
    [key: string]: number;
  };

  @IsEnum(['all-months', 'first-month'])
  @IsOptional()
  discountRules?: 'all-months' | 'first-month';

  @IsEnum(['multi-student', 'multi-class'])
  category: 'multi-student' | 'multi-class';
}
