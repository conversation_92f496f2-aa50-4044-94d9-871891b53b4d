import { Module } from '@nestjs/common';
import { DiscountService } from './discount.service';
import { DiscountController } from './discount.controller';
import { MongooseModule } from '@nestjs/mongoose';
import { Discount, DiscountSchema } from 'src/database/schema/discount';
import { AuthModule } from 'src/auth/auth.module';
import { DiscountCouponModule } from 'src/discount-coupon/discount-coupon.module';
import {
  DiscountCoupon,
  DiscountCouponSchema,
} from 'src/database/schema/discountCoupon';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Discount.name, schema: DiscountSchema },
      { name: DiscountCoupon.name, schema: DiscountCouponSchema },
    ]),
    AuthModule,
  ],
  controllers: [DiscountController],
  providers: [DiscountService],
  exports: [DiscountService],
})
export class DiscountModule {}
