import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as sharp from 'sharp';
import { ExternalImageProcessingService } from './external-image-processing.service';

export interface ProcessedImage {
  buffer: Buffer;
  filename: string;
  type: 'webp' | 'original';
  contentType: string;
}

@Injectable()
export class ImageProcessingService {
  private readonly logger = new Logger(ImageProcessingService.name);
  private readonly useExternalApi: boolean;

  constructor(
    private readonly configService: ConfigService,
    private readonly externalImageProcessingService: ExternalImageProcessingService,
  ) {
    this.useExternalApi = this.configService.get<boolean>(
      'USE_EXTERNAL_IMAGE_API',
      true,
    );
    this.logger.log(
      `Image processing mode: ${this.useExternalApi ? 'External API' : 'Local Sharp'}`,
    );
  }

  /**
   * Process an image to create WebP and original versions
   * @param imageBuffer - The original image buffer or base64 string
   * @param originalFilename - The original filename
   * @returns Array containing WebP and original versions
   */
  async processImageToWebPAndOriginal(
    imageBuffer: Buffer | string,
    originalFilename: string,
  ): Promise<ProcessedImage[]> {
    try {
      // Convert base64 to Buffer if needed
      const buffer = Buffer.isBuffer(imageBuffer)
        ? imageBuffer
        : Buffer.from(imageBuffer.replace(/^data:.*?;base64,/, ''), 'base64');

      const processedImages: ProcessedImage[] = [];
      const baseFilename = this.getBaseFilename(originalFilename);

      // Create WebP version using external API or fallback to Sharp
      try {
        let webpImage: ProcessedImage;

        if (this.useExternalApi) {
          try {
            // Check if external API is available
            const isApiHealthy =
              await this.externalImageProcessingService.healthCheck();
            if (isApiHealthy) {
              webpImage =
                await this.externalImageProcessingService.convertToWebP(
                  buffer,
                  originalFilename,
                  50, // quality
                );
              this.logger.debug('Created WebP version using external API');
            } else {
              throw new Error('External API health check failed');
            }
          } catch (externalError) {
            this.logger.warn('External API failed, falling back to Sharp', {
              error: externalError.message,
            });
            // Fallback to Sharp
            const webpBuffer = await sharp(buffer)
              .webp({ quality: 50, effort: 6 })
              .toBuffer();

            webpImage = {
              buffer: webpBuffer,
              filename: `${baseFilename}.webp`,
              type: 'webp',
              contentType: 'image/webp',
            };
            this.logger.debug('Created WebP version using Sharp fallback');
          }
        } else {
          // Use Sharp directly
          const webpBuffer = await sharp(buffer)
            .webp({ quality: 50, effort: 6 })
            .toBuffer();

          webpImage = {
            buffer: webpBuffer,
            filename: `${baseFilename}.webp`,
            type: 'webp',
            contentType: 'image/webp',
          };
          this.logger.debug('Created WebP version using Sharp');
        }

        processedImages.push(webpImage);
      } catch (webpError) {
        this.logger.error('Failed to create WebP version', {
          error: webpError.message,
        });
      }

      // Include original with preserved format
      try {
        const originalExtension = originalFilename
          .split('.')
          .pop()
          ?.toLowerCase();
        const contentTypeMap: Record<string, string> = {
          png: 'image/png',
          jpg: 'image/jpeg',
          jpeg: 'image/jpeg',
          gif: 'image/gif',
          bmp: 'image/bmp',
          webp: 'image/webp',
        };

        processedImages.push({
          buffer: buffer,
          filename: originalFilename,
          type: 'original',
          contentType: contentTypeMap[originalExtension || ''] || 'image/jpeg',
        });

        this.logger.debug('Added original version');
      } catch (originalError) {
        this.logger.error('Failed to process original image', {
          error: originalError.message,
        });
      }

      this.logger.log(
        `Successfully processed ${processedImages.length} image versions (WebP + Original)`,
      );
      return processedImages;
    } catch (error) {
      this.logger.error('Failed to process image', {
        error: error.message,
        stack: error.stack,
        originalFilename,
      });
      throw new Error(`Image processing failed: ${error.message}`);
    }
  }

  /**
   * Get base filename without extension
   */
  private getBaseFilename(filename: string): string {
    const lastDotIndex = filename.lastIndexOf('.');
    return lastDotIndex > 0 ? filename.substring(0, lastDotIndex) : filename;
  }

  /**
   * Get image metadata
   */
  async getImageMetadata(
    imageBuffer: Buffer | string,
  ): Promise<sharp.Metadata> {
    try {
      const buffer = Buffer.isBuffer(imageBuffer)
        ? imageBuffer
        : Buffer.from(imageBuffer.replace(/^data:.*?;base64,/, ''), 'base64');

      return await sharp(buffer).metadata();
    } catch (error) {
      this.logger.error('Failed to get image metadata', error);
      throw new Error(`Failed to get image metadata: ${error.message}`);
    }
  }

  /**
   * Convert a single image to WebP format
   */
  async convertToWebP(
    imageBuffer: Buffer | string,
    filename: string,
    quality: number = 90,
  ): Promise<ProcessedImage> {
    try {
      const buffer = Buffer.isBuffer(imageBuffer)
        ? imageBuffer
        : Buffer.from(imageBuffer.replace(/^data:.*?;base64,/, ''), 'base64');

      // Use external API or fallback to Sharp
      if (this.useExternalApi) {
        try {
          // Check if external API is available
          const isApiHealthy =
            await this.externalImageProcessingService.healthCheck();
          if (isApiHealthy) {
            const result =
              await this.externalImageProcessingService.convertToWebP(
                buffer,
                filename,
                quality,
              );
            this.logger.debug('Converted to WebP using external API');
            return result;
          } else {
            throw new Error('External API health check failed');
          }
        } catch (externalError) {
          this.logger.warn('External API failed, falling back to Sharp', {
            error: externalError.message,
            filename,
          });
          // Continue to Sharp fallback below
        }
      }

      // Use Sharp (either by choice or as fallback)
      const webpBuffer = await sharp(buffer)
        .webp({ quality, effort: 6 })
        .toBuffer();

      const baseFilename = this.getBaseFilename(filename);

      this.logger.debug('Converted to WebP using Sharp');
      return {
        buffer: webpBuffer,
        filename: `${baseFilename}.webp`,
        type: 'webp',
        contentType: 'image/webp',
      };
    } catch (error) {
      this.logger.error('Failed to convert image to WebP', {
        error: error.message,
        filename,
      });
      throw new Error(`WebP conversion failed: ${error.message}`);
    }
  }
}
