import * as dayjs from 'dayjs';

export interface ProrationParams {
  startDate: string | Date;
  endDate: string | Date;
  billingDay: number;
  tuitionBillingCycle: 'weekly' | 'bi-weekly' | 'monthly' | 'one-time';
  tuitionFee: number;
  today?: string | Date; // Optional - defaults to current date
}

/**
 * Calculates the prorated amount for a billing cycle
 * @param params - Proration parameters
 * @returns The prorated amount
 */
export function calculateProratedAmount(params: ProrationParams): number {
  let proratedAmount = 0;
  const today = params.today ? dayjs(params.today) : dayjs();
  const classStartDate = dayjs(params.startDate);

  if (params.tuitionBillingCycle === 'weekly') {
    const isProrated = today.isAfter(classStartDate);

    if (!isProrated) {
      proratedAmount = params.tuitionFee;
    } else {
      // Weekly billing logic - billingDay is day of week (0=Sunday, 1=Monday, etc.)
      const DAYS_IN_WEEK = 7;
      const dailyRate = params.tuitionFee / DAYS_IN_WEEK;

      // Use the later of today or class start date as the effective start
      const effectiveStartDate = today.isAfter(classStartDate)
        ? today
        : classStartDate;

      // Find the first billing date from class start
      let firstBillingDate = dayjs(classStartDate);

      // Adjust to the correct day of week for billing
      const currentDayOfWeek = firstBillingDate.day(); // 0=Sunday, 1=Monday, etc.
      const billingDayOfWeek = params.billingDay; // Should be 0-6

      let daysToAdd = billingDayOfWeek - currentDayOfWeek;
      if (daysToAdd <= 0) {
        daysToAdd += 7; // Move to next week if billing day has passed
      }

      firstBillingDate = firstBillingDate.add(daysToAdd, 'days');

      // Find the next billing date after effective start date
      let nextBillingDate = dayjs(firstBillingDate);

      // Keep adding 1 week until we find a date after effective start date
      while (
        nextBillingDate.isBefore(effectiveStartDate, 'day') ||
        nextBillingDate.isSame(effectiveStartDate, 'day')
      ) {
        nextBillingDate = nextBillingDate.add(1, 'week');
      }

      // Calculate days from effective start date to next billing date
      const daysToCharge = nextBillingDate.diff(effectiveStartDate, 'days');

      proratedAmount = Math.round(dailyRate * daysToCharge * 100) / 100;
    }
  } else if (params.tuitionBillingCycle === 'bi-weekly') {
    const isProrated = today.isAfter(classStartDate);

    if (!isProrated) {
      proratedAmount = params.tuitionFee;
    } else {
      // Bi-weekly billing logic - billingDay is day of month
      const DAYS_IN_TWO_WEEKS = 14;
      const dailyRate = params.tuitionFee / DAYS_IN_TWO_WEEKS;

      // Use the later of today or class start date as the effective start
      const effectiveStartDate = today.isAfter(classStartDate)
        ? today
        : classStartDate;

      // Find the first billing date from class start
      let firstBillingDate = dayjs(classStartDate);

      // Adjust to the correct day of week for billing
      const currentDayOfWeek = firstBillingDate.day(); // 0=Sunday, 1=Monday, etc.
      const billingDayOfWeek = params.billingDay; // Should be 0-6

      let daysToAdd = billingDayOfWeek - currentDayOfWeek;
      if (daysToAdd <= 0) {
        daysToAdd += 7; // Move to next week if billing day has passed
      }

      firstBillingDate = firstBillingDate.add(daysToAdd, 'days');

      // Find the next billing date after effective start date
      let nextBillingDate = dayjs(firstBillingDate);

      // Keep adding 1 week until we find a date after effective start date
      while (
        nextBillingDate.isBefore(effectiveStartDate, 'day') ||
        nextBillingDate.isSame(effectiveStartDate, 'day')
      ) {
        nextBillingDate = nextBillingDate.add(2, 'week');
      }

      // Calculate days from effective start date to next billing date
      const daysToCharge = nextBillingDate.diff(effectiveStartDate, 'days');

      proratedAmount = Math.round(dailyRate * daysToCharge * 100) / 100;
    }
  } else if (params.tuitionBillingCycle === 'one-time') {
    proratedAmount = params.tuitionFee;
  } else {
    // Monthly billing logic - SIMPLIFIED AND FIXED

    // If today is before class start, charge full amount
    if (today.isBefore(classStartDate, 'day')) {
      proratedAmount = params.tuitionFee;
    } else {
      // Use the later of today or class start date
      const effectiveStartDate = today.isAfter(classStartDate)
        ? today
        : classStartDate;

      // Find the first billing date from class start
      let firstBillingDate = dayjs(classStartDate).date(params.billingDay);

      // If billing day is before class start day in same month, move to next month
      if (firstBillingDate.isBefore(classStartDate, 'day')) {
        firstBillingDate = firstBillingDate.add(1, 'month');
      }

      // Find next billing date after effective start date
      let nextBillingDate = dayjs(firstBillingDate);
      let attempts = 0;
      while (
        (nextBillingDate.isBefore(effectiveStartDate, 'day') ||
          nextBillingDate.isSame(effectiveStartDate, 'day')) &&
        attempts < 12
      ) {
        nextBillingDate = nextBillingDate.add(1, 'month');
        attempts++;
      }

      if (attempts >= 12) {
        throw new Error('Could not find valid billing date within 12 months');
      }

      // Check if class has an end date and limit billing to that
      let billingEndDate = dayjs(nextBillingDate);
      if (params.endDate) {
        const classEndDate = dayjs(params.endDate);
        if (classEndDate.isBefore(nextBillingDate, 'day')) {
          billingEndDate = dayjs(classEndDate).add(1, 'day'); // Include end date
        }
      }

      // Calculate days from effective start to billing end date
      const totalDays = billingEndDate.diff(effectiveStartDate, 'days');

      if (totalDays <= 0) {
        return 0;
      }

      // Use the month where most of the period falls for daily rate calculation
      const midPoint = dayjs(effectiveStartDate).add(
        Math.floor(totalDays / 2),
        'days',
      );
      const daysInBillingMonth = midPoint.endOf('month').date();
      const dailyRate = params.tuitionFee / daysInBillingMonth;

      proratedAmount = Math.round(dailyRate * totalDays * 100) / 100;
    }
  }

  return proratedAmount;
}

/**
 * Helper function to calculate proration from entity object (for backward compatibility)
 * @param entity - Entity with billing information
 * @param todayOverride - Optional date override for testing
 * @returns The prorated amount
 */
export function calculateProratedAmountFromEntity(
  entity: any,
  todayOverride?: string | Date,
): number {
  return calculateProratedAmount({
    startDate: entity.startDate,
    endDate: entity.endDate,
    billingDay: entity.billingDay,
    tuitionBillingCycle: entity.tuitionBillingCycle,
    tuitionFee: entity.tuitionFee,
    today: todayOverride,
  });
}

export function isScheduledEntity(entity: any) {
  if (!entity?.session) {
    return false;
  }
  return (
    dayjs().isBefore(dayjs(entity.session.billingDate), 'day') &&
    entity.session.billingDate !== null
  );
}
