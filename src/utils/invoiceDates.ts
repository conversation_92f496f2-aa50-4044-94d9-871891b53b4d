// OPTIMIZED Invoice Dates Generation - Maximum performance with safety
// Dedicated module for invoice date calculations
import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

// Ultra-fast fixed interval generation (weekly, bi-weekly)
const generateFixedInterval = (
  startTime: number,
  endTime: number,
  intervalMs: number,
): Date[] => {
  const dates: Date[] = [];
  let currentTime = startTime;

  // Generate dates
  while (currentTime < endTime) {
    dates.push(new Date(currentTime));
    currentTime += intervalMs;
  }

  return dates;
};

// Optimized monthly generation (handles month-end edge cases)
const generateMonthlyOptimized = (
  startDate: Date,
  endDate: Date,
  billingDay?: number,
): Date[] => {
  const dates: Date[] = [];

  if (!billingDay) {
    throw new Error('Billing day is required for monthly billing');
  }

  // Use UTC to avoid timezone issues
  const startUTC = dayjs.utc(startDate);
  const endUTC = dayjs.utc(endDate);

  // Get the billing date for the start month in UTC
  const firstBillingDate = startUTC.startOf('month').date(billingDay);

  // Get the billing date for the end month in UTC
  const lastBillingDate = endUTC.startOf('month').date(billingDay);

  // Only include the last billing date if end date >= billing day of end month
  const shouldIncludeLastBilling = endUTC.date() >= billingDay;
  const actualLastBillingDate = shouldIncludeLastBilling
    ? lastBillingDate
    : lastBillingDate.subtract(1, 'month');

  // Generate all billing dates from start month to end month
  let currentDate = firstBillingDate;

  while (!currentDate.isAfter(actualLastBillingDate)) {
    dates.push(currentDate.toDate());
    currentDate = currentDate.add(1, 'month');
  }

  return dates;
};

// Optimized yearly generation
const generateYearlyOptimized = (startDate: Date, endDate: Date): Date[] => {
  const dates: Date[] = [];
  const start = dayjs(startDate);
  const end = dayjs(endDate);

  // Always add the start date
  dates.push(start.toDate());

  let currentDate = start.add(1, 'year');
  while (currentDate.isBefore(end)) {
    dates.push(currentDate.toDate());
    currentDate = currentDate.add(1, 'year');
  }

  return dates;
};

// MAIN EXPORT - Best optimized version
export const generateInvoiceDates = (
  startDate: Date,
  endDate: Date,
  cycle: string,
  billingDay?: number,
): Date[] => {
  // Fast input validation
  if (!startDate || !endDate || !cycle) {
    throw new Error(
      'Invalid input: startDate, endDate, and cycle are required',
    );
  }

  const start = dayjs(startDate);
  const end = dayjs(endDate);

  // Fast date validation
  if (!start.isValid() || !end.isValid() || start.isAfter(end)) {
    throw new Error('Invalid date range');
  }

  const cycleLower = cycle.toLowerCase();

  // Pre-calculate for safety check (max 10 years)
  const diffDays = end.diff(start, 'day');
  if (diffDays > 3650) {
    throw new Error('Date range too large');
  }

  switch (cycleLower) {
    case 'weekly':
      return generateFixedInterval(start.valueOf(), end.valueOf(), 604800000); // 7 * 24 * 60 * 60 * 1000
    case 'bi-weekly':
      return generateFixedInterval(start.valueOf(), end.valueOf(), 1209600000); // 14 * 24 * 60 * 60 * 1000
    case 'monthly':
      return generateMonthlyOptimized(startDate, endDate, billingDay);
    case 'yearly':
      return generateYearlyOptimized(startDate, endDate);
    default:
      throw new Error(
        `Invalid cycle: ${cycle}. Must be one of: weekly, bi-weekly, monthly, yearly`,
      );
  }
};

// Export types for better TypeScript support
export type BillingCycle = 'weekly' | 'bi-weekly' | 'monthly' | 'yearly';

// UTILITY: Validate billing cycle
export const isValidBillingCycle = (cycle: string): cycle is BillingCycle => {
  return ['weekly', 'bi-weekly', 'monthly', 'yearly'].includes(
    cycle.toLowerCase(),
  );
};

// UTILITY: Estimate invoice count (useful for UI/planning)
export const estimateInvoiceCount = (
  startDate: Date,
  endDate: Date,
  cycle: string,
): number => {
  if (!startDate || !endDate || !cycle) return 0;

  const start = dayjs(startDate);
  const end = dayjs(endDate);
  const diffDays = end.diff(start, 'day');

  switch (cycle.toLowerCase()) {
    case 'weekly':
      return Math.ceil(diffDays / 7);
    case 'bi-weekly':
      return Math.ceil(diffDays / 14);
    case 'monthly':
      return Math.ceil(diffDays / 30); // Rough estimate
    case 'yearly':
      return Math.ceil(diffDays / 365);
    default:
      return 0;
  }
};
