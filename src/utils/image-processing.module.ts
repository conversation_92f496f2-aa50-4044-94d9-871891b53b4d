import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { ImageProcessingService } from './image-processing.service';
import { ExternalImageProcessingService } from './external-image-processing.service';

@Module({
  imports: [
    HttpModule.register({
      timeout: 30000, // 30 seconds timeout for image processing
      maxRedirects: 5,
    }),
    ConfigModule,
  ],
  providers: [ImageProcessingService, ExternalImageProcessingService],
  exports: [ImageProcessingService, ExternalImageProcessingService],
})
export class ImageProcessingModule {}
