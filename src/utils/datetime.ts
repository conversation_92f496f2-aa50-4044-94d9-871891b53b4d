import * as dayjs from 'dayjs';

export const formatTimeToDate = (
  timeStr: string,
  baseDate: Date = new Date(),
) => {
  const [time, period] = timeStr.replace('.', ':').split(' ');
  const [hours, minutes] = time.split(':');

  // Create base date
  let date = dayjs(baseDate);

  // Set the time components
  date = date.hour(
    period.toUpperCase() === 'PM' && parseInt(hours) !== 12
      ? parseInt(hours) + 12
      : period.toUpperCase() === 'AM' && parseInt(hours) === 12
        ? 0
        : parseInt(hours),
  );
  date = date.minute(parseInt(minutes));
  date = date.second(0);
  date = date.millisecond(0);

  return date;
};

export const formatDateTime = (date: Date | dayjs.Dayjs) => {
  const dayjsDate = dayjs.isDayjs(date) ? date : dayjs(date);
  return dayjsDate.format('MMM D, YYYY, h:mm:ss A');
};

export const formatTime = (time: string) => {
  if (!time) return '';

  const [hours, minutes] = time.split(':');
  const hour = parseInt(hours, 10);
  const minute = parseInt(minutes, 10);

  const period = hour >= 12 ? 'PM' : 'AM';
  const formattedHour = hour % 12 || 12;
  const formattedMinute = minute.toString().padStart(2, '0');

  return `${formattedHour}:${formattedMinute} ${period}`;
};
