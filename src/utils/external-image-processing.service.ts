import { Injectable, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { ProcessedImage } from './image-processing.service';

export interface ImageProcessingApiResponse {
  success: boolean;
  data?: any;
  error?: string;
  message?: string;
}

export interface ImageConversionOptions {
  format?: 'jpeg' | 'jpg' | 'png' | 'webp' | 'avif' | 'tiff';
  quality?: number;
  width?: number;
  height?: number;
  fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
  background?: string;
  progressive?: boolean;
  lossless?: boolean;
}

export interface ImageMetadata {
  format: string;
  width: number;
  height: number;
  channels: number;
  depth: string;
  density: number;
  hasAlpha: boolean;
  hasProfile: boolean;
  isAnimated: boolean;
  pages: number;
  originalName: string;
  uploadSize: number;
}

@Injectable()
export class ExternalImageProcessingService {
  private readonly logger = new Logger(ExternalImageProcessingService.name);
  private readonly apiBaseUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.apiBaseUrl = this.configService.get<string>(
      'IMAGE_PROCESSING_API_URL',
      'https://enrollio-image-processing-670370710253.us-central1.run.app',
    );
  }

  /**
   * Convert image to WebP format using external API
   */
  async convertToWebP(
    imageBuffer: Buffer | string,
    filename: string,
    quality: number = 90,
  ): Promise<ProcessedImage> {
    const startTime = Date.now();
    try {
      const buffer = Buffer.isBuffer(imageBuffer)
        ? imageBuffer
        : Buffer.from(imageBuffer.replace(/^data:.*?;base64,/, ''), 'base64');

      this.logger.debug(`Starting WebP conversion for ${filename}`, {
        originalSize: buffer.length,
        quality,
        apiUrl: this.apiBaseUrl,
      });

      const formData = new FormData();
      const blob = new Blob([buffer], { type: 'image/*' });
      formData.append('image', blob, filename);

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.apiBaseUrl}/api/image/convert?format=webp&quality=${quality}`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
            responseType: 'arraybuffer',
            timeout: 30000, // 30 second timeout
          },
        ),
      );

      const webpBuffer = Buffer.from(response.data);
      const baseFilename = this.getBaseFilename(filename);
      const processingTime = Date.now() - startTime;

      this.logger.log(`Successfully converted ${filename} to WebP`, {
        originalSize: buffer.length,
        webpSize: webpBuffer.length,
        compressionRatio:
          ((1 - webpBuffer.length / buffer.length) * 100).toFixed(2) + '%',
        processingTimeMs: processingTime,
        quality,
      });

      return {
        buffer: webpBuffer,
        filename: `${baseFilename}.webp`,
        type: 'webp',
        contentType: 'image/webp',
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      const errorDetails = {
        error: error.message,
        filename,
        quality,
        apiUrl: this.apiBaseUrl,
        processingTimeMs: processingTime,
        statusCode: error.response?.status,
        statusText: error.response?.statusText,
      };

      if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
        this.logger.error(
          'External image processing API is unreachable',
          errorDetails,
        );
        throw new HttpException(
          'Image processing service is temporarily unavailable',
          HttpStatus.SERVICE_UNAVAILABLE,
        );
      } else if (error.response?.status === 413) {
        this.logger.error(
          'Image file too large for external API',
          errorDetails,
        );
        throw new HttpException(
          'Image file is too large for processing',
          HttpStatus.PAYLOAD_TOO_LARGE,
        );
      } else if (error.response?.status === 415) {
        this.logger.error(
          'Unsupported image format for external API',
          errorDetails,
        );
        throw new HttpException(
          'Unsupported image format',
          HttpStatus.UNSUPPORTED_MEDIA_TYPE,
        );
      } else if (
        error.code === 'ECONNABORTED' ||
        error.message.includes('timeout')
      ) {
        this.logger.error(
          'External image processing API timeout',
          errorDetails,
        );
        throw new HttpException(
          'Image processing timeout - please try again',
          HttpStatus.REQUEST_TIMEOUT,
        );
      } else {
        this.logger.error(
          'Failed to convert image to WebP using external API',
          errorDetails,
        );
        throw new HttpException(
          `WebP conversion failed: ${error.message}`,
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }
    }
  }

  /**
   * Convert image to any supported format using external API
   */
  async convertImage(
    imageBuffer: Buffer | string,
    filename: string,
    options: ImageConversionOptions,
  ): Promise<ProcessedImage> {
    try {
      const buffer = Buffer.isBuffer(imageBuffer)
        ? imageBuffer
        : Buffer.from(imageBuffer.replace(/^data:.*?;base64,/, ''), 'base64');

      const formData = new FormData();
      const blob = new Blob([buffer], { type: 'image/*' });
      formData.append('image', blob, filename);

      // Build query parameters
      const queryParams = new URLSearchParams();
      if (options.format) queryParams.append('format', options.format);
      if (options.quality)
        queryParams.append('quality', options.quality.toString());
      if (options.width) queryParams.append('width', options.width.toString());
      if (options.height)
        queryParams.append('height', options.height.toString());
      if (options.fit) queryParams.append('fit', options.fit);
      if (options.background)
        queryParams.append('background', options.background);
      if (options.progressive !== undefined)
        queryParams.append('progressive', options.progressive.toString());
      if (options.lossless !== undefined)
        queryParams.append('lossless', options.lossless.toString());

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.apiBaseUrl}/api/image/convert?${queryParams.toString()}`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
            responseType: 'arraybuffer',
          },
        ),
      );

      const convertedBuffer = Buffer.from(response.data);
      const baseFilename = this.getBaseFilename(filename);
      const outputFormat = options.format || 'webp';

      this.logger.debug(
        `Successfully converted ${filename} to ${outputFormat}`,
        {
          originalSize: buffer.length,
          convertedSize: convertedBuffer.length,
          compressionRatio:
            ((1 - convertedBuffer.length / buffer.length) * 100).toFixed(2) +
            '%',
        },
      );

      return {
        buffer: convertedBuffer,
        filename: `${baseFilename}.${outputFormat}`,
        type: outputFormat as 'webp' | 'original',
        contentType: this.getContentType(outputFormat),
      };
    } catch (error) {
      this.logger.error('Failed to convert image using external API', {
        error: error.message,
        filename,
        options,
        apiUrl: this.apiBaseUrl,
      });
      throw new HttpException(
        `Image conversion failed: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Resize image using external API
   */
  async resizeImage(
    imageBuffer: Buffer | string,
    filename: string,
    width?: number,
    height?: number,
    fit: string = 'cover',
    format?: string,
  ): Promise<ProcessedImage> {
    try {
      const buffer = Buffer.isBuffer(imageBuffer)
        ? imageBuffer
        : Buffer.from(imageBuffer.replace(/^data:.*?;base64,/, ''), 'base64');

      const formData = new FormData();
      const blob = new Blob([buffer], { type: 'image/*' });
      formData.append('image', blob, filename);

      // Build query parameters
      const queryParams = new URLSearchParams();
      if (width) queryParams.append('width', width.toString());
      if (height) queryParams.append('height', height.toString());
      queryParams.append('fit', fit);
      if (format) queryParams.append('format', format);

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.apiBaseUrl}/api/image/resize?${queryParams.toString()}`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
            responseType: 'arraybuffer',
          },
        ),
      );

      const resizedBuffer = Buffer.from(response.data);
      const baseFilename = this.getBaseFilename(filename);
      const outputFormat = format || this.getOriginalFormat(filename);

      this.logger.debug(`Successfully resized ${filename}`, {
        originalSize: buffer.length,
        resizedSize: resizedBuffer.length,
        dimensions: `${width}x${height}`,
        fit,
      });

      return {
        buffer: resizedBuffer,
        filename: `${baseFilename}.${outputFormat}`,
        type: outputFormat === 'webp' ? 'webp' : 'original',
        contentType: this.getContentType(outputFormat),
      };
    } catch (error) {
      this.logger.error('Failed to resize image using external API', {
        error: error.message,
        filename,
        width,
        height,
        fit,
        apiUrl: this.apiBaseUrl,
      });
      throw new HttpException(
        `Image resize failed: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Optimize image using external API
   */
  async optimizeImage(
    imageBuffer: Buffer | string,
    filename: string,
    quality?: number,
  ): Promise<ProcessedImage> {
    try {
      const buffer = Buffer.isBuffer(imageBuffer)
        ? imageBuffer
        : Buffer.from(imageBuffer.replace(/^data:.*?;base64,/, ''), 'base64');

      const formData = new FormData();
      const blob = new Blob([buffer], { type: 'image/*' });
      formData.append('image', blob, filename);

      // Build query parameters
      const queryParams = new URLSearchParams();
      if (quality) queryParams.append('quality', quality.toString());

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.apiBaseUrl}/api/image/optimize?${queryParams.toString()}`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data',
            },
            responseType: 'arraybuffer',
          },
        ),
      );

      const optimizedBuffer = Buffer.from(response.data);
      const originalFormat = this.getOriginalFormat(filename);

      this.logger.debug(`Successfully optimized ${filename}`, {
        originalSize: buffer.length,
        optimizedSize: optimizedBuffer.length,
        compressionRatio:
          ((1 - optimizedBuffer.length / buffer.length) * 100).toFixed(2) + '%',
      });

      return {
        buffer: optimizedBuffer,
        filename: filename,
        type: 'original',
        contentType: this.getContentType(originalFormat),
      };
    } catch (error) {
      this.logger.error('Failed to optimize image using external API', {
        error: error.message,
        filename,
        quality,
        apiUrl: this.apiBaseUrl,
      });
      throw new HttpException(
        `Image optimization failed: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get image metadata using external API
   */
  async getImageMetadata(
    imageBuffer: Buffer | string,
    filename: string,
  ): Promise<ImageMetadata> {
    try {
      const buffer = Buffer.isBuffer(imageBuffer)
        ? imageBuffer
        : Buffer.from(imageBuffer.replace(/^data:.*?;base64,/, ''), 'base64');

      const formData = new FormData();
      const blob = new Blob([buffer], { type: 'image/*' });
      formData.append('image', blob, filename);

      const response = await firstValueFrom(
        this.httpService.post(`${this.apiBaseUrl}/api/image/info`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }),
      );

      const apiResponse: ImageProcessingApiResponse = response.data;
      if (!apiResponse.success) {
        throw new Error(apiResponse.error || 'Failed to get image metadata');
      }

      return apiResponse.data as ImageMetadata;
    } catch (error) {
      this.logger.error('Failed to get image metadata using external API', {
        error: error.message,
        filename,
        apiUrl: this.apiBaseUrl,
      });
      throw new HttpException(
        `Failed to get image metadata: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Check if external API is available
   */
  async healthCheck(): Promise<boolean> {
    try {
      const startTime = Date.now();
      const response = await firstValueFrom(
        this.httpService.get(`${this.apiBaseUrl}/health`, {
          timeout: 5000,
        }),
      );
      const responseTime = Date.now() - startTime;

      const isHealthy = response.status === 200;

      if (isHealthy) {
        this.logger.debug('External image processing API health check passed', {
          apiUrl: this.apiBaseUrl,
          responseTimeMs: responseTime,
          status: response.status,
        });
      } else {
        this.logger.warn(
          'External image processing API health check returned non-200 status',
          {
            apiUrl: this.apiBaseUrl,
            responseTimeMs: responseTime,
            status: response.status,
          },
        );
      }

      return isHealthy;
    } catch (error) {
      const errorDetails = {
        error: error.message,
        apiUrl: this.apiBaseUrl,
        code: error.code,
        statusCode: error.response?.status,
      };

      if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
        this.logger.warn(
          'External image processing API is unreachable',
          errorDetails,
        );
      } else if (
        error.code === 'ECONNABORTED' ||
        error.message.includes('timeout')
      ) {
        this.logger.warn(
          'External image processing API health check timeout',
          errorDetails,
        );
      } else {
        this.logger.warn(
          'External image processing API health check failed',
          errorDetails,
        );
      }

      return false;
    }
  }

  /**
   * Helper method to get base filename without extension
   */
  private getBaseFilename(filename: string): string {
    return filename.replace(/\.[^/.]+$/, '');
  }

  /**
   * Helper method to get original format from filename
   */
  private getOriginalFormat(filename: string): string {
    const extension = filename.split('.').pop()?.toLowerCase();
    return extension || 'jpg';
  }

  /**
   * Helper method to get content type from format
   */
  private getContentType(format: string): string {
    const contentTypeMap: Record<string, string> = {
      png: 'image/png',
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      gif: 'image/gif',
      bmp: 'image/bmp',
      webp: 'image/webp',
      avif: 'image/avif',
      tiff: 'image/tiff',
      tif: 'image/tiff',
    };
    return contentTypeMap[format.toLowerCase()] || 'image/jpeg';
  }
}
