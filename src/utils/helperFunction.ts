import { CreateEnrollmentDto } from 'src/enrollment/dto/create-enrollment.dto';
import { RecurrenceFrequency } from 'src/gohighlevel/dto/createCalendarDto';
import {
  addDays,
  startOfYear,
  endOfYear,
  differenceInMinutes,
  differenceInDays,
  addMonths,
  differenceInMonths,
  addYears,
  differenceInYears,
} from 'date-fns';
import { Model, Types } from 'mongoose';
import { Proration } from 'src/database/schema/prorations';
import { CreateEventDto } from 'src/events/dto/create-event.dto';
import { formatInTimeZone, fromZonedTime } from 'date-fns-tz';
import * as dayjs from 'dayjs';
import axios from 'axios';

// Helper function to calculate age based on DOB
export async function calculateAge(dob: Date): Promise<string> {
  const ageDiffMs = Date.now() - dob.getTime();
  const ageDate = new Date(ageDiffMs);
  return `${Math.abs(ageDate.getUTCFullYear() - 1970)}yr ${ageDate.getUTCMonth()}m`;
}

export function convertTo24HourTime(time: string): {
  hour: number;
  minute: number;
} {
  // Input example: "Dec 11, 2024, 4:14:55 AM"
  const [timeWithSeconds, period] = time.split(' ').slice(-2); // ["4:14:55", "AM"]
  const [hourStr, minuteStr] = timeWithSeconds.split(':'); // ["4", "14", "55"]
  let hour = parseInt(hourStr);

  // Convert to 24-hour format without any timezone manipulation
  if (period === 'PM' && hour !== 12) {
    hour += 12;
  } else if (period === 'AM' && hour === 12) {
    hour = 0;
  }

  return {
    hour,
    minute: parseInt(minuteStr),
  };
}

export function calculateSlotDuration(
  startTime: string,
  endTime: string,
): number {
  const startDate = new Date(startTime);
  const endDate = new Date(endTime);
  return differenceInMinutes(endDate, startDate);
}

export function mapIntervalUpperCase(
  billingCycle: string,
): RecurrenceFrequency {
  const cycleMap: { [key: string]: RecurrenceFrequency } = {
    Daily: RecurrenceFrequency.DAILY,
    Weekly: RecurrenceFrequency.WEEKLY,
    Monthly: RecurrenceFrequency.MONTHLY,
  };
  return cycleMap[billingCycle] || RecurrenceFrequency.WEEKLY;
}

export function mapInterval(billingCycle: string): {
  interval: string;
  isProrated: boolean;
} {
  switch (billingCycle.toLowerCase()) {
    case 'prorated':
      return {
        interval: 'month',
        isProrated: true,
      };
    case 'monthly':
      return {
        interval: 'month',
        isProrated: false,
      };
    case 'weekly':
      return {
        interval: 'week',
        isProrated: false,
      };
    case 'yearly':
      return {
        interval: 'year',
        isProrated: false,
      };
    default:
      return {
        interval: 'month',
        isProrated: false,
      };
  }
}

export async function createBodyToCreatePriceForAProductInGHL(
  createEnrollmentDto: CreateEnrollmentDto | CreateEventDto,
  locationId: string,
  actionType: 'class' | 'event',
  ghlProductId?: string,
  prorationModel?: Model<Proration>,
  currency?: string,
) {
  const { interval, isProrated } = mapInterval(
    createEnrollmentDto.tuitionBillingCycle,
  );

  const initialStartDate = new Date(createEnrollmentDto.startDate);
  const finalEndDate = new Date(createEnrollmentDto.endDate);

  if (isProrated) {
    // Calculate initial proration
    const daysInFirstMonth = new Date(
      initialStartDate.getFullYear(),
      initialStartDate.getMonth() + 1,
      0,
    ).getDate();
    const remainingDays = daysInFirstMonth - initialStartDate.getDate() + 1;
    const tuitionFee = Number(createEnrollmentDto.tuitionFee);
    const initialProratedAmount = Number(
      (tuitionFee / daysInFirstMonth) * remainingDays,
    ).toFixed();

    const firstOfNextMonth = new Date(
      initialStartDate.getFullYear(),
      initialStartDate.getMonth() + 1,
      1,
    );

    const daysInLastMonth = new Date(
      finalEndDate.getFullYear(),
      finalEndDate.getMonth() + 1,
      0,
    ).getDate();

    const finalProratedAmount = Number(
      (tuitionFee / daysInLastMonth) * finalEndDate.getDate(),
    ).toFixed();

    // Save proration data to MongoDB
    await prorationModel.create({
      ghlProductId,
      initial: {
        amount: initialProratedAmount,
        startDate: initialStartDate.toISOString(),
        endDate: new Date(firstOfNextMonth.getTime() - 1).toISOString(),
        description: `Initial prorated amount for ${remainingDays} days`,
        daysInMonth: daysInFirstMonth,
        remainingDays: remainingDays,
      },
      final: {
        amount: finalProratedAmount,
        startDate: new Date(
          finalEndDate.getFullYear(),
          finalEndDate.getMonth(),
          1,
        ).toISOString(),
        endDate: finalEndDate.toISOString(),
        description: `Final prorated amount for ${finalEndDate.getDate()} days`,
        daysInMonth: daysInLastMonth,
        remainingDays: finalEndDate.getDate(),
      },
      regularAmount: tuitionFee,
      billingCycleAnchor: firstOfNextMonth.toISOString(),
      isProrated: isProrated,
    });
  } else {
    const tuitionFee = Number(createEnrollmentDto.tuitionFee);

    let initialEndDate, finalStartDate, finalProratedAmount;
    if (interval === 'week') {
      initialEndDate = addDays(initialStartDate, 6);
      const diffDays = differenceInDays(finalEndDate, initialStartDate);
      const weeks = Math.floor(diffDays / 7);
      finalStartDate = addDays(initialStartDate, weeks * 7);

      finalProratedAmount = Math.round((tuitionFee / 7) * diffDays);
    } else if (interval === 'month') {
      initialEndDate = addMonths(initialStartDate, 1);
      initialEndDate = addDays(initialEndDate, -1);

      const diffMonths = differenceInMonths(finalEndDate, initialStartDate);
      finalStartDate = addMonths(initialStartDate, diffMonths);

      const daysInLastMonth = new Date(
        finalStartDate.getFullYear(),
        finalStartDate.getMonth() + 1,
        0,
      ).getDate();

      const diffDays = differenceInDays(finalEndDate, finalStartDate);

      finalProratedAmount = Math.round(
        (tuitionFee / daysInLastMonth) * diffDays,
      );
    } else if (interval === 'year') {
      initialEndDate = addYears(initialStartDate, 1);
      initialEndDate = addDays(initialEndDate, -1);

      const diffYears = differenceInYears(finalEndDate, initialStartDate);
      finalStartDate = addYears(initialStartDate, diffYears);

      const daysInLastYear =
        new Date(finalStartDate.getFullYear(), 1, 29).getMonth() === 1
          ? 366
          : 365;
      const diffDays = differenceInDays(finalEndDate, finalStartDate);

      finalProratedAmount = Math.round(
        (tuitionFee / daysInLastYear) * diffDays,
      );
    }

    // Save proration data to MongoDB
    await prorationModel.create({
      ghlProductId,
      initial: {
        amount: tuitionFee,
        startDate: initialStartDate.toISOString(),
        endDate: initialEndDate.toISOString(),
        description: 'First billing cycle amount',
      },
      final: {
        amount: finalProratedAmount,
        startDate: finalStartDate.toISOString(),
        endDate: finalEndDate.toISOString(),
        description: 'Last billing cycle amount',
      },
      regularAmount: tuitionFee,
      billingCycleAnchor: initialEndDate.toISOString(),
      isProrated: isProrated,
    });
  }

  // Return GHL price body without proration details
  const priceBody: any = {
    name: createEnrollmentDto.title,
    type:
      createEnrollmentDto.tuitionBillingCycle === 'one-time'
        ? 'one_time'
        : 'recurring',
    currency: currency,
    amount: createEnrollmentDto.tuitionFee,
    locationId: locationId,
    metadata: {
      startDate: createEnrollmentDto.startDate,
      endDate: createEnrollmentDto.endDate,
      isProrated: isProrated,
    },
  };

  // Only add recurring if not one-time
  if (createEnrollmentDto.tuitionBillingCycle !== 'one-time') {
    priceBody.recurring = {
      intervalCount: 1,
      interval: interval,
    };
  }

  return priceBody;
}

export function calculateBlockDateRanges(
  startDateString: Date,
  endDateString: Date,
  timeZone = 'UTC',
) {
  // Parse ISO date strings into Date objects
  const startDate = new Date(startDateString);
  const endDate = new Date(endDateString);

  // Get years
  const yearStart = startDate.getFullYear();
  const yearEnd = endDate.getFullYear();

  // Create formatted date strings - using ISO format for better parsing
  const firstRangeStartStr = `${yearStart}-01-01T00:00:00`;

  // Calculate day before start date
  const dayBeforeStart = new Date(startDate);
  dayBeforeStart.setDate(dayBeforeStart.getDate() - 1);
  const dayBeforeStartMonth = String(dayBeforeStart.getMonth() + 1).padStart(
    2,
    '0',
  );
  const dayBeforeStartDay = String(dayBeforeStart.getDate()).padStart(2, '0');
  const firstRangeEndStr = `${dayBeforeStart.getFullYear()}-${dayBeforeStartMonth}-${dayBeforeStartDay}T23:59:59`;

  // For second range, use the exact date strings we want
  const dayAfterEnd = new Date(endDate);
  dayAfterEnd.setDate(dayAfterEnd.getDate() + 1);
  const dayAfterEndMonth = String(dayAfterEnd.getMonth() + 1).padStart(2, '0');
  const dayAfterEndDay = String(dayAfterEnd.getDate()).padStart(2, '0');
  const secondRangeStartStr = `${dayAfterEnd.getFullYear()}-${dayAfterEndMonth}-${dayAfterEndDay}T00:00:00`;

  const secondRangeEndStr = `${yearEnd}-12-31T23:59:59`;

  // Parse dates with fromZonedTime to correctly handle timezone
  // This converts a zoned time into a date using the provided timezone
  const firstRangeStartInTZ = fromZonedTime(firstRangeStartStr, timeZone);
  const firstRangeEndInTZ = fromZonedTime(firstRangeEndStr, timeZone);
  const secondRangeStartInTZ = fromZonedTime(secondRangeStartStr, timeZone);
  const secondRangeEndInTZ = fromZonedTime(secondRangeEndStr, timeZone);

  // Format for output - Use XXX for timezone offset with colon
  const format = "yyyy-MM-dd'T'HH:mm:ssXXX";

  // Format all dates with formatInTimeZone to get proper timezone display
  const firstRange = {
    start: formatInTimeZone(firstRangeStartInTZ, timeZone, format),
    end: formatInTimeZone(firstRangeEndInTZ, timeZone, format),
  };

  const secondRange = {
    start: formatInTimeZone(secondRangeStartInTZ, timeZone, format),
    end: formatInTimeZone(secondRangeEndInTZ, timeZone, format),
  };

  return { firstRange, secondRange };
}

export const formatToISODateTime = (timeStr: string): string => {
  // Parse the time as if it's UTC+0
  const [time, period] = timeStr.match(/(\d+:\d+)\s*(AM|PM)/i).slice(1);
  const [hours, minutes] = time.split(':');

  let hour = parseInt(hours);
  if (period.toUpperCase() === 'PM' && hour !== 12) {
    hour += 12;
  } else if (period.toUpperCase() === 'AM' && hour === 12) {
    hour = 0;
  }

  // Create date string in ISO format treating the time as UTC
  const dateString = `${new Date().toISOString().split('T')[0]}T${hour.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00.000Z`;

  return new Date(dateString).toISOString();
};

export const convertTo24Hour = (time12h: string): string => {
  const [time, period] = time12h.split(' ');
  const [hours, minutes] = time.split(/[.:]/); // handles both . and :

  let hour = parseInt(hours);
  if (period.toUpperCase() === 'PM' && hour !== 12) {
    hour += 12;
  } else if (period.toUpperCase() === 'AM' && hour === 12) {
    hour = 0;
  }

  return `${hour.toString().padStart(2, '0')}:${minutes}`;
};

export const formatToISODate = (dateStr: string): string => {
  const [month, day, year] = dateStr.split(/[-/]/);
  // Create date directly in UTC using Date.UTC
  const date = new Date(
    Date.UTC(
      parseInt(year),
      parseInt(month) - 1, // months are 0-based in JavaScript
      parseInt(day),
      0, // hours
      0, // minutes
      0, // seconds
      0, // milliseconds
    ),
  );
  return date.toISOString();
};

export function calculateDailyRate(
  tuitionFee: number,
  billingCycle: string,
): number {
  switch (billingCycle.toLowerCase()) {
    case 'weekly':
      return tuitionFee / 7; // Weekly fee divided by days in a week
    case 'monthly':
    case 'prorated':
      return tuitionFee / 30; // Monthly fee divided by average days in a month
    case 'yearly':
      return tuitionFee / 365; // Yearly fee divided by days in a year
    default:
      return tuitionFee / 30; // Default to monthly calculation
  }
}

export const formatDateTimeAvailability = (date: Date) => {
  return date.toLocaleString(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: 'numeric',
    second: 'numeric',
    hour12: true,
  });
};

export const JobNames = {
  CREATE_PRODUCT: 'create-product',
  CREATE_PRODUCT_PRICE: 'create-product-price',
  CREATE_CALENDAR: 'create-calendar',
  BLOCK_CALENDAR_SLOTS: 'block-calendar-slots',
};

// Currency symbols mapping
export const currencySymbols = {
  USD: '$',
  CAD: 'C$',
  AUD: 'A$',
  NZD: 'NZ$',
  GBP: '£',
};

/**
 * Determines the appropriate payment method types for a studio based on their currency
 * US Bank Account (ACH) is only available for USD studios
 * @param studioCurrency - The currency code of the studio (e.g., 'USD', 'AUD', 'CAD')
 * @param includeCard - Whether to include card payments (default: true)
 * @returns Array of payment method types for Stripe
 */
export const getPaymentMethodTypes = (
  studioCurrency: string,
  includeCard: boolean = true,
): string[] => {
  const paymentMethods: string[] = [];

  // Add card payment method if requested
  if (includeCard) {
    paymentMethods.push('card');
  }

  // Only add US bank account for USD studios
  if (studioCurrency === 'USD') {
    paymentMethods.push('us_bank_account');
  }

  return paymentMethods;
};

/**
 * Checks if a studio supports US Bank Account (ACH) payments
 * @param studioCurrency - The currency code of the studio
 * @returns true if the studio supports ACH payments (USD only)
 */
export const supportsUSBankAccount = (studioCurrency: string): boolean => {
  return studioCurrency === 'USD';
};

export const generateId = () => {
  return new Types.ObjectId().toString();
};

/**
 * Calculates the subscription start date based on billing cycle
 * @param entity - The entity with billing information
 * @returns The subscription start date
 */
export const calculateSubscriptionStartDate = (entity: any): Date => {
  const today = dayjs();
  const entityStartDate = dayjs(entity.startDate);

  // Find the first valid billing date (on or after class start date)
  const firstBillingDate = dayjs(entityStartDate).date(entity.billingDay);

  // If today is before or equal to first billing date, start on first billing date
  if (
    today.isSame(firstBillingDate, 'day') ||
    today.isBefore(firstBillingDate, 'day')
  ) {
    return firstBillingDate.toDate();
  }

  // Otherwise, start on next billing cycle
  let nextBillingDate = dayjs(firstBillingDate);

  if (entity.tuitionBillingCycle === 'weekly') {
    while (
      nextBillingDate.isSame(today, 'day') ||
      nextBillingDate.isBefore(today, 'day')
    ) {
      nextBillingDate = nextBillingDate.add(7, 'days');
    }
  } else if (entity.tuitionBillingCycle === 'bi-weekly') {
    while (
      nextBillingDate.isSame(today, 'day') ||
      nextBillingDate.isBefore(today, 'day')
    ) {
      nextBillingDate = nextBillingDate.add(14, 'days');
    }
  } else {
    while (
      nextBillingDate.isSame(today, 'day') ||
      nextBillingDate.isBefore(today, 'day')
    ) {
      nextBillingDate = nextBillingDate.add(1, 'month');
    }
  }

  return nextBillingDate.toDate();
};

/**
 * Gets the GCP server's external IP address
 * @returns Promise<string | null> The IP address or null if not available
 */
export async function getGcpIpAddress(): Promise<string | null> {
  try {
    const response = await axios.get(
      'http://metadata.google.internal/computeMetadata/v1/instance/network-interfaces/0/access-configs/0/external-ip',
      {
        headers: {
          'Metadata-Flavor': 'Google',
        },
      },
    );
    return response.data;
  } catch (error) {
    console.error('Error getting GCP IP:', error);
    // Fallback to environment variable if available
    return process.env.IP_ADDRESS || null;
  }
}
