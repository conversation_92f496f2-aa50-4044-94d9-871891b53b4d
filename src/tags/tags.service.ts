import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CreateTagDto } from './dto/create-tag.dto';
import { UpdateTagDto } from './dto/update-tag.dto';
import { Tag } from 'src/database/schema/tags';
import { GohighlevelService } from '../gohighlevel/gohighlevel.service';

@Injectable()
export class TagsService {
  private readonly logger = new Logger(TagsService.name);
  constructor(
    @InjectModel(Tag.name) private readonly tagModel: Model<Tag>,
    private readonly ghlService: GohighlevelService,
  ) {}

  async create(createTagDto: CreateTagDto, studioId: string) {
    const convertedStudioId = Types.ObjectId.createFromHexString(studioId);

    const existingTag = await this.tagModel
      .findOne({
        studioId: convertedStudioId,
        fieldName: createTagDto.fieldName,
      })
      .exec();

    if (existingTag) {
      return existingTag;
    }

    // First create tag in GHL
    const ghlTag = await this.ghlService.addTagToLocation(
      studioId,
      createTagDto.fieldName,
    );

    // Then save to our database with the GHL tag ID
    const tag = new this.tagModel({
      ...createTagDto,
      studioId: convertedStudioId,
      ghlId: ghlTag.tag?.id || ghlTag.id,
    });

    return await tag.save();
  }

  async findAll(
    studioId: string,
    { page, limit, search }: { page: number; limit: number; search: string },
  ) {
    const convertedStudioId = Types.ObjectId.createFromHexString(studioId);
    const skip = (page - 1) * limit;

    // Create the filter once to use in both queries
    const filter = {
      studioId: convertedStudioId,
      fieldName: { $regex: search, $options: 'i' },
    };

    const [tags, total] = await Promise.all([
      this.tagModel.find(filter).skip(skip).limit(limit).exec(),
      this.tagModel.countDocuments(filter),
    ]);

    return {
      items: tags,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string, studioId: string) {
    const convertedStudioId = Types.ObjectId.createFromHexString(studioId);
    const tag = await this.tagModel
      .findOne({
        _id: Types.ObjectId.createFromHexString(id),
        studioId: convertedStudioId,
      })
      .exec();

    if (!tag) {
      throw new NotFoundException(`Tag with ID ${id} not found`);
    }

    return tag;
  }

  async update(id: string, updateTagDto: UpdateTagDto, studioId: string) {
    this.logger.debug(`Updating tag ${id} for studio ${studioId}`);
    const convertedStudioId = Types.ObjectId.createFromHexString(studioId);

    // First, find the existing tag to get the GHL tagId
    const existingTag = await this.tagModel
      .findOne({
        _id: Types.ObjectId.createFromHexString(id),
        studioId: convertedStudioId,
      })
      .exec();

    if (!existingTag) {
      this.logger.warn(`Tag ${id} not found for studio ${studioId}`);
      throw new NotFoundException(`Tag with ID ${id} not found`);
    }

    if (existingTag.ghlId) {
      this.logger.debug(
        `Updating tag in GHL for studio ${studioId}, ghlId: ${existingTag.ghlId}`,
      );
      await this.ghlService.updateLocationTag(
        studioId,
        existingTag.ghlId,
        updateTagDto.fieldName,
      );
    } else {
      this.logger.debug(`Skipping GHL update for tag ${id} - no ghlId present`);
    }

    const updatedTag = await this.tagModel
      .findOneAndUpdate(
        {
          _id: id,
          studioId: convertedStudioId,
        },
        { $set: updateTagDto },
        { new: true },
      )
      .exec();

    this.logger.debug(`Successfully updated tag ${id} for studio ${studioId}`);
    return updatedTag;
  }

  async remove(id: string, studioId: string) {
    const convertedStudioId = Types.ObjectId.createFromHexString(studioId);

    // First find the tag to get the GHL tag ID
    const existingTag = await this.tagModel
      .findOne({
        _id: Types.ObjectId.createFromHexString(id),
        studioId: convertedStudioId,
      })
      .exec();

    if (!existingTag) {
      throw new NotFoundException(`Tag with ID ${id} not found`);
    }

    // Delete the tag in GHL first
    await this.ghlService.deleteLocationTag(studioId, existingTag.ghlId);

    // Then delete from our database
    const deletedTag = await this.tagModel
      .findOneAndDelete({
        _id: id,
        studioId: convertedStudioId,
      })
      .exec();

    return deletedTag;
  }
}
