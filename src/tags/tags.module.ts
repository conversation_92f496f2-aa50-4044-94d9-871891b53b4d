import { Modu<PERSON> } from '@nestjs/common';
import { TagsService } from './tags.service';
import { TagsController } from './tags.controller';
import { JwtModule } from '@nestjs/jwt';
import { GohighlevelModule } from 'src/gohighlevel/gohighlevel.module';
import { MongooseModule } from '@nestjs/mongoose';
import { Tag, TagSchema } from 'src/database/schema/tags';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Tag.name, schema: TagSchema }]),
    GohighlevelModule,
    JwtModule,
  ],
  controllers: [TagsController],
  providers: [TagsService],
})
export class TagsModule {}
