import {
  <PERSON>,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
} from '@nestjs/common';
import { TagsService } from './tags.service';
import { CreateTagDto } from './dto/create-tag.dto';
import { UpdateTagDto } from './dto/update-tag.dto';
import { JwtAuthGuard } from 'src/auth/auth.guard';
import { Request } from 'express';

@UseGuards(JwtAuthGuard)
@Controller('tags')
export class TagsController {
  constructor(private readonly tagsService: TagsService) {}

  @Post()
  create(@Req() request: Request, @Body() createTagDto: CreateTagDto) {
    const studioId = request['locationId'];
    return this.tagsService.create(createTagDto, studioId);
  }

  @Get()
  findAll(
    @Req() request: Request,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('search') search: string = '',
  ) {
    const studioId = request['locationId'];
    return this.tagsService.findAll(studioId, {
      page: Number(page),
      limit: Number(limit),
      search,
    });
  }

  @Get(':id')
  findOne(@Req() request: Request, @Param('id') id: string) {
    const studioId = request['locationId'];
    return this.tagsService.findOne(id, studioId);
  }

  @Patch(':id')
  update(
    @Req() request: Request,
    @Param('id') id: string,
    @Body() updateTagDto: UpdateTagDto,
  ) {
    const studioId = request['locationId'];
    return this.tagsService.update(id, updateTagDto, studioId);
  }

  @Delete(':id')
  remove(@Req() request: Request, @Param('id') id: string) {
    const studioId = request['locationId'];
    return this.tagsService.remove(id, studioId);
  }
}
