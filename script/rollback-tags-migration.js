const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config();

// Connection URI - adjust as needed or set MONGO_URI env variable
const MONGO_URI = process.env.MONGO_URI;
const DB_NAME = 'test'; // Adjust if your database name is different

if (!MONGO_URI) {
  console.error('Error: MONGO_URI environment variable is not set.');
  process.exit(1);
}

async function rollback() {
  const client = new MongoClient(MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });

  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db(DB_NAME);
    const customFormsCollection = db.collection('customforms');
    const tagsCollection = db.collection('tags');
    const enrollmentsCollection = db.collection('enrollments');

    console.log('\n=== ROLLBACK: Reverting Tags Migration ===\n');

    // Step 1: Create reverse mapping from Tag IDs to CustomForm IDs
    console.log('Step 1: Creating reverse mapping...');

    const tags = await tagsCollection.find({}).toArray();
    const reverseMapping = new Map(); // tagId -> customFormId

    for (const tag of tags) {
      // Find corresponding CustomForm record
      const customForm = await customFormsCollection.findOne({
        studio: tag.studioId,
        fieldName: tag.fieldName,
        fieldType: 'tags',
      });

      if (customForm) {
        reverseMapping.set(tag._id.toString(), customForm._id);
        console.log(
          `Mapped Tag ${tag._id} -> CustomForm ${customForm._id} (${tag.fieldName})`,
        );
      } else {
        console.log(`WARNING: No CustomForm found for tag: ${tag.fieldName}`);
      }
    }

    console.log(`Created reverse mapping for ${reverseMapping.size} tags`);

    // Step 2: Update enrollment documents to reference CustomForm IDs
    console.log('\nStep 2: Updating enrollment documents...');

    const enrollmentsWithTags = await enrollmentsCollection
      .find({
        tags: { $exists: true, $not: { $size: 0 } },
      })
      .toArray();

    console.log(`Found ${enrollmentsWithTags.length} enrollments with tags`);

    let updatedEnrollments = 0;
    for (const enrollment of enrollmentsWithTags) {
      const updatedTags = [];
      let hasChanges = false;

      for (const tagId of enrollment.tags) {
        const tagIdString = tagId.toString();
        if (reverseMapping.has(tagIdString)) {
          // Replace with CustomForm ID
          updatedTags.push(reverseMapping.get(tagIdString));
          hasChanges = true;
        } else {
          // Keep existing ID (might already be a CustomForm ID)
          updatedTags.push(tagId);
        }
      }

      if (hasChanges) {
        await enrollmentsCollection.updateOne(
          { _id: enrollment._id },
          { $set: { tags: updatedTags } },
        );
        updatedEnrollments++;
        console.log(
          `Updated enrollment: ${enrollment._id} (${enrollment.title || 'No title'})`,
        );
      }
    }

    console.log(`\nUpdated ${updatedEnrollments} enrollment documents`);

    // Step 3: Verification
    console.log('\nStep 3: Verification...');

    // Check if enrollments now reference CustomForm IDs
    const customFormTagIds = Array.from(reverseMapping.values());
    const enrollmentsWithCustomFormRefs = await enrollmentsCollection
      .find({
        tags: { $in: customFormTagIds },
      })
      .toArray();

    console.log(
      `${enrollmentsWithCustomFormRefs.length} enrollments now reference CustomForm tag IDs`,
    );

    console.log('\n=== ROLLBACK COMPLETED ===');
    console.log('\nNext steps:');
    console.log(
      '1. Update your code to use CustomForm collection for tags again',
    );
    console.log(
      '2. Change enrollment schema to reference "CustomForm" instead of "Tag"',
    );
    console.log(
      '3. Update enrollment service to use customFormModel instead of tagModel',
    );
    console.log(
      '4. Test the application to ensure tag functionality works correctly',
    );
  } catch (error) {
    console.error('Rollback failed:', error);
    process.exit(1);
  } finally {
    await client.close();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the rollback
rollback().catch(console.error);
