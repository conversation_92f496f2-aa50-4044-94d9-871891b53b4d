import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { getModelToken } from '@nestjs/mongoose';
import {
  Enrollment,
  EnrollmentDocument,
} from '../src/database/schema/enrollment';
import { EnrollmentService } from '../src/enrollment/enrollment.service';
import { GcpStorageService } from '../src/gcp-storage/gcp-storage.service';
import axios from 'axios';
import * as path from 'path';
import { Model } from 'mongoose';

async function main() {
  const app = await NestFactory.createApplicationContext(AppModule, {
    logger: ['error', 'warn', 'log'],
  });

  const enrollmentModel = app.get<Model<EnrollmentDocument>>(
    getModelToken(Enrollment.name),
  );
  const gcp = app.get(GcpStorageService);
  const enrollmentService = app.get(EnrollmentService);

  const cursor = enrollmentModel
    .find({
      $and: [
        { $or: [{ webpImageUrl: { $exists: false } }, { webpImageUrl: '' }] },
        {
          $or: [
            { originalImageUrl: { $ne: '' } },
            { defaultImageUrl: { $ne: '' } },
          ],
        },
        { isDeleted: false },
      ],
    })
    .cursor();

  let processed = 0;
  for await (const cls of cursor) {
    try {
      console.log(`\nProcessing doc: ${(cls as any)._id}`);
      const src = cls.originalImageUrl || cls.defaultImageUrl;
      if (!src) {
        console.log('No source image URL found, skipping.');
        continue;
      }
      console.log(`Source URL: ${src}`);

      // download
      const resp = await axios.get<ArrayBuffer>(src, {
        responseType: 'arraybuffer',
      });
      const buffer = Buffer.from(resp.data);

      // derive original filename from the URL
      const filename = path.basename(src.split('?')[0] || 'class-img.jpg');

      // Convert + upload
      const { webpUrl } = await gcp.uploadImageVariantsToGCP(
        undefined,
        buffer,
        filename,
        (cls as any).studio.toString(),
        'class-preview-image',
        (cls as any)._id.toString(),
      );
      console.log(`Generated WebP URL: ${webpUrl}`);

      if (!webpUrl) {
        console.warn(`No WebP produced for ${(cls as any)._id}`);
        continue;
      }

      let result = await enrollmentModel.updateOne(
        { _id: (cls as any)._id },
        { $set: { webpImageUrl: webpUrl } },
      );
      console.log(`Update result: ${JSON.stringify(result)}`);

      // Invalidate related caches so new image URL is picked up
      try {
        await enrollmentService.invalidateRelatedCaches(
          (cls as any).studio.toString(),
          [(cls as any)._id.toString()],
        );
      } catch (cacheErr) {
        console.warn(
          'Cache invalidation failed for',
          (cls as any)._id.toString(),
          cacheErr.message,
        );
      }

      processed++;
      console.log(`✅ Updated ${(cls as any).title} (${(cls as any)._id})`);
    } catch (e) {
      console.error(`❌ Failed for ${(cls as any)._id}:`, (e as Error).message);
    }
  }

  console.log(`Done. Converted ${processed} enrollments.`);
  await app.close();
}
main();

//npx ts-node -r tsconfig-paths/register script/backfill-webp.ts
