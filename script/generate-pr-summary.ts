#!/usr/bin/env ts-node

import fetch from 'node-fetch';

interface PRData {
  title: string;
  body: string;
  diff: string;
  filesChanged: string[];
}

interface OpenAIResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  error?: {
    message: string;
  };
}

async function generatePRSummary(prData: PRData): Promise<string> {
  const openaiApiKey = process.env.OPENAI_API_KEY;

  if (!openaiApiKey) {
    throw new Error('OPENAI_API_KEY environment variable is required');
  }

  // Truncate diff if too long (OpenAI has token limits)
  let truncatedDiff = prData.diff;
  if (prData.diff.length > 8000) {
    truncatedDiff =
      prData.diff.substring(0, 8000) + '... [diff truncated due to length]';
  }

  const userContent = `Analyze this pull request and provide a comprehensive summary:

**Title:** ${prData.title}

**Description:** ${prData.body || 'No PR description provided.'}

**Files Changed:**
${prData.filesChanged.join('\n')}

**Code Changes:**
${truncatedDiff}

Please provide a detailed summary that covers:
1. What functionality was added/modified/fixed
2. Key technical changes made
3. Files and components affected
4. Business impact or problem solved

Keep it concise but comprehensive (2-4 sentences).`;

  const payload = {
    model: 'gpt-4o',
    messages: [
      {
        role: 'system',
        content:
          'You are an expert software engineer and code reviewer. Analyze pull requests and provide comprehensive, technical summaries that explain what was changed, why, and the impact. Focus on the actual code changes and business logic.',
      },
      {
        role: 'user',
        content: userContent,
      },
    ],
    max_tokens: 500,
    temperature: 0.3,
  };

  try {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${openaiApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(
        `OpenAI API request failed with status ${response.status}`,
      );
    }

    const data: OpenAIResponse = await response.json();

    if (data.error) {
      throw new Error(`OpenAI API error: ${data.error.message}`);
    }

    return data.choices[0]?.message?.content || 'Failed to generate summary';
  } catch (error) {
    console.error('Error generating PR summary:', error);
    return 'Failed to generate AI summary due to an error';
  }
}

// Main execution when run as script
async function main() {
  try {
    const prData: PRData = {
      title: process.env.PR_TITLE || '',
      body: process.env.PR_BODY || '',
      diff: process.env.PR_DIFF || '',
      filesChanged: (process.env.FILES_CHANGED || '')
        .split('\n')
        .filter((f) => f.trim()),
    };

    const summary = await generatePRSummary(prData);

    // Output the summary for GitHub Actions to capture
    console.log(summary);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  main();
}

export { generatePRSummary, PRData };
