db.students.updateMany({ _id: ObjectId('67d4efbeba3c605bb79e3d19') }, [
  {
    $set: {
      attendance: {
        $map: {
          input: '$attendance',
          as: 'att',
          in: {
            // Keep all existing fields and add/update status
            $mergeObjects: [
              '$$att', // Retains original fields (attendanceId, classId, date, etc.)
              {
                status: {
                  $switch: {
                    branches: [
                      {
                        case: {
                          $eq: [
                            '$$att.attendanceId',
                            ObjectId('67d0b31ff81c9d2d59e07399'),
                          ],
                        },
                        then: 'absent',
                      },
                      {
                        case: {
                          $eq: [
                            '$$att.attendanceId',
                            ObjectId('6755cbda13d44e9caf275212'),
                          ],
                        },
                        then: 'absent',
                      },
                      {
                        case: {
                          $eq: [
                            '$$att.attendanceId',
                            ObjectId('6755cbcd13d44e9caf27520e'),
                          ],
                        },
                        then: 'present',
                      },
                      {
                        case: {
                          $eq: [
                            '$$att.attendanceId',
                            ObjectId('6755cbda13d44e9caf275211'),
                          ],
                        },
                        then: 'absent',
                      },
                      {
                        case: {
                          $eq: [
                            '$$att.attendanceId',
                            ObjectId('6755cbda13d44e9caf27520f'),
                          ],
                        },
                        then: 'absent',
                      },
                      {
                        case: {
                          $eq: [
                            '$$att.attendanceId',
                            ObjectId('6755cbcd13d44e9caf27520c'),
                          ],
                        },
                        then: 'present',
                      },
                      {
                        case: {
                          $eq: [
                            '$$att.attendanceId',
                            ObjectId('6755cbcd13d44e9caf27520d'),
                          ],
                        },
                        then: 'absent',
                      },
                    ],
                    default: '$$att.status', // Preserve existing status if no match
                  },
                },
              },
            ],
          },
        },
      },
    },
  },
]);
