const { MongoClient } = require('mongodb');
require('dotenv').config();

// Load the MongoDB URI from environment variables
const MONGO_URI = process.env.MONGO_URI;
if (!MONGO_URI) {
  console.error('Error: MONGO_URI environment variable is not set.');
  process.exit(1); // Exit the script if the URI is not set
}
const DB_NAME = 'test'; // Name of the database to connect to

async function migrate() {
  // Create a new MongoDB client instance
  const client = new MongoClient(MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true, // Use the new connection management engine
  });

  await client.connect(); // Connect to the MongoDB server
  const db = client.db(DB_NAME); // Get the database instance

  // Define the collections to work with
  const students = db.collection('students');
  const classHistories = db.collection('classhistories');

  const BATCH_SIZE = 100; // Number of operations to process in a single batch
  let batch = []; // Array to hold batch operations

  // Aggregate query to transform student data into class history format
  const cursor = students.aggregate(
    [
      { $match: { enrollments: { $exists: true, $not: { $size: 0 } } } }, // Match students with non-empty enrollments
      { $unwind: '$enrollments' }, // Deconstruct the enrollments array into individual documents
      {
        $project: {
          studentId: '$_id', // Include the student ID
          studioId: 1, // Include the studio ID
          classId: '$enrollments.enrollmentId', // Map enrollmentId to classId
          status: '$enrollments.subscriptionStatus', // Include subscription status
          createdAt: '$enrollments.enrolledDate', // Include enrollment creation date
          updatedAt: '$enrollments.enrolledDate', // Include enrollment update date
        },
      },
      {
        $group: {
          _id: '$classId', // Group by classId
          studioId: { $first: '$studioId' }, // Take the first studioId for the group
          students: {
            $push: {
              // Collect all students in the group
              studentId: '$studentId',
              status: '$status',
              createdAt: '$createdAt',
              updatedAt: '$updatedAt',
            },
          },
        },
      },
    ],
    { allowDiskUse: true },
  ); // Allow disk usage for large aggregations

  // Process the aggregated data in batches
  while (await cursor.hasNext()) {
    const doc = await cursor.next(); // Get the next document from the cursor
    const updateOperation = {
      updateOne: {
        filter: { classId: doc._id }, // Match the classId in the classHistories collection
        update: {
          $setOnInsert: { classId: doc._id, studioId: doc.studioId }, // Set classId and studioId if the document is inserted
          $addToSet: { students: { $each: doc.students } }, // Add students to the array, avoiding duplicates
        },
        upsert: true, // Insert the document if it doesn't exist
      },
    };
    batch.push(updateOperation); // Add the operation to the batch

    // Execute the batch if it reaches the defined size
    if (batch.length >= BATCH_SIZE) {
      await classHistories.bulkWrite(batch); // Perform bulk write
      console.log(`Processed ${batch.length} class history records.`);
      batch = []; // Reset the batch
    }
  }

  // Process any remaining operations in the batch
  if (batch.length > 0) {
    await classHistories.bulkWrite(batch);
    console.log(`Processed ${batch.length} class history records.`);
  }

  await client.close(); // Close the MongoDB connection
}

// Run the migration function and handle errors
migrate().catch((err) => {
  console.error('Migration failed:', err);
  process.exit(1); // Exit with an error code if the migration fails
});
