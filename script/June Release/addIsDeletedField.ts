import { MongoClient } from 'mongodb';

async function migrate() {
  const uri = 'mongodb://localhost:27017/test';
  const client = new MongoClient(uri);

  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db();
    const eventsCollection = db.collection('events');
    const enrollmentsCollection = db.collection('enrollments');

    // Update events
    const eventsResult = await eventsCollection.updateMany(
      { isDeleted: { $exists: false } },
      { $set: { isDeleted: false } },
    );

    console.log(`Updated ${eventsResult.modifiedCount} events`);

    // Update enrollments
    const enrollmentsResult = await enrollmentsCollection.updateMany(
      { isDeleted: { $exists: false } },
      { $set: { isDeleted: false } },
    );

    console.log(`Updated ${enrollmentsResult.modifiedCount} enrollments`);

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Error during migration:', error);
  } finally {
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

// Run the migration
migrate().catch(console.error);
