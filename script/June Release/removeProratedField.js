require('dotenv').config({ path: '../../.env' });
const { MongoClient } = require('mongodb');

// MongoDB connection details
const MONGO_URI = 'mongodb://localhost:27017/test';
const DB_NAME = 'test';

async function main() {
  // Create a new MongoDB client instance
  const client = new MongoClient(MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });

  try {
    console.log('Connecting to MongoDB...');
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db(DB_NAME);
    const enrollments = db.collection('enrollments');
    const events = db.collection('events');

    // Update enrollments
    console.log('\nProcessing enrollments...');
    const enrollmentResult = await enrollments.updateMany(
      { tuitionBillingCycle: 'prorated' },
      {
        $set: {
          tuitionBillingCycle: 'monthly',
          billingDay: 1,
        },
      },
    );
    console.log(`Updated ${enrollmentResult.modifiedCount} enrollments`);

    // Update events
    console.log('\nProcessing events...');
    const eventResult = await events.updateMany(
      { tuitionBillingCycle: 'prorated' },
      {
        $set: {
          tuitionBillingCycle: 'monthly',
          billingDay: 1,
        },
      },
    );
    console.log(`Updated ${eventResult.modifiedCount} events`);

    // Verify updates
    console.log('\nVerifying updates...');
    const enrollmentCount = await enrollments.countDocuments({
      tuitionBillingCycle: 'monthly',
      billingDay: 1,
    });
    const eventCount = await events.countDocuments({
      tuitionBillingCycle: 'monthly',
      billingDay: 1,
    });

    console.log(`\nVerification results:`);
    console.log(`Enrollments updated to Monthly billing: ${enrollmentCount}`);
    console.log(`Events updated to Monthly billing: ${eventCount}`);
  } catch (error) {
    console.error('Script error:', error);
  } finally {
    // Close MongoDB connection
    await client.close();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the script
main().catch(console.error);
