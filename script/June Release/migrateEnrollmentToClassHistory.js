const { MongoClient } = require('mongodb');
require('dotenv');

// Connection URI - adjust as needed or set MONGO_URI env variable
const MONGO_URI = process.env.MONGO_URI;
console.log(MONGO_URI);
if (!MONGO_URI) {
  console.error('Error: MONGO_URI environment variable is not set.');
  process.exit(1);
}
const DB_NAME = 'test';

async function migrate() {
  const client = new MongoClient(MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });
  await client.connect();
  const db = client.db(DB_NAME);

  const enrollmentHistories = db.collection('enrollmenthistories');
  const classHistories = db.collection('classhistories');

  const BATCH_SIZE = 100;

  const cursor = enrollmentHistories.find({
    enrollments: { $exists: true, $not: { $size: 0 } },
  });
  const map = new Map();

  while (await cursor.hasNext()) {
    const doc = await cursor.next();
    (doc.enrollments || []).forEach((rec) => {
      const key = rec.enrollmentId.toString();
      if (!map.has(key)) {
        map.set(key, {
          classId: rec.enrollmentId,
          studioId: doc.studioId,
          students: [],
        });
      }
      map.get(key).students.push({
        studentId: doc.studentId,
        status: rec.status,
        createdAt: rec.createdAt,
        updatedAt: rec.updatedAt,
      });
    });

    // If map size exceeds BATCH_SIZE, insert and clear
    if (map.size >= BATCH_SIZE) {
      const toInsert = Array.from(map.values());
      await classHistories.insertMany(toInsert);
      console.log(`Inserted ${toInsert.length} class history records.`);
      map.clear();
    }
  }

  // Insert any remaining records
  if (map.size > 0) {
    const toInsert = Array.from(map.values());
    await classHistories.insertMany(toInsert);
    console.log(`Inserted ${toInsert.length} class history records.`);
  } else {
    console.log('No records to migrate.');
  }

  await client.close();
}

migrate().catch((err) => {
  console.error('Migration failed:', err);
  process.exit(1);
});
