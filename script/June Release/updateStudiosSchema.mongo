// This approach only adds fields that don't exist, preserving existing values
db.studios.updateMany(
  {},
  [
    {
      $set: {
        paymentProvider: {
          $ifNull: ["$paymentProvider", "stripe"]
        },
        paymentProcessingMethod: {
          $ifNull: ["$paymentProcessingMethod", "auto"]
        },
        prorationMode: {
          $ifNull: ["$prorationMode", "full_month_always"]
        }
      }
    }
  ]
);

// Verify the update
print("Updated documents count:");
db.studios.countDocuments({
  paymentProvider: { $exists: true },
  paymentProcessingMethod: { $exists: true },
  prorationMode: { $exists: true }
});

print("Sample document:");
db.studios.findOne({}, {
  subaccountName: 1,
  paymentProvider: 1,
  paymentProcessingMethod: 1,
  prorationMode: 1
});