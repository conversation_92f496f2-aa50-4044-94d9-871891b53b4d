import { MongoClient } from 'mongodb';
import { Types } from 'mongoose';
import {
  PaymentTransactionStatus,
  PaymentProvider,
  PaymentMethod,
  InvoiceStatus,
  InvoiceType,
  PaymentTransactionType,
  PaymentTransactionSource,
  PaymentTransactionEntityType,
} from '../../src/stripe/type';
import { SubscriptionStatus } from '../../src/database/schema/subscription';
import * as fs from 'fs';
import * as path from 'path';
import * as XLSX from 'xlsx';

// OPTIMIZED Invoice Dates Generation - Maximum performance with safety
// Dedicated module for invoice date calculations
import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

// Interface definitions for better type safety
interface MigrationStats {
  subscriptionsCreated: number;
  transactionsMigrated: number;
  invoicesMigrated: number;
  errors: string[];
}

interface ExcelData {
  subscriptions: any[];
  paymentTransactions: any[];
  subscriptionInvoices: any[];
}

async function migrateData() {
  const client = new MongoClient('mongodb://localhost:27017/test');

  try {
    await client.connect();
    const db = client.db();

    // Create backup directory
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = path.join(__dirname, 'backups', timestamp);
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    // Initialize data containers for Excel and JSON
    const excelData: ExcelData = {
      subscriptions: [],
      paymentTransactions: [],
      subscriptionInvoices: [],
    };

    // Migration statistics
    const stats = {
      subscriptionsCreated: 0,
      transactionsMigrated: 0,
      invoicesMigrated: 0,
      errors: [] as string[],
    };

    // Studios to exclude
    const excludedStudioIds = [
      'VGZndtOFHrO4NIELBQYo',
      'hqD2EpUwBJg1nEBWr4jT',
      'VL4Nw4E5ZLQQrYWSkaDf',
      'ysJeaNivwASZjyKd59tf',
      '675c1dcbaaa25e6e35b6cc9d',
      'w3aMCzwq82RmvBK8eoqz',
      '674b4c2ea29058fee896809f',
      '675c1dcbaaa25e6e35b6cc9d',
      'CYO1RIi6zPFTaT3nMpHO',
      'ddj53Ce9N7CKtanLhm0I',
      't34wsZgFiq6fyBrps0Ps',
      '6760a40f6bc16c08a5d38765',
      '681541e3abdf2a1c74acf9b6',
      '683e1da17973656d66b3e7a5',
      '683f54bfb59ec253c3d5ddbb',
      '6806a30bd9b5fb9632dc0fe7',
      '6838377ed0897eef8ec50dd7',
      '674f6f70ac644669b0bcaee2',
      '6802b6ce5ea81457fb662993',
      '67f995e5776d13ae8b9bec10',
      '67ea5cca8c7409901d58ec33',
      '67c3459ab85ca168ae5bdb48',
      '672b9825183dae2d5a45a97e',
      '67f9961d776d13ae8b9bec28',
      '681297beb94020d9738e4715',
      '681baef368e4266bd34f65f3',
      '674b4d5ba29058fee8968105',
      '674f754732dfd723ecaf4b17',
      '6760a40f6bc16c08a5d38765',
      '672e37318bb62b7e2c144170',
      '674b4c2ea29058fee896809f',
      '675858c4fb3c0929ae78c34d',
      '6842f9703c7dcd199cb709ab',
      'WxRUKJa9Ab2f18xyFbrx',
      'vjXpb4geA4iXyTJ0uuv2',
      'VGZndtOFHrO4NIELBQYo',
      'wF51D6rONZLtMh2ahuZk',
      '4rHOXpthXOIFJpwrcI0u',
      'hjtHNz0uY5RKmI2LBV8x',
      '5SKWOuSlwAzcroS44vbq',
      'NYKNVqzg0u1Cyr2VMYk9',
      'e841RYDRilLzg76f3yaB',
      'gB2iGlSo0hmjkF6wrK1x',
      '4XtZz2aXLPnW3E9Dr604',
      'YNHjgzLR3iqXcwy7nLFT',
      'AqRvhETPh5DnHf2U5NL4',
      'ztXIKfqHs2o80XfZfh8c',
      'PvfddjcOTfQVU0sLqOz0',
      'hqD2EpUwBJg1nEBWr4jT',
      'ysJeaNivwASZjyKd59tf',
      'w3aMCzwq82RmvBK8eoqz',
      'CYO1RIi6zPFTaT3nMpHO',
      'ddj53Ce9N7CKtanLhm0I',
      'fX5a1Cqi57YLqJhh27jo',
      'VdoKsCvCQ64MoggqILzW',
    ];

    const excludedTransactionIds = [
      '67eaf727c1f1d9e86445ab3a',
      '67eaf855c1f1d9e86445adde',
      '67eafd2ac1f1d9e86445b21b',
      '67ebd9e1c60cfe0cdb8f1bfd',
      '67b3bc226c2f59dc4e7a2569',
      '67b3bcae6c2f59dc4e7a26cc',
      '67b506e4394e5ad48559b2f5',
      '68432ba735bc33d11d7ba561',
      '68432c34bd0db7925476ea86',
      '68432bea35bc33d11d7ba582',
      '682f3f5a46c4b8aece0219f7', // one-time txn
      '68308b0b26e8098bbc83ba41', // Gracie McClain
      '68308afe26e8098bbc83b9e6', // Evie McClain
      '682f3f7246c4b8aece021ace', // one-time txn
      '682f475746c4b8aece0224b9', // checkout not paid - no invoice
      '68308f1226e8098bbc83cb0d', // checkout not paid - no invoice
      '68308e7f26e8098bbc83c9f2', // checkout not paid - no invoice
    ];
    // Get transactions with basic filtering
    const results = await db
      .collection('transactions')
      .find({
        'details.studioId': {
          $nin: excludedStudioIds,
        },
        _id: {
          $nin: excludedTransactionIds.map((id) =>
            Types.ObjectId.createFromHexString(id),
          ),
        },
      })
      .toArray();

    console.log(`Found ${results.length} transactions to process`);

    // Function to find student ID by parent ID and name
    async function findStudentIdsByParent(
      parentId: string,
      students: any[],
      db: any,
    ) {
      if (!parentId || !students || students.length === 0) return [];

      const studentNames = students.map((s) => ({
        firstName: s.firstName,
        lastName: s.lastName,
        fullName: `${s.firstName} ${s.lastName}`.toLowerCase(),
      }));

      const matchedStudents = await db
        .collection('students')
        .find({ parentId })
        .toArray();

      const studentIds = matchedStudents
        .filter((s) =>
          studentNames.some(
            (n) => `${s.firstName} ${s.lastName}`.toLowerCase() === n.fullName,
          ),
        )
        .map((s) => s._id);

      console.log(`Found ${studentIds.length} students for parent ${parentId}`);
      return studentIds;
    }

    // Add this function after the findStudentIdsByParent function
    async function resolveStudioId(studioIdOrLocationId: string, db: any) {
      if (!studioIdOrLocationId) return '000000000000000000000000';

      // Check if it's a valid ObjectId format
      const objectIdPattern = /^[0-9a-fA-F]{24}$/;
      if (objectIdPattern.test(studioIdOrLocationId)) {
        return studioIdOrLocationId;
      }

      // If not ObjectId format, try to find studio by locationId
      try {
        const studio = await db
          .collection('studios')
          .findOne({ locationId: studioIdOrLocationId });
        if (studio && studio._id) {
          return studio._id.toString();
        }
      } catch (error) {
        console.error(
          `Error resolving studioId for locationId ${studioIdOrLocationId}:`,
          error,
        );
      }

      return '000000000000000000000000';
    }

    // Add this function after resolveStudioId
    async function fetchSessionId(checkoutId: string, db: any) {
      if (!checkoutId) return null;

      try {
        // Try enrollment collection first
        const enrollment = await db
          .collection('enrollments')
          .findOne({ _id: Types.ObjectId.createFromHexString(checkoutId) });
        if (enrollment?.session) {
          return enrollment.session;
        }

        // Try event collection if not found in enrollment
        const event = await db
          .collection('events')
          .findOne({ _id: Types.ObjectId.createFromHexString(checkoutId) });
        if (event?.session) {
          return event.session;
        }
      } catch (error) {
        console.error(
          `Error fetching sessionId for checkoutId ${checkoutId}:`,
          error,
        );
      }

      return null;
    }

    // Add this helper function to map invoices to line items
    async function mapInvoicesToLineItems(
      invoices: any[] = [],
      txn: any = {},
      product,
    ) {
      if (!invoices || invoices.length === 0) {
        const productName = product.title;
        // Default line item for tuition fee
        return [
          {
            price_data: {
              currency: 'usd',
              product_data: {
                name: productName || 'Class Payment',
                description: 'Tuition Fee',
              },
              unit_amount: Math.round(txn.amount * 100) || 0,
            },
            quantity: 1,
          },
        ];
      }

      return invoices.map((invoice) => {
        const description = invoice.type;

        return {
          price_data: {
            currency: 'usd',
            product_data: {
              name:
                invoice.product ||
                invoice.name ||
                txn.productName ||
                'Class Payment',
              description: description || 'Tuition Fee',
            },
            unit_amount: Math.round((invoice.amount || 0) * 100),
          },
          quantity: 1,
        };
      });
    }

    // Process each transaction
    for (const result of results) {
      try {
        // Get student IDs through various methods
        let studentIds = [];
        let parentId;

        // 1. Direct studentIds from details
        if (result.details?.studentIds?.length > 0) {
          parentId = result.details?.parentId;
          studentIds = result.details.studentIds;
        }
        // 2. Single studentId from details
        else if (result.details?.studentId) {
          parentId = result.details?.parentId;
          studentIds = [result.details.studentId];
        }
        // 3. For Parent transactions, try to find students by name
        else if (result.entityType === 'Parent' && result.entityId) {
          parentId = result.entityId;
          studentIds = await findStudentIdsByParent(
            parentId,
            result.details?.students || [],
            db,
          );
        }

        // Skip if no student IDs found
        if (studentIds.length === 0) {
          console.log(`No student IDs found for transaction ${result._id}`);
          continue;
        }

        if (result._id.toString() === '676dac86919df4731800068b') {
          console.log('blah');
        }

        const groupId = new Types.ObjectId().toString();
        let product;
        let sessionId;
        let entityId;
        const possibleSessionId =
          result.details.sessionId ||
          (result.details.sessionId &&
            result.details.sessionId.length > 0 &&
            result.details.sessionId[0]) ||
          null;
        if (result.details.checkoutType === 'class') {
          product = await db.collection('enrollments').findOne({
            _id: Types.ObjectId.createFromHexString(result.details.checkoutId),
          });
          entityId = result.details.checkoutId;
          sessionId =
            possibleSessionId ||
            (await fetchSessionId(result.details?.checkoutId, db));
        } else if (result.details.entityType === 'class') {
          product = await db.collection('enrollments').findOne({
            _id: Types.ObjectId.createFromHexString(result.details.entityId),
          });
          entityId = result.details.entityId;
          sessionId =
            possibleSessionId ||
            (await fetchSessionId(result.details?.entityId, db));
        } else if (result.details.checkoutType === 'event') {
          product = await db.collection('events').findOne({
            _id: Types.ObjectId.createFromHexString(result.details.checkoutId),
          });
          entityId = result.details.checkoutId;
          sessionId =
            possibleSessionId ||
            (await fetchSessionId(result.details?.checkoutId, db));
        } else if (result.details.entityType === 'event') {
          product = await db.collection('events').findOne({
            _id: Types.ObjectId.createFromHexString(result.details.entityId),
          });
          entityId = result.details.entityId;
          sessionId =
            possibleSessionId ||
            (await fetchSessionId(result.details?.entityId, db));
        }

        let session;
        try {
          if (sessionId) {
            if (Array.isArray(sessionId)) {
              if (sessionId.length > 0) {
                session = await db.collection('sessions').findOne({
                  _id: Types.ObjectId.createFromHexString(sessionId[0]),
                });
              }
            } else {
              session = await db.collection('sessions').findOne({
                _id: sessionId,
              });
            }
          }
        } catch (error) {
          console.error(
            `Error fetching session for sessionId ${sessionId}:`,
            error,
          );
        }

        // Create transactions and invoices for each student
        for (const studentId of studentIds) {
          let lineItems;
          if (result.status !== 'pending') {
            lineItems = (result.details.line_items || []).map((item) => ({
              ...item,
              quantity: 1,
              price_data: {
                ...item.price_data,
                unit_amount: item.price_data?.unit_amount || 0,
              },
            }));
          }
          let invoices = [];
          if (product) {
            invoices = await resolveInvoicesFromTransactionId(
              db,
              result._id.toString(),
              studentId,
            );
            if (invoices && invoices.length === 0) {
              invoices = await resolveInvoicesFromTransactionId(
                db,
                result.stripePaymentIntentId,
                studentId,
              );

              if (invoices && invoices.length === 0) {
                let sessionName = session?.name;
                if (
                  result.details.studioId === '78KGw68ufREcSjdfJRnq' &&
                  sessionName === 'Summer Camps 2025 '
                ) {
                  sessionName = 'Summer 2025';
                }
                invoices = await resolveInvoicesFromStudentId(
                  db,
                  [product.title, sessionName],
                  studentId,
                );
              }
            }
            if (invoices.length > 0) {
              lineItems = await mapInvoicesToLineItems(
                invoices,
                result,
                product,
              );
            }
          } else {
            invoices = await resolveInvoicesFromTransactionId(
              db,
              result.stripePaymentIntentId,
              studentId,
            );

            if (invoices.length > 0) {
              lineItems = await mapInvoicesToLineItems(
                invoices,
                result,
                product,
              );
            }
          }

          let appliedDiscount = 0;

          if (lineItems) {
            // Separate discount items from regular line items
            const { regularItems, discountAmount } = lineItems.reduce(
              (acc, item) => {
                if (item.price_data?.product_data?.description === 'Discount') {
                  return {
                    ...acc,
                    discountAmount:
                      acc.discountAmount + (item.price_data?.unit_amount || 0),
                  };
                }
                return {
                  ...acc,
                  regularItems: [...acc.regularItems, item],
                };
              },
              { regularItems: [], discountAmount: 0 },
            );

            // Update line items to only include non-discount items
            lineItems = regularItems;
            appliedDiscount =
              regularItems.length > 0 ? discountAmount / 100 : 0;
          }

          let amountLineItems = 0;
          try {
            if (lineItems) {
              amountLineItems =
                lineItems.reduce(
                  (acc, item) =>
                    acc +
                    (item.price_data?.unit_amount || 0) * (item.quantity || 1),
                  0,
                ) / 100;
            }
          } catch (error) {
            console.error(
              `Error calculating amountLineItems for transaction ${result._id}:`,
              error,
            );
          }

          const stripeSessionId =
            result.details?.stripeSessionId || result.stripeSessionId;
          const paymentIntentId =
            result.details?.stripePaymentIntentId ||
            result.stripePaymentIntentId;

          let txnStatus = result.status;
          if (invoices && invoices.length > 0) {
            const hasCancelledInvoice = invoices.some(
              (inv) => inv.status === 'cancelled',
            );
            if (hasCancelledInvoice) {
              txnStatus = 'cancelled';
            } else {
              txnStatus = invoices[0].status;
            }
          }
          const newTxnStatus =
            result.amount > 0
              ? mapTransactionStatus(txnStatus)
              : PaymentTransactionStatus.FREE;

          const amount =
            newTxnStatus === PaymentTransactionStatus.PENDING
              ? result.amount / studentIds.length
              : amountLineItems - appliedDiscount;
          if (
            stripeSessionId.startsWith('cs_live') ||
            stripeSessionId === '' ||
            stripeSessionId.startsWith('pi_')
          ) {
            const billingCycle =
              product?.tuitionBillingCycle === 'prorated'
                ? 'monthly'
                : product?.tuitionBillingCycle || 'one-time';
            let newTransaction;
            let subscriptionInvoices = [];
            try {
              if (lineItems && lineItems.length > 0) {
                // Create new transaction
                newTransaction = {
                  _id: new Types.ObjectId(),
                  studioId: Types.ObjectId.createFromHexString(
                    await resolveStudioId(result.details?.studioId, db),
                  ),
                  parentId: Types.ObjectId.createFromHexString(
                    parentId || '000000000000000000000000',
                  ),
                  studentId: Types.ObjectId.createFromHexString(studentId),
                  groupId: studentIds.length > 1 ? groupId : null,
                  paymentSource:
                    result.entityType === 'Parent'
                      ? PaymentTransactionSource.PARENT_REGISTRATION_FORM
                      : PaymentTransactionSource.PARENT_PORTAL_PRODUCT_BUY,
                  type: mapTransactionType(result.transactionType),
                  typeId: Types.ObjectId.createFromHexString(entityId),
                  amount: amount,
                  status: newTxnStatus,
                  paymentProvider: PaymentProvider.STRIPE,
                  paymentMethod: mapPaymentMethod(result.paymentMethod),
                  retryAttempts: 0,
                  isRetry: false,
                  metadata: {
                    line_items: lineItems,
                    totalAmount: amount,
                    appliedDiscount: appliedDiscount,
                    walletAmountUsed: 0,
                    discountSplit: result.details.discountSplit || {},
                    sessionId: sessionId,
                    stripeSessionId: stripeSessionId,
                    paymentIntentId: paymentIntentId,
                    description: result.notes || 'class payment',
                    billingDate:
                      result.details?.billingDate || result.createdAt,
                    migrationData: {
                      oldTransactionId: result._id.toString(),
                      migrationDate: new Date(),
                    },
                  },
                  createdAt: result.createdAt,
                  updatedAt: result.updatedAt || new Date(),
                  __v: 0,
                };
                // Insert transaction into database
                await db
                  .collection('paymenttransactions')
                  .insertOne(newTransaction);

                excelData.paymentTransactions.push(newTransaction);
                stats.transactionsMigrated++;
              }
            } catch (error) {
              console.log(`Error creating new transaction:`, error);
            }

            if (
              newTransaction &&
              newTransaction.metadata.line_items &&
              newTransaction.metadata.line_items.length > 0 &&
              product
            ) {
              try {
                if (product.billingDay == null) {
                  product.billingDay = 1;
                }
              } catch (error) {
                if (!product) {
                  console.log(`Error setting billingDay for product:`, error);
                } else {
                  continue;
                }
              }
              const subStartDate = calculateSubscriptionStartDate(
                product,
                new Date(result.createdAt),
              );

              let subscription;
              try {
                // Create subscription
                subscription = {
                  _id: new Types.ObjectId(),
                  studioId: newTransaction.studioId,
                  parentId: newTransaction.parentId,
                  studentId: newTransaction.studentId,
                  entityId: newTransaction.typeId,
                  entityType:
                    newTransaction.type === 'enrollment' ||
                    newTransaction.type === 'class'
                      ? 'class'
                      : 'event',
                  startDate: subStartDate,
                  endDate: new Date(product.endDate),
                  billingCycle: billingCycle,
                  baseAmount: amount,
                  finalAmount: amount - appliedDiscount,
                  status: SubscriptionStatus.ACTIVE,
                  nextPaymentDate: new Date(product.endDate),
                  metadata: {
                    notes:
                      'Migration from Stripe' +
                      (product && product.tuitionBillingCycle
                        ? ''
                        : ' - Using OneTime Billing because the entity was not found.'),
                    transactionId: newTransaction._id.toString(),
                    appliedDiscount: appliedDiscount || 0,
                  },
                };

                // Insert subscription into database
                await db.collection('subscriptions').insertOne(subscription);

                excelData.subscriptions.push(subscription);
                stats.subscriptionsCreated++;
              } catch (error) {
                console.error(
                  `Error creating subscription for transaction ${result._id}:`,
                  error,
                );
              }

              if (newTransaction && newTransaction.amount > 0) {
                // Create invoice(s) based on billing cycle
                if (subscription.billingCycle === 'one-time') {
                  // For one-time billing, create a single invoice
                  const invoiceDiscountAmount = appliedDiscount
                    ? appliedDiscount
                    : 0;
                  let payments: {
                    method: PaymentMethod;
                    amount: number;
                    date: Date;
                  }[] = [];

                  payments.push({
                    method: newTransaction.paymentMethod,
                    amount: subscription.baseAmount - invoiceDiscountAmount,
                    date: new Date(result.createdAt),
                  });

                  const oneTimeInvoice = {
                    _id: new Types.ObjectId(),
                    studioId: subscription.studioId,
                    subscriptionId: subscription._id,
                    parentId: subscription.parentId,
                    studentId: subscription.studentId,
                    entityId: subscription.entityId,
                    entityType: subscription.entityType,
                    baseAmount: subscription.baseAmount,
                    status:
                      newTransaction.status ===
                      PaymentTransactionStatus.SCHEDULED
                        ? InvoiceStatus.SCHEDULED
                        : newTransaction.status ===
                            PaymentTransactionStatus.PAID
                          ? InvoiceStatus.PAID
                          : newTransaction.status ===
                              PaymentTransactionStatus.CANCELLED
                            ? InvoiceStatus.CANCELLED
                            : InvoiceStatus.PENDING,
                    paymentProvider: newTransaction.paymentProvider,
                    paymentMethod: newTransaction.paymentMethod,
                    line_items: newTransaction.metadata.line_items.map(
                      (item) => ({
                        name: item.price_data.product_data.name,
                        amount: item.price_data.unit_amount / 100,
                        type: item.price_data.product_data.description,
                        quantity: item.quantity,
                        total:
                          (item.price_data.unit_amount * item.quantity) / 100,
                      }),
                    ),
                    payments: payments,
                    type: InvoiceType.ONE_TIME,
                    dueDate: new Date(newTransaction.metadata.billingDate),
                    finalAmount: subscription.baseAmount - appliedDiscount,
                    startDate: new Date(result.createdAt),
                    billingCycle: subscription.billingCycle,
                    endDate: product
                      ? new Date(product.endDate)
                      : new Date(result.createdAt),
                    metadata: {
                      attemptCount: 0,
                      internalTransactionId: newTransaction._id.toString(),
                      appliedDiscount: appliedDiscount,
                    },
                    createdAt: new Date(result.createdAt),
                    updatedAt: new Date(result.updatedAt) || new Date(),
                  };

                  // Insert one-time invoice into database
                  await db
                    .collection('subscriptioninvoices')
                    .insertOne(oneTimeInvoice);

                  excelData.subscriptionInvoices.push(oneTimeInvoice);
                  stats.invoicesMigrated++;
                } else {
                  let invoiceDates;
                  try {
                    // For recurring billing, generate multiple invoices
                    invoiceDates = generateInvoiceDates(
                      subscription.startDate,
                      subscription.endDate,
                      subscription.billingCycle,
                    );

                    if (invoiceDates === undefined) {
                      invoiceDates = [subscription.startDate];
                    }

                    subscriptionInvoices = invoiceDates.map((date, index) => {
                      // Only apply discount to first invoice if it exists
                      const invoiceDiscountAmount =
                        index === 0 ? appliedDiscount : 0;
                      let payments: {
                        method: PaymentMethod;
                        amount: number;
                        date: Date;
                      }[] = [];

                      let status = InvoiceStatus.UPCOMING;
                      if (index === 0) {
                        // Set appropriate status for first invoice based on transaction status
                        switch (newTransaction.status) {
                          case PaymentTransactionStatus.PAID:
                            status = InvoiceStatus.PAID;
                            payments.push({
                              method: newTransaction.paymentMethod,
                              amount:
                                subscription.baseAmount - invoiceDiscountAmount,
                              date: new Date(result.createdAt),
                            });
                            break;
                          case PaymentTransactionStatus.SCHEDULED:
                            status = InvoiceStatus.SCHEDULED;
                            break;
                          case PaymentTransactionStatus.CANCELLED:
                            status = InvoiceStatus.CANCELLED;
                            break;
                          default:
                            status = InvoiceStatus.PENDING;
                        }
                      }

                      const lineItems =
                        index === 0
                          ? newTransaction.metadata.line_items.map((item) => ({
                              name: item.price_data.product_data.name,
                              amount: item.price_data.unit_amount / 100,
                              type: item.price_data.product_data.description,
                              quantity: item.quantity,
                              total:
                                (item.price_data.unit_amount * item.quantity) /
                                100,
                            }))
                          : newTransaction.metadata.line_items
                              .filter(
                                (item) =>
                                  item.price_data.product_data.description ===
                                  'Tuition Fee',
                              )
                              .map((item) => ({
                                name: item.price_data.product_data.name,
                                amount: subscription.baseAmount,
                                type: 'Tuition Fee',
                                quantity: 1,
                                total: subscription.baseAmount,
                              }));

                      return {
                        studioId: subscription.studioId,
                        subscriptionId: subscription._id,
                        parentId: subscription.parentId,
                        studentId: subscription.studentId,
                        entityId: subscription.entityId,
                        entityType: subscription.entityType,
                        status,
                        paymentProvider: newTransaction.paymentProvider,
                        paymentMethod: newTransaction.paymentMethod,
                        type: InvoiceType.SUBSCRIPTION,
                        line_items: lineItems,
                        baseAmount: subscription.baseAmount,
                        finalAmount:
                          subscription.baseAmount - invoiceDiscountAmount,
                        dueDate:
                          index === 0
                            ? new Date(newTransaction.metadata.billingDate)
                            : date,
                        startDate: date,
                        endDate:
                          invoiceDates[index + 1] ||
                          new Date(date.getTime() + 24 * 60 * 60 * 1000),
                        metadata: {
                          attemptCount: 0,
                          internalTransactionId:
                            index === 0 ? newTransaction._id?.toString() : null,
                          appliedDiscount: invoiceDiscountAmount,
                        },
                        payments: payments,
                        createdAt: new Date(result.createdAt),
                        updatedAt: new Date(result.updatedAt) || new Date(),
                      };
                    });

                    // Insert all invoices into database
                    if (subscriptionInvoices.length > 0) {
                      await db
                        .collection('subscriptioninvoices')
                        .insertMany(subscriptionInvoices);
                    }
                    excelData.subscriptionInvoices.push(
                      ...subscriptionInvoices,
                    );
                    stats.invoicesMigrated += subscriptionInvoices.length;
                  } catch (error) {
                    console.error(
                      `Error generating invoice dates for transaction ${result._id}:`,
                      error,
                    );
                  }
                }
              }
            }

            try {
              const subscription = await db
                .collection('subscriptions')
                .findOne({
                  studentId: Types.ObjectId.createFromHexString(studentId),
                  entityId: Types.ObjectId.createFromHexString(result.entityId),
                  studioId: Types.ObjectId.createFromHexString(
                    await resolveStudioId(result.details?.studioId, db),
                  ),
                });
              if (subscription) {
                // Update student's subscriptionId in enrollments
                await db.collection('students').updateOne(
                  {
                    _id: Types.ObjectId.createFromHexString(studentId),
                    'enrollments.enrollmentId':
                      Types.ObjectId.createFromHexString(result.entityId),
                  },
                  {
                    $set: {
                      'enrollments.$.subscriptionId':
                        subscription._id.toString(),
                    },
                  },
                );
              }
            } catch (error) {
              console.error(
                `Error updating student's subscriptionId in enrollments for transaction ${result._id}:`,
                error,
              );
            }
          }
        }
      } catch (error) {
        stats.errors.push(
          `Error processing transaction ${result._id}: ${error.message}`,
        );
        try {
          let studio;

          if (result.details.studioId instanceof Types.ObjectId) {
            studio = await db.collection('studios').findOne({
              _id: result.details.studioId,
            });
          } else {
            studio = await db.collection('studios').findOne({
              locationId: result.details.studioId,
            });
          }

          if (studio) {
            stats.errors.push(
              `Studio ID: ${result.details.studioId} - ${studio.subaccountName}`,
            );
          }
        } catch (error) {}
      }
    }

    // Save migration stats
    fs.writeFileSync(
      path.join(backupDir, 'migration_stats.json'),
      JSON.stringify(stats, null, 2),
    );

    // Save Excel data
    const workbook = XLSX.utils.book_new();

    async function getAdditionalDetails(
      db,
      studioId,
      studentId,
      parentId,
      entityId,
      entityType,
    ) {
      const [studio, student, parent, entity] = await Promise.all([
        db.collection('studios').findOne({ _id: studioId }),
        db.collection('students').findOne({ _id: studentId }),
        db.collection('parents').findOne({ _id: parentId }),
        entityType === 'class' || entityType === 'enrollment'
          ? db.collection('enrollments').findOne({ _id: entityId })
          : db.collection('events').findOne({ _id: entityId }),
      ]);

      return {
        studioSubaccountName: studio?.subaccountName || 'Unknown',
        studentName: student
          ? `${student.firstName} ${student.lastName}`
          : 'Unknown',
        parentName: parent
          ? `${parent.firstName} ${parent.lastName}`
          : 'Unknown',
        entityName: entity?.title || entity?.name || 'Unknown',
      };
    }

    // Format payment transactions for better visibility
    const formattedTransactions = await Promise.all(
      excelData.paymentTransactions.map(async (tx) => {
        const details = await getAdditionalDetails(
          db,
          tx.studioId,
          tx.studentId,
          tx.parentId,
          tx.typeId,
          tx.type,
        );

        if (
          tx.metadata.migrationData.oldTransactionId ===
          '67ec2d3de439992dbeb1ad83'
        ) {
          console.log(tx.metadata.migrationData.oldTransactionId);
        }

        try {
          return {
            studioSubaccountName: details.studioSubaccountName,
            studentName: details.studentName,
            parentName: details.parentName,
            entityName: details.entityName,
            oldTransactionId: tx.metadata.migrationData.oldTransactionId,
            amount: tx.amount,
            amountLineItems: tx.metadata.line_items.reduce(
              (acc, item) => acc + item.price_data.unit_amount * item.quantity,
              0,
            ),
            status: tx.status,
            studentId: tx.studentId.toString(),
            parentId: tx.parentId.toString(),
            studioId: tx.studioId.toString(),
            type: tx.type,
            typeId: tx.typeId.toString(),
            sessionId: tx.metadata.sessionId,
            stripeSessionId: tx.metadata.stripeSessionId,
            paymentIntentId: tx.metadata.paymentIntentId,
            description: tx.metadata.description,
            discountSplit: JSON.stringify(tx.metadata.discountSplit),
            migrationDate: tx.metadata.migrationDate,
            ...tx.metadata,
            ...tx,
            line_items: JSON.stringify(tx.metadata.line_items, null, 2),
          };
        } catch (error) {
          console.error(
            `Error getting additional details for transaction ${tx._id}:`,
            error,
          );
        }
      }),
    );

    // Add payment transactions sheet
    const transactionsWS = XLSX.utils.json_to_sheet(formattedTransactions);

    // Adjust column widths for better readability
    const colWidths = {
      A: 30, // oldTransactionId
      B: 15, // amount
      C: 15, // status
      D: 30, // studentId
      E: 30, // parentId
      F: 30, // studioId
      G: 100, // line_items
    };

    transactionsWS['!cols'] = Object.entries(colWidths).map(([_, width]) => ({
      wch: width,
    }));
    XLSX.utils.book_append_sheet(
      workbook,
      transactionsWS,
      'PaymentTransactions',
    );

    // Format subscriptions for better visibility
    const formattedSubscriptions = await Promise.all(
      excelData.subscriptions.map(async (sub) => {
        const details = await getAdditionalDetails(
          db,
          sub.studioId,
          sub.studentId,
          sub.parentId,
          sub.entityId,
          sub.entityType,
        );

        return {
          studioSubaccountName: details.studioSubaccountName,
          studentName: details.studentName,
          parentName: details.parentName,
          entityName: details.entityName,
          subscriptionId: sub._id.toString(),
          studioId: sub.studioId.toString(),
          parentId: sub.parentId.toString(),
          studentId: sub.studentId.toString(),
          entityId: sub.entityId.toString(),
          entityType: sub.entityType,
          startDate: sub.startDate,
          endDate: sub.endDate,
          billingCycle: sub.billingCycle,
          baseAmount: sub.baseAmount,
          finalAmount: sub.finalAmount,
          status: sub.status,
          nextPaymentDate: sub.nextPaymentDate,
          notes: sub.metadata.notes,
          transactionId: sub.metadata.transactionId,
          appliedDiscount: sub.metadata.appliedDiscount,
        };
      }),
    );

    // Add subscriptions sheet
    const subscriptionsWS = XLSX.utils.json_to_sheet(formattedSubscriptions);
    XLSX.utils.book_append_sheet(workbook, subscriptionsWS, 'Subscriptions');

    // Format subscription invoices for better visibility
    const formattedInvoices = await Promise.all(
      excelData.subscriptionInvoices.map(async (inv) => {
        const details = await getAdditionalDetails(
          db,
          inv.studioId,
          inv.studentId,
          inv.parentId,
          inv.entityId,
          inv.entityType,
        );

        return {
          studioSubaccountName: details.studioSubaccountName,
          studentName: details.studentName,
          parentName: details.parentName,
          entityName: details.entityName,
          invoiceId: inv._id.toString(),
          studioId: inv.studioId.toString(),
          subscriptionId: inv.subscriptionId.toString(),
          parentId: inv.parentId.toString(),
          studentId: inv.studentId.toString(),
          entityId: inv.entityId.toString(),
          entityType: inv.entityType,
          baseAmount: inv.baseAmount,
          finalAmount: inv.finalAmount,
          status: inv.status,
          dueDate: inv.dueDate,
          startDate: inv.startDate,
          endDate: inv.endDate,
          line_items: JSON.stringify(inv.line_items, null, 2),
          payments: JSON.stringify(inv.payments, null, 2),
          appliedDiscount: inv.metadata.appliedDiscount,
        };
      }),
    );

    // Add subscription invoices sheet
    const invoicesWS = XLSX.utils.json_to_sheet(formattedInvoices);
    XLSX.utils.book_append_sheet(workbook, invoicesWS, 'SubscriptionInvoices');

    // Save Excel file
    XLSX.writeFile(workbook, path.join(backupDir, 'migration_data.xlsx'));

    // Save complete JSON dump
    fs.writeFileSync(
      path.join(backupDir, 'complete_migration_data.json'),
      JSON.stringify(excelData, null, 2),
    );

    console.log('\nMigration Statistics:');
    console.log('---------------------');
    console.log(`Transactions Migrated: ${stats.transactionsMigrated}`);
    console.log(`Invoices Migrated: ${stats.invoicesMigrated}`);
    console.log(`Errors: ${stats.errors.length}`);
    console.log(
      `\nExcel file created at: ${path.join(backupDir, 'migration_data.xlsx')}`,
    );
    console.log(
      `Complete JSON dump created at: ${path.join(backupDir, 'complete_migration_data.json')}`,
    );

    if (stats.errors.length > 0) {
      console.log('\nErrors encountered:');
      stats.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }
  } catch (error) {
    console.error('Fatal error during migration:', error);
  } finally {
    await client.close();
  }
}

async function resolveInvoicesFromStudentId(db, productName, studentId) {
  const invoices = await db
    .collection('invoices')
    .find({
      entityId: studentId,
      product: { $in: productName },
    })
    .toArray();

  return invoices;
}

async function resolveInvoicesFromTransactionId(db, transactionId, studentId) {
  if (!transactionId) {
    return [];
  }

  const invoices = await db
    .collection('invoices')
    .find({
      entityId: studentId,
      transactionId: transactionId,
    })
    .toArray();

  return invoices;
}

// Helper function to determine billing cycle based on transaction patterns

function mapTransactionType(oldType: string): PaymentTransactionType {
  const typeMap: Record<string, PaymentTransactionType> = {
    enrollment: PaymentTransactionType.ENROLLMENT,
    event: PaymentTransactionType.EVENT,
  };
  return typeMap[oldType] || PaymentTransactionType.ENROLLMENT;
}

function mapTransactionStatus(oldStatus: string): PaymentTransactionStatus {
  const statusMap: Record<string, PaymentTransactionStatus> = {
    pending: PaymentTransactionStatus.PENDING,
    paid: PaymentTransactionStatus.PAID,
    failed: PaymentTransactionStatus.FAILED,
    refunded: PaymentTransactionStatus.FULL_REFUND,
    scheduled: PaymentTransactionStatus.SCHEDULED,
    completed: PaymentTransactionStatus.PAID,
    cancelled: PaymentTransactionStatus.CANCELLED,
  };
  return statusMap[oldStatus] || PaymentTransactionStatus.PENDING;
}

function mapInvoiceStatus(oldStatus: string): InvoiceStatus {
  const statusMap: Record<string, InvoiceStatus> = {
    pending: InvoiceStatus.PENDING,
    paid: InvoiceStatus.PAID,
    failed: InvoiceStatus.FAILED,
    refunded: InvoiceStatus.FULL_REFUND,
    scheduled: InvoiceStatus.SCHEDULED,
  };
  return statusMap[oldStatus] || InvoiceStatus.PENDING;
}

function mapPaymentMethod(oldMethod: string): PaymentMethod {
  const methodMap: Record<string, PaymentMethod> = {
    card: PaymentMethod.CARD,
    cash: PaymentMethod.CASH,
    check: PaymentMethod.CHECK,
    wallet: PaymentMethod.WALLET,
    ach: PaymentMethod.US_BANK_ACCOUNT,
    bank_transfer: PaymentMethod.US_BANK_ACCOUNT,
  };
  return methodMap[oldMethod] || PaymentMethod.CARD;
}

// Helper function to determine match type
function determineMatchType(transaction: any, invoice: any): string {
  if (invoice.transactionId === transaction._id.toString()) {
    return 'ObjectId Match';
  }
  if (invoice.transactionId === transaction.paymentIntentId) {
    return 'Stripe Payment Intent Match';
  }
  if (
    transaction.studentIds.includes(invoice.entityId) ||
    transaction.parentTransactionStudentNames.includes(invoice.name)
  ) {
    return transaction.entityType === 'Parent'
      ? 'Parent-Student-Product Match'
      : 'Student-Product Match';
  }
  return 'Fallback Match (EntityId + Amount)';
}

/**
 * Calculates the subscription start date based on billing cycle
 * @param entity - The entity with billing information
 * @returns The subscription start date
 */
export const calculateSubscriptionStartDate = (
  entity: any,
  todayDate?: Date,
): Date => {
  const today = dayjs(todayDate || new Date());
  const entityStartDate = dayjs(entity.startDate);

  // Find the first valid billing date (on or after class start date)
  const firstBillingDate = dayjs(entityStartDate).date(entity.billingDay);

  // If today is before or equal to first billing date, start on first billing date
  if (
    today.isSame(firstBillingDate, 'day') ||
    today.isBefore(firstBillingDate, 'day')
  ) {
    return firstBillingDate.toDate();
  }

  // Otherwise, start on next billing cycle
  let nextBillingDate = dayjs(firstBillingDate);

  if (entity.tuitionBillingCycle === 'weekly') {
    while (
      nextBillingDate.isSame(today, 'day') ||
      nextBillingDate.isBefore(today, 'day')
    ) {
      nextBillingDate = nextBillingDate.add(7, 'days');
    }
  } else if (entity.tuitionBillingCycle === 'bi-weekly') {
    while (
      nextBillingDate.isSame(today, 'day') ||
      nextBillingDate.isBefore(today, 'day')
    ) {
      nextBillingDate = nextBillingDate.add(14, 'days');
    }
  } else {
    while (
      nextBillingDate.isSame(today, 'day') ||
      nextBillingDate.isBefore(today, 'day')
    ) {
      nextBillingDate = nextBillingDate.add(1, 'month');
    }
  }

  return nextBillingDate.toDate();
};

// Ultra-fast fixed interval generation (weekly, bi-weekly)
const generateFixedInterval = (
  startTime: number,
  endTime: number,
  intervalMs: number,
): Date[] => {
  const dates: Date[] = [];
  let currentTime = startTime;

  // Generate dates
  while (currentTime < endTime) {
    dates.push(new Date(currentTime));
    currentTime += intervalMs;
  }

  return dates;
};

// Optimized monthly generation (handles month-end edge cases)
const generateMonthlyOptimized = (
  startDate: Date,
  endDate: Date,
  billingDay?: number,
): Date[] => {
  const dates: Date[] = [];

  // if (!billingDay) {
  //   throw new Error('Billing day is required for monthly billing');
  // }
  billingDay = 1;

  // Use UTC to avoid timezone issues
  const startUTC = dayjs.utc(startDate);
  const endUTC = dayjs.utc(endDate);

  // Get the billing date for the start month in UTC
  const firstBillingDate = startUTC.startOf('month').date(billingDay);

  // Get the billing date for the end month in UTC
  const lastBillingDate = endUTC.startOf('month').date(billingDay);

  // Only include the last billing date if end date >= billing day of end month
  const shouldIncludeLastBilling = endUTC.date() >= billingDay;
  const actualLastBillingDate = shouldIncludeLastBilling
    ? lastBillingDate
    : lastBillingDate.subtract(1, 'month');

  // Generate all billing dates from start month to end month
  let currentDate = firstBillingDate;

  while (!currentDate.isAfter(actualLastBillingDate)) {
    dates.push(currentDate.toDate());
    currentDate = currentDate.add(1, 'month');
  }

  return dates;
};

// Optimized yearly generation
const generateYearlyOptimized = (startDate: Date, endDate: Date): Date[] => {
  const dates: Date[] = [];
  const start = dayjs(startDate);
  const end = dayjs(endDate);

  // Always add the start date
  dates.push(start.toDate());

  let currentDate = start.add(1, 'year');
  while (currentDate.isBefore(end)) {
    dates.push(currentDate.toDate());
    currentDate = currentDate.add(1, 'year');
  }

  return dates;
};

// MAIN EXPORT - Best optimized version
export const generateInvoiceDates = (
  startDate: Date,
  endDate: Date,
  cycle: string,
  billingDay?: number,
): Date[] => {
  // Fast input validation
  if (!startDate || !endDate || !cycle) {
    throw new Error(
      'Invalid input: startDate, endDate, and cycle are required',
    );
  }

  const start = dayjs(startDate);
  const end = dayjs(endDate);

  // Fast date validation
  if (!start.isValid() || !end.isValid() || start.isAfter(end)) {
    throw new Error('Invalid date range');
  }

  const cycleLower = cycle.toLowerCase();

  // Pre-calculate for safety check (max 10 years)
  const diffDays = end.diff(start, 'day');
  if (diffDays > 3650) {
    throw new Error('Date range too large');
  }

  switch (cycleLower) {
    case 'weekly':
      return generateFixedInterval(start.valueOf(), end.valueOf(), 604800000); // 7 * 24 * 60 * 60 * 1000
    case 'bi-weekly':
      return generateFixedInterval(start.valueOf(), end.valueOf(), 1209600000); // 14 * 24 * 60 * 60 * 1000
    case 'monthly':
      return generateMonthlyOptimized(startDate, endDate, billingDay);
    case 'yearly':
      return generateYearlyOptimized(startDate, endDate);
    default:
      throw new Error(
        `Invalid cycle: ${cycle}. Must be one of: weekly, bi-weekly, monthly, yearly`,
      );
  }
};

// Export types for better TypeScript support
export type BillingCycle = 'weekly' | 'bi-weekly' | 'monthly' | 'yearly';

// UTILITY: Validate billing cycle
export const isValidBillingCycle = (cycle: string): cycle is BillingCycle => {
  return ['weekly', 'bi-weekly', 'monthly', 'yearly'].includes(
    cycle.toLowerCase(),
  );
};

// UTILITY: Estimate invoice count (useful for UI/planning)
export const estimateInvoiceCount = (
  startDate: Date,
  endDate: Date,
  cycle: string,
): number => {
  if (!startDate || !endDate || !cycle) return 0;

  const start = dayjs(startDate);
  const end = dayjs(endDate);
  const diffDays = end.diff(start, 'day');

  switch (cycle.toLowerCase()) {
    case 'weekly':
      return Math.ceil(diffDays / 7);
    case 'bi-weekly':
      return Math.ceil(diffDays / 14);
    case 'monthly':
      return Math.ceil(diffDays / 30); // Rough estimate
    case 'yearly':
      return Math.ceil(diffDays / 365);
    default:
      return 0;
  }
};

// Run the migration
migrateData().catch(console.error);
