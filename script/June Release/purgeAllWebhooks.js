require('dotenv').config({ path: '../../.env' });
const { MongoClient } = require('mongodb');
const Stripe = require('stripe');

// MongoDB connection details
const MONGO_URI = 'mongodb://localhost:27017/test';
const DB_NAME = 'test';

async function updateOrCreateWebhook(credential, locationId) {
  try {
    const stripe = new Stripe(credential.apiSecret, {
      apiVersion: '2025-02-24.acacia',
    });

    const backendUrl =
      'https://enrollio-be-dot-enrollio-portal.uc.r.appspot.com';
    const webhookConfig = {
      url: `${backendUrl}/webhooks/stripe?locationId=${locationId}`,
      enabled_events: [
        'product.created',
        'payment_intent.succeeded',
        'payment_intent.payment_failed',
        'charge.refunded',
      ],
    };

    let webhook;

    // If webhookId exists, try to update it
    if (credential.webhookId) {
      try {
        webhook = await stripe.webhookEndpoints.retrieve(credential.webhookId);
        if (webhook) {
          // Update existing webhook
          webhook = await stripe.webhookEndpoints.update(
            credential.webhookId,
            webhookConfig,
          );
          console.log('Updated existing webhook');
        }
      } catch (error) {
        console.log('Existing webhook not found, will create new one');
        webhook = null;
      }
    }

    // If no webhook exists or update failed, create new one
    if (!webhook) {
      webhook = await stripe.webhookEndpoints.create(webhookConfig);
      console.log('Created new webhook');
    }

    return {
      webhookId: webhook.id,
      webhookSecret: webhook.secret,
    };
  } catch (error) {
    throw new Error(`Failed to update or create webhook: ${error.message}`);
  }
}

async function main() {
  // Create a new MongoDB client instance
  const client = new MongoClient(MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });

  try {
    console.log('Connecting to MongoDB...');
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db(DB_NAME);
    const studios = db.collection('studios');
    const credentials = db.collection('credentials');

    // Get all studios
    const allStudios = await studios.find({}).toArray();
    console.log(`Found ${allStudios.length} studios`);

    // Loop through each studio
    for (const studio of allStudios) {
      try {
        if (!studio.subaccountName) {
          console.log(
            `Skipping studio with ID ${studio._id} - no subaccountName found`,
          );
          continue;
        }

        console.log(
          `\nProcessing studio: ${studio.subaccountName} (${studio._id})`,
        );

        // Find credential for this studio
        const credential = await credentials.findOne({
          studioId: studio._id.toString(),
        });

        if (credential && credential.apiSecret) {
          console.log(`Found credential for studio ${studio.subaccountName}`);

          // Update or create webhook
          const webhookResult = await updateOrCreateWebhook(
            credential,
            studio._id.toString(),
          );
          console.log(
            `Successfully processed webhook for studio ${studio.subaccountName}`,
          );

          // Update credential
          await credentials.updateOne(
            { studioId: studio._id.toString() },
            {
              $set: {
                webhookId: webhookResult.webhookId,
                webhookSecret: webhookResult.webhookSecret,
              },
            },
          );
          console.log(`Updated credential for studio ${studio.subaccountName}`);
        } else {
          console.log(
            `No valid credential found for studio ${studio.subaccountName}`,
          );
        }
      } catch (error) {
        console.error(
          `Error processing studio ${studio.subaccountName || studio._id}:`,
          error,
        );
      }
    }

    console.log('\nFinished processing all studios');
  } catch (error) {
    console.error('Script error:', error);
  } finally {
    // Close MongoDB connection
    await client.close();
    console.log('Disconnected from MongoDB');
  }
}

// Run the script
main().catch(console.error);
