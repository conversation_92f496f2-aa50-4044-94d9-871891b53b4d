const { MongoClient, ObjectId } = require('mongodb');
require('dotenv').config();

// Connection URI - adjust as needed or set MONGO_URI env variable
const MONGO_URI = process.env.MONGO_URI;
const DB_NAME = 'test'; // Adjust if your database name is different

if (!MONGO_URI) {
  console.error('Error: MONGO_URI environment variable is not set.');
  process.exit(1);
}

async function migrate() {
  const client = new MongoClient(MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });

  try {
    await client.connect();
    console.log('Connected to MongoDB');

    const db = client.db(DB_NAME);
    const customFormsCollection = db.collection('customforms');
    const tagsCollection = db.collection('tags');
    const enrollmentsCollection = db.collection('enrollments');

    console.log(
      '\n=== MIGRATION: CustomForm Tags to Dedicated Tags Collection ===\n',
    );

    // Step 1: Find all tag records in customforms collection
    console.log('Step 1: Finding tag records in customforms collection...');
    const tagCustomForms = await customFormsCollection
      .find({
        fieldType: 'tags',
      })
      .toArray();

    console.log(
      `Found ${tagCustomForms.length} tag records in customforms collection`,
    );

    if (tagCustomForms.length === 0) {
      console.log(
        'No tag records found in customforms. Migration may not be needed.',
      );
      return;
    }

    // Step 2: Create mapping of old CustomForm IDs to new Tag IDs
    console.log('\nStep 2: Creating tags in dedicated tags collection...');
    const idMapping = new Map(); // oldCustomFormId -> newTagId
    const processedTags = new Map(); // studioId:fieldName -> tagId (to prevent duplicates)

    // First, get all existing tags to avoid duplicates
    const existingTags = await tagsCollection.find({}).toArray();
    for (const tag of existingTags) {
      const key = `${tag.studioId}:${tag.fieldName}`;
      processedTags.set(key, tag._id);
    }

    console.log(
      `Found ${existingTags.length} existing tags in tags collection`,
    );

    // Group CustomForm tags by studio and fieldName to handle duplicates
    const tagGroups = new Map();
    for (const customFormTag of tagCustomForms) {
      const key = `${customFormTag.studio}:${customFormTag.fieldName}`;
      if (!tagGroups.has(key)) {
        tagGroups.set(key, []);
      }
      tagGroups.get(key).push(customFormTag);
    }

    console.log(
      `Found ${tagGroups.size} unique tag combinations (studio:fieldName)`,
    );

    for (const [key, customFormTags] of tagGroups) {
      const [studioId, fieldName] = key.split(':');
      let tagId;

      if (processedTags.has(key)) {
        // Tag already exists in tags collection
        tagId = processedTags.get(key);
        console.log(`Tag "${fieldName}" already exists in tags collection`);
      } else {
        // Create new tag in tags collection (use the first CustomForm record as source)
        const sourceCustomForm = customFormTags[0];
        const newTag = {
          studioId: new ObjectId(studioId),
          fieldName: fieldName,
          fieldType: 'tags',
          ghlId: sourceCustomForm.ghlId || null,
          createdAt: sourceCustomForm.createdAt || new Date(),
          updatedAt: sourceCustomForm.updatedAt || new Date(),
        };

        try {
          const result = await tagsCollection.insertOne(newTag);
          tagId = result.insertedId;
          processedTags.set(key, tagId);
          console.log(`Created new tag: "${fieldName}" with ID: ${tagId}`);
        } catch (error) {
          if (error.code === 11000) {
            // Duplicate key error - tag was created by another process
            console.log(
              `Duplicate detected for "${fieldName}", fetching existing tag...`,
            );
            const existingTag = await tagsCollection.findOne({
              studioId: new ObjectId(studioId),
              fieldName: fieldName,
            });
            tagId = existingTag._id;
            processedTags.set(key, tagId);
          } else {
            throw error;
          }
        }
      }

      // Map all CustomForm IDs in this group to the same Tag ID
      for (const customFormTag of customFormTags) {
        idMapping.set(customFormTag._id.toString(), tagId);
        if (customFormTags.length > 1) {
          console.log(
            `  Mapped duplicate CustomForm ${customFormTag._id} -> Tag ${tagId}`,
          );
        }
      }
    }

    console.log(
      `\nCreated mapping for ${idMapping.size} CustomForm -> Tag relationships`,
    );

    // Step 2.5: Create unique index to prevent future duplicates
    console.log('\nStep 2.5: Creating unique index on tags collection...');
    try {
      await tagsCollection.createIndex(
        { studioId: 1, fieldName: 1 },
        { unique: true, name: 'unique_studio_fieldName' },
      );
      console.log('✅ Created unique index on studioId + fieldName');
    } catch (error) {
      if (error.code === 85) {
        console.log('Index already exists, skipping...');
      } else {
        console.log('Warning: Could not create unique index:', error.message);
      }
    }

    // Step 3: Update enrollment documents to reference new tag IDs
    console.log('\nStep 3: Updating enrollment documents...');

    // Find all enrollments that have tags
    const enrollmentsWithTags = await enrollmentsCollection
      .find({
        tags: { $exists: true, $not: { $size: 0 } },
      })
      .toArray();

    console.log(`Found ${enrollmentsWithTags.length} enrollments with tags`);

    let updatedEnrollments = 0;
    for (const enrollment of enrollmentsWithTags) {
      const updatedTags = [];
      let hasChanges = false;

      for (const tagId of enrollment.tags) {
        const tagIdString = tagId.toString();
        if (idMapping.has(tagIdString)) {
          // Replace with new tag ID
          updatedTags.push(idMapping.get(tagIdString));
          hasChanges = true;
        } else {
          // Keep existing ID (might already be a Tag ID)
          updatedTags.push(tagId);
        }
      }

      if (hasChanges) {
        await enrollmentsCollection.updateOne(
          { _id: enrollment._id },
          { $set: { tags: updatedTags } },
        );
        updatedEnrollments++;
        console.log(
          `Updated enrollment: ${enrollment._id} (${enrollment.title || 'No title'})`,
        );
      }
    }

    console.log(`\nUpdated ${updatedEnrollments} enrollment documents`);

    // Step 4: Verification
    console.log('\nStep 4: Verification...');

    // Count tags in both collections
    const customFormTagsCount = await customFormsCollection.countDocuments({
      fieldType: 'tags',
    });
    const dedicatedTagsCount = await tagsCollection.countDocuments({});

    console.log(`CustomForm tags count: ${customFormTagsCount}`);
    console.log(`Dedicated tags count: ${dedicatedTagsCount}`);

    // Check for duplicate tags in the tags collection
    const duplicateCheck = await tagsCollection
      .aggregate([
        {
          $group: {
            _id: { studioId: '$studioId', fieldName: '$fieldName' },
            count: { $sum: 1 },
            ids: { $push: '$_id' },
          },
        },
        { $match: { count: { $gt: 1 } } },
      ])
      .toArray();

    if (duplicateCheck.length > 0) {
      console.log(
        `❌ WARNING: Found ${duplicateCheck.length} duplicate tag combinations:`,
      );
      duplicateCheck.forEach((dup) => {
        console.log(
          `  - Studio ${dup._id.studioId}, Field "${dup._id.fieldName}": ${dup.count} duplicates`,
        );
      });
    } else {
      console.log('✅ No duplicate tags found in tags collection');
    }

    // Check if any enrollments still reference CustomForm tag IDs
    const oldCustomFormIds = Array.from(idMapping.keys()).map(
      (id) => new ObjectId(id),
    );
    const enrollmentsWithOldRefs = await enrollmentsCollection
      .find({
        tags: { $in: oldCustomFormIds },
      })
      .toArray();

    if (enrollmentsWithOldRefs.length > 0) {
      console.log(
        `❌ WARNING: ${enrollmentsWithOldRefs.length} enrollments still reference old CustomForm tag IDs`,
      );
    } else {
      console.log('✅ All enrollment tag references have been updated');
    }

    // Verify all enrollment tags now exist in tags collection
    const allEnrollmentTags = await enrollmentsCollection
      .aggregate([
        { $match: { tags: { $exists: true, $not: { $size: 0 } } } },
        { $unwind: '$tags' },
        { $group: { _id: '$tags' } },
      ])
      .toArray();

    const enrollmentTagIds = allEnrollmentTags.map((t) => t._id);
    const existingTagIds = await tagsCollection.find({}, { _id: 1 }).toArray();
    const existingTagIdStrings = existingTagIds.map((t) => t._id.toString());

    const orphanedTags = enrollmentTagIds.filter(
      (tagId) => !existingTagIdStrings.includes(tagId.toString()),
    );

    if (orphanedTags.length > 0) {
      console.log(
        `❌ WARNING: ${orphanedTags.length} enrollment tag references don't exist in tags collection`,
      );
    } else {
      console.log('✅ All enrollment tag references exist in tags collection');
    }

    console.log('\n=== MIGRATION COMPLETED ===');
    console.log('\nNext steps:');
    console.log(
      '1. Test the application to ensure tag functionality works correctly',
    );
    console.log(
      '2. If everything works, you can optionally remove tag records from customforms collection',
    );
    console.log(
      '3. Consider running: db.customforms.deleteMany({fieldType: "tags"})',
    );
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await client.close();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the migration
migrate().catch(console.error);
