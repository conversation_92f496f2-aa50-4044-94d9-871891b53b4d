name: Build Check on PR

on:
  pull_request:
    types: [opened, synchronize, reopened]
    branches: [main, beta, release/june-2025]
  workflow_dispatch:

jobs:
  build:
    name: Build Check
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 9
          run_install: false

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'pnpm'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Build
        run: pnpm run build

      # - name: Test
      #   run: pnpm run test

      - name: Report status
        if: always()
        run: |
          if [ ${{ job.status }} == 'success' ]; then
            echo "✅ Build succeeded!"
          else
            echo "❌ Build failed!"
            exit 1
          fi

      - name: Report test status
        if: always()
        run: |
          if [ ${{ job.status }} == 'success' ]; then
            echo "✅ Tests passed!"
          else
            echo "❌ Tests failed!"
            exit 1
          fi
