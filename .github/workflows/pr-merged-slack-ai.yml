name: Notify Slack with Summary on PR Merge

on:
  pull_request:
    types: [closed]

jobs:
  notify-slack:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    permissions:
      pull-requests: read
      contents: read

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get PR Details and Diff
        id: pr-details
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          set -x # Enable debugging

          # Get PR details
          PR_NUMBER="${{ github.event.pull_request.number }}"
          
          # Get the diff/patch
          PR_DIFF=$(curl -s \
            -H "Authorization: Bearer $GITHUB_TOKEN" \
            -H "Accept: application/vnd.github.v3.diff" \
            "https://api.github.com/repos/${{ github.repository }}/pulls/$PR_NUMBER")
          
          # Get files changed
          API_RESPONSE=$(curl -s \
            -H "Authorization: Bearer $GITHUB_TOKEN" \
            -H "Accept: application/vnd.github.v3+json" \
            "https://api.github.com/repos/${{ github.repository }}/pulls/$PR_NUMBER/files" | \
            jq -r '.[].filename' | head -20)
          
          # Save outputs with proper multiline handling
          {
            echo "pr_diff<<EOF"
            echo "$PR_DIFF"
            echo "EOF"
          } >> $GITHUB_OUTPUT
          
          {
            echo "files_changed<<EOF"
            echo "$FILES_CHANGED"
            echo "EOF"
          } >> $GITHUB_OUTPUT

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'

      - name: Install dependencies
        run: |
          cd script
          npm install

      - name: Generate AI Summary
        id: summarize
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          PR_TITLE: ${{ github.event.pull_request.title }}
          PR_BODY: ${{ github.event.pull_request.body }}
          PR_DIFF: ${{ steps.pr-details.outputs.pr_diff }}
          FILES_CHANGED: ${{ steps.pr-details.outputs.files_changed }}
        run: |
          cd script
          
          # Run the TypeScript script and capture output
          SUMMARY=$(npx ts-node generate-pr-summary.ts)
          
          # Save output with proper multiline handling
          {
            echo "summary<<EOF"
            echo "$SUMMARY"
            echo "EOF"
          } >> $GITHUB_OUTPUT

      - name: Send Enhanced Slack Notification
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
        run: |
          # Robustly count the number of changed files
          FILES_STRING="${{ steps.pr-details.outputs.files_changed }}"
          if [ -z "$FILES_STRING" ]; then
            FILES_COUNT=0
          else
            FILES_COUNT=$(echo "$FILES_STRING" | grep -c .)
          fi
          
          # Create Slack payload using jq for safe JSON construction
          jq -n \
            --arg title "*Title:*\n${{ github.event.pull_request.title }}" \
            --arg author "*Author:*\n@${{ github.event.pull_request.user.login }}" \
            --arg files "*Files Changed:*\n$FILES_COUNT files" \
            --arg repo "*Repository:*\n${{ github.repository }}" \
            --arg summary "*Summary:*\n${{ steps.summarize.outputs.summary }}" \
            --arg url "${{ github.event.pull_request.html_url }}" \
            '{
              blocks: [
                {
                  type: "header",
                  text: {type: "plain_text", text: "🚀 Pull Request Merged"}
                },
                {
                  type: "section",
                  fields: [
                    {type: "mrkdwn", text: $title},
                    {type: "mrkdwn", text: $author},
                    {type: "mrkdwn", text: $files},
                    {type: "mrkdwn", text: $repo}
                  ]
                },
                {
                  type: "section",
                  text: {type: "mrkdwn", text: $summary}
                },
                {
                  type: "actions",
                  elements: [
                    {type: "button", text:{type:"plain_text", text:"View PR"}, url:$url}
                  ]
                }
              ]
            }' > slack_payload.json

          # Send to Slack with error handling
          HTTP_STATUS=$(curl -w "%{http_code}" -o /dev/null -s -X POST \
            -H 'Content-type: application/json' \
            -d @slack_payload.json \
            "$SLACK_WEBHOOK_URL")
          
          if [ "$HTTP_STATUS" -ne 200 ]; then
            echo "Failed to send Slack notification. HTTP status: $HTTP_STATUS"
            exit 1
          fi

          # Clean up
          rm slack_payload.json