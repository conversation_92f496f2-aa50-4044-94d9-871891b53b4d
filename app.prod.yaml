runtime: nodejs20

env: standard

instance_class: F1

handlers:
  - url: /.*
    script: auto
    secure: always
  - url: .*
    script: auto

env_variables:
  NODE_ENV: 'production'
  GCS_ENV_BUCKET: 'enrollio_env_prod'
  GCS_ENV_FILE: 'env.prod'
  REDIS_HOST: redis-14828.c8.us-east-1-4.ec2.redns.redis-cloud.com
  REDIS_PORT: 14828
  REDIS_PASSWORD: wWDDmAV6FCgoUtAuBcFqWvQsIiFi7NbK
  REDIS_USERNAME: default
  CLIENT_ID: 66f2a7e4a99cef868fe37608-m1gdnxrx
  CLIENT_SECRET: 4f5ef2eb-d0b1-4ea6-ad1f-ce9d41da6551

  
automatic_scaling:
  min_idle_instances: automatic
  max_idle_instances: automatic
  min_pending_latency: automatic
  max_pending_latency: automatic
  target_cpu_utilization: 0.65
  max_instances: 3

entrypoint: npm run start:prod

service: enrollio-be

service_account: <EMAIL>